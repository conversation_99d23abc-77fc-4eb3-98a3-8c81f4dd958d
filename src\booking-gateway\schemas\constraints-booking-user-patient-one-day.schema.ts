import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY } from './constants';

const Schema = mongoose.Schema;

export const ConstraintsUserPatientBookingOneDaySchema = new Schema({
    id: { type: String },
    date: { type: Date },
    patientId: { type: String },
    userId: { type: String },
    constraintValue: { type: String, required: true, unique: true },
    status: { type: Number, default: 0 },
    partnerId: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
}, {
    collection: CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY,
    timestamps: true,
}).plugin(jsonMongo);
