import * as mongoose from 'mongoose';
import { SUBJECT_HOME_PAGE_IDS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const SubjectsHomePageIdsSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        id: {
            type: String,
            required: true,
        },
        cta: {
            type: Object,
            default: {},
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        status: {
            type: Boolean,
            default: false,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: SUBJECT_HOME_PAGE_IDS_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
