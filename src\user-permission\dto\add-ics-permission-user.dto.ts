import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsMongoId, IsNotEmpty } from 'class-validator';

export class  IAddIcsPermissionToUser {
    @ApiProperty()
    @IsMongoId()
    @IsNotEmpty({ message: 'User không được để trống !'})
    userId: string;

    @ApiProperty()
    @IsArray()
    @IsNotEmpty({message: 'Danh sách permissions không được để trống'})
    permissions: string[];

    @ApiProperty()
    @IsNotEmpty({message: 'isCS không được để trống'})
    isCS: boolean;
    
    @ApiProperty()
    @IsNotEmpty({message: 'isCare247 không được để trống'})
    isCare247: boolean;
}
