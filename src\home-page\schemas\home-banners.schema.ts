import * as mongoose from 'mongoose';
import { BANNERS_HOME_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const BannersHomeSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        status: {
            type: Boolean,
            default: false,
        },
        imageUrl: { type: String, required: true },
        cta: { type: Object, required: true },
        platform: {
            type: String,
            default: 'desktop',
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        alt: {
            type: String,
            default: null,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: BANNERS_HOME_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
