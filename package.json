{"name": "nest-api", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "build": "cross-env NODE_ENV=development nest build --watch --webpack", "build:no-watch-check-nestjs-command": "cross-env NODE_ENV=development nest build", "build:test": "cross-env NODE_ENV=test nest build", "build:prod": "cross-env NODE_ENV=production nest build", "build:dev": "cross-env NODE_ENV=development nest build", "build:staging": "cross-env NODE_ENV=staging nest build", "start": "cross-env NODE_ENV=development node dist/main", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "nest start --debug --watch", "start:test": "cross-env NODE_ENV=test node dist/main", "start:prod": "cross-env NODE_ENV=production node --max-old-space-size=6144 dist/main", "start:staging": "cross-env NODE_ENV=staging node dist/main", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "cli": "ts-node src/cli.ts", "nestjs-command:prod": "node dist/cli.js"}, "dependencies": {"@meanie/mongoose-to-json": "2.4.2", "@nestjs/common": "^6.11.11", "@nestjs/config": "^0.0.7", "@nestjs/core": "^6.11.11", "@nestjs/event-emitter": "^1.0.0", "@nestjs/jwt": "^6.1.1", "@nestjs/mongoose": "6.3.1", "@nestjs/passport": "^6.1.1", "@nestjs/platform-express": "^6.11.11", "@nestjs/schedule": "^0.2.0", "@nestjs/serve-static": "^2.0.0", "@nestjs/swagger": "^4.1.2", "@nestjsplus/config": "^1.0.14", "@ntegral/nestjs-sentry": "1.0.4", "@sendgrid/mail": "^6.5.4", "anymatch": "^3.1.3", "@types/cron": "^1.7.2", "@types/md5": "^2.1.33", "axios": "^0.19.2", "cache-manager": "^3.4.4", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.2.3", "class-validator": "0.13.2", "cron": "^1.8.2", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "exceljs": "3.8.0", "express-basic-auth": "^1.2.0", "express-ip": "^1.0.4", "express-rate-limit": "^5.1.1", "faker": "^4.1.0", "firebase-admin": "^9.0.0", "firebase-dynamic-links": "1.2.0", "form-data": "^3.0.0", "joi": "^14.3.1", "jsbarcode": "^3.11.5", "jsonwebtoken": "8.5.1", "knex": "^0.20.4", "lodash": "^4.17.15", "md5": "^2.2.1", "moment": "^2.24.0", "mongo-gridfs": "^1.1.0", "mongodb": "3.6.10", "mongoose": "5.9.3", "multer-gridfs-storage": "^4.2.0", "mysql": "^2.17.1", "nestjs-command": "^1.3.0", "nestjs-mailgun": "^4.0.3", "onesignal-node": "^3.0.0", "passport": "^0.4.1", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "qrcode": "^1.4.4", "query-string": "^6.11.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.0", "rxjs": "^6.5.3", "slugify": "^1.4.5", "sprintf-js": "^1.1.2", "strong-soap": "3.0.0", "swagger-ui-express": "^4.1.2", "throttle": "^1.0.3", "uuid": "^7.0.2", "validator": "^13.1.1", "whatwg-url": "6.5.0", "xmldom": "^0.6.0"}, "devDependencies": {"@nestjs/cli": "^6.9.0", "@nestjs/schematics": "^6.7.0", "@nestjs/testing": "^6.7.1", "@types/anymatch": "^3.0.4", "@types/cache-manager": "^3.4.1", "@types/cache-manager-redis-store": "^2.0.0", "@types/express": "^4.17.1", "@types/faker": "^4.1.10", "@types/jest": "^24.0.18", "@types/joi": "^14.3.4", "@types/jsonwebtoken": "8.5.1", "@types/lodash": "4.14.202", "@types/mongoose": "5.7.3", "@types/multer": "^1.4.7", "@types/node": "^12.7.5", "@types/passport-jwt": "^3.0.3", "@types/passport-local": "^1.0.33", "@types/supertest": "^2.0.8", "@types/throttle": "^1.0.1", "@types/uuid": "^3.4.7", "@types/whatwg-url": "8.2.1", "@types/yargs": "^15.0.4", "cross-env": "^7.0.0", "jest": "^24.9.0", "prettier": "^1.18.2", "supertest": "^4.0.2", "ts-jest": "^24.1.0", "ts-loader": "^6.1.1", "ts-node": "^8.4.1", "tsconfig-paths": "^3.9.0", "tslint": "^5.20.0", "typescript": "~4.5.2", "webpack-node-externals": "1.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "./coverage", "testEnvironment": "node"}, "resolutions": {"colors": "1.4.0", "memoizee": "0.4.15"}}