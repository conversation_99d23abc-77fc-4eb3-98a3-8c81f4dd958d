import { Document } from 'mongoose';

export interface ICancelBookingLog {
    uuid: string;
    name: string;
    summary: string;
    nameParent: string;
    params: any;
    response: any;
    errorBody: any;
    message: any;
    nameRepo: string;
    bookingCode: string;
    bookingDate: Date;
    status: number;
    partnerId: string;
}

export type ICancelBookingLogDocument = ICancelBookingLog & Document;
