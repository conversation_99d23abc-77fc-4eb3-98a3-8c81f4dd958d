import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class BookingQueryDto {

    @ApiProperty({
        description: 'searchText',
        required: false,
        type: String,
    })
    searchText?: string;

    @ApiProperty({
        description: 'appId',
        required: false,
        type: String,
    })
    appId?: string;

    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId?: string;

    @ApiProperty({
        description: 'platform - web, mobile, app',
        required: false,
        type: String,
    })
    platform?: string;

    @ApiProperty({
        description: 'cskhUserId - ID của CSKH thực hiện cancel',
        required: false,
        type: String,
    })
    cskhUserId?: string;


    @ApiProperty({
        description: 'reasonIds - filter theo reason IDs',
        required: false,
        type: [String],
    })
    reasonIds?: string;

    //có bank account info or lý do thôi không gửi lên thì all
    @ApiProperty({
        description: 'type - deprecated, sử dụng hasBankAccount thay thế',
        required: false,
        type: Number,
    })
    type?: number;
    

    //Ngày khám
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toDate?: string;

    //Thời gian thanh toán
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromBookingDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toBookingDate?: string;

    gatewayId?: string;

    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @Transform(value => Number(value))
    pageIndex?: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
