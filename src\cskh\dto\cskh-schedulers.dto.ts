import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CskhSchedulersDto {
    @ApiProperty({
        description: 'ID của cơ sở y tế',
        example: '63f76bc3f4a8b8e5c1234567',
    })
    @IsNotEmpty({ message: 'PartnerId là bắt buộc.' })
    @IsString({ message: 'PartnerId phải là một chuỗi.' })
    partnerId: string;

    @ApiProperty({
        description: 'ID của cơ sở y tế',
        example: '63f76bc3f4a8b8e5c1234567',
    })
    @IsNotEmpty({ message: 'PartnerId là bắt buộc.' })
    @IsString({ message: 'PartnerId phải là một chuỗi.' })
    partner: string;

    @ApiProperty({
        description: 'ID của nhân viên CSKH',
        example: '63f76bc3f4a8b8e5c7654321',
    })
    @IsNotEmpty({ message: 'CskhUserId là bắt buộc.' })
    @IsString({ message: 'CskhUserId phải là một chuỗi.' })
    cskhUserId: string;

    @ApiProperty({
        description: 'ID của lịch trình',
        example: '63f76bc3f4a8b8e5c8765432',
    })
    @IsNotEmpty({ message: 'ScheduleId là bắt buộc.' })
    @IsString({ message: 'ScheduleId phải là một chuỗi.' })
    scheduleId: string;

    @ApiProperty({
        description: 'Ngày bắt đầu',
        example: '2025-01-01T00:00:00.000Z',
    })
    @IsNotEmpty()
    fromDate: string;

    @ApiProperty({
        description: 'Ngày kết thúc',
        example: '2025-01-07T23:59:59.999Z',
    })
    @IsNotEmpty()
    toDate: string;

    @ApiProperty({
        description: 'Tiêu đề',
        example: 'ABC',
    })
    @IsString()
    title: string;
}
