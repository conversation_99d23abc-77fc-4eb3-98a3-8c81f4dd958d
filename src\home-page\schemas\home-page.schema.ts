import * as mongoose from 'mongoose';
import { HOME_PAGE } from './constants';

const Schema = mongoose.Schema;

export const HomePageSchema = new Schema(
    {
        ads: { type: Schema.Types.Mixed },
        partner: { type: Schema.Types.Mixed },
        hospitals: { type: Schema.Types.Mixed },
        telemed_doctor_in_month: { type: Schema.Types.Mixed },
        service_in_month: { type: Schema.Types.Mixed },
        testimonials: { type: Schema.Types.Mixed },
        subjects: { type: Schema.Types.Mixed },
        bannerServices: { type: Schema.Types.Mixed },
        banners: { type: Schema.Types.Mixed },
        bannersMulti: { type: Schema.Types.Mixed },
    },
    {
        collection: HOME_PAGE,
        timestamps: true,
    },
);