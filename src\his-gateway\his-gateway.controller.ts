import {
    Controller,
    Get,
    Headers,
    Body,
    Post,
    Query,
    Param,
    UseInterceptors,
    UseGuards,
    Req,
    // C<PERSON>Key,
    // Logger,
} from '@nestjs/common';
import { HisGatewayService } from './his-gateway.service';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { BookingTreeTypeDTO } from './dto/booking-tree-type.dto';
import { BookingTreeDynamicDTO } from './dto/booking-tree-dynamic.dto';
import { BookingTreeCurrentNodeDTO } from './dto/booking-tree-current-node.dto';
import { CacheManagerService } from 'src/cache-manager/cache-manager.service';
import { AppCskhInterceptor } from 'src/middleware/app-cskh.interceptor';
import { YearOldValidationDto } from '../patient-mongo/dto/year-old-validation.dto';
import { AuthGuard } from '@nestjs/passport';
import { PatientMongoService } from '../patient-mongo/patient-mongo.service';
import { PartnerCSKHGuard } from '../common/guards/partner-cskh.guard';
// import { getCacheKeyByObject } from './utils/helper';

@Controller('his-gateway')
@ApiTags('HIS Gateway - Quản lý HIS Gateway.')
export class HisGatewayController {
    constructor(
        private readonly service: HisGatewayService,
        private readonly cache: CacheManagerService,
        private readonly patientMongoService: PatientMongoService,
    ) { }

    @Get('check-find-one-update')
    async updateAvailableSlot(
        @Query('bookingSlotId') bookingSlotId: string,
    ): Promise<any> {
        return this.service.updateAvailableSlot(bookingSlotId);
    }

    @Get('test-time-id')
    async testTimeId(): Promise<any> {
        return await this.service.testTimeId();
    }

    @Get('test-boi-so')
    @ApiOperation({
        summary: 'Test bội số',
        description: 'Test bội số',
    })
    async testBoiSo(): Promise<any> {
        return await this.service.testBoiSo();
    }

    @Get('booking-tree')
    @ApiOperation({
        summary: 'Lấy thông tin của booking tree',
        description: 'Lấy thông tin của booking tree',
    })
    async getBookingTree(
        @Headers('partnerid') partnerid: string,
    ): Promise<any> {
        // const prefix = 'booking_tree';
        // const cacheKey = `${prefix}_${partnerid}`;
        // const bookingTreeCache = await this.cache.get(cacheKey);
        // if (bookingTreeCache) {
        //     return bookingTreeCache;
        // } else {
        //     const dataBookingTree = await this.service.getBookingTree(
        //         partnerid,
        //     );
        //     await this.cache.set(cacheKey, dataBookingTree);
        //     return dataBookingTree;
        // }
        return this.service.getBookingTree(partnerid);
    }

    @Post('booking-tree-v2')
    @ApiOperation({
        summary: 'Lấy thông tin của booking tree',
        description: 'Lấy thông tin của booking tree',
    })
    async getBookingTreeV2(
        @Headers('partnerid') partnerid: string,
        @Body() data: BookingTreeTypeDTO,
    ): Promise<any> {
        // const prefix = 'booking_tree_v2';
        // const keyFormData = getCacheKeyByObject(data);
        // const bookingTreeCacheKey = keyFormData
        //     ? `${partnerid}_${keyFormData}`
        //     : `${partnerid}`;
        // const cacheKey = `${prefix}_${bookingTreeCacheKey}`;
        // const bookingTreeCache = await this.cache.get(cacheKey);
        // if (bookingTreeCache) {
        //     return bookingTreeCache;
        // } else {
        //     const dataBookingTree = await this.service.getBookingTreeV2(
        //         partnerid,
        //         data,
        //     );
        //     await this.cache.set(cacheKey, dataBookingTree);
        //     return dataBookingTree;
        // }
        return this.service.getBookingTreeV2(partnerid, data);
    }

    @Post('booking-tree-dynamic')
    @ApiOperation({
        summary: 'Lấy thông tin của booking tree',
        description: 'Lấy thông tin của booking tree',
    })
    async getBookingTreeDynamic(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: BookingTreeDynamicDTO,
    ): Promise<any> {
        // const prefix = 'booking_tree_dynamic';
        // const keyFormData = getCacheKeyByObject(formData);
        // const bookingTreeCacheKey = keyFormData
        //     ? `${partnerid}_${keyFormData}`
        //     : `${partnerid}`;
        // const cacheKey = `${prefix}_${bookingTreeCacheKey}`;
        // const bookingTreeCache = await this.cache.get(cacheKey);
        // if (bookingTreeCache) {
        //     return bookingTreeCache;
        // } else {
        //     const dataBookingTree = await this.service.getBookingTreeDynamic(
        //         partnerid,
        //         formData,
        //     );
        //     await this.cache.set(cacheKey, dataBookingTree);
        //     return dataBookingTree;
        // }
        return this.service.getBookingTreeDynamic(partnerid, formData, appid);
    }

    @Post('booking-tree-dynamic-current-node')
    @ApiOperation({
        summary: 'Lấy thông tin của booking tree',
        description: 'Lấy thông tin của booking tree',
    })
    async getBookingTreeDynamicCurrentNode(
        @Headers('partnerid') partnerid: string,
        @Body() formData: BookingTreeCurrentNodeDTO,
    ): Promise<any> {
        // const prefix = 'booking_tree_dynamic_current_node';
        // const keyFormData = getCacheKeyByObject(formData);
        // const bookingTreeCacheKey = keyFormData
        //     ? `${partnerid}_${keyFormData}`
        //     : `${partnerid}`;
        // const cacheKey = `${prefix}_${bookingTreeCacheKey}`;
        // const bookingTreeCache = await this.cache.get(cacheKey);
        // if (bookingTreeCache) {
        //     return bookingTreeCache;
        // } else {
        //     const dataBookingTree = await this.service.getBookingTreeCurrentNode(
        //         partnerid,
        //         formData,
        //     );
        //     await this.cache.set(cacheKey, dataBookingTree);
        //     return dataBookingTree;
        // }
        return this.service.getBookingTreeCurrentNode(partnerid, formData);
    }

    @Get("medpro-care/:partnerId")
    @UseInterceptors(AppCskhInterceptor)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getMedproCare (
        @Req() req,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhtoken?: string,
        @Headers('platform') platform?: string,
        @Headers('version') version?: string,
        @Headers('locale') locale?: string,
    ) {
        let userMongoId
        const user = req.user;
        userMongoId = user.userMongoId;

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhtoken);
        if (cskhInfo?.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }
        return this.service.getMedproCareFromPortal(appid, partnerid, userMongoId, platform, version, locale)
    }

    @Post("medpro-care")
    @UseInterceptors(AppCskhInterceptor)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'), PartnerCSKHGuard)
    async getMedproCarePost (
        @Req() req,
        @Body() formData: YearOldValidationDto,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhtoken?: string,
        @Headers('platform') platform?: string,
        @Headers('version') version?: string,
        @Headers('locale') locale?: string,
    ) {
        let userMongoId: string;
        let isCs: boolean;
        const user = req.user;
        userMongoId = user.userMongoId;

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhtoken);

        if (cskhInfo?.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }
        if (cskhInfo?.cskhUserId) {
            isCs = true
        }
        
        return this.service.getMedproCarePost(formData, appid, partnerid, userMongoId, platform, version, locale, isCs)
    }
    
    @Get("message-event/medpro-care/:partnerId")
    getMedproCareFromMessageEvent (
        @Param('partnerId') partnerId: string,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Query('transactionId') transactionId: string,
    ) {
        return this.service.getMedproCareFromMessageEvent(appid, partnerid, partnerId, platform, version, transactionId)
    }

    @Post('test-golden-hour')
    async isWeekdayGoldenHourCare247(@Body() formData: any): Promise<any> {
        return await this.service.isWeekdayGoldenHourCare247(formData.date, formData.partnerId);
    }

    @Post('care247/landing-page')
    async getDataMedproCare(@Body() formData: any): Promise<any> {
        return await this.service.getDataMedproCareLandingPage();
    }
}
