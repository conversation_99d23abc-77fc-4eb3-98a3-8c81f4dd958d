import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { last, size } from 'lodash';
import { JwtUserConfigService } from '../../config/config.user.jwt.service';
import { UserService } from '../../user/user.service';
import { GlobalSettingService } from '../../global-setting/global-setting.service';
import { UtilService } from '../../config/util.service';
import { InjectModel } from '@nestjs/mongoose';
import { PATIENT_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';
import { Model } from 'mongoose';
import { IPatient } from '../../patient-mongo/intefaces/patient.inteface';

@Injectable()
export class AnKhangCSKHGuard implements CanActivate {
    constructor(
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        private jwtUserConfigService: JwtUserConfigService, 
        private readonly globalSettingService: GlobalSettingService,
        private readonly utilService: UtilService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();

        const { authorization, cskhtoken } = request.headers;
        const { headers, body, params, query, url, method } = request;

        if (size(authorization) > 0 && cskhtoken) {
            try {
                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const cskhUsersAnKhang = await this.globalSettingService.findByKeyAndRepoName('AN_KHANG_CSKH_USER');
                    let cskhUsersAnKhangList = cskhUsersAnKhang ? cskhUsersAnKhang.split(',') : [];
                    if (cskhUsersAnKhangList.includes(userMongoId)) {
                        if (url.includes('update-without-msbn')) {
                            const findPatient = await this.patientModel.findOne({ id: body.id }).exec();
                            if (findPatient && findPatient.cskhUserId !== userMongoId) {
                                throw new HttpException('Bạn chưa được phân quyền sử dụng chức năng này. Vui lòng liên hệ bộ phận CSKH Medpro để được hỗ trợ.', HttpStatus.FORBIDDEN);
                            }
                        } else {
                            throw new HttpException('Bạn chưa được phân quyền sử dụng chức năng này. Vui lòng liên hệ bộ phận CSKH Medpro để được hỗ trợ.', HttpStatus.FORBIDDEN);
                        }
                    }
                }
                return true;
            } catch (error) {
                throw new HttpException('Bạn chưa được phân quyền sử dụng chức năng này. Vui lòng liên hệ bộ phận CSKH Medpro để được hỗ trợ.', HttpStatus.FORBIDDEN);
            }
        }

        return true;
    }
}
