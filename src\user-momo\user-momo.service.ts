import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import * as jwt from 'jsonwebtoken';
import * as uuid from 'uuid';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { JwtModuleOptions, JwtService } from '@nestjs/jwt';
import { UserService } from 'src/user/user.service';
import { IUser } from 'src/user/interfaces/user.interface';
import { CreateUserMomoDto } from './dto/create-user-momo.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UtilService } from 'src/config/util.service';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';

@Injectable()
export class UserMomoService {
    private readonly logger: Logger = new Logger(UserMomoService.name);
    constructor(
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly userService: UserService,
        private readonly jwtService: JwtService,
        @InjectSentry() private readonly clientSentry: SentryService,
        private readonly eventEmitter: EventEmitter2,
        private readonly utilService: UtilService,
    ) {}

    async generateUUID(): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyMomoJwtOptions();
        const secretKey = jwt.sign({ momoId: uuid.v4().replace(/-/g, '') }, jwtOptions.secret, jwtOptions.signOptions);
        return secretKey;
    }

    async getMomoToken(secretKey: string): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyMomoJwtOptions();
        let momoId = '';
        try {
            const jwtVerify: any = jwt.verify(`${secretKey}`.trim(), jwtOptions.secret);
            momoId = jwtVerify.momoId;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        return momoId;
    }

    async createUserWithToken(secretKey: string): Promise<any> {
        const momoId = secretKey; // await this.getMomoToken(secretKey);
        /* kiểm tra xem có thông tin này chưa  */
        let payload = { username: '', sub: 0, userMongoId: null };

        try {
            const checkMomoId: IUser = await this.userService.checkMomoId(momoId);
            if (!checkMomoId) {
                /* tạo thông tin user */
                const user: IUser = await this.userService.createUserMoMo(momoId);
                /* gán thông tin */
                try {
                    await this.userService.getUserIdAndCreateSessionV1('medpro', 'noConfig', user);
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'getUserIdAndCreateSessionV1',
                        summary: 'getUserIdAndCreateSessionV1',
                        nameParent: 'createUserWithToken',
                        params: { secretKey },
                        errorBody: this.utilService.errorHandler(error),
                        message: error?.message || 'Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1',
                    });
                    this.clientSentry.instance().captureException(error);
                }
                payload = {
                    ...payload,
                    username: momoId,
                    userMongoId: user._id,
                };
            } else {
                payload = {
                    ...payload,
                    username: momoId,
                    userMongoId: checkMomoId._id,
                };
            }
            const resultData = {
                userId: 0,
                userName: momoId,
                fullName: '',
                number: '',
                email: momoId,
                historyBookingCount: 0,
                patientCount: 0,
            };
            const token = this.jwtService.sign(payload);
            return { ...resultData, token };
        } catch (error) {
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    async createUserMomo(formData: CreateUserMomoDto): Promise<any> {
        const momoId = formData.userId;
        const { email, phone } = formData.userInfo;
        /* kiểm tra xem có thông tin này chưa  */
        let payload = { username: '', sub: 0, userMongoId: null };
        const username = phone
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        // .replace(/^01/, '+84');
        try {
            const userInfo: IUser = await this.userService.findByUsername(username, momoId);
            if (!userInfo) {
                /* tạo thông tin user */
                const user = await this.userService.createUserMoMo(username);
                try {
                    await this.userService.getUserIdAndCreateSessionV1('medpro', 'noConfig', user);
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'getUserIdAndCreateSessionV1',
                        summary: 'getUserIdAndCreateSessionV1',
                        nameParent: 'createUserMomo',
                        params: { ...formData },
                        errorBody: this.utilService.errorHandler(error),
                        message: error?.message || 'Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1',
                    });
                    this.clientSentry.instance().captureException(error);
                }
                payload = {
                    ...payload,
                    username,
                    userMongoId: user._id,
                };
            } else {
                payload = {
                    ...payload,
                    username,
                    userMongoId: userInfo._id,
                };
            }
            const isHavePatient = userInfo?.patients?.length > 0 ? true : false;
            const fullName = formData.userInfo?.name || '';
            const resultData = {
                userId: 0,
                userName: username,
                fullName,
                email,
                havePatient: isHavePatient,
            };
            const token = this.jwtService.sign(payload);
            return { ...resultData, token };
        } catch (error) {
            this.logger.error(`Error when exec createUserMomo with momoId: ${momoId}. Cause: ${error.message}`);
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'createUserMomo',
                summary: 'createUserMomo',
                nameParent: 'createUserMomo',
                params: { ...formData },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!',
            });
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!', HttpStatus.BAD_REQUEST);
        }
    }
}
