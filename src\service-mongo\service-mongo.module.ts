import { HttpModule, Module } from '@nestjs/common';
import { ServiceMongoController } from './service-mongo.controller';
import { ServiceMongoService } from './service-mongo.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    CONFIG_COLLECTION_NAME,
    DOCTOR_DESCRIPTION_COLLECTION_NAME,
    DOCTOR_TELEMED_DESCRIPTION_COLLECTION_NAME,
    SCHEDULE_COLLECTION_NAME,
    SEARCH_KEYWORD_COLLECTION_NAME,
    SERVICE_COLLECTION_NAME,
    SERVICE_DESCRIPTION_COLLECTION_NAME,
    SHIFT_COLLECTION_NAME
} from './schemas/constants';
import { ServiceSchema } from './schemas/service.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { SECTION_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { SectionSchema } from 'src/booking-gateway/schemas/section.schema';
import { ShiftSchema } from './schemas/shift.schema';
import { ConfigSchema } from './schemas/config.schema';
import { ScheduleSchema } from './schemas/schedule.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { GlobalSettingModule } from '../global-setting/global-setting.module';
import { SearchKeywordsSchema } from './schemas/search-keywords.schema';
import { ServiceDescriptionSchema } from './schemas/service-description.schema';
import { DoctorDescriptionSchema } from '../doctor-description/schema/doctor-description.schema';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../hospital-description/schema/constants';
import { HospitalDescriptionSchema } from '../hospital-description/schema/hospital-description.schema';
import { DoctorTelemedDescriptionSchema } from "./schemas/doctor-sort.schema";
import { JwtModule } from "@nestjs/jwt";
import { JwtUserConfigService } from "../config/config.user.jwt.service";
import { UMC_DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { UmcDoctorSchema } from '../doctor-mongo/schemas/umc-doctor.schema';
import { DanhMucBangGiaSchema } from '../danh-muc-bang-gia/schemas/danh-muc-bang-gia.schema';
import { FeatureModule } from '../feature/feature.module';
import { AppIdSchema } from 'src/app-id/schemas/app-id.schema';
import { APP_ID_COLLECTION_NAME } from 'src/app-id/schemas/constants';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { FeeRefundConfigsSchema } from 'src/cash-back/schemas/fee-refund-configs.schema';
import { HomePageServiceProvider } from 'src/home-page/persistence/home-page.service';
import { HOME_PAGE } from '../home-page/schemas/constants';
import { HomePageSchema } from '../home-page/schemas/home-page.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
            { name: SECTION_COLLECTION_NAME, schema: SectionSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: APP_ID_COLLECTION_NAME, schema: AppIdSchema },
            { name: SHIFT_COLLECTION_NAME, schema: ShiftSchema },
            { name: CONFIG_COLLECTION_NAME, schema: ConfigSchema },
            { name: SCHEDULE_COLLECTION_NAME, schema: ScheduleSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: SERVICE_DESCRIPTION_COLLECTION_NAME, schema: ServiceDescriptionSchema },
            { name: DOCTOR_TELEMED_DESCRIPTION_COLLECTION_NAME, schema: DoctorTelemedDescriptionSchema },
            { name: DOCTOR_DESCRIPTION_COLLECTION_NAME, schema: DoctorDescriptionSchema },
            { name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, schema: HospitalDescriptionSchema },
            { name: UMC_DOCTOR_COLLECTION_NAME, schema: UmcDoctorSchema },
            { name: FEE_REFUND_CONFIGS_COLLECTION_NAME, schema: FeeRefundConfigsSchema },
            { name: HOME_PAGE, schema: HomePageSchema },
        ]),
        MongooseModule.forFeature(
            [
                {
                    name: SEARCH_KEYWORD_COLLECTION_NAME,
                    schema: SearchKeywordsSchema,
                },
            ],
            'services',
        ),
        JwtModule.registerAsync({
            useExisting: JwtUserConfigService,
        }),
        FeatureModule,
        GlobalSettingModule,
    ],
    controllers: [ServiceMongoController],
    providers: [ServiceMongoService, HomePageServiceProvider],
    exports: [ServiceMongoService],
})
export class ServiceMongoModule {}
