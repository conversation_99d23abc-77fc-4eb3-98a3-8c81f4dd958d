import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';

const schema = mongoose.Schema;

export const PartnerInFeatureSchema = new schema(
    {
        partnerId: String,
        message: String,
        sortOrder: { type: Number, default: Number.MAX_VALUE },
        features: { type: [FeatureSchema], default: [] },
    },
    {
        timestamps: true,
        autoIndex: false,
    },
).plugin(jsonMongo);
