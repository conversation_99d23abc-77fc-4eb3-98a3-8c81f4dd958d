import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { PageOptionnsDto } from "../common/base/page-options.dto";
import { SCHEMA_NEWS_DESCRIPTION_COLLECTION_NAME } from "./schema/constants";
import { ISchemaNews } from "./interface/schema-news.interface";

@Injectable()
export class SchemaNewsService {
    private readonly logger = new Logger(SchemaNewsService.name);

    constructor(@InjectModel(SCHEMA_NEWS_DESCRIPTION_COLLECTION_NAME) private schemaDesModel: Model<ISchemaNews>) {
    }

    async getSchemaNewss(query: PageOptionnsDto): Promise<any> {
        const seoSql = {};
        const total = await this.schemaDesModel.countDocuments(seoSql).exec();
        let thisSchemaDes = await this.schemaDesModel.find(seoSql).skip(query.skip).limit(query.limit).exec();
        thisSchemaDes = thisSchemaDes.map((s: any) => {
            s.faq = s.faq.sort((a, b) => a?.sortOrder - b?.sortOrder);
            return s;
        });

        return {
            page: query.page,
            limit: query.limit,
            rows: thisSchemaDes,
            total
        };
    }

    async getSchemaNews(id: string): Promise<any> {
        let thisSchemaNewsDes: any = await this.schemaDesModel.findOne({ slug: id });
        if (!thisSchemaNewsDes) return null;
        thisSchemaNewsDes.faq = thisSchemaNewsDes?.faq?.sort((a, b) => a?.sortOrder - b?.sortOrder);
        thisSchemaNewsDes.howto = thisSchemaNewsDes?.howto?.sort((a, b) => a?.sortOrder - b?.sortOrder);
        return thisSchemaNewsDes;
    }
}
