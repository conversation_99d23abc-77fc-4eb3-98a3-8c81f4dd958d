import { Module } from '@nestjs/common';
import { PushNotifController } from './push-notif.controller';
import { PushNotifService } from './push-notif.service';
import { NotificationSettingService } from 'src/notification-setting/notification-setting.service';
import { PushDeviceService } from 'src/push-device/push-device.service';
import { MongooseModule } from '@nestjs/mongoose';
import { PushDeviceSchema } from 'src/push-device/schemas/push-device.schema';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { ReceiverListSchema } from 'src/message-config/schemas/receiver-list.schema';
import { RECEIVER_LIST_COLLECTION_NAME, RECEIVER_VERSION_COLLECTION_NAME, MESSAGE_ITEM_COLLECTION_NAME, MESSAGE_GROUP_PHONE_COLLECTION_NAME } from 'src/message-config/schemas/constants';
import { ReceiverVersionListSchema } from 'src/message-config/schemas/receiver-version.schema';
import { MessageItemSchema } from 'src/message-config/schemas/message-item.schema';
import { EVENT_COLLECTION_NAME, MESSAGE_SEND_RECORD_COLLECTION_NAME } from 'src/event/schemas/constants';
import { EventSchema } from 'src/event/schemas/event.schema';
import { MessageGroupPhoneSchema } from 'src/message-config/schemas/message-group-phone.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { MessageSendRecordSchema } from 'src/event/schemas/message-send-record.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
      { name: RECEIVER_LIST_COLLECTION_NAME, schema: ReceiverListSchema },
      { name: RECEIVER_VERSION_COLLECTION_NAME, schema: ReceiverVersionListSchema },
      { name: MESSAGE_ITEM_COLLECTION_NAME, schema: MessageItemSchema },
      { name: EVENT_COLLECTION_NAME, schema: EventSchema },
      { name: MESSAGE_GROUP_PHONE_COLLECTION_NAME, schema: MessageGroupPhoneSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: MESSAGE_SEND_RECORD_COLLECTION_NAME, schema: MessageSendRecordSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
    ]),
  ],
  controllers: [PushNotifController],
  providers: [PushNotifService, NotificationSettingService, PushDeviceService],
})
export class PushNotifModule { }
