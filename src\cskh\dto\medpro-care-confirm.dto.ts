import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MedproCareConfirmDto {
    @ApiProperty({ description: 'id care247', required: true })
    @IsNotEmpty({ message: 'id is required' })
    id: string;
    
    @ApiProperty({ description: 'id user cs', required: true })
    @IsNotEmpty({ message: 'cskhConfirmedCare247 is required' })
    cskhConfirmedCare247: string;
}
