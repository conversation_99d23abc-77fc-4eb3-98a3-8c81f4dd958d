import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME, PUSH_NOTIF_CSKH, PUSH_NOTIF_CSKH_CONSTRAINT } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { CskhController } from './cskh.controller';
import { CskhService } from './cskh.service';
import { UserModule } from '../user/user.module';
import { GlobalSettingModule } from '../global-setting/global-setting.module';
import { ResetOtpLogSchema } from './schemas/reset-otp-log.schema';
import { BOOKING_CARE_247_CONSTRAINT, BOOKING_CARE_247_SUCCESS, RESET_OTP_LOGS_COLLECTION_NAME } from './schemas/constants';
import { REPAYMENT_LOG_COLLECTION_NAME } from '../message-event/schemas/constants';
import { RepaymentLogSchema } from '../message-event/schemas/repayment-log.schema';
import {
    BOOKING_CARE_247,
    BOOKING_COLLECTION_NAME,
    BOOKING_LOCKED_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    VIEW_BOOKING_LOG_COLLECTION_NAME,
} from '../booking-gateway/schemas/constants';
import { BookingSchema } from '../booking-gateway/schemas/booking.schema';
import { BookingLockSchema } from '../booking-gateway/schemas/booking-locked.schema';
import { ViewBookingLogSchema } from '../booking-gateway/schemas/view-booking-log.schema';
import { PaymentSchema } from '../booking-gateway/schemas/payment.schema';
import { PatientSchema } from '../patient-mongo/schemas/patient.schema';
import { PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { BookingCare247Success } from './schemas/booking-care-247-success.schema';
import { BookingCare247Schema } from '../booking-gateway/schemas/booking-care-247.schema';
import { BookingCare247ConstraintSchema } from './schemas/booking-care247-constraint.schema';
import { BookingGatewayModule } from '../booking-gateway/booking-gateway.module';
import { PushNotifCskhSchema } from './schemas/push-notif-cskh.schema';
import { PushNotifCskhConstraintSchema } from './schemas/push-notif-cskh-constraint.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: RESET_OTP_LOGS_COLLECTION_NAME, schema: ResetOtpLogSchema },
            { name: REPAYMENT_LOG_COLLECTION_NAME, schema: RepaymentLogSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: BOOKING_LOCKED_COLLECTION_NAME, schema: BookingLockSchema },
            { name: VIEW_BOOKING_LOG_COLLECTION_NAME, schema: ViewBookingLogSchema },
            { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: BOOKING_CARE_247_SUCCESS, schema: BookingCare247Success },
            { name: BOOKING_CARE_247, schema: BookingCare247Schema },
            { name: BOOKING_CARE_247_CONSTRAINT, schema: BookingCare247ConstraintSchema },
            { name: PUSH_NOTIF_CSKH, schema: PushNotifCskhSchema },
            { name: PUSH_NOTIF_CSKH_CONSTRAINT, schema: PushNotifCskhConstraintSchema },
        ]),
        UserModule,
        GlobalSettingModule,
        PatientMongoModule,
        BookingGatewayModule,
    ],
    controllers: [CskhController],
    providers: [CskhService],
    exports: [CskhService],
})
export class CskhModule {}
