import { JobUpdateKpiSchema } from './schemas/job-update-kpi.schema';
import { forwardRef, HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME, PUSH_NOTIF_CSKH, PUSH_NOTIF_CSKH_CONSTRAINT } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { USER_COLLECTION_NAME, USER_LOGIN_LOG_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { CskhController } from './cskh.controller';
import { CskhService } from './cskh.service';
import { UserModule } from '../user/user.module';
import { GlobalSettingModule } from '../global-setting/global-setting.module';
import { ResetOtpLogSchema } from './schemas/reset-otp-log.schema';
import { AUDIT_LOG_CSKH, BOOKING_CARE_247_CONSTRAINT, BOOKING_CARE_247_CONSTRAINT_USER, BOOKING_CARE_247_FLAGS, BOOKING_CARE_247_SUCCESS, CSKH_CONTACT_CUSTOMERS, INTRODUCTION_CARE247_SERVICE_CHANGES, KPI_CSKH, RESET_OTP_LOGS_COLLECTION_NAME, SETTING_SALARY_CARE247, TRACKING_PUSH_NOTIF_CARE247, TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA, CSKH_SCHEDULE_CATEGORIES, CSKH_SCHEDULERS, CARE247_SALES, CSKH_SURVEY_CARE247_COLLECTION, RETRY_PAYMENT_BOOKING, CALL_BOT_CSKH, REFUND_TRANSACTION_SMS, JOB_UPDATE_KPI, CARE247_INDEPENDENT_PAYMENT } from './schemas/constants';
import { REPAYMENT_LOG_COLLECTION_NAME, BOOKING_REFERRAL_COLLECTION_NAME } from '../message-event/schemas/constants';
import { RepaymentLogSchema } from '../message-event/schemas/repayment-log.schema';
import { BookingReferralSchema } from '../message-event/schemas/booking-referral.schema';
import {
    BOOKING_CARE_247,
    BOOKING_COLLECTION_NAME,
    BOOKING_IGNORE_CONVERT,
    BOOKING_LOCKED_COLLECTION_NAME,
    CONVERT_USER_CSKH,
    PAYMENT_COLLECTION_NAME,
    RESERVE_BOOKING_LOGS_COLLECTION_NAME,
    SECTION_COLLECTION_NAME,
    VIEW_BOOKING_LOG_COLLECTION_NAME,
} from '../booking-gateway/schemas/constants';
import { BookingSchema } from '../booking-gateway/schemas/booking.schema';
import { BookingLockSchema } from '../booking-gateway/schemas/booking-locked.schema';
import { ViewBookingLogSchema } from '../booking-gateway/schemas/view-booking-log.schema';
import { PaymentSchema } from '../booking-gateway/schemas/payment.schema';
import { BookingCare247Success } from './schemas/booking-care-247-success.schema';
import { BookingCare247Schema } from '../booking-gateway/schemas/booking-care-247.schema';
import { BookingCare247ConstraintSchema } from './schemas/booking-care247-constraint.schema';
import { PushNotifCskhSchema } from './schemas/push-notif-cskh.schema';
import { PushNotifCskhConstraintSchema } from './schemas/push-notif-cskh-constraint.schema';
import { SettingSalaryCare247Schema } from './schemas/setting-salary-care247.schema';
import { UserPermissionSchema } from 'src/user-permission/schema/user-permission.schema';
import { USER_PERMISSION_COLLECTION_NAME } from 'src/user-permission/schema/constant';
import { UserLoginLogSchema } from '../user/schemas/user-login-logs.schema';
import { AuditLogCskhSchema } from './schemas/audit-log-cskh.schema';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from 'src/report/schemas/constants';
import { ReportTransactionDailySchema } from 'src/report/schemas/report-daily.schema';
import { MailModule } from '../mailgun/mailgun.module';
import { TrackingPushNotifTCKQSchema } from './schemas/tracking-push-notif-tckq.schema';
import { Care247IntroductionChangesSchema } from './schemas/introduction-care247-changes.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { PatientSchema } from '../patient-mongo/schemas/patient.schema';
import { BookingCare247ConstraintUserSchema } from './schemas/booking-care247-constraint-user.schema';
import { BookingCare247FlagsSchema } from './schemas/booking-care247-flags.schema';
import { TrackingPushNotifCare247Schema } from './schemas/tracking-push-notif-care247.schema';
import { KpiCskhSchema } from './schemas/kpi-cskh.schema';
import { ContactCustomersSchema } from './schemas/contact-customers.schema';
import { CskhScheduleCategoriesSchema } from './schemas/cskh-schedule-categories.schema';
import { CskhSchedulersSchema } from './schemas/cskh-schedulers.schema';
import { CskhSurveyCare247Schema } from './schemas/cskh-survey-care247.schema';
import { Care247SalesSchema } from './schemas/care247-sales.schema';
import { RetryPaymentBookingSchema } from './schemas/retry-payment-bookings.schema';
import { CallBotCskhSchema } from './schemas/call-bot-cskh.schema';
import { ConvertUserCskhSchema } from '../booking-gateway/schemas/convert-user-cskh.schema';
import { BookingIgnoreConvertSchema } from '../booking-gateway/schemas/booking-ignore-convert.schema';
import { ReserveBookingLogsSchema } from 'src/booking-gateway/schemas/reserve-booking-logs.schema';
import { SectionSchema } from 'src/booking-gateway/schemas/section.schema';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { DoctorSchema } from 'src/doctor-mongo/schemas/doctor.schema';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { RoomSchema } from 'src/room-mongo/schemas/room.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import { RefundTransactionSmsSchema } from './schemas/refund-transaction-sms.schema';
import { SmsService } from '../sms/sms.service';
import { PARTNER_CONFIG_COLLECTION_NAME } from '../partner-config/schemas/constants';
import { PartnerConfigSchema } from '../partner-config/schemas/partner-config.schema';
import { Care247IndependentPaymentSchema } from './schemas/care247-payment-token.schema';
import { HisGatewayModule } from '../his-gateway/his-gateway.module';
import { PaymentMethodModule } from '../payment-method/payment-method.module';
import { BookingGatewayModule } from '../booking-gateway/booking-gateway.module';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: AUDIT_LOG_CSKH, schema: AuditLogCskhSchema },
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: RESET_OTP_LOGS_COLLECTION_NAME, schema: ResetOtpLogSchema },
            { name: REPAYMENT_LOG_COLLECTION_NAME, schema: RepaymentLogSchema },
            { name: BOOKING_REFERRAL_COLLECTION_NAME, schema: BookingReferralSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: BOOKING_LOCKED_COLLECTION_NAME, schema: BookingLockSchema },
            { name: VIEW_BOOKING_LOG_COLLECTION_NAME, schema: ViewBookingLogSchema },
            { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
            { name: BOOKING_CARE_247_SUCCESS, schema: BookingCare247Success },
            { name: BOOKING_CARE_247, schema: BookingCare247Schema },
            { name: BOOKING_CARE_247_CONSTRAINT, schema: BookingCare247ConstraintSchema },
            { name: PUSH_NOTIF_CSKH, schema: PushNotifCskhSchema },
            { name: PUSH_NOTIF_CSKH_CONSTRAINT, schema: PushNotifCskhConstraintSchema },
            { name: SETTING_SALARY_CARE247, schema: SettingSalaryCare247Schema },
            { name: USER_PERMISSION_COLLECTION_NAME, schema: UserPermissionSchema },
            { name: USER_LOGIN_LOG_COLLECTION_NAME, schema: UserLoginLogSchema },
            { name: DAILY_TRANSACTION_REPORT_COLLECTION_NAME, schema: ReportTransactionDailySchema },
            { name: TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA, schema: TrackingPushNotifTCKQSchema },
            { name: TRACKING_PUSH_NOTIF_CARE247, schema: TrackingPushNotifCare247Schema },
            { name: INTRODUCTION_CARE247_SERVICE_CHANGES, schema: Care247IntroductionChangesSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: BOOKING_CARE_247_CONSTRAINT_USER, schema: BookingCare247ConstraintUserSchema },
            { name: BOOKING_CARE_247_FLAGS, schema: BookingCare247FlagsSchema },
            { name: KPI_CSKH, schema: KpiCskhSchema },
            { name: CSKH_CONTACT_CUSTOMERS, schema: ContactCustomersSchema },
            { name: CSKH_SCHEDULE_CATEGORIES, schema: CskhScheduleCategoriesSchema },
            { name: CSKH_SCHEDULERS, schema: CskhSchedulersSchema },
            { name: CSKH_SURVEY_CARE247_COLLECTION, schema: CskhSurveyCare247Schema },
            { name: CARE247_SALES, schema: Care247SalesSchema },
            { name: RETRY_PAYMENT_BOOKING, schema: RetryPaymentBookingSchema },
            { name: CALL_BOT_CSKH, schema: CallBotCskhSchema },
            { name: CONVERT_USER_CSKH, schema: ConvertUserCskhSchema },
            { name: BOOKING_IGNORE_CONVERT, schema: BookingIgnoreConvertSchema },
            { name: RESERVE_BOOKING_LOGS_COLLECTION_NAME, schema: ReserveBookingLogsSchema },
            { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
            { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
            { name: ROOM_COLLECTION_NAME, schema: RoomSchema },
            { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
            { name: SECTION_COLLECTION_NAME, schema: SectionSchema },
            { name: REFUND_TRANSACTION_SMS, schema: RefundTransactionSmsSchema },
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: JOB_UPDATE_KPI, schema: JobUpdateKpiSchema },
            { name: CARE247_INDEPENDENT_PAYMENT, schema: Care247IndependentPaymentSchema },
        ]),
        UserModule,
        MailModule,
        GlobalSettingModule,
        HisGatewayModule,
        forwardRef(() => PaymentMethodModule),
        forwardRef(() => PatientMongoModule),
    ],
    controllers: [CskhController],
    providers: [CskhService, SmsService],
    exports: [CskhService],
})
export class CskhModule {}
