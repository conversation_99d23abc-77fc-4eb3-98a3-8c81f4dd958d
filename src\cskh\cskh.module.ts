import { forwardRef, HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME, PUSH_NOTIF_CSKH, PUSH_NOTIF_CSKH_CONSTRAINT } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { USER_COLLECTION_NAME, USER_LOGIN_LOG_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { CskhController } from './cskh.controller';
import { CskhService } from './cskh.service';
import { UserModule } from '../user/user.module';
import { GlobalSettingModule } from '../global-setting/global-setting.module';
import { ResetOtpLogSchema } from './schemas/reset-otp-log.schema';
import { AUDIT_LOG_CSKH, BOOKING_CARE_247_CONSTRAINT, BOOKING_CARE_247_CONSTRAINT_USER, BOOKING_CARE_247_FLAGS, BOOKING_CARE_247_SUCCESS, CSKH_CONTACT_CUSTOMERS, INTRODUCTION_CARE247_SERVICE_CHANGES, KPI_CSKH, RESET_OTP_LOGS_COLLECTION_NAME, SETTING_SALARY_CARE247, TRACKING_PUSH_NOTIF_CARE247, TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA, CSKH_SCHEDULE_CATEGORIES, CSKH_SCHEDULERS, CARE247_SALES, CSKH_SURVEY_CARE247_COLLECTION, RETRY_PAYMENT_BOOKING, CALL_BOT_CSKH } from './schemas/constants';
import { REPAYMENT_LOG_COLLECTION_NAME } from '../message-event/schemas/constants';
import { RepaymentLogSchema } from '../message-event/schemas/repayment-log.schema';
import {
    BOOKING_CARE_247,
    BOOKING_COLLECTION_NAME,
    BOOKING_LOCKED_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    VIEW_BOOKING_LOG_COLLECTION_NAME,
} from '../booking-gateway/schemas/constants';
import { BookingSchema } from '../booking-gateway/schemas/booking.schema';
import { BookingLockSchema } from '../booking-gateway/schemas/booking-locked.schema';
import { ViewBookingLogSchema } from '../booking-gateway/schemas/view-booking-log.schema';
import { PaymentSchema } from '../booking-gateway/schemas/payment.schema';
import { BookingCare247Success } from './schemas/booking-care-247-success.schema';
import { BookingCare247Schema } from '../booking-gateway/schemas/booking-care-247.schema';
import { BookingCare247ConstraintSchema } from './schemas/booking-care247-constraint.schema';
import { PushNotifCskhSchema } from './schemas/push-notif-cskh.schema';
import { PushNotifCskhConstraintSchema } from './schemas/push-notif-cskh-constraint.schema';
import { SettingSalaryCare247Schema } from './schemas/setting-salary-care247.schema';
import { UserPermissionSchema } from 'src/user-permission/schema/user-permission.schema';
import { USER_PERMISSION_COLLECTION_NAME } from 'src/user-permission/schema/constant';
import { UserLoginLogSchema } from '../user/schemas/user-login-logs.schema';
import { AuditLogCskhSchema } from './schemas/audit-log-cskh.schema';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from 'src/report/schemas/constants';
import { ReportTransactionDailySchema } from 'src/report/schemas/report-daily.schema';
import { MailModule } from '../mailgun/mailgun.module';
import { TrackingPushNotifTCKQSchema } from './schemas/tracking-push-notif-tckq.schema';
import { Care247IntroductionChangesSchema } from './schemas/introduction-care247-changes.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { PatientSchema } from '../patient-mongo/schemas/patient.schema';
import { BookingCare247ConstraintUserSchema } from './schemas/booking-care247-constraint-user.schema';
import { BookingCare247FlagsSchema } from './schemas/booking-care247-flags.schema';
import { TrackingPushNotifCare247Schema } from './schemas/tracking-push-notif-care247.schema';
import { KpiCskhSchema } from './schemas/kpi-cskh.schema';
import { ContactCustomersSchema } from './schemas/contact-customers.schema';
import { CskhScheduleCategoriesSchema } from './schemas/cskh-schedule-categories.schema';
import { CskhSchedulersSchema } from './schemas/cskh-schedulers.schema';
import { CskhSurveyCare247Schema } from './schemas/cskh-survey-care247.schema';
import { Care247SalesSchema } from './schemas/care247-sales.schema';
import { RetryPaymentBookingSchema } from './schemas/retry-payment-bookings.schema';
import { CallBotCskhSchema } from './schemas/call-bot-cskh.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: AUDIT_LOG_CSKH, schema: AuditLogCskhSchema },
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: RESET_OTP_LOGS_COLLECTION_NAME, schema: ResetOtpLogSchema },
            { name: REPAYMENT_LOG_COLLECTION_NAME, schema: RepaymentLogSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: BOOKING_LOCKED_COLLECTION_NAME, schema: BookingLockSchema },
            { name: VIEW_BOOKING_LOG_COLLECTION_NAME, schema: ViewBookingLogSchema },
            { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
            { name: BOOKING_CARE_247_SUCCESS, schema: BookingCare247Success },
            { name: BOOKING_CARE_247, schema: BookingCare247Schema },
            { name: BOOKING_CARE_247_CONSTRAINT, schema: BookingCare247ConstraintSchema },
            { name: PUSH_NOTIF_CSKH, schema: PushNotifCskhSchema },
            { name: PUSH_NOTIF_CSKH_CONSTRAINT, schema: PushNotifCskhConstraintSchema },
            { name: SETTING_SALARY_CARE247, schema: SettingSalaryCare247Schema },
            { name: USER_PERMISSION_COLLECTION_NAME, schema: UserPermissionSchema },
            { name: USER_LOGIN_LOG_COLLECTION_NAME, schema: UserLoginLogSchema },
            { name: DAILY_TRANSACTION_REPORT_COLLECTION_NAME, schema: ReportTransactionDailySchema },
            { name: TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA, schema: TrackingPushNotifTCKQSchema },
            { name: TRACKING_PUSH_NOTIF_CARE247, schema: TrackingPushNotifCare247Schema },
            { name: INTRODUCTION_CARE247_SERVICE_CHANGES, schema: Care247IntroductionChangesSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: BOOKING_CARE_247_CONSTRAINT_USER, schema: BookingCare247ConstraintUserSchema },
            { name: BOOKING_CARE_247_FLAGS, schema: BookingCare247FlagsSchema },
            { name: KPI_CSKH, schema: KpiCskhSchema },
            { name: CSKH_CONTACT_CUSTOMERS, schema: ContactCustomersSchema },
            { name: CSKH_SCHEDULE_CATEGORIES, schema: CskhScheduleCategoriesSchema },
            { name: CSKH_SCHEDULERS, schema: CskhSchedulersSchema },
            { name: CSKH_SURVEY_CARE247_COLLECTION, schema: CskhSurveyCare247Schema },
            { name: CARE247_SALES, schema: Care247SalesSchema },
            { name: RETRY_PAYMENT_BOOKING, schema: RetryPaymentBookingSchema },
            { name: CALL_BOT_CSKH, schema: CallBotCskhSchema },
        ]),
        UserModule,
        MailModule,
        GlobalSettingModule,
        forwardRef(() => PatientMongoModule),
        // PatientMongoModule,
    ],
    controllers: [CskhController],
    providers: [CskhService],
    exports: [CskhService],
})
export class CskhModule {}
