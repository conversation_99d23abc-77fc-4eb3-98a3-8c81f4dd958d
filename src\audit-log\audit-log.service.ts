import { ServiceLogRequest } from './dto/service-log.request.dto';
import { IServiceLog, IServiceLogDocument } from './interfaces/service-log.schema';
import { CreateAuditLogDto } from './dto/audit-log.dto';
import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { get } from 'lodash';

import { IAuditLog, IAuditLogDocument } from './interfaces/audit-log.interface';
import { AUDIT_LOG_NAME, AUDIT_LOG_GET_NAME, CANCEL_BOOKING_LOG_NAME, ExposeUrl, LOG_REQUEST_EVENT, LOG_SERVICE_EVENT, SERVICE_LOG_NAME, SERVICE_TRACKING_LOG_NAME } from './constant';
import { OnEvent } from '@nestjs/event-emitter';
import { AuditLogRequest } from './dto/audit-log.request';
import { CreateServiceLog } from './dto/create-service.dto';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ICancelBookingLogDocument } from './interfaces/cancel-booking-log.schema';
import { IAuditLogGetDocument } from './interfaces/audit-log-get.interface';
import { IServiceTrackingLogDocument } from './interfaces/service-tracking-log.schema';

@Injectable()
export class AuditLogService {
    constructor(
        @InjectModel(AUDIT_LOG_NAME) private readonly auditLogModel: Model<IAuditLogDocument>,
        @InjectModel(AUDIT_LOG_GET_NAME) private readonly auditLogGetModel: Model<IAuditLogGetDocument>,
        @InjectModel(SERVICE_LOG_NAME) private readonly serviceLogModel: Model<IServiceLogDocument>,
        @InjectModel(SERVICE_TRACKING_LOG_NAME) private readonly serviceTrackingLogModel: Model<IServiceTrackingLogDocument>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(CANCEL_BOOKING_LOG_NAME) private cancelBookingLogModel: Model<ICancelBookingLogDocument>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private config: ConfigRepoService,
    ) { }

    async processCheckStatus(partnerIdCheck: string): Promise<any> {
        const cancelLogs = await this.cancelBookingLogModel.find({}).exec();
        for await (const log of cancelLogs) {
            const bookingId = get(log, 'params.bookingId', 0);
            const partnerId = get(log, 'params.partnerId', '');
            if (bookingId > 0 && `${partnerIdCheck}`.trim() === `${partnerId}`.trim()) {
                let tableName = '';
                if (partnerId === 'umc') {
                    tableName = 'booking';
                } else if (partnerId === 'nhidong1') {
                    tableName = 'nd1_booking';
                }
                const bookingInfo = await this.pkhPatientKnex(tableName).where('id', bookingId).first();
                if (bookingInfo) {
                    const status = bookingInfo.status;
                    const bookingDate = bookingInfo.booking_date;
                    const bookingCode = bookingInfo.transaction_code_gd;
                    /* update lại. */
                    await this.cancelBookingLogModel.findByIdAndUpdate({ _id: log._id }, { status, bookingDate, bookingCode, partnerId }).exec();
                }
            }
        }
    }

    @OnEvent(LOG_REQUEST_EVENT, { async: true })
    async onRequestSend(request: any): Promise<void> {
        if (this.filterUrl(request.url)) {
            try {
                const newLog = (({
                    headers,
                    body,
                    params,
                    query,
                    url,
                    method }) => ({
                        headers,
                        body,
                        params,
                        query,
                        url,
                        method,
                    }))(request);
                await this.createAuditLog(newLog);
            } catch (error) {
                throw error;
            }
        }

    }

    @OnEvent(LOG_SERVICE_EVENT, { async: true })
    async onServiceError(payload: any): Promise<void> {
        try {
            await this.createServiceLog(payload);
        } catch (error) {
            console.log(`@OnEvent(LOG_SERVICE_EVENT, { async: true }): `, error)
            throw error;
        }
    }

    async createServiceLog(data: CreateServiceLog): Promise<IServiceLog> {
        try {
            if (data.userId) {
                return await this.serviceTrackingLogModel.create({ ...data, nameRepo: this.config.getRepoName() });
            } else {
                return await this.serviceLogModel.create({ ...data, nameRepo: this.config.getRepoName() });
            }
        } catch (error) {
            throw error;
        }
    }

    async getServiceLogs(formData: ServiceLogRequest): Promise<any> {
        try {
            let filter = {};
            const { pageSize, pageIndex, nameParent, nameRepo } = formData;
            if (nameParent) {
                filter = { ...filter, nameParent };
            }
            if (nameRepo) {
                filter = { ...filter, nameRepo };
            }
            const [rows, totalRows] = await Promise.all([
                this.serviceLogModel
                    .find({})
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .where(filter)
                    .sort({ createdAt: `desc` })
                    .exec(),
                this.serviceLogModel.find(filter).countDocuments(),
            ]);
            return {
                pageSize,
                pageIndex,
                rows,
                totalRows,
            };
        } catch (error) {
            throw error;
        }
    }

    async getServiceTrackingLogs(formData: ServiceLogRequest): Promise<any> {
            let filter = {};
            const { pageSize = 100, pageIndex = 0, nameParent, nameRepo, userId } = formData;
            if (nameParent) {
                filter = { ...filter, nameParent };
            }
            if (nameRepo) {
                filter = { ...filter, nameRepo };
            }
            if (userId) {
                filter = { ...filter, nameRepo, userId };
            }

            const [rows, totalRows] = await Promise.all([
                this.serviceTrackingLogModel
                    .find(filter)
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .sort({ createdAt: `desc` })
                    .exec(),
                this.serviceTrackingLogModel.find(filter).countDocuments(),
            ]);

            return {
                pageSize,
                pageIndex,
                rows,
                totalRows,
            };
    }

    async getServiceLogById(id: string): Promise<IServiceLog> {
        try {
            return await this.serviceLogModel.findById(id);
        } catch (error) {
            throw error;
        }
    }

    async createAuditLog(data: CreateAuditLogDto): Promise<IAuditLog | Boolean> {
        try {
            // Save log method is GET
            if (data.method === 'GET') {
                return await this.auditLogGetModel.create({ ...data, nameRepo: this.config.getRepoName() });
            }

            if (data.method === 'POST' && data.url === '/user/phone-register') {
                return false;
            }

            // Save log method is not GET
            return await this.auditLogModel.create({ ...data, nameRepo: this.config.getRepoName() });
        } catch (error) {
            throw error;
        }
    }

    async getAuditLogs(formData: AuditLogRequest): Promise<any> {
        try {
            let filter = {};
            const { pageSize, pageIndex, nameRepo } = formData;
            if (nameRepo) {
                filter = { ...filter, nameRepo };
            }
            const [rows, totalRows] = await Promise.all([
                this.auditLogModel
                    .find({})
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .where(filter)
                    .sort({ createdAt: `desc` })
                    .exec(),
                this.auditLogModel.find(filter).countDocuments(),
            ]);
            return {
                pageSize,
                pageIndex,
                rows,
                totalRows,
            };
        } catch (error) {
            throw error;
        }
    }

    async getAuditLogById(id: string): Promise<IAuditLog> {
        try {
            return await this.auditLogModel.findById(id);
        } catch (error) {
            throw error;
        }
    }

    filterUrl(url: string): boolean {
        if (url.length <= 1) {
            return false;
        }
        for (const reg of Object.values(ExposeUrl)) {
            const ex = new RegExp(reg);
            return ex.test(url) ? false : true;
        }
        return true;
    }
}
