import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CloneDataDto {
    @ApiProperty({
        description: 'Source appId to clone from',
        example: 'medpro'
    })
    @IsString()
    @IsNotEmpty()
    fromAppId: string;

    @ApiProperty({
        description: 'Target appId to clone to',
        example: 'momo'
    })
    @IsString()
    @IsNotEmpty()
    toAppId: string;

    @ApiProperty({
        description: 'Source repo to clone from',
        example: 'beta'
    })
    @IsString()
    @IsNotEmpty()
    fromRepo: string;

    @ApiProperty({
        description: 'Target repo to clone to',
        example: 'live'
    })
    @IsString()
    @IsNotEmpty()
    toRepo: string;
}
