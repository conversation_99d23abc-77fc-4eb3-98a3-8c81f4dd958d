import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PAYMENT_CONFIG_COLLECTION_NAME } from './constant';


const Schema = mongoose.Schema;

export const PaymenConfigtSchema = new Schema({
    id: { type: String },
    createTime: { type: Date },
    partnerId: { type: String },
    name: { type: String },
    methodId: { type: String },
    paymentTypes: [{ type: Schema.Types.Mixed }],
    calculatorScript: { type: String },
    rate: { type: Number },
    constRate: { type: Number },
    medproFee: { type: Number },
    status: { type: Number },
    sequence: { type: Number },
    groupId: { type: Number },
    minAmount: { type: Number },
    maxAmount: { type: Number },
    highlight: { type: Boolean },
    imageId: { type: String },
    appIds: { type: String },
    treeIds: { type: String },
    platforms: { type: String },
    bannerId: { type: String },
    description: { type: String },
    countries: [{ type: Schema.Types.Mixed }],
}, {
    collection: PAYMENT_CONFIG_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
