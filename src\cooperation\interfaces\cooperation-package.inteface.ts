import { Document } from 'mongoose';

export interface ICooperationPackage extends Document {
    name: string; // Tên gói hợp tác (VD: "CƠ BẢN", "NÂNG CAO", "CAO CẤP")
    price: number; // <PERSON><PERSON><PERSON> gói hợp tác (VD: 6.899.000, 10.899.000, 16.899.000)
    durationInMonths: number; // Thời gian sử dụng (tháng)
    isPopular?: boolean; // Gói này có phổ biến không (true/false)
    type: number;
    features: {
        name: string; // Tên tính năng (VD: "Website cơ bản", "Chức năng đặt khám")
        isAvailable: boolean; // Tính năng này có trong gói không (true/false)
        details?: string; // Chi tiết của tính năng (nếu có)
    }[];
}
