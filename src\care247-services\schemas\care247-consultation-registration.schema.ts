import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CARE247_CONSULTATION_REGISTRATIONS } from './constants';

const Schema = mongoose.Schema;

export const Care247ConsultationRegistrationSchema = new Schema(
    {
        phone: { type: String, required: true },
        fullname: { type: String, required: true },
        email: { type: String, required: false },
    },
    {
        collection: CARE247_CONSULTATION_REGISTRATIONS,
        timestamps: true,
    },
).plugin(jsonMongo);
