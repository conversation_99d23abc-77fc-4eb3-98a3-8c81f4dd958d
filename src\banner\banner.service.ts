import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { REPO_NAME_BETA } from 'src/common/constants';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IBannerHeader } from './interface/banner-header.interface';
import { IBannerStickyBig } from './interface/banner-sticky-big.interface';
import { IBannerStickySmall } from './interface/banner-sticky-small.interface';
import { BANNER_HEADER_COLLECTION_NAME, BANNER_STICKY_BIG_COLLECTION_NAME, BANNER_STICKY_SMALL_COLLECTION_NAME } from './schema/constant';
import moment = require('moment');

@Injectable()
export class BannerService {
    private readonly repoName: string;
    constructor(
        @InjectModel(BANNER_HEADER_COLLECTION_NAME) private bannerHeaderModal: Model<IBannerHeader>,
        @InjectModel(BANNER_STICKY_SMALL_COLLECTION_NAME) private bannerStickySmallModal: Model<IBannerStickySmall>,
        @InjectModel(BANNER_STICKY_BIG_COLLECTION_NAME) private bannerStickyBigModal: Model<IBannerStickyBig>,
        private readonly repoConfigService: ConfigRepoService,
    ) {
        this.repoName = this.repoConfigService.getRepoName();
    }

    private getDateFilter(headers: any) {
    const currentDate = moment().toDate();
    let repo = 'beta';
    if (!REPO_NAME_BETA.includes(this.repoName)) {
        repo = 'live';
    }
    return {
        repo,
        status: true,
        appid: headers?.appid || 'medpro',
        $or: [
            { display: true },
            {
                display: false,
                $expr: {
                    $and: [
                        { $lte: [{ $toDate: '$fromDate' }, currentDate] },
                        { $gte: [{ $toDate: '$toDate' }, currentDate] },
                    ],
                },
            },
        ],
    };
}


    private pickLatestValidBanner = (items: any[]) => {
         if (!items.length) return [];
            return [items.sort((a, b) => moment(b.createdAt).diff(a.createdAt))[0]];
    };

    async getBanner(appid: string) {
        try {
            const filter = this.getDateFilter({ appid });
            const [headers, stickyBigs, stickySmalls] = await Promise.all([
                this.bannerHeaderModal.find(filter).lean(),
                this.bannerStickyBigModal.find(filter).lean(),
                this.bannerStickySmallModal.find(filter).lean(),
            ]);

            return {
                header: this.pickLatestValidBanner(headers),
                stickyBig: this.pickLatestValidBanner(stickyBigs),
                stickySmall: this.pickLatestValidBanner(stickySmalls),
            };
        } catch (error) {
            throw new HttpException(error?.message || 'Có lỗi xảy ra khi lấy danh sách banner', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
