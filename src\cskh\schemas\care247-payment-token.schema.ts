import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { CARE247_INDEPENDENT_PAYMENT } from './constants';
import { BOOKING_CARE_247, BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { PATIENT_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const Care247IndependentPaymentSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String, required: true },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    bookingId: { type: String, required: true },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    care247: { type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 },
    payment: { type: Schema.Types.ObjectId, ref: PAYMENT_COLLECTION_NAME },
    token: { type: String, required: true },
    link: { type: String },
    active: { type: Boolean, default: true },
    bankInfo: { type: Schema.Types.Mixed },
    smsStatus: { type: Number, default: 0 }, //0: khởi tạo, 1: đã gửi, -2: lỗi
    smsTimeSent: { type: Date },
    userAction: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    smsContent: { type: String },
    note: { type: String }
}, {
    collection: CARE247_INDEPENDENT_PAYMENT,
    timestamps: true,
}).plugin(jsonMongo);
