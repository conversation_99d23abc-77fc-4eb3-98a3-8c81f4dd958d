import * as mongoose from 'mongoose';
import * as moment from 'moment';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CSKH_CONTACT_CUSTOMERS } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { BOOKING_CARE_247 } from 'src/booking-gateway/schemas/constants';

const Schema = mongoose.Schema;

export const ContactCustomersSchema = new Schema(
    {
        bookingCare247: { type: String, required: true },
        bookingCare247Info: { type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 },
        bookingCount: { type: Number, default: 0 },
        onePerson: { type: Boolean, default: false },
        callResults: { type: String, default: '' },
        userCreate: { type: String },
        userCreateInfo: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        userUpdate: { type: String },
        userUpdateInfo: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        description: { type: String, default: '' },
        dateCreate: { type: Date, default: () => moment().toDate() },
        dateUpdate: { type: Date, default: () => moment().toDate() },
    },
    {
        collection: CSKH_CONTACT_CUSTOMERS,
        timestamps: true,
        versionKey: false, // Không lưu trữ __v
    },
).plugin(jsonMongo);
