import { Document } from 'mongoose';
import { IUserIDentityInfo } from 'src/zalo-pay/interface/user-zalo-resource.interface';

export interface IUser extends Document {
    zaloUserResource: IUserIDentityInfo
    username: string;
    readonly password: string;
    readonly salt: string;
    email: string;
    readonly phone: string;
    fullname: string;
    patients: string[];
    prevPatients: string[];
    momoId: string;
    medproId: string;
    readonly isCS: boolean;
    readonly isCare247: boolean;
    readonly userIdV1: number;
    readonly tvUser: string;
    readonly daLieuUser: number;
    readonly nd1User: number;
    readonly umcUser: number;
    readonly nextStep: string;
    readonly prevUser: string;
    readonly referralCode?: string;
    partners?: string[];
    viettelMoneyId?:string;
    profileImage?: string;
    zaloId: string;
    surname: string;
    name: string;
    isSaveEmail: boolean
}
