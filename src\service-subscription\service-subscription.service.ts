import { HttpService, Injectable } from '@nestjs/common';
import { UrlConfigService } from '../config/config.url.service';
import { HeadersDto } from '../common/base/headers.dto';
import { Model } from 'mongoose';
import { IUser } from '../user/interfaces/user.interface';
import { InjectModel } from '@nestjs/mongoose';
import { USER_COLLECTION_NAME } from '../user/schemas/constants';

@Injectable()
export class ServiceSubscriptionService {
    constructor(
        private httpService: HttpService,
        private urlConfigService: UrlConfigService,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
    ) {}

    async getServiceByUser(query: any, headers: HeadersDto) {
        const username = query.phone.replace(/^(\+84|84|0)/, '+84');
        const user = await this.userModel.findOne({ username, medproId: `mp${username}` }).exec();

        const { data } = await this.httpService
            .get(`${this.urlConfigService.serviceSubScriptionUrl}/service-subscription/get-by-user`, {
                headers: {
                    partnerid: headers.partnerid || '',
                },
                params: {
                    ownerId: user.username,
                },
            })
            .toPromise();

        return data;
    }
}
