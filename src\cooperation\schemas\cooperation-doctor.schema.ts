import * as mongoose from 'mongoose';
import { COOPERATION, COOPERATION_PACKAGES, COOPERATION_PACKAGES_PRICE } from './constants';
import { CooperationPackagesSchema } from './cooperation-packages.schema';

const Schema = mongoose.Schema;

export const CooperationDoctorSchema = new Schema(
    {
        fullname: { type: String, required: true, trim: true },
        email: { type: String, required: true, trim: true, unique: true },
        phone: { type: String, required: true, trim: true },
        certificateImage: { type: String, required: true },
        packageId: { type: String },
        package: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES },
        packagePriceId: { type: String },
        packagePrice: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES_PRICE },
        role: { type: String },
        subject: { type: String },
        treatments: { type: String },
        address: { type: String },
        birthDate: { type: Date },
        sex: { type: Number },
        workplace: { type: String },
        note: { type: String },
    },
    {
        collection: COOPERATION,
        timestamps: true, // Tự động tạo `createdAt` và `updatedAt`
    },
);
