import { Controller, Post, Body, Query, Get, Param, Delete } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DoctorMongoService } from './doctor-mongo.service';
import { CreateDoctorDTO } from './dto/create-doctor.dto';
import { OutCreateDoctorDTO } from './dto/out-create-doctor.dto';
import { plainToClass } from 'class-transformer';
import { UpdateDoctorDTO } from './dto/update-doctor.dto';

@Controller('mongo/doctor')
@ApiTags('<PERSON>ác sĩ- Quản lý <PERSON>ác sĩ trên MongoDB')
export class DoctorMongoController {

    constructor(
        private readonly doctorMongoService: DoctorMongoService,
    ) { }

    @Post('create')
    async create(@Query() createDoctorDTO: CreateDoctorDTO): Promise<OutCreateDoctorDTO> {
        return plainToClass(OutCreateDoctorDTO, await this.doctorMongoService.create(createDoctorDTO));
    }

    @Get('view/:id')
    async findById(@Param('id') id: string): Promise<any> {
        return this.doctorMongoService.findOne(id);
    }

    @Post('update')
    async update(@Query() updateDoctorDTO: UpdateDoctorDTO): Promise<any> {
        return this.doctorMongoService.updateOne(updateDoctorDTO);
    }

    @Get('get-all-by-hospital')
    async find(@Query('hospitalId') hospitalId: string): Promise<any> {
        return this.doctorMongoService.find(hospitalId);
    }

    @Delete('delete/:id')
    async deleteById(@Param('id') id: string): Promise<any> {
        return this.doctorMongoService.deleteById(id);
    }

    @Get('umc-doctor')
    async getAllUmcDoctor(@Query() query: any): Promise<any> {
        return this.doctorMongoService.getAllUmcDoctor(query);
    }

    @Get('umc-doctor/:id')
    async getUmcDoctor(@Param('id') id: string): Promise<any> {
        return this.doctorMongoService.getUmcDoctor(id);
    }

}
