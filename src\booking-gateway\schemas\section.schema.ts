import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SECTION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const SectionSchema = new Schema({
    id: { type: String },
    name: { type: String },
    code: { type: String },
    partnerId: { type: String },
}, {
    collection: SECTION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
