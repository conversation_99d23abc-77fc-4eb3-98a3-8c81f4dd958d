export const MESSAGE_EVENT = 'message_event';
export const MESSAGE_EVENT_NOTIF_INFORM = 'message_event_notif_inform';
export const UPDATE_BOOKING_V1_EVENT = 'update_booking_v1_event';
export const SYNC_V1_EVENT = 'sync_v1_event';
export const SYNC_V1_CANCEL_BOOKING_EVENT = 'sync_v1_cancel_booking_event';
export const SYNC_USER_INFO = 'sync-user-info';
export const TRACKING_CANCEL_RESERVATIONS = 'tracking_cancel_reservation';
export const HANDLE_UPDATE_STATUS_BOOKING_V1_FAIL = 'handle_update_status_booking_v1_fail';
export const EVENT_SURVEY_FORM_NOTY_DALIEU = 'event_survey_form_noty_dalieu';
export const EVENT_MAIL_BOOKING_NEW_NOTICE = 'event_mail_booking_new_notice';
export const EVENT_MAIL_BOOKING_NEW_NOTICE_V2 = 'event_mail_booking_new_notice_v2';
export const UPDATE_CONSTRAINT_AND_BOOKING_RULE = 'update_constraint_and_booking_rule';
export const SYNC_BOOKING_TRONG_NGAY = 'sync_booking_trong_ngay';
export const CRON_TIMEOUT_BOOKING_PROCESS = 'cron.timeout.booking.process';
export const EVENT_CLOSE_ORDER_PAYMENT_HUB = 'event.close.order.payment.hub';
export const EVENT_TIMEOUT_TRANSACTION_LOGS = 'event.timeout.transaction.logs';
export const FORCE_PAYMENT_HUB_CALLBACK_BE = 'force_payment_hub_callback_be';
export const PUSH_NOTIF_AND_SEND_MAIL_BOOKING_SUCCESS = 'push_notif_and_send_mail_booking_success';
export const GET_CHECK_IN_ROOM_BOOKING_NOTE = 'get_check_in_room_booking_note';
export const FIND_REFERRAL_CODE = 'find_referral_code';
export const BOOKING_FULL_SLOT_EVENT = 'booking.full.slot';
export const EVENT_CREATE_KPI_CSKH = 'event_create_kpi_cskh';
export const EVENT_UPDATE_KPI_CSKH = 'event_update_kpi_cskh';
export const EVENT_BOOKING_SUCCESS = 'event_booking_success';
export const LARK_NOTIF_COOPERATE_MEDPRO = 'lark_notif_cooperate_medpro';
export const EVENT_RETRY_SYNC_BOOKING = 'event_retry_sync_booking';

export const REPAYMENT_SUCCESS_EVENT = 'repayment.success';
export enum TransporterEvent {
    PUSH = 'push',
    MAIL = 'mail',
}
