import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { MailService } from './mailgun.service';
import { MailgunConfigService } from '../config/config.mailgun.sevice';
import { MailgunModule } from 'nestjs-mailgun';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
    imports: [
        ConfigModule.forRoot(),
        MailgunModule.forAsyncRoot({
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) => ({
              username: 'api',
              key: configService.get<string>('MAILGUN_API_KEY'),
              domain: configService.get<string>('MAILGUN_DOMAIN'),
            }),
            inject: [ConfigService],
        }),
    ],
    providers: [MailService],
    exports: [MailService],
})
export class MailModule {}
