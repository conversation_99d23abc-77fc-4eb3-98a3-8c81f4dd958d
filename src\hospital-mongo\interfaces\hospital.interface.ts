import { Document } from 'mongoose';
import { IFeature } from 'src/feature-mongo/interfaces/feature.interface';

export interface IHospital extends Document {
    readonly partnerId: string;
    readonly name: string;
    readonly slug: string;
    readonly short_name: string;
    readonly sms_name: string;
    readonly image: string;
    readonly banner: string;
    readonly status: number;
    readonly city_id: string;
    city?: string;
    readonly district_id: string; 
    district?: string;
    readonly address: string;
    readonly base_url: string;
    readonly lat: number;
    readonly long: number;
    readonly hotline: string;
    readonly subjects: string[];
    readonly services: string[];
    readonly doctors: string[];
    readonly rooms: string[];
    features: IFeature[];
    readonly notifAppId: string;
    readonly notifApiKey: string;
    message: string;
    readonly sortOrder: number;
      // static resources
    readonly website: string;
    readonly email: string;
    readonly phone: string;
    readonly hotlinePartner: string;
    readonly hotlineMedpro: string;
    readonly workingTime: string;
    readonly workingDate: string;
    readonly googleMap: string;
    readonly ios: string;
    readonly android: string;
    readonly bienLai: boolean;
    deliveryStatus: number;
    circleLogo: string;
    readonly hospitalType: number; // 1 hospital | 2 clinic
    readonly newHospitalType: number; // 1 bệnh viện công | 2 bệnh viện tư | 3 phòng khám | 4 phòng mạch
    newHospitalTypes: number[]; // 1 BV công | 2: BV tư | 3: Phòng khám | 4: Phòng mạch | 5: xét nghiệm | 6: y tế tại nhà | 7: tiêm chủng
    sharePaymentConfigUrl?: string;
    sponsored: boolean;
    listingPackagePaid: boolean;
    isCashBack?: boolean;
    readonly packageImgDefault: string;
    isContractSigned: boolean;
    
}
