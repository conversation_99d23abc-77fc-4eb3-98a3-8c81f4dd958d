import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf } from 'class-validator';

export class MedproCareAddonDTO {
    @ApiProperty({
        description: 'booking id',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin booking id',
    })
    @ValidateIf(o => o.platform)
    bookingId: string;
    
    @ApiProperty({
        description: 'medproCareServiceId',
        required: true,
        type: String,
    })
    medproCareServiceId: string;
}