import * as mongoose from 'mongoose';
import { DEAL_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from './constants';
import { COOPERATION_PACKAGES } from '../../cooperation/schemas/constants';

const Schema = mongoose.Schema;

export const DealSchema = new Schema(
    {
        fullname: { type: String, required: true },
        phone: { type: String, required: true },
        partnerName: { type: String, required: true },
        email: { type: String },
        businessImage: { type: [String], required: true },
        medicalLicenseImage: { type: [String], required: true },
        invoiceInformation: {
            invoiceRecipient: { type: Number },
            taxCode: { type: String },
            cccd: { type: String },
            unitName: { type: String },
            fullname: { type: String },
            countryCode: { type: String },
            cityId: { type: String },
            districtId: { type: String },
            wardId: { type: String },
            addresss: { type: String },
            invoiceEmail: { type: String },
        },
        status: { type: Number },
        transactionId: { type: String },
        payment: { type: Schema.Types.ObjectId, ref: PAYMENT_COLLECTION_NAME },
        packageId: { type: String, required: true },
        package: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES },
        typeCooperation: { type: String },
        note: { type: String },
    },
    {
        collection: DEAL_COLLECTION_NAME,
        timestamps: true,
    },
);
