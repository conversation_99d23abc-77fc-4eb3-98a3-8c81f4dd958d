import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { IRatingHospital } from './interface/rating-hospital.interface';
import { InjectModel } from '@nestjs/mongoose';
import { RATING_HOSPITAL_COLLECTION_NAME } from './schema/constants';
import { CreateRatingHospitalDto } from './dto/create-rating-hospital.dto';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';

@Injectable()
export class RatingHospitalService {
    private readonly logger = new Logger(RatingHospitalService.name);
    constructor(
        @InjectModel(RATING_HOSPITAL_COLLECTION_NAME) private ratingHospitalModel: Model<IRatingHospital>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
    ) {}

    async getAllRatingHospital(filterDto: any): Promise<any> {
        try {
            const { bookingCode, hospitalId, rating } = filterDto;
            const filter: any = {};

            if (bookingCode) filter.bookingCode = bookingCode;
            if (hospitalId) filter.hospitalId = hospitalId;
            if (rating) filter.rating = rating;

            const ratings = await this.ratingHospitalModel.find(filter).sort({ createdAt: -1 }).exec();
            const sortedRatings = ratings.sort((a, b) => Number(b.isPrioritize) - Number(a.isPrioritize));
            return sortedRatings;
        } catch (error) {
            throw new HttpException({ message: 'Đã xảy ra lỗi khi lấy danh sách đánh giá', error: error.message }, 500);
        }
    }

    async createRatingHospital(createRatingDto: CreateRatingHospitalDto): Promise<any> {
        try {
            const [hospital, booking, existingRating, user] = await Promise.all([
                this.hospitalModel.findOne({ slug: createRatingDto.slug }).exec(),
                this.bookingModel.findOne({ bookingCode: createRatingDto.bookingCode }).exec(),
                this.ratingHospitalModel.findOne({ bookingCode: createRatingDto.bookingCode }).exec(),
                this.userModel.findById(createRatingDto?.userId).exec(),
            ]);

            if (!hospital) {
                throw new HttpException({ message: 'Không tìm thấy thông tin cơ sở y tế!' }, 404);
            }
            if (!booking) {
                throw new HttpException({ message: `Không tìm thấy mã phiếu khám: ${createRatingDto.bookingCode}` }, 404);
            }
            if (booking?.partnerId !== hospital.partnerId) {
                throw new HttpException(
                    { message: `Phiếu khám : ${createRatingDto.bookingCode} thuộc cơ sở y tế khác. Vui lòng điền đúng mã phiếu để được đánh giá` },
                    404,
                );
            }
            if (existingRating) {
                throw new HttpException({ message: `Mã phiếu khám ${createRatingDto.bookingCode} đã được đánh giá trước đó!` }, 400);
            }
            const newRating = new this.ratingHospitalModel({
                ...createRatingDto,
                booking: booking._id,
                hospital: hospital._id,
                hospitalId: hospital._id,
                user: user?._id,
            });

            await newRating.save();
            return newRating;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }

            throw new HttpException({ message: 'Đã xảy ra lỗi trong quá trình lưu đánh giá', error: error.message }, 500);
        }
    }
}
