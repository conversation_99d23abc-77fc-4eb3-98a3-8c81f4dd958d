import {
    Body,
    CacheInterceptor,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    CACHE_MANAGER,
    Controller,
    Delete,
    Get,
    HttpException,
    HttpStatus,
    Inject,
    Post,
    Put,
    Query,
    UseInterceptors,
    Patch,
    Param,
} from '@nestjs/common';
import { Cache } from 'cache-manager';
import { AUTO_KEY, SECOND, VALUE_KEY } from './cache-manager.constant';
import {
    UpdateValueCache,
    ValueCache,
} from './interface/cache-manager.interface';
import { CacheManagerService } from './cache-manager.service';
import { CreateCacheDto } from './dto/createCache.dto';

@Controller('/cache-manager')
export class CacheManagerController {
    constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly service: CacheManagerService) {}

    @Post()
    async createCache(@Body() body: ValueCache): Promise<any> {
        try {
            return await this.cacheManager.set(
                body.key,
                body.content,
                { ttl: 5 * SECOND },
            );
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Get('list')
    async getListCacheGlobal(@Body() body: ValueCache): Promise<any> {
        return await this.service.getListCacheGlobal();
    }

    @Post('delete-by-cache')
    async deleteByCache(@Body() body: {cache: string, type?:string}): Promise<any> {
        return await this.service.findByKeyAndDelete(body?.cache, body?.type);
    }

    
    @Post('create-by-cache')
    async createByCache(@Body() body:CreateCacheDto): Promise<any> {
        return await this.service.createByCache(body);
    }

    @Patch('update-by-cache')
    async updateByCache(@Body() body:CreateCacheDto): Promise<any> {
        return await this.service.updateByCache(body);
    }

    @Delete('delete-by-key-global/:key')
    async deleteByCacheGlobal(@Param('key') key: string): Promise<any> {
        return await this.service.deleteByCacheGlobal(key);
    }

    @Get()
    async getCache(@Query(`cache`) cache: string): Promise<any> {
        try {
            return await this.cacheManager.get(cache);
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Get('keys')
    async getKeys(): Promise<any> {
        try {
            return await this.cacheManager.store.keys();
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Get(`/auto`)
    @CacheKey(AUTO_KEY)
    @CacheTTL(5 * SECOND)
    @UseInterceptors(CacheInterceptor)
    async testAutoCache(): Promise<any> {
        try {
            return [1, 2, 3, 4, 5, 6, 7, 8];
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Put()
    async updateCache(@Body() body: UpdateValueCache): Promise<any> {
        try {
            const { key, ...data } = body;
            return await this.cacheManager.set(
                key,
                { ...data },
                { ttl: 5 * SECOND },
            );
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Delete()
    async deleteCache(@Query(`cache`) cache: string): Promise<any> {
        try {
            await this.cacheManager.del(cache);
            return { message: 'Xóa thành công' };
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Delete(`/reset`)
    async resetAllCache(): Promise<any> {
        try {
            await this.cacheManager.reset();
            return { message: 'Reset thành công' };
        } catch (error) {
            throw new HttpException(
                `Lỗi Server : ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
}
