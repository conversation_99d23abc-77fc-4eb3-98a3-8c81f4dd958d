import { Document, Types } from 'mongoose';

export interface ICancelReasonItem {
  id: string;
  name: string;
  content: string;
}

export interface IBookingCancelReason extends Document {
  bookingId: string;
  booking:Types.ObjectId;
  reasons: ICancelReasonItem[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IBankAccount extends Document {
  bookingId: string;
  booking:Types.ObjectId;
  bankCode: string;
  bankName: string;
  bankNumber: string;
  accountName: string;
  accountType?: string;
  reasonId: string;
  reason: string;
  bookingCode: string;
  transactionId: string;
  partnerId: string;
  appId: string;
  userId: string;
}

export interface ICancelBookingResult {
  success: boolean;
  message: string;
  cancelReasonId?: string;
  bankAccountId?: string;
}

export interface IExecuteCancellationResult {
  cancelReasonId: string;
  bankAccountId?: string;
  updateResult:any
}

export interface ICancelBookingValidation {
  isValid: boolean;
  errors: string[];
}

export interface ICancellationReasonConfig {
  key: string;
  label: string;
  requiresInput?: boolean;
  input?: {
    type: string;
    placeholder: string;
    required: boolean;
  };
}

export interface IReasonValidation {
  isValid: boolean;
  errors: string[];
}

export interface IBankAccountRequirement {
  gatewayId: string;
  paymentMethodDetail: string;
  description?: string;
}

export interface IBookingCancellation extends Document {
  bookingId: string;
  booking: Types.ObjectId;
  partnerId: string;
  appId: string;
  userId: string;

  reasonIds: string[];
  reasonsData: any[];
  otherContent?: string;
  
  bankCode?: string;
  bankName?: string;
  bankNumber?: string;
  accountName?: string;
  accountType?: string;
  hasBankAccount: boolean;
  
  cskh?: string;
  cskhUserId?: string;
  
  gatewayId: string;
  paymentMethodDetail?: string;
  paymentMethod?: string;
  message: string;
  cancelledBy: string;
  userActionId: string;
  canceledDate?: string;
  status:number;

  subTotal: number;
  platform?: string;
  transactionId: string;
  bookingCode?: string;
  bookingDate?: Date;
  bookingCreateAt?: Date;
}
