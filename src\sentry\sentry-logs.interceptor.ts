import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from "@nestjs/common";
import { Observable } from "rxjs";
import { catchError } from "rxjs/operators";
import * as Sentry from '@sentry/node';

@Injectable()
export class SentryLogsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        console.log('Caught error:', error.message); // Debug
        Sentry.captureException(error);
        throw error;
      }),
    );
  }
}