import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { ENTERPRISE_REGISTRATION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const EnterpriseRegistrationSchema = new Schema(
    {
        fullName: {
            type: String,
            required: true,
            trim: true,
        },
        company: {
            type: String,
            required: true,
            trim: true,
        },
        quantity: {
            type: Number,
            required: true,
        },
        phoneNumber: {
            type: String,
            required: true,
            trim: true,
        },
        note: {
            type: String,
            trim: true,
            default: '',
        },
    },
    {
        collection: ENTERPRISE_REGISTRATION_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);