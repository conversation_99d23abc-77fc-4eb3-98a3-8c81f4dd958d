import { Injectable, HttpException, HttpStatus, HttpService } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Model, Connection } from 'mongoose';
import { IPatientInfo } from './interfaces/patient-info.interface';
import { PATIENT_INFO_COLLECTION_NAME, HEALTH_HISTORY_COLLECTION_NAME } from './schemas/constants';
import { CreatePatientInfoDto } from './dto/create-patient-info.dto';
import { UpdatePatientInfoDto } from './dto/update-patient-info.dto';
import { QueryPatientInfoDto } from './dto/query-patient-info.dto';
import { ScanQrCodeDto, UpdateVitalSignsByQrDto } from './dto/health-metrics.dto';
import { LARK_NOTIF_PATIENT_INFO_CREATED } from 'src/message-event/constant';
import { UrlConfigService } from 'src/config/config.url.service';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { IDistrict } from 'src/district-mongo/interfaces/district.interface';
import { IWard } from 'src/ward-mongo/interfaces/ward.interface';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { InjectConnection } from '@nestjs/mongoose';
import * as QRCode from 'qrcode';
import * as jwt from 'jsonwebtoken';
import * as moment from 'moment';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { IHealthHistory, IHealthHistoryDocument } from './interfaces/health-history.interface';
import { HealthStatisticsQueryDto } from './dto/health-statistics-query.dto';
import * as uuid from 'uuid';

@Injectable()
export class PatientInfoService {
    constructor(
        @InjectModel(PATIENT_INFO_COLLECTION_NAME)
        private readonly patientInfoModel: Model<IPatientInfo>,
        @InjectModel(HEALTH_HISTORY_COLLECTION_NAME)
        private readonly healthHistoryModel: Model<IHealthHistoryDocument>,
        @InjectModel(PATIENT_COLLECTION_NAME)
        private readonly patientModel: Model<IPatient>,
        @InjectModel(CITY_COLLECTION_NAME)
        private readonly cityModel: Model<ICity>,
        @InjectModel(DISTRICT_COLLECTION_NAME)
        private readonly districtModel: Model<IDistrict>,
        @InjectModel(WARD_COLLECTION_NAME)
        private readonly wardModel: Model<IWard>,
        private readonly eventEmitter: EventEmitter2,
        private readonly urlConfigService: UrlConfigService,
        private readonly globalSettingService: GlobalSettingService,
        @InjectConnection() private readonly connection: Connection,
        private readonly httpService: HttpService,
        private readonly jwtUserConfigService: JwtUserConfigService,
    ) {}

    private encodePatientId(patientId: string): string {
        const jwtOptions = this.jwtUserConfigService.createJwtOptions();
        const secret = jwtOptions.secret;
        const payload = {
            patientId: patientId,
            timestamp: moment().unix(),
            iat: moment().unix(),
        };

        const token = jwt.sign(payload, secret);
        
        return token;
    }

    /**
     * Decode patient ID from QR code data using JWT
     */
    private decodePatientId(encodedData: string): string {
        try {
            const jwtOptions = this.jwtUserConfigService.createJwtOptions();
            const secret = jwtOptions.secret;
            const decoded = jwt.verify(encodedData, secret) as any;

            if (!decoded.patientId) {
                throw new HttpException(
                    'QR code không chứa thông tin patient ID hợp lệ',
                    HttpStatus.BAD_REQUEST,
                );
            }

            return decoded.patientId;
        } catch (error) {
            if (error.name === 'JsonWebTokenError') {
                throw new HttpException(
                    'QR code không hợp lệ',
                    HttpStatus.BAD_REQUEST,
                );
            } else {
                throw new HttpException(
                    'QR code không hợp lệ',
                    HttpStatus.BAD_REQUEST,
                );
            }
        }
    }

    /**
     * Generate QR code for patient
     */
    private async generateQRCode(patientId: string): Promise<{ qrCodeData: string; qrCodeImage: string }> {
        const qrCodeData = this.encodePatientId(patientId);
        const qrCodeImage = await QRCode.toDataURL(qrCodeData, {
            errorCorrectionLevel: 'M',
            type: 'image/png',
            quality: 0.92,
            margin: 1,
        });
        
        return { qrCodeData, qrCodeImage };
    }

    /**
     * Save health history record
     */
    private async saveHealthHistory(
        patientInfoId: string,
        patientId: string | null,
        createPatientInfoDto: CreatePatientInfoDto,
        recordType: 'INITIAL' | 'CHECKUP' | 'EMERGENCY' | 'FOLLOW_UP' = 'INITIAL'
    ): Promise<void> {
        try {
            const healthHistoryData: IHealthHistory = {
                patientInfoId: patientInfoId,
                patientId: patientId,
                recordDate: new Date(),
                recordType: recordType as any,
                createdBy: 'system',
                notes: `Hồ sơ ban đầu - Lý do khám: ${createPatientInfoDto.reasonForVisit || 'Khám tổng quát'}`
            };

            // Thêm vital signs nếu có
            if (createPatientInfoDto.vitalSigns) {
                healthHistoryData.vitalSigns = {
                    weight: createPatientInfoDto.vitalSigns.weight ? Number(createPatientInfoDto.vitalSigns.weight) : undefined,
                    height: createPatientInfoDto.vitalSigns.height ? Number(createPatientInfoDto.vitalSigns.height) : undefined,
                    temperature: createPatientInfoDto.vitalSigns.temperature ? Number(createPatientInfoDto.vitalSigns.temperature) : undefined,
                    pulse: createPatientInfoDto.vitalSigns.pulse ? Number(createPatientInfoDto.vitalSigns.pulse) : undefined,
                    spO2: createPatientInfoDto.vitalSigns.spO2 ? Number(createPatientInfoDto.vitalSigns.spO2) : undefined,
                    bloodPressure: createPatientInfoDto.vitalSigns.bloodPressure,
                };
            }

            // Lưu vào MongoDB sử dụng Mongoose model
            const newHealthHistory = new this.healthHistoryModel(healthHistoryData);
            await newHealthHistory.save();

            console.log(`Đã lưu lịch sử sức khỏe cho patient: ${patientInfoId}`);
        } catch (error) {
            console.error(`Lỗi khi lưu lịch sử sức khỏe: ${error.message}`, error.stack);
            // Không throw error để không ảnh hưởng đến main process
        }
    }

    async create(createPatientInfoDto: CreatePatientInfoDto): Promise<IPatientInfo> {
        try {
            // Call queue API outside of transaction
            let queueResponse: any = null;
            try {
                queueResponse = await this.callQueueAPI(createPatientInfoDto);
            } catch (queueError) {
                 throw new HttpException(
                    'Không thể lấy số thứ tự từ Portal. Vui lòng thử lại sau.',
                    HttpStatus.BAD_REQUEST,
                );
            }

            // Start transaction only for database operations
            const session = await this.connection.startSession();
            session.startTransaction();
            
            try {
                let savedPatient: any = null;
                if (queueResponse) {
                    try {
                        // Split patient name into surname and name
                        const fullName = createPatientInfoDto.patientName.trim();
                        const nameWords = fullName.split(' ');
                        const name = nameWords.pop() || '';
                        const surname = nameWords.join(' ') || '';
                        
                        const birthDate = new Date(createPatientInfoDto.birthDate);
                        const birthYear = birthDate.getFullYear();

                        // Tạo UUID cho patient
                        const uuidv4 = uuid.v4();
                        const patientId = uuidv4.replace(/-/g, '');

                        const patientData = {
                            id: patientId,
                            surname: surname || '',
                            name: name || '',
                            mobile: createPatientInfoDto.phone || '',
                            birthyear: birthYear,
                            birthdate: moment(createPatientInfoDto.birthDate).format('YYYY-MM-DD'),
                            sex: createPatientInfoDto.gender,
                            cmnd: '',
                            email: '',
                            profession_id: '',
                            city_id: createPatientInfoDto.city_id || '',
                            district_id: createPatientInfoDto.district_id || '',
                            ward_id: createPatientInfoDto.ward_id || '',
                            address: createPatientInfoDto.address || '',
                            sourceId: 'medpro',
                            partnerId: 'medpro',
                        };

                        const newPatient = new this.patientModel(patientData);
                        savedPatient = await newPatient.save({ session });
                    } catch (patientError) {
                        console.error(`Lỗi khi tạo patient: ${patientError.message}`, patientError.stack);
                    }
                }

                // Lấy city, district, ward từ DB để gán đúng _id
                const [city, district, ward] = await Promise.all([
                    this.cityModel.findOne({ id: createPatientInfoDto.city_id }).lean(),
                    this.districtModel.findOne({ id: createPatientInfoDto.district_id }).lean(),
                    this.wardModel.findOne({ id: createPatientInfoDto.ward_id }).lean(),
                ]);

                const patientInfo = new this.patientInfoModel({
                    ...createPatientInfoDto,
                    ward: ward?._id,
                    district: district?._id,
                    city: city?._id,
                    patient: savedPatient?._id,
                });
                const savedPatientInfo = await patientInfo.save({ session });

                // Prepare update data
                const updateData: any = {};
                
                // Add queue data if available
                if (queueResponse) {
                    updateData.queueId = queueResponse.id;
                    updateData.dateId = queueResponse.dateId;
                    updateData.sequenceNumber = queueResponse.number;
                    updateData.queueStatus = queueResponse.status;
                    updateData.timeSlotId = queueResponse.timeSlotId;
                    updateData.timeShiftId = queueResponse.timeShiftId;
                    updateData.queueDate = queueResponse.date;
                    updateData.blockDate = queueResponse.blockDate;
                    updateData.dateInString = queueResponse.dateInString;
                    updateData.blockDateInString = queueResponse.blockDateInString;
                }

                // Generate QR code with actual patient ID
                try {
                    const actualQrResult = await this.generateQRCode(savedPatientInfo._id.toString());
                    updateData.qrCodeData = actualQrResult.qrCodeData;
                    updateData.qrCodeImage = actualQrResult.qrCodeImage;
                } catch (qrError) {
                    console.error(`Lỗi khi tạo QR code: ${qrError.message}`, qrError.stack);
                    // Continue without QR code - it's not critical
                }

                // Update patient info with all available data
                const updatedPatientInfo = await this.patientInfoModel.findByIdAndUpdate(
                    savedPatientInfo._id,
                    updateData,
                    { new: true, session }
                );
                
                await session.commitTransaction();
                session.endSession();
                
                // Emit event để gửi thông báo Lark
                try {
                    this.eventEmitter.emit(LARK_NOTIF_PATIENT_INFO_CREATED, updatedPatientInfo);
                } catch (error) {
                    console.error(`Lỗi khi phát sự kiện LARK_NOTIF_PATIENT_INFO_CREATED: ${error.message}`, error.stack);
                }
                
                return updatedPatientInfo;
            } catch (error) {
                // Rollback transaction
                await session.abortTransaction();
                session.endSession();
                throw error;
            }
        } catch (error) {
            // Throw error with specific message for queue API failure
            if (error.message && error.message.includes('Queue API error')) {
                throw new HttpException(
                    'Không thể lấy số thứ tự từ hệ thống. Vui lòng thử lại sau.',
                    HttpStatus.BAD_REQUEST,
                );
            }

            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi tạo thông tin bệnh nhân',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    private async getOpenSessionToken(): Promise<string> {
        try {
            // Lấy thông tin từ global setting (JSON)
            const configStr = await this.globalSettingService.findByKeyAndRepoName('QUEUE_SESSION_CONFIG');
            if (!configStr) {
                throw new Error('Thiếu cấu hình QUEUE_SESSION_CONFIG trong global setting');
            }
            let config: any;
            try {
                config = JSON.parse(configStr);
            } catch (e) {
                throw new Error('QUEUE_SESSION_CONFIG không phải là JSON hợp lệ');
            }
            const { username, password } = config || {};
            console.log(username, password);
            console.log(`${this.urlConfigService.getGetNumberQueueUrl()}/connector/v1/open-session`);
            const response = await this.httpService.post(
                `${this.urlConfigService.getGetNumberQueueUrl()}/connector/v1/open-session`,
                {
                    username,
                    password,
                }
            ).toPromise();
            console.log(response);
            if (response.data && response.data.access_token) {
                return response.data.access_token;
            }
            throw new Error('Không lấy được access_token từ open-session');
        } catch (error) {
            throw new Error('Lỗi : ' + (error.response?.data?.message || error.message));
        }
    }

    private async callQueueAPI(createPatientInfoDto: CreatePatientInfoDto): Promise<any> {
        try {
            const currentDate = new Date();
            const dateString = currentDate.getDate().toString().padStart(2, '0') + 
                             (currentDate.getMonth() + 1).toString().padStart(2, '0') + 
                             currentDate.getFullYear().toString();

            const genderText = createPatientInfoDto.gender === 1 ? 'Nam' : 'Nữ';
            const birthDate = new Date(createPatientInfoDto.birthDate);
            const birthYear = birthDate.getFullYear().toString();

            // Fetch names from database concurrently
            const [city, district, ward, queueId] = await Promise.all([
                this.cityModel.findOne({ id: createPatientInfoDto.city_id }).lean(),
                this.districtModel.findOne({ id: createPatientInfoDto.district_id }).lean(),
                this.wardModel.findOne({ id: createPatientInfoDto.ward_id }).lean(),
                this.globalSettingService.findByKeyAndRepoName('QUEUE_ID_CONFIG')
            ]);

            // Lấy access_token từ open-session
            const accessToken = await this.getOpenSessionToken();

            const requestData = {
                queueId: queueId,
                currentDate: dateString,
                bookingDetail: {
                    patientName: createPatientInfoDto.patientName,
                    patientGender: genderText,
                    patientPhone: createPatientInfoDto.phone,
                    patientBirthDate: birthDate.toLocaleDateString('en-GB'),
                    patientDistrict: district?.name,
                    patientWard: ward?.name,
                    patientProvince: city?.name,
                    patientBirthYear: birthYear,
                    patientAddress: createPatientInfoDto.address
                }
            };

            const response = await this.httpService.post(
                `${this.urlConfigService.getGetNumberQueueUrl()}/queue-api/queue/v2/getAndBlockQueueNumber`,
                requestData,
                {
                    headers: {
                        'partnerId': 'nhidong1',
                        'appId': 'medpro',
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`,
                    }
                }
            ).toPromise();

            return response.data;
        } catch (error) {
            console.error('Queue API error:', error.response?.data || error.message);
            const queueError = new Error('Queue API error: ' + (error.response?.data?.message || error.message));
            throw queueError;
        }
    }

    async findAll(queryDto: QueryPatientInfoDto): Promise<{
        data: IPatientInfo[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }> {
        try {
            const {
                patientName,
                phone,
                gender,
                fromDate,
                toDate,
                page = 1,
                limit = 10,
            } = queryDto;

            // Build filter object
            const filter: any = {};

            if (patientName) {
                filter.patientName = { $regex: patientName, $options: 'i' };
            }

            if (phone) {
                filter.phone = { $regex: phone, $options: 'i' };
            }

            if (gender) {
                filter.gender = gender;
            }

            if (fromDate || toDate) {
                filter.createdAt = {};
                if (fromDate) {
                    filter.createdAt.$gte = new Date(fromDate);
                }
                if (toDate) {
                    filter.createdAt.$lte = new Date(toDate);
                }
            }

            const skip = (page - 1) * limit;
            const total = await this.patientInfoModel.countDocuments(filter);
            const data = await this.patientInfoModel
                .find(filter)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .lean();

            return {
                data,
                total,
                page: Number(page),
                limit: Number(limit),
                totalPages: Math.ceil(total / limit),
            };
        } catch (error) {
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi lấy danh sách thông tin bệnh nhân',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async findOne(id: string): Promise<IPatientInfo> {
        try {
            const patientInfo = await this.patientInfoModel.findById(id).lean();
            if (!patientInfo) {
                throw new HttpException(
                    'Không tìm thấy thông tin bệnh nhân',
                    HttpStatus.NOT_FOUND,
                );
            }
            return patientInfo;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi lấy thông tin bệnh nhân',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async update(id: string, updatePatientInfoDto: UpdatePatientInfoDto): Promise<IPatientInfo> {
        try {
            const patientInfo = await this.patientInfoModel
                .findByIdAndUpdate(id, updatePatientInfoDto, { new: true })
                .lean();
            
            if (!patientInfo) {
                throw new HttpException(
                    'Không tìm thấy thông tin bệnh nhân',
                    HttpStatus.NOT_FOUND,
                );
            }
            
            return patientInfo;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi cập nhật thông tin bệnh nhân',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async remove(id: string): Promise<{ message: string }> {
        try {
            const result = await this.patientInfoModel.findByIdAndDelete(id);
            if (!result) {
                throw new HttpException(
                    'Không tìm thấy thông tin bệnh nhân',
                    HttpStatus.NOT_FOUND,
                );
            }
            return { message: 'Xóa thông tin bệnh nhân thành công' };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi xóa thông tin bệnh nhân',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    /**
     * Scan QR code and retrieve patient information
     */
    async scanQrCode(scanQrCodeDto: ScanQrCodeDto): Promise<IPatientInfo> {
        try {
            const patientId = this.decodePatientId(scanQrCodeDto.qrCodeData);
            
            const patientInfo = await this.patientInfoModel.findById(patientId)
                .populate('city')
                .populate('district')
                .populate('ward');
                
            if (!patientInfo) {
                throw new HttpException(
                    'Không tìm thấy thông tin bệnh nhân',
                    HttpStatus.NOT_FOUND,
                );
            }
            
            return patientInfo;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi quét QR code',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    /**
     * Update patient information using QR code
     */
    async updateVitalSignsByQr(updateVitalSignsByQrDto: UpdateVitalSignsByQrDto): Promise<IPatientInfo> {
        try {
            const patientId = this.decodePatientId(updateVitalSignsByQrDto.qrCodeData);
            
            const patientInfo = await this.patientInfoModel.findById(patientId);
            if (!patientInfo) {
                throw new HttpException(
                    'Không tìm thấy thông tin bệnh nhân',
                    HttpStatus.NOT_FOUND,
                );
            }

            // Prepare update data
            const updateData: any = {};

            // Update vital signs if provided
            if (updateVitalSignsByQrDto.vitalSigns) {
                const vitalSignsFields = ['weight', 'height', 'temperature', 'pulse', 'spO2', 'bloodPressure'];
                const vitalSignsData: any = {};
                
                // Handle special case for birthSign (needs Date conversion)
                if (updateVitalSignsByQrDto.vitalSigns.birthSign) {
                    vitalSignsData.birthSign = new Date(updateVitalSignsByQrDto.vitalSigns.birthSign);
                }
                
                // Handle other vital signs fields
                vitalSignsFields.forEach(field => {
                    if (updateVitalSignsByQrDto.vitalSigns[field]) {
                        vitalSignsData[field] = updateVitalSignsByQrDto.vitalSigns[field];
                    }
                });

                updateData.vitalSigns = vitalSignsData;
            }

            // Define fields that need special handling (Date conversion)
            const dateFields = ['birthDate'];
            const booleanFields = ['gender', 'isXRay', 'isHospitalized'];
            const stringFields = ['patientName', 'parentName', 'address', 'phone', 'reasonForVisit', 'ward_id', 'district_id', 'city_id'];

            // Handle date fields
            dateFields.forEach(field => {
                if (updateVitalSignsByQrDto[field]) {
                    updateData[field] = new Date(updateVitalSignsByQrDto[field]);
                }
            });

            // Handle boolean fields (check for undefined to allow false values)
            booleanFields.forEach(field => {
                if (updateVitalSignsByQrDto[field] !== undefined) {
                    updateData[field] = updateVitalSignsByQrDto[field];
                }
            });

            // Handle string fields
            stringFields.forEach(field => {
                if (updateVitalSignsByQrDto[field]) {
                    updateData[field] = updateVitalSignsByQrDto[field];
                }
            });

            // Update patient info
            const updatedPatientInfo = await this.patientInfoModel.findByIdAndUpdate(
                patientId,
                updateData,
                { new: true }
            ).populate('city')
             .populate('district')
             .populate('ward');

            // Lưu lịch sử sức khỏe khi bác sĩ cập nhật vital signs
            if (updateVitalSignsByQrDto.vitalSigns) {
                try {
                    // Tạo mock DTO để sử dụng với saveHealthHistory
                    const mockDto = {
                        vitalSigns: updateVitalSignsByQrDto.vitalSigns,
                        reasonForVisit: updateVitalSignsByQrDto.reasonForVisit || 'Cập nhật chỉ số sức khỏe',
                    } as any;

                    // Lấy patientId từ populated data hoặc trực tiếp từ patient field
                    const patientMongoId = (updatedPatientInfo as any).patient?._id?.toString() || 
                                         (updatedPatientInfo as any).patient?.toString() || 
                                         null;

                    await this.saveHealthHistory(
                        patientId,
                        patientMongoId,
                        mockDto,
                        'CHECKUP'
                    );
                } catch (healthHistoryError) {
                    console.error(`Lỗi khi lưu lịch sử sức khỏe: ${healthHistoryError.message}`, healthHistoryError.stack);
                    // Continue without failing the main process
                }
            }

            return updatedPatientInfo;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi cập nhật thông tin bệnh nhân',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    /**
     * Get QR code image for patient
     */
    async getQrCodeImage(patientId: string): Promise<{ qrCodeImage: string }> {
        try {
            const patientInfo = await this.patientInfoModel.findById(patientId);
            if (!patientInfo) {
                throw new HttpException(
                    'Không tìm thấy thông tin bệnh nhân',
                    HttpStatus.NOT_FOUND,
                );
            }

            if (!patientInfo.qrCodeImage) {
                // Generate new QR code if not exists
                const { qrCodeImage } = await this.generateQRCode(patientId);
                await this.patientInfoModel.findByIdAndUpdate(patientId, { qrCodeImage });
                return { qrCodeImage };
            }

            return { qrCodeImage: patientInfo.qrCodeImage };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                error?.message || 'Có lỗi xảy ra khi lấy QR code',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    /**
     * Get health history for a patient
     */
    async getHealthHistory(patientInfoId: string): Promise<IHealthHistory[]> {
        try {
            const histories = await this.healthHistoryModel
                .find({ patientInfoId })
                .sort({ recordDate: -1 })
                .lean();

            return histories;
        } catch (error) {
            console.error(`Lỗi khi lấy lịch sử sức khỏe: ${error.message}`, error.stack);
            throw new HttpException(
                'Có lỗi xảy ra khi lấy lịch sử sức khỏe',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    /**
     * Get health statistics for reporting
     */
    async getHealthStatistics(query: HealthStatisticsQueryDto): Promise<any> {
        try {
            // Build filter
            const filter: any = {};
            if (query.fromDate || query.toDate) {
                filter.recordDate = {};
                if (query.fromDate) {
                    filter.recordDate.$gte = new Date(query.fromDate);
                }
                if (query.toDate) {
                    filter.recordDate.$lte = new Date(query.toDate);
                }
            }
            if (query.recordType) {
                filter.recordType = query.recordType;
            }

            // Aggregate statistics using Mongoose model
            const statistics = await this.healthHistoryModel.aggregate([
                { $match: filter },
                {
                    $group: {
                        _id: '$recordType',
                        count: { $sum: 1 },
                        avgWeight: { $avg: '$vitalSigns.weight' },
                        avgHeight: { $avg: '$vitalSigns.height' },
                        avgTemperature: { $avg: '$vitalSigns.temperature' },
                        avgPulse: { $avg: '$vitalSigns.pulse' },
                        avgSpO2: { $avg: '$vitalSigns.spO2' },
                    }
                }
            ]);

            return {
                statistics,
                totalRecords: statistics.reduce((sum, stat) => sum + stat.count, 0),
                generatedAt: new Date(),
            };
        } catch (error) {
            console.error(`Lỗi khi lấy thống kê sức khỏe: ${error.message}`, error.stack);
            throw new HttpException(
                'Có lỗi xảy ra khi lấy thống kê sức khỏe',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
}
