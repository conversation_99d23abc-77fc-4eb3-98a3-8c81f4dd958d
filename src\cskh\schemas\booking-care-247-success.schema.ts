import * as mongoose from 'mongoose';
import { SUBJECT_COLLECTION_NAME } from '../../subject-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from '../../room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from '../../doctor-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from '../../service-mongo/schemas/constants';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';
import { BOOKING_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { BOOKING_CARE_247_SUCCESS } from './constants';

const Schema = mongoose.Schema;

export const BookingCare247Success = new Schema(
    {
        userId: { type: String, ref: USER_COLLECTION_NAME },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        bookingCode: { type: String },
        transactionId: { type: String },
        date: { type: Date },
        subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
        room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    },
    {
        collection: BOOKING_CARE_247_SUCCESS,
        timestamps: true,
    },
);
