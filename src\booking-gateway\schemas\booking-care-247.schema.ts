import * as mongoose from 'mongoose';
import { BOOKING_CARE_247, BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME, SECTION_COLLECTION_NAME } from './constants';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';
import { SUBJECT_COLLECTION_NAME } from '../../subject-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from '../../room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from '../../doctor-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from '../../service-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const BookingCare247Schema = new Schema(
    {
        id: { type: String },
        bookingId: { type: String },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        bookingCode: { type: String },
        bookingStatus: { type: Number },
        userId: { type: String },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        patientId: { type: String },
        patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
        patientVersionId: { type: String },
        partnerId: { type: String },
        appId: { type: String },
        platform: { type: String },
        name: { type: String },
        addonServices: { type: [Schema.Types.Mixed] },
        note: { type: String },
        instructor: { type: Schema.Types.Mixed },
        cskhUserIdGuide: { type: String },
        csAdmin: { type: Schema.Types.Mixed },
        medproCareNote: { type: String },
        status: { type: Number },
        provider: { type: Schema.Types.Mixed },
        time: { type: String },
        cancelInfo: { type: Schema.Types.Mixed },
        transactionId: { type: String },
        type: { type: String, default: 'primary'}, //original, primary, secondary, independent
        parentId: { type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 },
        date: { type: Date },
        subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
        room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        section: { type: Schema.Types.ObjectId, ref: SECTION_COLLECTION_NAME },
        cskhUserId: { type: String },
        cskh: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        bookingsRelation: [{
            _id: { type: String },
            bookingCode: { type: String },
            bookingCodeV1: { type: String },
        }],
        payment: { type: Schema.Types.ObjectId, ref: PAYMENT_COLLECTION_NAME },
        refundCare247: { type: Boolean, default: false },
        refundCare247Date: { type: Date },
        refundCare247User: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        refundCare247Note: { type: String },
        cskhConfirmedCare247: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        viewedPayment: { type: Boolean, default: false },
        isCSKHConfirmCare247: { type: Boolean, default: false },
    },
    {
        collection: BOOKING_CARE_247,
        timestamps: true,
    },
);