import { UrlConfigService } from './../config/config.url.service';
import { ClientUtilService } from './../config/client-util.service';
import { FeatureChildResponseEnum, FeatureResponseEnum } from './enum/feature-response.enum';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { chain, find, first, orderBy, pick, groupBy, get, map, findIndex, size, has } from 'lodash';
import { Model } from 'mongoose';
import * as moment from 'moment';
import { IFeature } from 'src/feature-mongo/interfaces/feature.interface';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

import { AddChildrenFeatureDTO } from './dto/add-child-feature-into-feature.dto';
import { AddFeatureIntoPartnerDTO } from './dto/add-feature-into-partner.dto';
import { AddPartnerIntoAppIdDTO } from './dto/add-partner-into-appId.dto';
import { CopyFeatureFromPartnerDTO } from './dto/copy-feature-from-partner.dto';
import { CreateAppIdDTO } from './dto/create-app-id.dto';
import { HospitalResponseDTO } from './dto/hospital-response.dto';
import { HospitalDTO } from './dto/hospital.dto';
import { PartnerResponseEnum } from './enum/partner-response.enum';
import { UpdateAppIdDTO } from './dto/update-app-id.dto';
import { UpdateChildrenInFeatureOfPartnerDTO } from './dto/update-children-feature.dto';
import { UpdateFeatureInPartnerDTO } from './dto/update-feature-in-partner.dto';
import { IAppId } from './interfaces/app-id.inteface';
import { APP_ID_COLLECTION_NAME, LISTING_COLLECTION_NAME } from './schemas/constants';
import { UpdatePartnerFeatureDto } from './dto/update-partner-feature.dto';
import { HospitalMongoService } from '../hospital-mongo/hospital-mongo.service';
import { GlobalSettingService } from '../global-setting/global-setting.service';
import { HeadersDto } from '../common/base/headers.dto';
import { ConfigRepoService } from '../config/config.repo.service';
import { UtilService } from '../config/util.service';
import { FeatureService } from '../feature/feature.service';
import { REPO_NAME_BETA } from '../common/constants';
import { CacheManagerService } from "../cache-manager/cache-manager.service";
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from "../report/schemas/constants";
import {HOSPITAL_DESCRIPTION_COLLECTION_NAME} from "../hospital-description/schema/constants";
import {IHospitalDescription} from "../hospital-description/interface/hospital-description.inteface";
import {IReportTransactionDaily} from "../report/interfaces/report-transaction-daily.interface";
import { PARTNER_DOMAIN_COLLECTION } from '../partner-domain/schema/constant';
import { IPartnerDomain } from '../partner-domain/interface/partner-domain.interface';
import { HomePageServiceInject } from 'src/home-page/persistence/home-page.service';
import { HomePageService } from 'src/home-page/home-page.service';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { IFeeRefundConfigs } from 'src/cash-back/interfaces/fee-refund-configs.interface';
@Injectable()
export class AppIdService {
    private logger = new Logger(AppIdService.name);
    private readonly repoName: string;

    constructor(
        @HomePageServiceInject() private readonly homePageService: HomePageService,
        @InjectModel(FEE_REFUND_CONFIGS_COLLECTION_NAME) private readonly feeRefundConfigs: Model<IFeeRefundConfigs>,
        @InjectModel(APP_ID_COLLECTION_NAME) private appIdModel: Model<IAppId>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(FEATURE_COLLECTION_NAME) private featureModel: Model<IFeature>,
        @InjectModel(DAILY_TRANSACTION_REPORT_COLLECTION_NAME) private transactionDailyModel: Model<IReportTransactionDaily>,
        @InjectModel(HOSPITAL_DESCRIPTION_COLLECTION_NAME) private hospitalDescriptionModel: Model<IHospitalDescription>,
        @InjectModel(PARTNER_DOMAIN_COLLECTION) private partnerDomainModel: Model<IPartnerDomain>,
        @InjectModel(LISTING_COLLECTION_NAME) private listingModel: Model<any>,
        private readonly client: ClientUtilService,
        private readonly config: UrlConfigService,
        private readonly cacheManagerService: CacheManagerService,
        private hospitalService: HospitalMongoService,
        private repoConfigService: ConfigRepoService,
        private globalSettingService: GlobalSettingService,
        private utilService: UtilService,
        private featureService: FeatureService,
    ) {
        this.repoName = this.repoConfigService.getRepoName();
    }

    async hospitalResponse(fromData: HospitalDTO): Promise<HospitalResponseDTO> {
        const response: HospitalResponseDTO = {
            partnerId: fromData.partnerId,
            name: fromData.name,
            address: fromData.address,
            hotline: fromData.hotline,
            website: fromData.website,
            workingTime: fromData.workingTime,
            workingDate: fromData.workingDate,
        };
        return response;
    }

    async isExistAppId(appId: string): Promise<boolean> {
        const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
        if (appIdInfo) {
            return true;
        }
        return false;
    }

    async isExistPartnerId(partnerId: string): Promise<boolean> {
        const partnerInfo = await this.hospitalModel.findOne({ partnerId }).exec();
        if (partnerInfo) {
            return true;
        }
        return false;
    }

    async isExistPartnerInAppId(appId: string, partnerId: string): Promise<boolean> {
        try {
            const partner = await this.appIdModel.findOne({ appId, 'detail.partnerId': partnerId }).exec();
            if (partner) {
                return true;
            }
        } catch (err) {
            this.logger.error(`Error handle get partner in appId: ${appId}, Error message: ${err.message}`);
            throw err;
        }
        return false;
    }

    async isExistFeature(featureId: string): Promise<any> {
        try {
            const feature = await this.featureModel.findById({ _id: featureId }).exec();
            if (feature) {
                return true;
            }
            return false;
        } catch (err) {
            this.logger.error(`Error when exec isExistFeature() with featureId: ${featureId}\nError: ${err.message}`);
            throw err;
        }
    }
    async findByFeatureBySlug(appId:string,slug: string): Promise<any> {
        try {
            const filterByLocale: any = { appId, country: this.utilService.getCountryByLocale('vn') };
    
            const app = await this.appIdModel.findOne(filterByLocale, { 'features.partners': false }).exec();

            const appObj = app.toObject();
                
            if (size(app.features)>0) {
                const filteredFeatures = appObj.features.filter((feature) => feature.slug === slug);
                return filteredFeatures.length > 0 ? filteredFeatures : null;
              }
            return 
        } catch (err) {
            this.logger.error(`Error when exec isExistFeature() with featureId: ${slug}\nError: ${err.message}`);
            throw err;
        }
    }

    async isExistFeatureInPartner(partnerId: string, featureId: string): Promise<any> {
        try {
            const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
            // find feature in partner
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            // validate feature
            if (feature) {
                return true;
            }
            return false;
        } catch (err) {
            this.logger.error(
                `Error when exec isExistFeatureInPartner() with featureId: ${featureId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async isExistChildFeatureInFeatureOfPartner(partnerId: string, featureId: string, childFeatureId: string): Promise<any> {
        try {
            const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
            // find feature in partner
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            // validate feature
            if (feature) {
                // find children feature in feature
                const childFeature = find(feature.children, item => {
                    return `${item._id}` === childFeatureId;
                });
                if (childFeature) {
                    return true;
                }
            }
            return false;
        } catch (err) {
            this.logger.error(
                `Error when exec isExistChildFeatureInFeatureOfPartner() with childrenFeatureId: ${childFeatureId},
                featureId: ${featureId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    // check feature is already exist in partner in appId
    async isAlreadyExistFeatureInPartner(appId: string, partnerId: string, featureId: string): Promise<boolean> {
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // get partner in appId
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            if (feature) {
                return true;
            }
            return false;
        } catch (error) {
            this.logger.error(`Error when exec isAlreadyExistFeatureInPartner() with appId: ${appId}\nError: ${error.message}`);
            throw error;
        }
    }

    // check children feature is already exist in feature in partner of appId
    async isAlreadyExistChildFeatureInParentOfPartner(appId: string, partnerId: string, featureId: string, childFeatureId: string): Promise<boolean> {
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // get partner in appId
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            const childFeature = find(feature.children, item => {
                return `${item._id}` === childFeature;
            });
            if (childFeature) {
                return true;
            }
            return false;
        } catch (error) {
            this.logger.error(`Error when exec isAlreadyExistChildFeatureInParentOfPartner() with appId: ${appId}\nError: ${error.message}`);
            throw error;
        }
    }

    async findAll(): Promise<any> {
        return this.appIdModel.find({}).exec();
    }

    async getDataByAppId(appId: string): Promise<any> {
        const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
        if (!appIdInfo) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        return appIdInfo;
    }

    async createAppId(createAppIdDTO: CreateAppIdDTO): Promise<IAppId> {
        if (await this.isExistAppId(createAppIdDTO.appId)) {
            throw new HttpException('appId is exitst', HttpStatus.BAD_REQUEST);
        }
        try {
            await this.appIdModel.syncIndexes();
            const appId = new this.appIdModel(createAppIdDTO);
            return await appId.save();
        } catch (err) {
            this.logger.error(`Error create AppId: ${createAppIdDTO.appId}, Error message: ${err.message}`);
            throw err;
        }
    }

    async validationAddFeatureIntoPartnerOfAppId(fromData: AddFeatureIntoPartnerDTO): Promise<any> {
        const { appId, partnerId, featureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerId = await this.isExistPartnerId(partnerId);
        const isExistFeature = await this.isExistFeature(featureId);
        const isAlreadyExistFeatureInPartner = await this.isAlreadyExistFeatureInPartner(appId, partnerId, featureId);
        // validate base feature in partner
        const isExistFeatureInPartner = await this.isExistFeatureInPartner(partnerId, featureId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeature) {
            throw new HttpException(`feature with featureId: ${featureId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
        if (isAlreadyExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} already exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
    }

    async addFeatureIntoPartnerOfAppId(fromData: AddFeatureIntoPartnerDTO): Promise<any> {
        await this.validationAddFeatureIntoPartnerOfAppId(fromData);
        const { appId, partnerId, featureId } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // find partner in appId
            const partner = find(appIdData.detail, { partnerId });
            // validation partner
            if (partner !== undefined) {
                // get feature
                const feature = await this.featureModel.findById({ _id: featureId }).exec();
                // add feature into partner
                partner.features.push(feature);
            }
            return (await appIdData.save()).toJSON();
        } catch (err) {
            this.logger.error(`Error when exec addFeatureIntoPartnerOfAppId() with appId: ${appId}\nError: ${err.message}`);
            throw err;
        }
    }

    async updatePartnerFeature(formData: UpdatePartnerFeatureDto): Promise<any> {
        const { appId, partnerId, id } = formData;
        const appIdData = await this.appIdModel.findOne({ appId }).exec();
        const partner = find(appIdData.detail, { partnerId });

        if (partner) {
            // add feature into partner
            const features = partner.features;
            const index = findIndex(features, { id });
            if (index >= 0) {
                const oldFeature = features[index];
                features.splice(index, 1, { ...oldFeature.toObject(), ...formData });
                let updatedAppId = appIdData.save();
                return features[index];
            } else {
                throw new HttpException({ message: `Không tìm thấy feature ${id}` }, HttpStatus.BAD_REQUEST);
            }
        } else {
            throw new HttpException({ message: `Không tìm thấy partnerId ${partnerId} trong appId ${appId}` }, HttpStatus.BAD_REQUEST);
        }
    }

    async validationAddChildFeatureIntoFeatureOfPartner(fromData: AddChildrenFeatureDTO): Promise<any> {
        const { appId, partnerId, featureId, childFeatureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerId = await this.isExistPartnerId(partnerId);
        const isExistFeature = await this.isExistFeature(featureId);
        const isExistChildFeatureInFeature = await this.isExistChildFeatureInFeatureOfPartner(partnerId, featureId, childFeatureId);
        const isAlreadyExistChildFeatureInPartner = await this.isAlreadyExistChildFeatureInParentOfPartner(
            appId,
            partnerId,
            featureId,
            childFeatureId,
        );
        // validate base feature in partner
        const isExistFeatureInPartner = await this.isExistFeatureInPartner(partnerId, featureId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeature) {
            throw new HttpException(`feature with featureId: ${featureId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistChildFeatureInFeature) {
            throw new HttpException(`childFeatureId: ${childFeatureId} does not exist in featureId: ${featureId}`, HttpStatus.BAD_REQUEST);
        }
        if (isAlreadyExistChildFeatureInPartner) {
            throw new HttpException(
                `childFeatureId: ${childFeatureId} already exist in featureID: ${featureId} of partnerId: ${partnerId}`,
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async addChildFeatureIntoFeatureOfPartner(fromData: AddChildrenFeatureDTO): Promise<any> {
        await this.validationAddChildFeatureIntoFeatureOfPartner(fromData);
        const { appId, partnerId, featureId, childFeatureId } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // find partner in appId
            const partner = find(appIdData.detail, { partnerId });
            // validation partner
            if (typeof partner !== undefined) {
                // find feature
                const feature = find(partner.features, item => {
                    return `${item._id}` === featureId;
                });
                // validate feature
                if (typeof feature !== undefined) {
                    // get feature data
                    const featureData = await this.featureModel.findById({ _id: featureId }).exec();
                    // add children feature into feature
                    feature.children.push(featureData);
                }
            }
            return (await appIdData.save()).toJSON();
        } catch (err) {
            this.logger.error(`Error when exec addFeatureIntoPartnerOfAppId() with appId: ${appId}\nError: ${err.message}`);
            throw err;
        }
    }

    async getPartnerInfo(partnerId: string): Promise<IHospital> {
        if (!(await this.isExistPartnerId(partnerId))) {
            throw new HttpException('partnerId not found', HttpStatus.BAD_REQUEST);
        }
        try {
            const partnerInfo = await this.hospitalModel.findOne({ partnerId }).exec();
            return partnerInfo;
        } catch (err) {
            this.logger.error(`Error get partnerId: ${partnerId}, Error message: ${err.message}`);
            throw err;
        }
    }

    async updateAppId(fromData: UpdateAppIdDTO): Promise<IAppId> {
        const { appId, ...updateData } = fromData;
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        try {
            const res = await this.appIdModel.findOneAndUpdate({ appId }, { ...updateData }, { new: true }).exec();
            return res;
        } catch (err) {
            this.logger.error(`Error updateAppId: ${err.message}`);
            throw err;
        }
    }

    async deleteAppId(appId: string): Promise<any> {
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        try {
            const res = await this.appIdModel.findOneAndDelete({ appId }).exec();
            return res;
        } catch (err) {
            this.logger.error(`Error deleteAppId: ${err.message}`);
            throw err;
        }
    }

    async addPartnerIntoAppId(fromData: AddPartnerIntoAppIdDTO): Promise<HospitalResponseDTO> {
        const { appId, partnerId } = fromData;
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        if (!(await this.isExistPartnerId(partnerId))) {
            throw new HttpException('partnerId not found', HttpStatus.BAD_REQUEST);
        }
        if (await this.isExistPartnerInAppId(appId, partnerId)) {
            throw new HttpException(`partnerId: ${partnerId} already exits in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            // Get AppId
            const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
            // Find partnerInfo
            const partner = await this.hospitalModel.findOne({ partnerId }).exec();
            // Add partner into appId detail
            const appIdUpdate = appIdInfo.detail.push(partner);
            await appIdInfo.save();
            const response = await this.hospitalResponse(partner);
            return response;
        } catch (err) {
            this.logger.error(`Error add Partner into AppId: ${appId}, Error: ${err.message}`);
            throw err;
        }
    }

    async deletePartnerInAppId(appId: string, partnerId: string): Promise<any> {
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        if (!(await this.isExistPartnerId(partnerId))) {
            throw new HttpException('partnerId not found', HttpStatus.BAD_REQUEST);
        }
        if (!(await this.isExistPartnerInAppId(appId, partnerId))) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            return await this.appIdModel.findOneAndUpdate({ appId }, { $pull: { detail: { partnerId } } }).exec();
        } catch (err) {
            this.logger.error(`Error delete Partner into AppId: ${appId}, Error: ${err.message}`);
            throw err;
        }
    }

    async deleteFeatureInPartnerOfAppId(appId: string, partnerId: string, featureId: string): Promise<any> {
        // check is exist appId
        const isExistAppId = await this.isExistAppId(appId);
        // check is exist partner in appId
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            // find appId
            const appIds = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIds.detail, { partnerId });
            // find feature in hospital by filter options
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            const featureIndex = hospital.features.indexOf(feature);
            // delete features with index in partner of appId
            hospital.features.splice(featureIndex, 1);
            return appIds.save();
        } catch (err) {
            this.logger.error(
                `Error when exec deleteFeatureInPartnerOfAppId() with appId: ${appId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async deleteChildFeatureInPartnerOfAppId(appId: string, partnerId: string, featureId: string, childFeatureId: string): Promise<any> {
        // check exist appId
        const isExistAppId = await this.isExistAppId(appId);
        // check exist partner in appId
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            // find appId
            const appIds = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIds.detail, { partnerId });
            // find feature in hospital by filter options
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            // find child feature in feature
            const childFeature = find(feature.children, item => {
                return `${item._id}` === childFeatureId;
            });
            const childFeatureIndex = feature.children.indexOf(childFeature);
            // delete children feature in feature with index in partner of appId
            feature.children.splice(childFeatureIndex, 1);
            return appIds.save();
        } catch (err) {
            this.logger.error(
                `Error when exec deleteChildFeatureInPartnerOfAppId() with appId: ${appId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async getFeatureOfPartnerInAppId(appId: string, partnerId: string): Promise<any> {
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            return partner.features;
        } catch (err) {
            this.logger.error(`Error when exec getFeatureOfPartnerInAppId() with partnerId: ${partnerId} and appId: ${appId}\nError: ${err.message}`);
        }
    }

    async handleInputDataForCopyFeature(appId: string, partnerId: string): Promise<any> {
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        const isExistPartnerId = await this.isExistPartnerId(partnerId);

        // validation for input data
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
    }

    async getFeatureFromPartner(partnerId: string): Promise<any> {
        try {
            const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
            return hospital.features;
        } catch (err) {
            this.logger.error(`Error when exec getFeatureFromPartner() with partnerId: ${partnerId}\nError: ${err.message}`);
            throw err;
        }
    }

    async preHandleCopyFeatureFormPartner(partnerId: string, appId: string): Promise<any> {
        try {
            // find appId
            const appIds = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIds.detail, { partnerId });
            // find feature in hospital by filter options
            let feature = hospital.features;
            if (feature) {
                feature = [];
                return appIds.save();
            }
        } catch (err) {
            this.logger.error(
                `Error when exec checkFeatureInPartnerIntoAppId() with partnerId: ${partnerId} and appId: ${appId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async copyFeatureFromBasePartnerToPartnerOfAppId(fromData: CopyFeatureFromPartnerDTO): Promise<any> {
        const { appId, partnerId } = fromData;
        // validation for input data
        await this.handleInputDataForCopyFeature(appId, partnerId);
        try {
            // Get feature from partner with partnerId
            const features = await this.getFeatureFromPartner(partnerId);
            // Pre handler
            await this.preHandleCopyFeatureFormPartner(partnerId, appId);
            // find appId
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIdData.detail, { partnerId });
            hospital.features = features;
            return (await appIdData.save()).toJSON();
        } catch (err) {
            this.logger.error(`Error when exec copyFeatureFromBasePartnerToPartnerOfAppId() with appId: ${appId}\nError: ${err.message}`);
            throw err;
        }
    }

    async validationForUpdateFeature(fromData: UpdateFeatureInPartnerDTO): Promise<any> {
        // declare variable
        const { appId, partnerId, featureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        const isExistFeatureInPartner = await this.isAlreadyExistFeatureInPartner(appId, partnerId, featureId);
        // validation process
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
    }

    async updateFeatureInPartner(fromData: UpdateFeatureInPartnerDTO): Promise<any> {
        // pre handler
        await this.validationForUpdateFeature(fromData);
        // declare variable
        const { appId, partnerId, featureId, ...updateData } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            // update data
            feature.set(updateData);
            return (await partner.save()).toJSON();
        } catch (error) {
            this.logger.error(`Error when exec updateFeatureInPartner() with appId: ${appId} and partnerId: ${partnerId}\nError: ${error.message}`);
            throw error;
        }
    }

    async validationForUpdateChildFeature(fromData: UpdateChildrenInFeatureOfPartnerDTO): Promise<any> {
        // declare variable
        const { appId, partnerId, featureId, childFeatureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        const isExistFeatureInPartner = await this.isAlreadyExistFeatureInPartner(appId, partnerId, featureId);
        const isExistChildFeatureInFeature = await this.isAlreadyExistChildFeatureInParentOfPartner(appId, partnerId, featureId, childFeatureId);
        // validation process
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistChildFeatureInFeature) {
            throw new HttpException(
                `childFeatureId: ${childFeatureId} does not exist in featureId: ${featureId} in partnerId: ${partnerId}`,
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async updateChildFeatureInFeatureOfPartner(fromData: UpdateChildrenInFeatureOfPartnerDTO): Promise<any> {
        // pre handler
        await this.validationForUpdateChildFeature(fromData);
        // declare variable
        const { appId, partnerId, featureId, ...updateData } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            const childFeature = find(feature.children, item => {
                return `${item._id}` === childFeature;
            });
            // update data
            childFeature.set(updateData);
            return (await partner.save()).toJSON();
        } catch (error) {
            this.logger.error(
                `Error when exec updateChildFeatureInFeatureOfPartner() with appId: ${appId} and partnerId: ${partnerId}\nError: ${error.message}`,
            );
            throw error;
        }
    }

    async getListPartnerByAppId(appId: string, country = 'VN'): Promise<any[]> {
        try {
            const app = await this.appIdModel.findOne({ appId, country });
            if (!app) {
                throw new HttpException(`appId: ${appId} không tồn tại!`, HttpStatus.NOT_FOUND);
            }
            const { detail } = app;
            if (!detail) {
                throw new HttpException('appId không có partner nào!', HttpStatus.BAD_REQUEST);
            }
            return detail.map(item => {
                return pick(item, Object.values(PartnerResponseEnum));
            });
        } catch (error) {
            throw error;
        }
    }

    async getFeatureInAppId(appId: string): Promise<any> {
        try {
            const detail = await this.getListPartnerByAppId(appId);
            let featureHandlers = [];
            let featureRes = [];
            for (const partner of detail) {
                const { features } = partner;
                featureHandlers = [...featureHandlers, ...features.map(feature => pick<any>(feature, Object.values(FeatureResponseEnum)))];
            }
            const featureGroup = groupBy(featureHandlers, 'type');
            for (const [key, value] of Object.entries(featureGroup)) {
                const feature = first(value);
                value.map(element => {
                    feature.children = [...feature.children, ...element.children];
                });
                if (feature.children && feature.children.length) {
                    const childrenGroup = groupBy(feature.children, 'type');
                    feature.children = Object.entries(childrenGroup).map(([keyChild, valueChild]) => {
                        return pick<any>(first(valueChild), Object.values(FeatureChildResponseEnum));
                    });
                }
                featureRes = [...featureRes, feature];
            }
            return featureRes;
        } catch (error) {
            throw error;
        }
    }

    async getPartnerByAppIdAndFeatureId(appId: string, featureId: string): Promise<any[]> {
        try {
            let partnerResponse = [];
            const detail = await this.getListPartnerByAppId(appId);
            for (const partner of detail) {
                let featureHandlers: any = [];
                const { features } = partner;
                if (features) {
                    for (const feature of features) {
                        const { children } = feature;
                        if (children) {
                            featureHandlers = [...featureHandlers, ...children];
                        }
                    }
                    featureHandlers = [...featureHandlers, ...features];
                }
                const isHaveFeature = featureHandlers.find(item => item.id === featureId);
                if (isHaveFeature) {
                    partnerResponse = [...partnerResponse, partner];
                }
            }
            return partnerResponse;
        } catch (error) {
            throw error;
        }
    }

    async getPartnerIdsInAppGroup(appId: string, country = 'VN'): Promise<string[]> {
        const appIdConfig = await this.appIdModel.findOne({ appId, country }).exec();

        if (!appIdConfig) {
            return [];
        }

        return map(get(appIdConfig, 'detail', []), 'partnerId');
    }

    async getFeaturesDomain(appId: string): Promise<any> {
        let listFeature: any[];
        const app = await this.appIdModel.findOne({ appId }).exec();
        if (app?.detail && app.detail.length) {
            const appObj = app.toObject();
            const { detail: partnersApp } = appObj;
            let features: any[] = [];
            let featureTypes: string[] = [];
            let partners: string[] = [];

            for (const partner of partnersApp) {
                features = [
                    ...features,
                    ...partner.features.map(feature => {
                        return {
                            ...feature,
                            partnerId: partner.partnerId,
                            partnerName: partner.name,
                            logoPartner: partner.image,
                        };
                    }),
                ];
                featureTypes = [...featureTypes, ...partner.features.map(feature => feature.type)];
                partners = [...partners, partner.partnerId];
            }

            const setFeturesTypes = new Set(featureTypes);
            const groupFeaturePartner = groupBy(features, 'partnerId');

            listFeature = [...setFeturesTypes].map(featureType => {
                let partnerFeatures: any[] = [];
                for (const partner of partners) {
                    const findFeature = groupFeaturePartner[partner]
                        ? groupFeaturePartner[partner].find(feature => feature.type === featureType)
                        : null;
                    if (findFeature) {
                        partnerFeatures = [
                            ...partnerFeatures,
                            {
                                partnerId: findFeature.partnerId,
                                disabled: findFeature.disabled,
                                message: findFeature.message,
                                name: findFeature.partnerName,
                                logoPartner: findFeature.logoPartner,
                                webRoute: findFeature?.webRoute,
                                mobileRoute: findFeature?.mobileRoute,
                                status: findFeature?.status,
                                mobileStatus: findFeature?.mobileStatus,
                            },
                        ];
                    }
                }
                const getInfoFeature = features.find(feature => feature.type === featureType);
                return {
                    type: featureType,
                    name: getInfoFeature.name,
                    iconFeature: getInfoFeature.image,
                    displayIcon: getInfoFeature?.displayIcon || '',
                    partners: partnerFeatures,
                };
            });
        } else {
            const hospital = await this.hospitalModel
                .findOne({
                    partnerId: appId,
                })
                .exec();
            if (hospital) {
                const { features = [] } = hospital.toObject();
                /* kiểm tra trước khi response về */
                let resultData: any = [];
                for (const feature of features) {
                    const getChildren = get(feature, 'children', []);
                    if (getChildren.length > 0) {
                        resultData = [...resultData, ...getChildren];
                    } else {
                        resultData = [...resultData, feature];
                    }
                }
                const setFeturesTypes = new Set(resultData.map(feature => feature.type));
                listFeature = [...setFeturesTypes].map(featureType => {
                    let partnerFeatures: any[] = [];

                    const findFeature = resultData.find(feature => feature.type === featureType);
                    if (findFeature) {
                        partnerFeatures = [
                            ...partnerFeatures,
                            {
                                partnerId: hospital.partnerId,
                                disabled: findFeature.disabled,
                                message: findFeature.message,
                                name: hospital.name,
                                logoPartner: hospital.image,
                                webRoute: findFeature?.webRoute,
                                mobileRoute: findFeature?.mobileRoute,
                                status: findFeature?.status,
                                mobileStatus: findFeature?.mobileStatus,
                            },
                        ];
                    }
                    return {
                        type: featureType,
                        name: findFeature?.name || '',
                        iconFeature: findFeature?.image || '',
                        displayIcon: findFeature?.displayIcon || '',
                        partners: partnerFeatures,
                    };
                });
            }
        }
        return listFeature;
    }

    async getByDomain(domain: string): Promise<any> {
        const data = await this.client.get(`${this.config.getBoUrl()}/partner-domain/single?domain=${domain}`);

        if (!data || !data?.partnerId) {
            throw new HttpException('Partner domain chưa được cấu hình cho bệnh viện này!', HttpStatus.BAD_REQUEST);
        }

        const features = await this.getFeaturesDomain(data.partnerId);

        let partners = [];
        for (const feature of features) {
            partners = [...partners, ...feature.partners.map(partner => partner.partnerId)];
        }
        partners = await Promise.all(
            [...new Set(partners)].map(async partnerId => {
                const hospital = await this.hospitalModel.findOne(
                    { partnerId },
                    {
                        partnerId: true,
                        phone: true,
                        email: true,
                        name: true,
                        logoPartner: true,
                        circleLogo: true,
                        address: true,
                    },
                );
                return {
                    partnerId: hospital?.partnerId,
                    disabled: hospital?.phone,
                    email: hospital?.email,
                    name: hospital?.name,
                    logoPartner: hospital?.image,
                    circleLogo: hospital?.circleLogo,
                    address: hospital?.address,
                };
            }),
        );

        return {
            ...data,
            appId: data.partnerId,
            features,
            partners,
        };
    }

    async getAppIdByDomain(domain: string) {
        try {
            let data;
            if (domain === 'umc.medpro.vn') {
                data = await this.partnerDomainModel.find({ partnerId: 'umc2' }).exec();
            } else {
                const handlerDomain = domain
                    .replace('https://', '')
                    .replace('http://', '')
                    .replace('/', '')
                    .replace('medpro.vn', 'medpro.com.vn')
                    .replace('-beta', '');
                data = await this.partnerDomainModel.find({ domain: { $in: [handlerDomain] } }).exec();
            }

            if (!data.length) {
                throw new HttpException(`Không tìm thấy partner với domain: ${domain} này !`, HttpStatus.NOT_FOUND);
            }

            const resData: any = first(data);

            if (!resData || !resData?.partnerId) {
                throw new HttpException('Partner domain chưa được cấu hình cho bệnh viện này!', HttpStatus.BAD_REQUEST);
            }

            return {
                appId: resData.partnerId,
                ...resData.toObject(),
            };

        } catch (error) {
            this.logger.error(`Error when exec getByDomain()\nError: ${error.message || error?.response.data.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            // this.eventEmitter.emit(LOG_SERVICE_EVENT, {
            //     name: 'getByDomain',
            //     summary: 'getByDomain',
            //     nameParent: 'getByDomain',
            //     params: { domain },
            //     message: errorMessage,
            // });
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getFeatureInApp(query: any = {}, headers: HeadersDto = {}) {
        const { locale, appid: appId, platform } = headers;

        const filterByLocale: any = { appId, country: this.utilService.getCountryByLocale(locale) };

        const app = await this.appIdModel.findOne(filterByLocale, { 'features.partners': false }).exec();

        let features = app.features || [];

        if (!REPO_NAME_BETA.includes(this.repoName)) {
            let featureFilterByPlatform = this.utilService.getPlatformType(platform) === 'web' ? f => f.status : f => f.mobileStatus;
            features = features.filter(featureFilterByPlatform);
        }

        features = features.map(f => {
            const { flow, _id, ...rest } = f.toObject();
            // không có disable thì không show message
            if (!f.disabled) {
                f.message = '';
            }

            if (f.message) {
                f.warningMessage = '';
            }

            const nextPartner = !(flow && flow.startsWith('feature'));

            return { ...rest, nextPartner, id: _id };
        });

        features = orderBy(features, ['priority'], ['asc'])
        
        // const bookingSubject = await this.globalSettingService.findByKeyAndRepoName('DAT_KHAM_THEO_CHUYEN_KHOA');
        // const bookingSubjectObj = bookingSubject ? JSON.parse(bookingSubject) : {};
        
        // if (bookingSubjectObj?.status || REPO_NAME_BETA.includes(this.repoName)) {
        //     features.splice(bookingSubjectObj?.sortOrder - 1, 0, bookingSubjectObj);
        // }

        return features
    }

    async featureFlowNextStep(query: any, headers: HeadersDto) {
        const { featureId, featureSlug, partnerFeatureType, city_id, district_id } = query;
        const { appid: appId, platform, locale } = headers;

        const filterByLocale: any = { appId, country: this.utilService.getCountryByLocale(locale) };

        const app = await this.appIdModel.findOne(
            filterByLocale,
            {
                features: {
                    $elemMatch: {
                        $or: [
                            { _id: featureId },
                            { slug: `${featureSlug}` }
                        ]
                    }
                }
            })
            .exec();

        if (!app) {
            throw new HttpException({ message: `Not found app ${appId}` }, 404);
        }

        const appFeature = first<any>(app.toObject().features);

        if (!appFeature) {
            throw new HttpException({ message: `Not found feature ${featureId || featureSlug}` }, 404);
        }

        // case feature đi trước partner
        if (appFeature.flow?.startsWith('feature') && !partnerFeatureType) {
            return this.getPartnerFeatureInFeatureInApp({ featureId, featureSlug }, headers);
        }

        const [inProgess, hold, partnersOnlyShowBeta, digimedOverrideName,checkStatusFeature] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_IN_PROGRESS'),
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_HOLD'),
            this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA', this.repoName),
            this.globalSettingService.findByKeyAndRepoName('DIGIMED_OVERRIDE_NAME'),
            this.globalSettingService.findByKeyAndRepoName('CHECK_STATUS_FEATURE')
        ]);
        let getConfigFeature = []
        if(checkStatusFeature){
            getConfigFeature = checkStatusFeature.split(',');
        }
        const deliveryListStatus = {
            hold,
            inProgess,
        };

        let partners = appFeature.partners;

        if (partnerFeatureType) {
            partners = partners.filter(p => {
                return p?.features?.filter(f => f.type === partnerFeatureType)?.length > 0;
            });
        }

        const featureFilterStatusByPlatform = this.utilService.getPlatformType(platform) === 'web' ? f => f.status : f => f.mobileStatus;
        // filter partner có cấu hình tính năng ở bước 3
        if(!getConfigFeature.includes(featureSlug)){
            partners = partners.filter(p => p.features?.filter(featureFilterStatusByPlatform)?.length > 0);
        }

        
        const partnerGroup = partners.reduce((c, e) => {
            c[e.partnerId] = e;
            return c;
        }, {});

        let partnerIds = Object.keys(partnerGroup);
        if (REPO_NAME_BETA.includes(this.repoConfigService.getRepoName())) {
            const partnerIdBeta = (await this.globalSettingService.findByKeyAndRepoName('PARTNERS_BETA_API_3_STEP')).split(',');
            partnerIds = Array.from(new Set([...partnerIds, ...partnerIdBeta]));
        }

        const condition = {
            partnerId: { $in: partnerIds },
            ...(city_id && { city_id }),
            ...(district_id && { district_id: { $in: district_id.split(',') } }),
        }

        let list = await this.hospitalModel
            .find(
                { ...condition },
                {
                    name: true,
                    partnerId: true,
                    image: true,
                    circleLogo: true,
                    address: true,
                    city_id: true,
                    district_id: true,
                    deliveryStatus: true,
                    message: true,
                    sponsored: true,
                    listingPackagePaid: true,
                    isCashBack:true,
                    newHospitalTypes: true,
                    slug: true,
                    sortOrderListing: true,
                    isContractSigned: true,
                    addressCensored: true,
                    city: true,
                    district: true,
                },
            )
            .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
            .populate('city')
            .populate('district')
            .lean();

        if (REPO_NAME_BETA.includes(this.repoName)) {

            list = list.map(value => {
                if (['medlatecbp', 'binhthanhhcm'].includes(value.partnerId)) {
                    return {
                        ...value,
                        isCashBack: true
                    };
                }
                return value;
            });
        }

        // ? Thêm popup content
        const fee = await this.homePageService.getPartnerCashBackPopupList()

        if (!partnersOnlyShowBeta) {
            const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
            const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
            list = list.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
        }

        const trackingDeliveryStatus = new Set([1, 2]);
        const hospitalList = await this.hospitalDescriptionModel.find();
        const hospitalDescriptionObj={}
        hospitalList.forEach(h => { if (!!h?.hospitalId) hospitalDescriptionObj[h.hospitalId] = h });
        list =  list.map((element) => {
            const hospitalDescription = hospitalDescriptionObj[element.slug];
            const { circleLogo } = element;
            const address = this.utilService.getAddressHospital(element.address, element.addressCensored, element.isContractSigned);
            let res = { 
                ...element, 
                ...(circleLogo && { image: circleLogo }),
                ...(fee.hasOwnProperty(element.partnerId) ? { popup: fee[element.partnerId] } : {}),
                address
            };

            if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                const deliveryMessage = this.hospitalService.deliveryMessageOptimize(element.deliveryStatus, deliveryListStatus);
                res = {
                    ...element,
                    deliveryMessage,
                    ...(circleLogo && { image: circleLogo }),
                };
            }

            if (has(element, 'slug')) {
                res.showPartnerInfo = !!hospitalDescriptionObj[element.slug]?.showPartnerInfo;
                res.layout = hospitalDescriptionObj[element.slug]?.layout || 'NORMAL';
            }

            if (element.partnerId === 'digimed' && appFeature.type === 'booking.doctor') {
                res.name = digimedOverrideName;
            }
            res.rating = hospitalDescriptionObj[element.slug]?.rating;
            return res;
        });
        list = list.map(element => {
            // override workFlow
            element.workFlow = appFeature.workFlow;
            const partnerInApp = partnerGroup[element.partnerId];

            // override message by partner in app
            if (partnerInApp?.message) {
                element.message = partnerInApp.message;
            }

            // override message, warningMessage by feature
            let partnerFeatures = partnerInApp?.features || [];

            if (!REPO_NAME_BETA.includes(this.repoName)) {
                partnerFeatures = partnerFeatures.filter(featureFilterStatusByPlatform);
            }

            if (size(partnerFeatures) === 1) {
                const partnerFeature = partnerFeatures[0];
                if (partnerFeature) {
                    // không có message và tính năng disabled
                    if (!element.message && partnerFeature.disabled === true) {
                        element.message = partnerFeature.message || 'Tính năng đang được phát triển!';
                    }

                    if (!element.message) {
                        element.warningMessage = partnerFeature.warningMessage;
                    }
                }
            } else if (!element.message && size(partnerFeatures) === 0) {
                element.message = 'Bệnh viện chưa triển khai tính năng!';
            }

            element.sortOrder = partnerGroup[element.partnerId]?.sortOrder;
            return element;
        });

        // list = list.sort((a, b) => {
        //     return partnerGroup[a.partnerId]?.sortOrder - partnerGroup[b.partnerId]?.sortOrder;
        // });

        let bookingYesterdayTotal = await this.cacheManagerService.get('daily-transaction-report:latest-report-group-by-partnerId');
        if (!bookingYesterdayTotal) {
            let filterDate = {};
            // 1: Monday, 0: Sunday
            if ([0,1].includes(moment().utc().add(7, 'hours').weekday())) {
                const fridayNewest = moment().utc().add(7, 'hours').days(1);
                filterDate = { createdAt: { $gte: fridayNewest.startOf('days').toISOString() }};
            }

            bookingYesterdayTotal = await this.transactionDailyModel.findOne(filterDate).sort({ createdAt: -1 }).exec();
            bookingYesterdayTotal = bookingYesterdayTotal?.hospitals.reduce((r, h) => {
                r[h.partnerId] = h.total;
                return r;
            }, {});
        }

        const hospitalsSponsored = list.filter(h => h.sponsored == true).sort((a, b) => (get(bookingYesterdayTotal, a.partnerId, null) > get(bookingYesterdayTotal, b.partnerId, null) ? -1 : 1));
        hospitalsSponsored.forEach((h0) => list.splice(list.findIndex(e => e._id == h0._id), 1));
        list.sort((a, b) => (get(bookingYesterdayTotal, a.partnerId, null) > get(bookingYesterdayTotal, b.partnerId, null) ? -1 : 1));
        list = [...hospitalsSponsored, ...list];

        const hospitalSortingManual: any = await this.globalSettingService.findByKeyAndRepoName('HOSPITALS_SORTING_MANUAL') || '{}';
        const hospitalSortingManualData: Object = JSON.parse(hospitalSortingManual);

        for (let k in hospitalSortingManualData) {
            const thisIndex = list.findIndex(l => l.partnerId == k);
            let targetIndex: number;

            if (has(hospitalSortingManualData, `${k}.next`)) {
                targetIndex = list.findIndex(l => l.partnerId == hospitalSortingManualData[k].next);
                if (targetIndex != -1 && thisIndex != -1) {
                    list.splice(targetIndex == 0 || targetIndex < thisIndex ? targetIndex : targetIndex-1, 0, list.splice(thisIndex, 1)[0]);
                }
            } else if (has(hospitalSortingManualData, `${k}.prev`)) {
                targetIndex = list.findIndex(l => l.partnerId == hospitalSortingManualData[k].prev);
                if (targetIndex != -1 && thisIndex != -1) {
                    list.splice(targetIndex == list.length-1 || targetIndex > thisIndex ? targetIndex : targetIndex+1, 0, list.splice(thisIndex, 1)[0]);
                }
            }
        }

        list = this.utilService.sortlistingPackagePaidHospital(list);

        //sort by listing
        const now = moment();

        const queryListing = {
            type: appFeature.type,
            category: 'hospital',
            status: true,
            $or: [
                { display: true },
                { fromDate: { $lte: now.toDate() }, toDate: { $gte: now.toDate() } }
            ]
        }
        const listing = await this.listingModel.find(queryListing).lean();
        if (listing && listing.length) {
            list = this.utilService.sortPartnerListingByBoMkt(list, listing, 'partnerId');
        }

        return list;
    }

    async partnerLocationByFeatureInApp(query: any, headers: HeadersDto = {}): Promise<any> {
        const listPartner = await this.featureFlowNextStep(
            {
                featureSlug: query?.featureSlug,
                featureId: query?.featureId,
                partnerFeatureType: query?.partnerFeatureType,
                appId: headers.appid,
            },
            headers,
        );

        const groupedByCityId = groupBy(listPartner, 'city_id');

        const result = map(groupedByCityId, (cityItems, city_id) => {
            const firstItem = cityItems[0];
            const uniqueTruthyDistricts = chain(cityItems)
                .map('district')
                .filter(Boolean)
                .uniqBy('_id')
                .value();
            return {
                city_id,
                city: { ...firstItem.city },
                district: uniqueTruthyDistricts
            };
        });

        return result;
    }

    async getPartnerFeatureInFeatureInApp(query: any, headers: HeadersDto = {}) {
        const { featureId, featureSlug } = query;
        const { appid: appId, partnerid, locale, platform } = headers;
        const partnerId = partnerid === 'medpro' ? null : partnerid;

        if (REPO_NAME_BETA.includes(this.repoName)) {
            if (appId === 'trungvuong' && partnerid === 'trungvuong') {
                const features = await this.featureService.getAllFeatureListByPartner(partnerid, appId);
                const findBookingDate = (features || []).filter(item => item.type === 'booking.date');
                return findBookingDate.length === 1 ? findBookingDate : features;
            }

            return this.featureService.getFeatureByVersion(partnerid, appId, 1, 1, platform);
        }

        const filterByLocale: any = {
            appId,
            features: {
                $elemMatch: {
                    $or: [
                        { _id: featureId },
                        { slug: `${featureSlug}` },
                    ]
                },
            },
            country: this.utilService.getCountryByLocale(locale),
        };

        let projection: any = {
            appId,
            features: {
                $elemMatch: {
                    $or: [
                        { _id: featureId },
                        { slug: `${featureSlug}` },
                    ]
                },
            },
        };

        if (partnerId) {
            projection = {
                ...projection,
                partners: { $elemMatch: { partnerId } },
            };
        }

        const featureFilterByPlatform = this.utilService.getPlatformType(platform) === 'web' ? f => f.status : f => f.mobileStatus;

        try {
            const app = await this.appIdModel
                .findOne(filterByLocale, {
                    ...projection,
                })
                .exec();

            let partners = app.toObject().features?.[0]?.partners || [];

            if (partnerId) {
                partners = partners.filter(p => p.partnerId === partnerId);
            }

            const allFeature = [];

            partners.forEach(p => {
                let partnerFeature = p.features || [];
                if (!REPO_NAME_BETA.includes(this.repoName)) {
                    partnerFeature = partnerFeature.filter(featureFilterByPlatform);
                }
                partnerFeature.forEach(f => (f.sortOrder = p.sortOrder));
                allFeature.push(...partnerFeature);
            });

            const featureGroupByType = allFeature.reduce((c, item) => {
                const { type } = item;

                if (c[type]) {
                    if (item.isPriority) {
                        c[type] = item;
                    }
                } else {
                    c[type] = item;
                }

                return c;
            }, {});

            const features: any[] = Object.values(featureGroupByType);
            features.map(f => {
                // không có disable thì không show message
                if (!f.disabled) {
                    f.message = '';
                }

                if (f.message) {
                    f.warningMessage = '';
                }

                return f;
            });

            return orderBy(features, [partnerId ? 'priority' : 'sortOrder'], ['asc']);
        } catch (err) {
            console.error('getPartnerFeatureInFeatureInApp: ', err);
            return [];
        }
    }

    overrideFeaturesModeBeta(features: any[]): any[] {
        const setRepoName = new Set(REPO_NAME_BETA);
        if (setRepoName.has(this.repoName)) {
            return features.map(f => {
                if (f.status) {
                    return { ...f, status: true, mobileStatus: true, message: '', disabled: false };
                } else {
                    return { ...f };
                }
            });
        } else {
            return features;
        }
    }

    overridePartnerModeBeta(partners: any[]): any[] {
        const setRepoName = new Set(REPO_NAME_BETA);
        if (setRepoName.has(this.repoName)) {
            return partners.map(h => {
                return { ...h, message: '' };
            });
        } else {
            return partners;
        }
    }

    public async getQuickNotifBookingPages(formData: any, appid: string, platform: string, locale: string): Promise<any> {
        try {
            const { slug, type } = formData;
            
            let slugConfig = slug;
            
            if (type) {
                const features = await this.getFeatureInApp(
                    {},
                    {
                        locale,
                        appid,
                        platform: 'pc',
                    },
                );
                if (!features) {
                    throw new HttpException('Không tìm thấy tính năng.', HttpStatus.FORBIDDEN);
                }
                
                slugConfig = type === 'booking.' ? 'dat-kham-tai-co-so' : features.find(feature => feature.type === type)?.slug;
            }

            const notifConfig = await this.globalSettingService.findByKeyAndRepoName('QUICK_NOTIF_BOOKING_PAGES');
            const notifObj = notifConfig ? JSON.parse(notifConfig) : {};
            const data = notifObj[slugConfig] || notifObj[type] || ''
            return {
                htmlContent: data
            }
        } catch (error) {
            console.log('error', error);
        }
    }

    resetCachePartnerByFeature() {
        this.cacheManagerService.delByPattern(`app-id:partner-by-feature-in-app*`);
        this.cacheManagerService.delByPattern(`app-id:feature-of-partner-in-feature-in-app*`);
        return true;
    }

    resetCachePopupV2() {
        this.cacheManagerService.delByPattern(`partner-config:popup-v2*`);
        return true;
    }
}
