import * as mongoose from 'mongoose';
import { HOSPITAL_COLLECTION_NAME, HOSPITAL_LOCATION_COLLECTION_NAME } from './constants';
import { CITY_COLLECTION_NAME } from '../../city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from '../../district-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const HospitalLocationSchema = new Schema(
    {
        geofenceId: { type: String },
        live: { type: Boolean },
        name: { type: String },
        tag: { type: String },
        externalId: { type: String },
        partnerId: { type: String },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        type: { type: String },
        geometryRadius: { type: Number },
        geometryCenter: { type: Schema.Types.Mixed },
        geometry: { type: Schema.Types.Mixed },
        ip: { type: Schema.Types.Mixed },
        enabled: { type: Boolean },
        address: { type: String },
        city_id: { type: String },
        city: { type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
        district_id: { type: String },
        district: { type: Schema.Types.ObjectId, ref: DISTRICT_COLLECTION_NAME },
        isContractSigned: { type: Boolean },
        listingPackagePaid: { type: Boolean },
        isCashBack: { type: Boolean },
        circleLogo: { type: String },
        slug: { type: String },
    },
    {
        collection: HOSPITAL_LOCATION_COLLECTION_NAME,
        timestamps: true,
    },
);