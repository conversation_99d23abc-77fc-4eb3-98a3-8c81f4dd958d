import { Controller, Post, Body, Get, Query, Headers, Param, Put, Delete, HttpCode, HttpStatus, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiHeader } from '@nestjs/swagger';
import { PartnerConfigService } from './partner-config.service';
import { CreatePartnerConfigDTO } from './dto/create-partner-config.dto';
import { UpdatePartnerConfigDTO } from './dto/update-partner-config.dto';
import { AddFiltersDTO } from './dto/add-filters.dto';
import { IPartnerConfig } from './intefaces/partner-config.inteface';
import { BoGuard } from 'src/common/guards/bo.guard';
import { AppCskhInterceptor } from 'src/middleware/app-cskh.interceptor';
import { HeadersDto } from './dto/headers.dto';
import { CacheManagerService } from '../cache-manager/cache-manager.service';

@Controller('partner-config')
@ApiTags('Partner Config - Quản lý Partner Config trên MongoDB')
export class PartnerConfigController {
    constructor(
        private readonly partnerConfig: PartnerConfigService,
        private cacheService: CacheManagerService,
    ) {}

    @Get('is-vat-invoice')
    async isVatInvoice(@Headers('partnerid') partnerid: string): Promise<any> {
        return this.partnerConfig.isVatInvoice(partnerid);
    }

    @Get('get-extra-config')
    @ApiHeader({
        name: 'partnerid',
        required: false,
    })
    @UseInterceptors(AppCskhInterceptor)
    async getExtraConfig(@Headers('appid') appid: string, @Headers('partnerid') partnerid: string): Promise<any> {
        const key = `partner-config:get-extra-config:app-id:${appid}:partnerid:${partnerid}:${this.cacheService.repoName}`;
        // console.log('getExtraConfig:', key)
        const cachedValue = await this.cacheService.get(key);
        if (cachedValue) {
            return cachedValue;
        }
        if (!!partnerid === false) {
            partnerid = appid;
        }
        const resData = await this.partnerConfig.getExtraConfig(partnerid);
        this.cacheService.set(key, resData, { ttl: 7200 }); // 2 days (milliseconds)
        return resData;
    }

    @Get('popup')
    @ApiOperation({ summary: 'Lấy thong tin Popup theo appid' })
    @HttpCode(200)
    async getPopup(@Headers() headers: HeadersDto): Promise<any> {
        return this.partnerConfig.getPopup(headers);
    }

    @Get()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: 'Lấy danh sách partner config hiện có' })
    async list(): Promise<any> {
        return this.partnerConfig.list();
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    @ApiOperation({ summary: 'Tạo partner config cho bệnh viện' })
    async create(@Body() formData: CreatePartnerConfigDTO): Promise<any> {
        return this.partnerConfig.create(formData);
    }

    @Put()
    @HttpCode(HttpStatus.OK)
    @UseGuards(BoGuard)
    @ApiOperation({ summary: 'Cập nhật partner config cho bệnh viện' })
    async update(
        @Body() formData: UpdatePartnerConfigDTO,
        @Headers('appboid') appBoId?: string,
        @Headers('appbokey') appBoKey?: string,
    ): Promise<any> {
        return this.partnerConfig.update(formData);
    }

    @Post('update-max-id')
    async updateMaxId(@Query('partnerId') partnerId: string): Promise<any> {
        return this.partnerConfig.updateMaxId(partnerId);
    }

    @Post('add-constraints')
    async addConstraints(@Query('partnerId') partnerId: string, @Body() formData: AddFiltersDTO): Promise<any> {
        return this.partnerConfig.addConstraints(partnerId, formData);
    }

    @Get('partner/:partnerId')
    @ApiOperation({ summary: 'Lấy partner config của bệnh viện' })
    async getPartnerConfigByPartnerId(@Param('partnerId') partnerId: string): Promise<any> {
        return this.partnerConfig.getPartnerConfigByPartnerId(partnerId);
    }

    @Delete('partner/:partnerId')
    @ApiOperation({ summary: 'Xóa partner config của bệnh viện' })
    @HttpCode(HttpStatus.NO_CONTENT)
    async deletePartnerConfig(@Param('partnerId') partnerId: string): Promise<any> {
        return this.partnerConfig.delete(partnerId);
    }

    @Put('seed-data')
    @ApiOperation({ summary: 'Fix data of bookingRules' })
    @HttpCode(200)
    async seedData(): Promise<any> {
        return this.partnerConfig.seedData();
    }

    @Get('partner-referral')
    @ApiOperation({ summary: 'Lấy danh sách BV Trung Chuyển của Partner' })
    @HttpCode(200)
    async partnerReferral(@Headers('partnerid') partnerId: string): Promise<any> {
        return this.partnerConfig.seedPartnerReferral(partnerId);
    }
}
