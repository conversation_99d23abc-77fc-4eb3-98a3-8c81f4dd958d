import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';

@Injectable()
export class UrlConfigService extends ConfigManager {
    getUrlCheckBookingRules(): string {
        return this.get<string>('API_URL_BOOKING_RULES');
    }

    public get isImplCashBackZn(): boolean {
        const isImplCashBackZn = this.get<string>('IS_IMPL_CASH_BACK_ZNS');
        const numberFromConfig = parseInt(isImplCashBackZn, 10);
        return Boolean(numberFromConfig);
    }

    provideConfigSpec() {
        return {
            API_URL_BOOKING_RULES: {
                validate: Joi.string(),
                required: true,
            },
            ENVIRONMENT: {
                validate: Joi.string(),
                required: false,
                default: 'DEVELOPMENT',
            },
            BASE_URL: {
                validate: Joi.string(),
                required: true,
            },
            PARTNER_URL: {
                validate: Joi.string(),
                required: true,
            },
            MOMO_END_POINT: {
                validate: Joi.string(),
                required: true,
            },
            API_DOCS_JSON: {
                validate: Joi.string(),
                required: true,
            },
            BOOKING_TREE_URL: {
                validate: Joi.string(),
                required: true,
            },
            BOOKING_TREE_V2_URL: {
                validate: Joi.string(),
                required: true,
            },
            INVOICE_URL: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_URL: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_PAY_PATH: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_TRANSACTION_PATH: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_FEE_URL: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_GET_FEE_PATH: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_SEARCH_QRCODE_FEE_PATH: {
                validate: Joi.string(),
                required: true,
            },
            PAYMENT_HUB_UPDATE_STATUS_FEE_PATH: {
                validate: Joi.string(),
                required: true,
            },
            SMS_CHECK_CODE: {
                validate: Joi.string(),
                required: true,
            },
            SMS_CHECK_PHONE: {
                validate: Joi.string(),
                required: true,
            },
            SMS_CHECK_CODE_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
            SMS_CHECK_PHONE_NHI_DONG_1: {
                validate: Joi.string(),
                required: true,
            },
            SMS_CHECK_CODE_UMC: {
                validate: Joi.string(),
                required: true,
            },
            SMS_CHECK_PHONE_UMC: {
                validate: Joi.string(),
                required: true,
            },
            CHORAY_TRACKING_ORDER_APP_KEY: {
                validate: Joi.string(),
                required: true,
            },
            CHORAY_TRACKING_ORDER_VIEW_INVOICE_URL: {
                validate: Joi.string(),
                required: true,
            },
            CHORAY_TRACKING_ORDER_GET_INVOICE_URL: {
                validate: Joi.string(),
                required: true,
            },
            CHORAY_TRACKING_ORDER_SEND_EMAIL_URL: {
                validate: Joi.string(),
                required: true,
            },
            FILTER_CHECK_URL: {
                validate: Joi.string(),
                required: true,
            },
            CARD_MANAGEMENT_URL: {
                validate: Joi.string(),
                required: true,
            },
            API_TELEMED_PROXY: {
                validate: Joi.string(),
                required: true,
            },
            KEY_HASHED_PIN: {
                validate: Joi.string(),
                required: true,
            },
            EVENT_PROCESSOR_URL: {
                validate: Joi.string(),
                required: true,
            },
            /* Chợ rẫy - Exam Results */
            CR_EXAM_RESULTS_URL: {
                validate: Joi.string(),
                required: true,
            },
            CR_EXAM_RESULTS_USERNAME: {
                validate: Joi.string(),
                required: true,
            },
            CR_EXAM_RESULTS_PASSWORD: {
                validate: Joi.string(),
                required: true,
            },
            // CR_EXAM_RESULTS_DATA_SIGN: {
            //     validate: Joi.string(),
            //     required: false,
            // },
            GATEWAY_API_URL: {
                validate: Joi.string(),
                required: true,
            },
            PROXY_API_URL: {
                validate: Joi.string(),
                required: true,
            },
            SERVICE_SUBSCRIPTION_API_URL: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    getCrExamResultConfig(): { UserName: string; PassWord: string; DataSign: string; RestApi: string } {
        return {
            UserName: this.get<string>('CR_EXAM_RESULTS_USERNAME'),
            PassWord: this.get<string>('CR_EXAM_RESULTS_PASSWORD'),
            DataSign: this.get<string>('CR_EXAM_RESULTS_DATA_SIGN'),
            RestApi: this.get<string>('CR_EXAM_RESULTS_URL'),
        };
    }

    getEventProcessorUrl(): string {
        return this.get<string>('EVENT_PROCESSOR_URL');
    }

    getKeyHashedPin(): string {
        return this.get<string>('KEY_HASHED_PIN');
    }

    loginPortalUrl(): string {
        const baseUrl = this.getPortalProxyUrl();
        return `${baseUrl}/user-service/v1/user/login`;
    }

    getRolesPortalUrl(): string {
        const baseUrl = this.getPortalProxyUrl();
        return `${baseUrl}/user-service/v1/user/role`;
    }

    getPortalProxyUrl(): string {
        return this.get<string>('API_PORTAL_PROXY');
    }

    getTranslateUrl(): string {
        return this.get<string>('API_PORTAL_TRANSLATE');
    }

    FilterCheckRestfulAPI(): string {
        return this.get<string>('FILTER_CHECK_URL');
    }

    CardManagementRestfulAPI(): string {
        return this.get<string>('CARD_MANAGEMENT_URL');
    }

    CardHubRestfulAPI(): string {
        return this.get<string>('CARD_HUB_URL');
    }

    getTrackingOrderChoRayAppKey(): string {
        return this.get<string>('CHORAY_TRACKING_ORDER_APP_KEY');
    }

    getTrackingOrderChoRayViewInvoiceUrl(): string {
        return this.get<string>('CHORAY_TRACKING_ORDER_VIEW_INVOICE_URL');
    }

    getTrackingOrderChoRayGetInvoiceUrl(): string {
        return this.get<string>('CHORAY_TRACKING_ORDER_GET_INVOICE_URL');
    }

    getTrackingOrderChoRaySendEmailUrl(): string {
        return this.get<string>('CHORAY_TRACKING_ORDER_SEND_EMAIL_URL');
    }

    getEnv(): string {
        return this.get<string>('ENVIRONMENT');
    }

    getRunOneEnv(): string {
        return this.get<string>('RUN_ONE_ENV') || "OFF";
    }

    getSyncDaLieuLogin(): boolean {
        return this.get<boolean>('SYNC_DALIEU_ENV') || false;
    }

    getSyncDHYDLogin(): boolean {
        return this.get<boolean>('SYNC_DHYD_ENV') || false;
    }

    getSyncNhiDong1Login(): boolean {
        return this.get<boolean>('SYNC_NHIDONG1_ENV') || false;
    }

    getCSKHSendMail(): boolean {
        return this.get<boolean>('CSKH_SEND_MAIL') || false;
    }

    getMultipleBookingsMode(): boolean {
        return this.get<boolean>('MULTIPLE_BOOKINGS') || false;
    }

    getSecretKeyResetUserPhone(): string {
        return this.get<string>('SECRET_KEY_RESET_USER_PHONE') || '';
    }

    getRunUMCSyncBooking(): boolean {
        return this.get<boolean>('RUN_UMC_SYNC_BOOKING') || false;
    }

    getRunPushNotif(): boolean {
        return this.get<boolean>('RUN_PUSH_NOTIF') || false;
    }

    getBaseUrl(): string {
        return this.get<string>('BASE_URL');
    }

    getSwaggerJsonUrl(): string {
        return this.get<string>('API_DOCS_JSON');
    }

    getSwaggerPassword(): string {
        return this.get<string>('SWAGGER_PASSWORD') || '123456';
    }

    getBookingTreeUrl(): string {
        return this.get<string>('BOOKING_TREE_URL');
    }

    getBookingTreeV2Url(): string {
        return this.get<string>('BOOKING_TREE_V2_URL');
    }

    getInvoiceUrl(): string {
        return this.get<string>('INVOICE_URL');
    }

    getPaymentHubUrl(): string {
        return this.get<string>('PAYMENT_HUB_URL');
    }

    getPaymentHubFeeUrl(): string {
        return this.get<string>('PAYMENT_HUB_FEE_URL');
    }

    getPaymentHubPayUrl(): string {
        const baseUrl = this.getPaymentHubUrl();
        const pathPayUrl = this.get<string>('PAYMENT_HUB_PAY_PATH');
        return `${baseUrl}${pathPayUrl}`;
    }

    getPaymentTransactionUrl(): string {
        const baseUrl = this.getPaymentHubUrl();
        const pathPayUrl = this.get<string>('PAYMENT_HUB_TRANSACTION_PATH');
        return `${baseUrl}${pathPayUrl}`;
    }

    getPaymentHubGetFeeUrl(): string {
        const baseUrl = this.getPaymentHubFeeUrl();
        const pathPayUrl = this.get<string>('PAYMENT_HUB_GET_FEE_PATH');
        return `${baseUrl}${pathPayUrl}`;
    }

    getPaymentHubSearchQRCodeFeeUrl(): string {
        const baseUrl = this.getPaymentHubFeeUrl();
        const pathPayUrl = this.get<string>('PAYMENT_HUB_SEARCH_QRCODE_FEE_PATH');
        return `${baseUrl}${pathPayUrl}`;
    }

    getPaymentHubUpdateStatusFeeUrl(): string {
        const baseUrl = this.getPaymentHubFeeUrl();
        const pathPayUrl = this.get<string>('PAYMENT_HUB_UPDATE_STATUS_FEE_PATH');
        return `${baseUrl}${pathPayUrl}`;
    }

    getPartnerUrl(): string {
        return this.get<string>('PARTNER_URL');
    }

    getMomoUrl(): string {
        return this.get<string>('MOMO_END_POINT');
    }

    getSMSCheckPhone(): string {
        return this.get<string>('SMS_CHECK_PHONE');
    }

    getSMSCheckCode(): string {
        return this.get<string>('SMS_CHECK_CODE');
    }

    getSMSCheckPhoneNhiDong1(): string {
        return this.get<string>('SMS_CHECK_PHONE_NHI_DONG_1');
    }

    getSMSCheckCodeNhiDong1(): string {
        return this.get<string>('SMS_CHECK_CODE_NHI_DONG_1');
    }

    getSMSCheckPhoneUMC(): string {
        return this.get<string>('SMS_CHECK_PHONE_UMC');
    }

    getSMSCheckCodeUMC(): string {
        return this.get<string>('SMS_CHECK_CODE_UMC');
    }

    getTelemedUrl(): string {
        return this.get<string>('API_TELEMED_PROXY');
    }

    getTelemedPortalUrl(): string {
        return this.get<string>('API_TELEMED_PORTAL_PROXY');
    }

    // NodeJS Proxy
    getTelemedGatewayAPI(): string {
        return this.get<string>('TELEMED_GATEWAY_API_URL');
    }

    getSwitchEventNotif(): boolean {
        return this.get<boolean>('SWITCH_EVENT_NOTIF');
    }

    getWarningExamResult(): boolean {
        return this.get<boolean>('WARNING_EXAM_RESULT') || false;
    }

    setFullFeature118(): boolean {
        return this.get<boolean>('FULL_FEATURE_118') || false;
    }

    getFeeSecretKey(): string {
        return this.get<string>('FEE_SECRET_KEY');
    }

    getEnvironment(): string {
        return this.get<string>('ENVIRONMENT');
    }

    getIgnoreMessageConfig(): string {
        return this.get<string>('BETA_IGNORE_MESSAGE') || '';
    }

    getAllowTelemedReminder(): boolean {
        return this.get<boolean>('RUN_TELEMED_REMINDER') || false;
    }

    getAPIBoId(): string {
        return this.get<string>('APP_BO_ID');
    }

    getAPIBoKey(): string {
        return this.get<string>('APP_BO_KEY');
    }

    getAppMedproApi(): string {
        return this.get<string>('APP_MEDPRO_API');
    }

    getIsEnableBookingTransform(): boolean {
        return this.get<boolean>('ENABLE_BOOKING_TRANSFORM') || false;
    }

    getCheckVersionMobile(): boolean {
        return this.get<boolean>('CHECK_VERSION_MOBILE') || false;
    }

    getBandWidthDownload(): number {
        return this.get<number>('BAND_WIDTH_DOWNLOAD_FILE') || 512000;
    }

    getEnableOverridePatientCode(): boolean {
        return this.get<boolean>(`ENABLE_OVERRIDE_PATIENT_CODE`) || false;
    }

    getKeyNameV1(): string {
        return this.get<string>('KEY_NAME_V1');
    }

    getClinicClientId(): string {
        return this.get<string>('CLINIC_CLIENT_ID');
    }

    getClinicSecretKey(): string {
        return this.get<string>('CLINIC_SECRET_KEY');
    }

    getGatewayAPIUrl(): string {
        return this.get<string>('GATEWAY_API_URL');
    }

    getBoUrl(): string {
        return this.get<string>('BO_URL');
    }

    getS3Url(): string {
        return this.get<string>('S3_URL');
    }

    getS3SecretKey(): string {
        return this.get<string>('S3_SECRET_KEY');
    }

    getS3Token(): string {
        return this.get<string>('S3_TOKEN');
    }

    getS3Organization(): string {
        return this.get<string>('S3_ORGANIZATION');
    }

    get getUrlAPI119(): string {
        return this.get<string>('URL_API_119');
    }

    get getGoogleAnalyticsUrl(): string {
        return this.get<string>('GOOGLE_ANALYTICS_URL');
    }

    get getGoogleAnalyticsOrganization(): string {
        return this.get<string>('GOOGLE_ANALYTICS_ORGANIZATION');
    }

    get getGoogleAnalyticsToken(): string {
        return this.get<string>('GOOGLE_ANALYTICS_TOKEN');
    }

    get getUrlPushRemindTelemed(): string {
        return this.get<string>('URL_API_REMIND_TELEMED');
    }

    get getUrlPushNotifV2(): string {
        return this.get<string>('API_PUSH_NOTIF');
    }

    get getUrlLongVan(): string {
        return this.get<string>('API_LONG_VAN');
    }

    get getUrlApiSearchService(): string {
        return this.get<string>('API_SEARCH_SERVICE');
    }

    get getProxyApiUrl(): string {
        return this.get<string>('PROXY_API_URL');
    }

    get serviceSubScriptionUrl(): string {
        return this.get<string>('SERVICE_SUBSCRIPTION_API_URL');
    }

    public get cashBackSurveyFormUrl (): string {
        return this.get<string>('CASH_BACK_SURVEY_FORM_URL');
    }

    public get cashBackTermAndServiceLink (): string {
        return this.get<string>('CASH_BACK_TERM_AND_SERVICE_LINK');
    }
}
