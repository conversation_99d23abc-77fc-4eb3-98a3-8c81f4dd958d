import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { Injectable, HttpService, OnModuleInit } from '@nestjs/common';
import { UrlConfigService } from 'src/config/config.url.service';
import * as FormData from 'form-data';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as fs from 'fs';
import { last } from 'lodash';
import { BASE_DIR, FileType, MimeType } from './constant';
import * as Throttle from 'throttle';
import * as path from 'path';

@Injectable()
export class UploadFileService implements OnModuleInit {
    private configBandWidth: number;

    constructor(
        private httpService: HttpService,
        private configUrlService: UrlConfigService,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
    ) { }

    onModuleInit() {
        try {
            this.configBandWidth = this.configUrlService.getBandWidthDownload();
        } catch (error) {
            console.log(error)
        }
    }

    async uploadFile(partnerid: string, files: any) {
        try {
            const { file } = files;
            const formData = new FormData();
            formData.append('file', file[0].buffer, file[0].originalname);
            const api = `${this.configUrlService.FilterCheckRestfulAPI()}/upload-file`;
            const res = await this.httpService.post<any>(api, formData, {
                headers: {
                    ...formData.getHeaders(),
                    partnerid,
                },
            }).toPromise();
            return {
                url: `${this.configUrlService.getBaseUrl()}${res.data.url}`,
            };
        } catch (error) {
            throw error;
        }
    }

    async getFileByBooking(id: string, res): Promise<any> {
        try {
            const { partnerId, insuranceFileUrl } = await this.bookingModel.findOne({ id }).select('partnerId insuranceFileUrl').exec();
            const file = last(insuranceFileUrl.split('/'));
            const stat = fs.statSync(`${BASE_DIR}/${partnerId}/${file}`);
            const result = await fs.createReadStream(`${BASE_DIR}/${partnerId}/${file}`);
            switch (path.extname(file).substr(1)) {
                case FileType.PDF:
                    res.header('Content-Type', MimeType.PDF);
                    break;
                case FileType.JPEG || FileType.JPG:
                    res.header('Content-Type', MimeType.PNG);
                    break;
                case FileType.PNG:
                    res.header('Content-Type', MimeType.PNG);
                    break;
            }
            res.header('Content-Length', stat.size);
            res.header('Content-Disposition', 'attachment; filename=' + file);
            const throttle = new Throttle(Number(this.configBandWidth));
            return result.pipe(throttle).pipe(res);
        } catch (error) {
            throw error;
        }
    }

}
