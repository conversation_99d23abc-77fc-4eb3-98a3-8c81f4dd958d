import * as mongoose from 'mongoose';
import { BOOKING_CANCELLATION_UNIQUE_COLLECTION_NAME } from './constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';

const Schema = mongoose.Schema;

export const BookingCancellationUniqueSchema = new Schema(
    {
        bookingId: { type: String, required: true, unique: true },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME, required: true },
        partnerId: { type: String, required: true },
        appId: { type: String, required: true },
        userId: { type: String, required: true },
        transactionId: { type: String, required: true },
        bookingCode: { type: String },
        cancelledBy: { type: String }, // 'USER', 'CSKH', 'PORTAL'
        cancelledAt: { type: Date, default: Date.now },
        platform: { type: String },
    },
    {
        collection: BOOKING_CANCELLATION_UNIQUE_COLLECTION_NAME,
        timestamps: true,
    },
);

// Index for better performance
BookingCancellationUniqueSchema.index({ bookingId: 1 }, { unique: true });
BookingCancellationUniqueSchema.index({ partnerId: 1 });
BookingCancellationUniqueSchema.index({ appId: 1 });
BookingCancellationUniqueSchema.index({ cancelledAt: 1 });
