import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MedproCareCancelDto {
    @ApiProperty({ description: 'id care247', required: true })
    @IsNotEmpty({ message: 'id is required' })
    id: string;
    
    @ApiProperty({ description: 'li do huy phieu care247', required: true })
    @IsNotEmpty({ message: 'cancelReason is required' })
    cancelReason: string;
}
