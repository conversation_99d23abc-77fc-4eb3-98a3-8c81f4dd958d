import { Module, HttpModule, forwardRef } from '@nestjs/common';
import { HisGatewayController } from './his-gateway.controller';
import { HisGatewayService } from './his-gateway.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    BOOKING_SLOT_COLLECTION_NAME,
    BOOKING_HIS_COLLECTION_NAME,
    CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME,
    BOOKING_NUMBER_COLLECTION_NAME,
} from './schemas/constants';
import { BookingSlotSchema } from './schemas/booking-slot.schema';
import { BookingHISSchema } from './schemas/booking.schema';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { ConstraintsSequenceNumberSchema } from './schemas/constraints-sequence-number.schema';
import { BOOKING_CARE_247, BOOKING_COLLECTION_NAME, EXPIRED_BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { ExpiredBookingSchema } from 'src/booking-gateway/schemas/expired-booking.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { CacheManagerModule } from 'src/cache-manager/cache-manager.module';
import { BookingNumberSchema } from './schemas/booking-numbers.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { BookingCare247Schema } from '../booking-gateway/schemas/booking-care-247.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { BookingSchema } from '../booking-gateway/schemas/booking.schema';
import { BOOKING_CARE_247_CONSTRAINT_USER } from '../cskh/schemas/constants';
import { BookingCare247ConstraintUserSchema } from '../cskh/schemas/booking-care247-constraint-user.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: BOOKING_SLOT_COLLECTION_NAME, schema: BookingSlotSchema },
            { name: BOOKING_HIS_COLLECTION_NAME, schema: BookingHISSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            {
                name: CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME,
                schema: ConstraintsSequenceNumberSchema,
            },
            {
                name: EXPIRED_BOOKING_COLLECTION_NAME,
                schema: ExpiredBookingSchema,
            },
            {
                name: PARTNER_CONFIG_COLLECTION_NAME,
                schema: PartnerConfigSchema,
            },
            { name: BOOKING_NUMBER_COLLECTION_NAME, schema: BookingNumberSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: BOOKING_CARE_247, schema: BookingCare247Schema },
            { name: BOOKING_CARE_247_CONSTRAINT_USER, schema: BookingCare247ConstraintUserSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
        ]),
        PatientMongoModule,
        forwardRef(() => CacheManagerModule),
    ],
    controllers: [HisGatewayController],
    providers: [HisGatewayService, GlobalSettingService],
    exports: [HisGatewayService],
})
export class HisGatewayModule { }
