import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
    EVENT_COLLECTION_NAME, SYNC_BOOKING_PROCESS, SYNC_PATIENT_PROCESS, SYNC_BOOKING_DATE_PROCESS,
    SYNC_SKIN_BOOKING_PROCESS, SYNC_SKIN_PATIENT_PROCESS, SYNC_USER_PROCESS, SYNC_ND1_BOOKING_PROCESS
} from 'src/event/schemas/constants';
import { Model } from 'mongoose';
import { IEvent } from 'src/event/intefaces/event.inteface';
import { GetFeederDTO } from './dto/get-feeder.dto';
import * as moment from 'moment';
import * as uuid from 'uuid';
import { HookNameJsonDTO } from './dto/hook-name-json.dto';
import { validateOrReject } from 'class-validator';
import { CreateEventHookDTO } from 'src/event/dto/create-event-hook.dto';
import { FEEDER_COLLECTION_NAME } from './schemas/constants';
import { IFeeder } from './intefaces/feeder.inteface';
import { EventChannel } from './dto/event-channel.dto';
import { CreateNewBookingV1HookDTO } from 'src/event/dto/create-new-booking-v1-hook.dto';
import { ISyncBookingProcess } from 'src/event/intefaces/sync-booking-process.inteface';
import { SyncProcessStatus } from 'src/event/dto/sync-status.dto';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { ISyncPatientProcess } from 'src/event/intefaces/sync-patient-process.inteface';
import { ISyncBookingDateProcess } from 'src/event/intefaces/sync-booking-date-process.inteface';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ISyncSkinBookingProcess } from 'src/event/intefaces/sync-skin-booking-process.inteface';
import { PartnerOldHospital } from 'src/sync-booking/dto/old-hospital.dto';
import { ISyncSkinPatientProcess } from 'src/event/intefaces/sync-skin-patient-process.inteface';
import { ISyncUserProcess } from 'src/event/intefaces/sync-user-process.inteface';
import { SyncBookingService } from 'src/sync-booking/sync-booking.service';
import { ISyncNd1BookingProcess } from 'src/event/intefaces/sync-nd1-booking-process.inteface';
// import { SyncUserService } from 'src/sync-user/sync-user.service';

@Injectable()
export class EventProccessorService {
    constructor(
        @InjectModel(EVENT_COLLECTION_NAME) private eventModel: Model<IEvent>,
        @InjectModel(FEEDER_COLLECTION_NAME) private feederModel: Model<IFeeder>,
        @InjectModel(SYNC_BOOKING_PROCESS) private syncBookingProcessModel: Model<ISyncBookingProcess>,
        @InjectModel(SYNC_ND1_BOOKING_PROCESS) private syncNd1BookingProcessModel: Model<ISyncNd1BookingProcess>,
        @InjectModel(SYNC_USER_PROCESS) private syncUserProcessModel: Model<ISyncUserProcess>,
        @InjectModel(SYNC_SKIN_BOOKING_PROCESS) private syncSkinBookingProcessModel: Model<ISyncSkinBookingProcess>,
        @InjectModel(SYNC_BOOKING_DATE_PROCESS) private syncBookingDateProcessModel: Model<ISyncBookingDateProcess>,
        @InjectModel(SYNC_PATIENT_PROCESS) private syncPatientProcessModel: Model<ISyncPatientProcess>,
        @InjectModel(SYNC_SKIN_PATIENT_PROCESS) private syncSkinPatientProcessModel: Model<ISyncSkinPatientProcess>,
        private readonly syncBookingService: SyncBookingService,
        // private readonly syncUserService: SyncUserService,
        @InjectSentry() private readonly clientSentry: SentryService,
    ) { }

    async getStartTime(): Promise<any> {
        return moment().toDate();
    }

    async encodeJSON(id: string): Promise<any> {
        const feeder = await this.feederModel.findOne({ id }).exec();
        if (feeder) {
            return JSON.stringify(feeder.toObject());
        }
        throw new HttpException('KHông tìm thấy feeder', HttpStatus.NOT_FOUND);

    }

    async getFeeder(formData: GetFeederDTO): Promise<any> {
        let params: any = {};
        if (formData.startTime) {
            params = {
                createTime: {
                    $gte: moment(formData.startTime).add(7, 'hours').toDate(),
                },
            };
        }

        const events = await this.feederModel.find({
            // topicId: 'deposit.confirm',
            ...params,
            isCompleted: false,
        }).exec();

        return events;
    }

    async hookBookingDateChanged(formData: any): Promise<any> {
        // const data: CreateNewBookingV1HookDTO = JSON.parse(formData.jsonData);
        const { eventId, eventType = '' } = formData;
        if (eventType && eventType === 'BOOKING_DATE_CHANGED') {
            /* tiến hành cập nhật lại sync process collection */
            const getSyncProcess = await this.syncBookingDateProcessModel.findOneAndUpdate({
                processId: eventId,
                syncStatus: SyncProcessStatus.INITIAL,
            }, {
                syncStatus: SyncProcessStatus.PENDING,
            }).exec();
            if (getSyncProcess) {
                this.clientSentry.instance().captureMessage(`Xử lý hook booking date thành công - ${JSON.stringify(formData)}`);
                return {
                    isOk: true,
                };
            } else {
                this.clientSentry.instance().captureException({
                    message: 'Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.',
                    formData,
                });
                throw new HttpException('Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.', HttpStatus.NOT_ACCEPTABLE);
            }

        } else {
            this.clientSentry.instance().captureMessage(`Event type không chính xác. Vui lòng kiểm tra lại. - ${JSON.stringify(formData)}`);
            throw new HttpException('Event type không chính xác. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async hookNewUserV1(formData: any): Promise<any> {
        const { eventId, eventType = '' } = formData;
        if (eventType && eventType === 'NEW_USER_V1') {
            /* tiến hành cập nhật lại sync process collection */
            const params = {
                processId: eventId,
                syncStatus: SyncProcessStatus.INITIAL,
            };
            const getSyncProcess = await this.syncUserProcessModel.findOneAndUpdate({
                ...params,
            }, {
                syncStatus: SyncProcessStatus.PENDING,
            }).exec();

            if (getSyncProcess) {
                this.clientSentry.instance().captureMessage(`Xử lý hook thành công - ${JSON.stringify(formData)}`);
                return {
                    isOk: true,
                };
            } else {
                this.clientSentry.instance().captureException({
                    message: 'Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.',
                    formData,
                });
                throw new HttpException('Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.', HttpStatus.NOT_ACCEPTABLE);
            }

        } else {
            this.clientSentry.instance().captureMessage(`Event type không chính xác. Vui lòng kiểm tra lại. - ${JSON.stringify(formData)}`);
            throw new HttpException('Event type không chính xác. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async hookNewBookingV1(formData: any): Promise<any> {
        // console.log(`data kich hoat hook `, JSON.stringify(formData));
        const { eventId, eventType = '', sourceId = '', eventData: { bookingId = 0 } } = formData;
        if (sourceId && eventType && eventType === 'NEW_BOOKING_V1') {
            /* tiến hành cập nhật lại sync process collection */
            let getSyncProcess: any = null;
            const params = {
                processId: eventId,
                syncStatus: SyncProcessStatus.INITIAL,
            };
            switch (sourceId) {
                case PartnerOldHospital.UMC:
                    getSyncProcess = await this.syncBookingProcessModel.findOneAndUpdate({
                        ...params,
                    }, {
                        syncStatus: SyncProcessStatus.PENDING,
                    }).exec();
                    break;
                case PartnerOldHospital.SKIN:
                    getSyncProcess = await this.syncSkinBookingProcessModel.findOneAndUpdate({
                        ...params,
                    }, {
                        syncStatus: SyncProcessStatus.PENDING,
                    }).exec();
                    break;
                default:
                    throw new HttpException('sourceid không tồn tại. Vui lòng kiểm tra lại thông tin.', HttpStatus.BAD_REQUEST);
            }

            if (getSyncProcess) {
                // this.clientSentry.instance().captureMessage(`Xử lý hook thành công - ${JSON.stringify(formData)}`);
                return {
                    isOk: true,
                };
            } else {
                switch (sourceId) {
                    case PartnerOldHospital.UMC:
                        await this.syncBookingService.syncOneBooking(bookingId);
                        break;
                    case PartnerOldHospital.SKIN:
                        await this.syncBookingService.syncOneSkinBooking(bookingId);
                        break;
                    default:
                        break;
                }

                return {
                    isOk: true,
                };
                // this.clientSentry.instance().captureException({
                //     message: 'Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.',
                //     formData,
                // });
                // throw new HttpException('Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.', HttpStatus.NOT_ACCEPTABLE);
            }

        } else {
            this.clientSentry.instance().captureMessage(`Event type không chính xác. Vui lòng kiểm tra lại. - ${JSON.stringify(formData)}`);
            throw new HttpException('Event type không chính xác. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
        }
    }

    // async hookNewSkinBookingV1(formData: any): Promise<any> {
    //     // const data: CreateNewBookingV1HookDTO = JSON.parse(formData.jsonData);
    //     const { eventId, eventType = '' } = formData;
    //     if (eventType && eventType === 'NEW_SKIN_BOOKING_V1') {
    //         /* tiến hành cập nhật lại sync process collection */
    //         const getSyncProcess = await this.syncSkinBookingProcessModel.findOneAndUpdate({
    //             processId: eventId,
    //             syncStatus: SyncProcessStatus.INITIAL,
    //         }, {
    //             syncStatus: SyncProcessStatus.PENDING,
    //         }).exec();
    //         if (getSyncProcess) {
    //             this.clientSentry.instance().captureMessage(`Xử lý hook thành công - ${JSON.stringify(formData)}`);
    //             return {
    //                 isOk: true,
    //             };
    //         } else {
    //             this.clientSentry.instance().captureException({
    //                 message: 'Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.',
    //                 formData,
    //             });
    //             throw new HttpException('Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.', HttpStatus.NOT_ACCEPTABLE);
    //         }

    //     } else {
    //         this.clientSentry.instance().captureMessage(`Event type không chính xác. Vui lòng kiểm tra lại. - ${JSON.stringify(formData)}`);
    //         throw new HttpException('Event type không chính xác. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
    //     }
    // }

    async hookNewPatientV1(formData: any): Promise<any> {
        const { eventId, eventType = '', sourceId = '', eventData: { patientId = 0 } } = formData;
        if (sourceId && eventType && eventType === 'NEW_PATIENT_V1') {
            /* tiến hành cập nhật lại sync process collection */
            /* tiến hành cập nhật lại sync process collection */
            let getSyncProcess: any = null;
            const params = {
                processId: eventId,
                syncStatus: SyncProcessStatus.INITIAL,
            };
            switch (sourceId) {
                case PartnerOldHospital.UMC:
                    getSyncProcess = await this.syncPatientProcessModel.findOneAndUpdate({
                        ...params,
                    }, {
                        syncStatus: SyncProcessStatus.PENDING,
                    }).exec();
                    break;
                case PartnerOldHospital.SKIN:
                    getSyncProcess = await this.syncSkinPatientProcessModel.findOneAndUpdate({
                        ...params,
                    }, {
                        syncStatus: SyncProcessStatus.PENDING,
                    }).exec();
                    break;
                default:
                    throw new HttpException('sourceid không tồn tại. Vui lòng kiểm tra lại thông tin.', HttpStatus.BAD_REQUEST);
            }

            if (getSyncProcess) {
                // this.clientSentry.instance().captureMessage(`Xử lý hook patient thành công - ${JSON.stringify(formData)}`);
                return {
                    isOk: true,
                };
            } else {
                switch (sourceId) {
                    case PartnerOldHospital.UMC:
                        await this.syncBookingService.syncPatient(patientId);
                        break;
                    // case PartnerOldHospital.SKIN:
                    //     await this.syncBookingService.syncOneSkinBooking(bookingId);
                    //     break;
                    default:
                        break;
                }

                return {
                    isOk: true,
                };
                // this.clientSentry.instance().captureException({
                //     message: 'Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.',
                //     formData,
                // });
                // throw new HttpException('Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.', HttpStatus.NOT_ACCEPTABLE);
            }

        } else {
            this.clientSentry.instance().captureMessage(`Event type không chính xác. Vui lòng kiểm tra lại. - ${JSON.stringify(formData)}`);
            throw new HttpException('Event type không chính xác. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async executeBookingDateChanged(booking: IBooking): Promise<any> {
        /* Lấy thông tin booking */
        const listChannel = [EventChannel.WEB, EventChannel.PUSH_NOTIF_APP];
        const eventInfo = {
            title: `Thông tin phiếu khám. Mã phiếu: ${booking.bookingCode}`,
            topicId: 'BOOKING_DATE_CHANGED',
            partnerId: booking.partnerId,
            appId: booking.appId,
            userId: booking.userId,
            createTime: moment().toDate(),
            eventData: booking.toObject(),
            isNotif: false,
            isPushNotif: false,
            isSendMail: false,
        };

        /* Duyệt từng channel để tạo event */
        const listEvent = listChannel.map(item => {
            // const type = -1;
            const boj: any = {
                type: -1,
            };
            switch (item) {
                case EventChannel.WEB:
                    boj.isNotif = true;
                    break;
                case EventChannel.PUSH_NOTIF_APP:
                    boj.isPushNotif = true;
                    // boj.type = -1;
                    break;
                case EventChannel.MAIL:
                    boj.isSendMail = true;
                    break;
                default:
                    break;
            }
            return {
                id: uuid.v4().replace(/-/g, ''),
                ...eventInfo,
                ...boj,
                channel: item,
            };
        });
        /* tiến hành save */
        const events = await this.eventModel.insertMany(listEvent);
        return events;
    }

    // async hookNewBookingV1(formData: HookNameJsonDTO): Promise<any> {
    //     const data: CreateNewBookingV1HookDTO = JSON.parse(formData.jsonData);
    //     const { eventId, eventType = '' } = data;
    //     if (eventType && eventType === 'NEW_BOOKING_V1') {
    //         /* tiến hành cập nhật lại sync process collection */
    //         const getSyncProcess = await this.syncBookingProcessModel.findOneAndUpdate({
    //             processId: eventId,
    //             syncStatus: SyncProcessStatus.INITIAL,
    //         }, {
    //             syncStatus: SyncProcessStatus.PENDING,
    //         }).exec();
    //         if (getSyncProcess) {
    //             this.clientSentry.instance().captureMessage(`Xử lý hook thành công - ${formData.jsonData}`);
    //             return {
    //                 isOk: true,
    //             };
    //         } else {
    //             this.clientSentry.instance().captureException({
    //                 message: 'Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.',
    //                 data,
    //             });
    //             throw new HttpException('Event này không tồn tại hoặc đã được xử lý rồi. Vui lòng không gửi lại.', HttpStatus.NOT_ACCEPTABLE);
    //         }

    //     } else {
    //         this.clientSentry.instance().captureMessage(`Event type không chính xác. Vui lòng kiểm tra lại. - ${formData.jsonData}`);
    //         throw new HttpException('Event type không chính xác. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
    //     }
    // }

    async hookName(formData: HookNameJsonDTO): Promise<any> {
        const data: CreateEventHookDTO = JSON.parse(formData.jsonData);
        const { id } = data;
        /* kiểm tra xem có tồn tại feeder này hay ko */
        const getFeederInfo = await this.feederModel.findOne({ id }).exec();
        if (!getFeederInfo) {
            throw new HttpException('Không tìm thấy thông tin feeder event.', HttpStatus.NOT_FOUND);
        }
        // if(getFeederInfo.isCompleted){
        //     throw new HttpException('Feeder event này đã được xử lý.')
        // }
        // return data;
        const feeder = new CreateEventHookDTO();
        /* gán thông tin */
        feeder.appId = data.appId;
        feeder.date = data.date;
        feeder.feederData = data.feederData;
        feeder.partnerId = data.partnerId;
        feeder.title = data.title;
        feeder.topicId = data.topicId;
        feeder.content = data?.content || '';
        feeder.userId = data.userId;

        await validateOrReject(feeder)
            .catch(error => {
                throw new HttpException(error, HttpStatus.BAD_REQUEST);
            });
        /* Dựa vào topicId sẽ lưa chọn cái xử lý khác nhau */
        const topicId = feeder.topicId;
        let listChannel = [];
        switch (topicId) {
            /* tiến hành tạo event */
            case 'deposit.confirm': {
                listChannel = [...listChannel, EventChannel.WEB, EventChannel.PUSH_NOTIF_APP, EventChannel.MAIL];
                break;
            }
            case 'deposit.payment.confirm': {
                listChannel = [...listChannel, EventChannel.WEB, EventChannel.PUSH_NOTIF_APP, EventChannel.MAIL];
                break;
            }
            default:
                listChannel = [...listChannel, EventChannel.WEB];
        }

        const eventInfo = {
            ...feeder,
            createTime: feeder.date,
            eventData: feeder.feederData,
            isNotif: false,
            isPushNotif: false,
            isSendMail: false,
        };

        /* Duyệt từng channel để tạo event */
        const listEvent = listChannel.map(item => {
            // const type = -1;
            const boj: any = {
                type: -1,
            };
            switch (item) {
                case EventChannel.WEB:
                    boj.isNotif = true;
                    break;
                case EventChannel.PUSH_NOTIF_APP:
                    boj.isPushNotif = true;
                    // boj.type = -1;
                    break;
                case EventChannel.MAIL:
                    boj.isSendMail = true;
                    // boj.type = -1;
                    break;
                // case EventChannel.PUSH_NOTIF_APP:
                //     boj.isPushNotif = true;
                //     boj.type = -1;
                //     break;
                default:
                    break;
            }
            return {
                id: uuid.v4().replace(/-/g, ''),
                ...eventInfo,
                ...boj,
                channel: item,
            };
        });
        /* tiến hành save */
        const events = await this.eventModel.insertMany(listEvent);
        /* tiến hành cập nhật lại feeder */
        await this.feederModel.findOneAndUpdate({ id }, { isCompleted: true }).exec();
        /* trả về response */
        return {
            message: 'Hệ thống xử lý feeder event thành công.',
        };
    }

}
