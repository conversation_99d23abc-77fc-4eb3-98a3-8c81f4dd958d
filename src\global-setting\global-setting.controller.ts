import { Controller, Get, Post, Body, Headers, Param, Delete, Put, HttpCode, HttpStatus, UseGuards, UseFilters } from '@nestjs/common';
import { GlobalSettingService } from './global-setting.service';
import { CreateGlobalSettingDto } from './dto/create-global-setting.dto';
import { UpdateGlobalSettingDto } from './dto/update-global-setting.dto';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { IGlobalSetting } from './interfaces/global-setting.interface';
import { BoGuard } from 'src/common/guards/bo.guard';
import { NotFoundFilter } from 'src/common/filters/not-found.filter';

@Controller('global-setting')
@ApiTags('Quản lý Global Setting')
@UseFilters(NotFoundFilter)
export class GlobalSettingController {
    constructor(private readonly globalSettingService: GlobalSettingService) {}

    @Post()
    @UseGuards(BoGuard)
    @ApiOperation({ summary: 'Tạo GlobalSetting', description: 'Tạo GlobalSetting' })
    async create(
        @Body() formData: CreateGlobalSettingDto,
        @Headers('appboid') appBoId: string,
        @Headers('appbokey') appBoKey: string,
    ): Promise<IGlobalSetting> {
        return this.globalSettingService.create(formData);
    }

    @Get()
    @ApiOperation({ summary: 'Lấy danh sách GlobalSetting', description: 'Lấy danh sách GlobalSetting' })
    async findAll(): Promise<IGlobalSetting[]> {
        return this.globalSettingService.findAll();
    }

    @Get(':id')
    @ApiOperation({ summary: 'Tìm GlobalSetting theo id', description: 'Tìm GlobalSetting theo id' })
    async findOne(@Param('id') id: string): Promise<IGlobalSetting> {
        return this.globalSettingService.findOne(id);
    }

    @Put()
    @UseGuards(BoGuard)
    @ApiOperation({ summary: 'Tạo GlobalSetting' })
    async update(
        @Body() formData: UpdateGlobalSettingDto,
        @Headers('appboid') appBoId: string,
        @Headers('appbokey') appBoKey: string,
    ): Promise<IGlobalSetting> {
        return this.globalSettingService.update(formData);
    }

    @Delete(':id')
    @UseGuards(BoGuard)
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({ summary: 'Xóa GlobalSetting' })
    async remove(@Param('id') id: string, @Headers('appboid') appBoId: string, @Headers('appbokey') appBoKey: string): Promise<any> {
        return this.globalSettingService.remove(id);
    }

    @Get('find/:repoName')
    @ApiOperation({ summary: 'Tìm GlobalSetting theo repoName', description: 'Tìm GlobalSetting theo repoName' })
    async findByRepoName(@Param('repoName') repoName: string): Promise<IGlobalSetting[]> {
        return this.globalSettingService.findByRepoName(repoName);
    }
}
