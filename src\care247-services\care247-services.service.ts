import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigRecaptchaService } from 'src/config/config.recapcha.service';
import { LARK_NOTIF_CARE247_CONSULTATION_REGISTRATION } from 'src/message-event/constant';
import { ICare247ConsultationRegistrationDto } from './dto/create-care247-consultation-registration.dto';
import { ICare247ConsultationRegistration } from './interfaces/care247-consultation-registration.interface';
import { CARE247_CONSULTATION_REGISTRATIONS } from './schemas/constants';

@Injectable()
export class Care247Service {
    private logger = new Logger(Care247Service.name);

    constructor(
        @InjectModel(CARE247_CONSULTATION_REGISTRATIONS) private care247ConsultationRegistrationModel: Model<ICare247ConsultationRegistration>,
        private eventEmmiter: EventEmitter2,
        private readonly recaptchaConfig: ConfigRecaptchaService,
        private readonly httpService: HttpService,
    ) {}

    async verifyGoogleRecaptcha(formData: { captchaResponse: string }) {
        try {
            const { data } = await this.httpService
                .post(
                    'https://www.google.com/recaptcha/api/siteverify',
                    {},
                    {
                        params: {
                            secret: this.recaptchaConfig.getSecret(),
                            response: formData.captchaResponse,
                        },
                    },
                )
                .toPromise();

            if (data.success !== true) {
                throw new HttpException({ message: 'Lỗi xác thực reCAPTCHA, vui lòng thử lại!', recaptchaError: true }, HttpStatus.BAD_REQUEST);
            }
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }

            console.error('error verifyGoogleRecapcha: ', err);

            throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
        }
    }

    async createConsultationRegistration(dto: ICare247ConsultationRegistrationDto): Promise<any> {
        try {
            const { captchaResponse, ...data } = dto;
            if (captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }

            // Tạo document mới từ dữ liệu DTO
            const newRegistration = new this.care247ConsultationRegistrationModel(data);
            const savedRegistration = await newRegistration.save();

            try {
                this.eventEmmiter.emit(LARK_NOTIF_CARE247_CONSULTATION_REGISTRATION, {
                    fullname: dto.fullname,
                    phone: dto.phone,
                    email: dto?.email,
                });
            } catch (error) {
                this.logger.error(`Lỗi khi phát sự kiện LARK_NOTIF_CARE247_CONSULTATION_REGISTRATION: ${error.message}`, error.stack);
            }

            return savedRegistration;
        } catch (error) {
            // Ghi log lỗi để debug
            this.logger.error(`Lỗi khi đăng ký tư vấn Care247: ${error.message}`);
            return { success: false, message: error.message };
        }
    }
}
