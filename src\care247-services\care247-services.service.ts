import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { UserService } from '../user/user.service';
import { ICare247HospitalDto } from './dto/create-care247-partner.dto';
import { ICare247ServiceDto } from './dto/create-care247-service.dto';
import { IUpdateCare247HospitalDto } from './dto/update-care247-partner.dto';
import { ICare247Hospital } from './interfaces/care247-partner.inteface';
import { ICare247Service } from './interfaces/care247-service.inteface';
import { CARE247_SERVICE_CHANGES, CARE247_SERVICES, CARE247_SERVICES_PARTNER_CONTRAINTS, CARE247_SERVICES_PARTNERS } from './schemas/constants';
import { ICare247ContrainsHospital } from './interfaces/care247-partner-contrains.inteface';
import { ICare247ServiceChanges } from './interfaces/care247-service-changes.inteface';

@Injectable()
export class Care247Service {
    private logger = new Logger(Care247Service.name);

    constructor(
        @InjectModel(CARE247_SERVICES) private care247ServiceModel: Model<ICare247Service>,
        @InjectModel(CARE247_SERVICES_PARTNERS) private care247ServicePartnerModel: Model<ICare247Hospital>,
        @InjectModel(CARE247_SERVICES_PARTNER_CONTRAINTS) private care247ServicePartnerContraintsModel: Model<ICare247ContrainsHospital>,
        @InjectModel(CARE247_SERVICE_CHANGES) private care247ServiceChangesModal: Model<ICare247ServiceChanges>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        private userService: UserService,
    ) {}

    async createCare247Service(userId: string, body: ICare247ServiceDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const newService = new this.care247ServiceModel(body);
            return await newService.save();
        } catch (error) {
            this.logger.error(`Error creating service: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi tạo dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCare247Service(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const services = await this.care247ServiceModel.find().exec();
            return services;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async updateCare247Service(userId: string, id: string, updateData: ICare247ServiceDto) {
        try {
            // Check if the user has the required CS role
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
    
            const dataTracking: any = {
                dataBefore: {},
                dataAfter: {},
                userId,
                userAction: userId
            };
    
            const existingService = await this.care247ServiceModel.findById(id).exec();
            if (!existingService) {
                throw new HttpException('Dịch vụ không tồn tại.', HttpStatus.NOT_FOUND);
            }
            dataTracking.dataBefore = existingService.toObject();
    
            const updatedService = await this.care247ServiceModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
    
            dataTracking.dataAfter = updatedService.toObject();
    
            await this.care247ServiceChangesModal.create(dataTracking)
    
            return updatedService;
        } catch (error) {
            throw new HttpException(error?.message || 'Lỗi cập nhật dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }    

    async getCare247ServiceChanges(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            return await this.care247ServiceChangesModal.find()
                    .populate({ path: 'userAction', select: { fullname: true, username: true } })
                    .sort({ createdAt: -1 })
                    .exec()
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy lịch sử thay đổi dịch vụ đặt thêm.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async deleteCare247Service(userId: string, id: string) {
        if (!(await this.userService.isCs(userId))) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }

        try {
            const deletedService = await this.care247ServiceModel.findByIdAndDelete(id).exec();

            if (!deletedService) {
                throw new HttpException('Dịch vụ không tồn tại.', HttpStatus.NOT_FOUND);
            }

            return {
                message: 'Xóa dịch vụ thành công.',
                id,
            };
        } catch (error) {
            this.logger.error(`Error deleting service: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi xóa dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async getListHospitalCare247(userId: string) {
        try {
            // Kiểm tra quyền
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const hospitals = await this.care247ServicePartnerModel
                .find()
                .populate({
                    path: 'hospital',
                    select: {
                        partnerId: true,
                        phone: true,
                        email: true,
                        name: true,
                        logoPartner: true,
                        circleLogo: true,
                        address: true,
                    },
                })
                .exec();

            return hospitals;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    private checkAndProcessAddonServices(
        addonServices: any[],
        care247Services: any[]
    ): boolean {
        return addonServices.some((service) => {
            const matchedService = care247Services.find(
                (s) => s._id.toString() === service._id
            );
    
            if (!matchedService) {
                throw new HttpException(
                    `Dịch vụ với ID ${service._id} không tồn tại trong hệ thống.`,
                    HttpStatus.BAD_REQUEST
                );
            }
            return (
                service.price !== matchedService.price ||
                service.originalPrice !== matchedService.originalPrice
            );
        });
    }

    async createListHospitalCare247(userId: string, body: ICare247HospitalDto) {
        try {
            // Kiểm tra quyền của user
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { hospital, status, addonServices } = body;

            // Lấy dữ liệu từ bảng care247ServiceModel
            const care247Services = await this.care247ServiceModel.find({}).lean();

            const hasCustomPrice = this.checkAndProcessAddonServices(
                addonServices,
                care247Services
            );

            // Tạo mới bookingCare247Constraint
            const bookingCare247Constraint = new this.care247ServicePartnerContraintsModel({
                hospital: hospital,
            });
            await bookingCare247Constraint.save();

            // Lưu bệnh viện với dịch vụ đã được kiểm tra
            const newHospital = new this.care247ServicePartnerModel({
                hospital,
                status: status || false,
                customPrice:hasCustomPrice,
                addonServices: addonServices,
            });

            const result = await newHospital.save();
            return result;
        } catch (error) {
            if (error.code === 11000) {
                throw new HttpException('Cơ sở y tế đã được cấu hình dịch vụ đặt thêm !', HttpStatus.BAD_REQUEST);
            }
            this.logger.error(error);
            throw new HttpException(error?.message || 'Lỗi tạo danh sách bệnh viện Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async updateCare247HospitalById(
        userId: any,
        id: string,
        body: IUpdateCare247HospitalDto
    ): Promise<any> {
        try {
            // Kiểm tra quyền của user
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            // Kiểm tra bệnh viện có tồn tại không
            const existingHospital = await this.care247ServicePartnerModel.findById(id).exec();
            if (!existingHospital) {
                throw new HttpException(
                    'Bệnh viện Care247 không tồn tại.',
                    HttpStatus.NOT_FOUND
                );
            }
    
            // Lấy dữ liệu từ care247ServiceModel
            const care247Services = await this.care247ServiceModel.find({}).lean();
    
            // Gọi hàm xử lý addonServices
            const  hasCustomPrice  = this.checkAndProcessAddonServices(
                body.addonServices,
                care247Services
            );
    
            // Chuẩn bị dữ liệu cập nhật
            const updateData: Partial<IUpdateCare247HospitalDto> = {
                status: body.status,
                addonServices: body.addonServices,
                customPrice: hasCustomPrice, // Thêm customPrice vào dữ liệu cập nhật
            };
    
            // Cập nhật bệnh viện
            const updatedHospital = await this.care247ServicePartnerModel
                .findByIdAndUpdate(id, updateData, { new: true })
                .populate({
                    path: 'hospital',
                    select: {
                        partnerId: true,
                        phone: true,
                        email: true,
                        name: true,
                        logoPartner: true,
                        circleLogo: true,
                        address: true,
                    },
                })
                .exec();
    
            return updatedHospital;
        } catch (error) {
            this.logger.error(`Error updating hospital: ${error.message}`, error.stack);
            throw new HttpException(
                error?.message || 'Lỗi cập nhật bệnh viện Care247.',
                HttpStatus.BAD_REQUEST
            );
        }
    }
    

    async getPartnerCare247ById(userId: string, id: string): Promise<any> {
        try {
            // Kiểm tra quyền
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            // Tìm bệnh viện theo ID
            const hospital = await this.care247ServicePartnerModel
                .findById(id)
                .populate({
                    path: 'hospital',
                    select: {
                        partnerId: true,
                        phone: true,
                        email: true,
                        name: true,
                        logoPartner: true,
                        circleLogo: true,
                        address: true,
                    },
                })
                .exec();

            return hospital;
        } catch (error) {
            this.logger.error(`Error retrieving hospital by ID: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy thông tin bệnh viện Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async deleteCare247HospitalById(userId: string, id: string): Promise<any> {
        try {
            // Kiểm tra quyền
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            // Kiểm tra sự tồn tại của bệnh viện
            const existingHospital = await this.care247ServicePartnerModel.findById(id).exec();
            if (!existingHospital) {
                throw new HttpException('Bệnh viện không tồn tại trong hệ thống. Vui lòng kiểm tra lại mã bệnh viện.', HttpStatus.NOT_FOUND);
            }

            // Xóa ràng buộc trong care247ServicePartnerContraintsModel
            const constraintResult = await this.care247ServicePartnerContraintsModel.deleteOne({ hospital: existingHospital.hospital }).exec();

            if (constraintResult.deletedCount === 0) {
                this.logger.warn(`Constraint for hospital ${existingHospital.hospital} not found or already deleted.`);
            }

            // Xóa bệnh viện trong care247ServicePartnerModel
            const deletedHospital = await this.care247ServicePartnerModel.findByIdAndDelete(id).exec();

            if (deletedHospital) {
                return {
                    message: 'Xóa bệnh viện Care247 và ràng buộc thành công.',
                    isOk: true,
                };
            } else {
                throw new HttpException(
                    'Đã xảy ra lỗi trong quá trình xóa bệnh viện. Vui lòng liên hệ bộ phận hỗ trợ.',
                    HttpStatus.INTERNAL_SERVER_ERROR,
                );
            }
        } catch (error) {
            this.logger.error(`Error deleting hospital: ${error.message}`, error.stack);
            throw new HttpException(
                error?.message || 'Đã xảy ra lỗi trong quá trình xóa bệnh viện. Vui lòng liên hệ bộ phận hỗ trợ.',
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async updateTimeBeforeBooking(id: string, timeBeforeBookingCare247: number): Promise<any> {
        try {
            // Kiểm tra bệnh viện có tồn tại
            const existingHospital = await this.care247ServicePartnerModel.findById(id).exec();
            if (!existingHospital) {
                throw new HttpException(
                    'Bệnh viện Care247 không tồn tại.',
                    HttpStatus.NOT_FOUND
                );
            }
    
            // Cập nhật trường timeBeforeBookingCare247
            const updatedHospital = await this.care247ServicePartnerModel
                .findByIdAndUpdate(
                    id,
                    { timeBeforeBookingCare247 },
                    { new: true }
                )
                .exec();
    
            return updatedHospital;
        } catch (error) {
            this.logger.error(`Error updating timeBeforeBookingCare247: ${error.message}`, error.stack);
            throw new HttpException(
                error?.message || 'Lỗi cập nhật thời gian trước khi đặt.',
                HttpStatus.BAD_REQUEST
            );
        }
    }
    
}
