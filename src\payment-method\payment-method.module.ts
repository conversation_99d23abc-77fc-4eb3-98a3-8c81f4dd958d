import { GlobalSettingSchema } from './../global-setting/schemas/global-setting.schema';
import { BOOKING_CARE_247, BOOKING_COLLECTION_NAME, BOOKING_ORDER_COLLECTION_NAME, CONSTRAINTS_DATE_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { HttpModule, Module } from '@nestjs/common';
import { PaymentMethodController } from './payment-method.controller';
import { PaymentMethodService } from './payment-method.service';
import { MongooseModule } from '@nestjs/mongoose';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ConstraintsDateSchema } from 'src/booking-gateway/schemas/constraints-date.shema';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { UserSchema } from '../user/schemas/user.schema';
import { USER_COLLECTION_NAME, VISA_USER_COLLECTION_NAME } from '../user/schemas/constants';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { GlobalSettingModule } from '../global-setting/global-setting.module';
import { JwtModule } from '@nestjs/jwt';
import { JwtUserConfigService } from '../config/config.user.jwt.service';
import { HisGatewayModule } from '../his-gateway/his-gateway.module';
import { BookingCare247Schema } from '../booking-gateway/schemas/booking-care-247.schema';
import { VisaUserSchema } from '../user/schemas/visa-users.schema';
import { COOPERATION_PACKAGES } from '../cooperation/schemas/constants';
import { CooperationPackagesSchema } from '../cooperation/schemas/cooperation-packages.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
            { name: CONSTRAINTS_DATE_COLLECTION_NAME, schema: ConstraintsDateSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: BOOKING_CARE_247, schema: BookingCare247Schema },
            { name: VISA_USER_COLLECTION_NAME, schema: VisaUserSchema },
            { name: COOPERATION_PACKAGES, schema: CooperationPackagesSchema },
        ]),
        PatientMongoModule,
        GlobalSettingModule,
        HisGatewayModule,
        JwtModule.registerAsync({
            useExisting: JwtUserConfigService,
        }),
    ],
    controllers: [PaymentMethodController],
    providers: [PaymentMethodService],
    exports: [PaymentMethodService],
})
export class PaymentMethodModule {}
