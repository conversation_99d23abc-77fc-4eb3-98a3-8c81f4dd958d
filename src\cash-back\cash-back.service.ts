import * as moment from "moment"
import * as queryString from 'query-string';
import { HttpService, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { StorageUserBankingInfoOnSubmitDto } from './dto/storage-user-banking-info-on-submit.dto';
import { CASH_BACK_FORM_COLLECTION_NAME } from './constants';
import { ICashBackForm } from './interfaces/cash-back-form.interface';
import { UrlConfigService } from 'src/config/config.url.service';
import * as FormData from 'form-data';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { PushNotifService } from "src/push-notif/push-notif.service";
import { BookingRelation } from "./interfaces/booking-relation.interface";
import { GlobalSettingService } from "src/global-setting/global-setting.service";

// export interface CashBackService {
//     sendCashBackNotificationOnSuccessfulBooking(): Promise<{
//         isOK: boolean,
//         message: string,
//     }[]>;
//     pushCashBack(): Promise<any>,
//     storageUserBankingInfoOnSubmit(payload: StorageUserBankingInfoOnSubmitDto & { invoiceImages: string[] }): Promise<{ isOk: boolean ,message: string }>;
//     storageFileToS3(file: Express.Multer.File): Promise<{ fileUrl: string }>;
// }

@Injectable()
export class CashBackService {
    private readonly repoName: string
    private readonly serviceName: string;
    private readonly logger: Logger;

    constructor(
        @InjectModel(CASH_BACK_FORM_COLLECTION_NAME)
        private readonly cashBackFormModel: Model<ICashBackForm>,
        @InjectModel(USER_COLLECTION_NAME)
        private readonly userModel: Model<IUser>,
        @InjectModel(BOOKING_COLLECTION_NAME)
        private readonly bookingModel: Model<IBooking>,
        private readonly configRepoService: ConfigRepoService,
        private readonly urlConfigService: UrlConfigService,
        private readonly http: HttpService,
        private readonly pushNotifService: PushNotifService,
        private globalSettingService: GlobalSettingService,
    ){
        this.repoName = this.configRepoService.getRepoName();
        this.serviceName = this.constructor.name;
        this.logger = new Logger(CashBackService.name);
    }

    private async callApiSendNotificationToLarkGroup(payload: {
        medproId: string
        hoso: string
        thoigian: string
        benhvien: string
        maphieu: string
        trangthai: string,
        danhgiacsyt: string
        danhgiamedpro: string
        tennganhang: string
        sotaikhoan: string
        tentaikhoan: string
        tongtien: string
    }) {
        let response: { isOk: boolean, message: string };
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/notification/cash-back-survey`

        try {   
            const { data } = await this.http.post<{ isOK: boolean }>(url, payload).toPromise();
            response = {
                isOk: data.isOK,
                message: data.isOK ? 'Gửi thông tin thanh toán của người dùng đến lark group thành công' : 'Gửi thông tin thanh toán của người dùng đến lark group thất bại'
            }
        } catch (error) {
            response = {
                isOk: false,
                message: 'Gửi thông tin thanh toán của người dùng đến lark group thất bại'
            }       
            this.logger.error(
                `Có lỗi trong quá trình gửi thông tin thanh toán của người dùng đến lark group`,
                `${this.repoName} - ${this.serviceName} - ${this.callApiSendNotificationToLarkGroup.name} - ${error.message}`,
            )
        }
        return response;
    }

    private returnBookingStatus(status: number) {
        let _status: string;
        
        switch (status) {
            case -2:
                _status = 'Đã hủy';
                break;
            case 0:
                _status = 'Chưa thanh toán';
                break;
            case 1:
                _status = 'Đã thanh toán';
                break;
            case 6:
                _status = 'Thanh toán hộ';
                break;
            case 2:
                _status = 'Đã khám';
                break;
        }

        return _status;
    }

    // ?hide_bookingId=1&hide_partnerId=1&hide_userId=1&prefill_bookingId=T202412345679&prefill_partnerId=umc&prefill_userId=**********
    private async sendNotificationToLarkGroup({
        bookingId,
        formData
    }: {
        bookingId: string,
        formData: {
            danhgiacsyt: string
            danhgiamedpro: string
            tennganhang: string
            sotaikhoan: string
            tentaikhoan: string
            tongtien: string
        }
    }) {
        const booking = await this.bookingModel.findOne({ bookingCode: bookingId }).populate("partner").populate('patient').exec();
        const user = await this.userModel.findOne({ _id: booking.userId }).exec();
        const patient = booking.patient as unknown as IPatient;
        const partner = booking.partner as unknown as IHospital;

        const { isOk, message } = await this.callApiSendNotificationToLarkGroup({
            medproId: `${user.username} - ${user.fullname}`,
            hoso: `${patient.mobile} (${patient.surname} ${patient.name})`,
            thoigian: moment.utc().add(7,"hour").format('YYYY-MM-DD HH:mm:ss'),
            benhvien: partner.name,
            maphieu: booking.bookingCode,
            trangthai: this.returnBookingStatus(booking?.status),
            ...formData
        });

        if(!isOk){
            throw new InternalServerErrorException(message);
        }

        return {
            isOk,
            message,
        };
    }

    private get s3PartnerUploadUrl() {
        const url = this.urlConfigService.getS3Url() || "https://s3-testing.medpro.com.vn";
        return `${url}/storage/upload/partner`;
    }

    private get getS3HeaderConfig() {
        return {
            token: this.urlConfigService.getS3Token() || "pkh@123",
            organization: this.urlConfigService.getS3Organization() || "medpro-admin",
            options: 'public',
        };
    }

    public async storageFileToS3(file: Express.Multer.File) {
        let uploadToS3Response: AxiosResponse<{ fileUrl: string }>
        
        const formData = new FormData();
        formData.append('file', file?.buffer, file?.originalname);

        try {
            uploadToS3Response = await this.http.post<{ fileUrl: string }>(
                this.s3PartnerUploadUrl, 
                formData, 
                {
                    headers: {
                    ...formData.getHeaders(),
                    ...this.getS3HeaderConfig,
                    },
                })
            .toPromise();
        } catch (error) {
            this.logger.error(
                `Có lỗi trong quá trình upload file lên S3 khi người dùng submit form hoàn tiền`,
                `${this.repoName} - ${this.serviceName} - ${this.storageFileToS3.name} - ${error.message}`,
            )
            throw new InternalServerErrorException(
                `S3 xảy ra lỗi : ${error?.response.message || error?.message}! Xin thử lại sau!`,
            );
        }

        if (!uploadToS3Response.data.fileUrl) {
            throw new InternalServerErrorException('Upload file không hành công!')
        }

        return {
            fileUrl: uploadToS3Response.data.fileUrl,
        };
    }

    private async ensureBankingUserInformationUnique(whereClause: {
        userId: string,
        bookingId: string,
        partnerId: string
    }) {
        const existUser = await this.cashBackFormModel.findOne(whereClause).exec();

        if(existUser) {
            throw new Error('Thông tin chuyển khoản của người dùng đã tồn tại trong hệ thống');
        }  
    }

    public async storageUserBankingInfoOnSubmit(payload: StorageUserBankingInfoOnSubmitDto & { invoiceImages: string[] }): Promise<{ isOk: boolean, message: string }> {
        const {
            ratingHospital,
            ratingMedpro,
            bankName,
            bankAccountNumber,
            bankAccountName,
            totalAmount,
            invoiceImages,
            prefill_partnerId,
            prefill_userId,
            prefill_bookingId,
        } = payload;

        let response: {
            isOk: boolean,
            confirmMessage?: string,
            message: string,
        } = {
            isOk: true,
            message: ""
        }

        try {
            await this.cashBackFormModel.create({
                ratingHospital: ratingHospital,
                ratingMedpro: ratingMedpro,
                bankName: bankName,
                bankAccountNumber: bankAccountNumber,
                bankAccountName: bankAccountName,
                totalAmount: totalAmount,
                invoiceImages: invoiceImages,
                partnerId: prefill_partnerId,
                userId: prefill_userId,
                bookingId: prefill_bookingId,
            });
           
            response = {
                isOk: true,
                confirmMessage: `Cảm ơn bạn đã dành thời gian hoàn thành khảo sát và tham gia chương trình hoàn tiền. Ý kiến của bạn rất quan trọng để Medpro chăm sóc bạn tốt hơn. Hãy đăng nhập Medpro trên thiết bị di động thường xuyên để không bỏ lỡ thông báo cùng các Chương trình ưu đãi khác của Medpro nhé!`,
                message: "Lưu thông tin chuyển khoản của người dùng xuống db thành công",
            }
        } catch (error) {
            // ? Xử lý trường hợp duplicate thông tin chuyển khoản của người dùng
            if(error.code === 11000) {
                response = {
                    isOk: false,
                    message: `Thông tin chuyển khoản của người dùng đã tồn tại trong hệ thống`,
                }
            }

            this.logger.error(
                `Có lỗi trong quá trình lưu thông tin chuyển khoản người dùng xuống db khi người dùng submit form hoàn tiền`,
                `${this.repoName} - ${this.serviceName} - ${this.storageUserBankingInfoOnSubmit.name} - ${error.message}`,
            );
            response = {
                isOk: false,
                message: `${this.repoName} - ${this.serviceName} - ${this.storageUserBankingInfoOnSubmit.name} - ${error.message}`,
            }
        }
        
        if(response.isOk) {
            const { isOk, message } = await this.sendNotificationToLarkGroup({
                bookingId: prefill_bookingId,
                formData: {
                    danhgiacsyt: ratingHospital,
                    danhgiamedpro: ratingMedpro,
                    tennganhang: bankName,
                    sotaikhoan: bankAccountNumber,
                    tentaikhoan: bankAccountName,
                    tongtien: totalAmount,
                }
            });
           
            if(!isOk) {
                response = {
                    isOk: false,
                    message: message,
                }
            }
        }
     
        return response
    }

    private get cashBackSurveyFormUrl() {
        return this.urlConfigService.cashBackSurveyFormUrl || "https://testing.medpro.com.vn/khao-sat-hoan-tien"
    }

    private get cashBackTermAndServiceLink() {
        return this.urlConfigService.cashBackTermAndServiceLink || "https://pkh-medpro.larksuite.com/wiki/UXjlwGQDLiBCbMkcpLQuf4zBs5b"
    }
    
    private generateNotificationMessage (booking: BookingRelation) {
        const partner = booking.partner as unknown as IHospital;
        const partnerName = partner.name;
        const partnerId = partner.partnerId;
        const bookingCode = booking.bookingCode;
        const userPhoneOrUserName = booking.user.username;
        
        const linkCashBackForm = queryString.stringifyUrl({
            url: this.cashBackSurveyFormUrl,
            query: {
                hide_bookingId: 1,
                hide_partnerId: 1,
                hide_userId: 1,
                prefill_bookingId: bookingCode,
                prefill_partnerId: partnerId,
                prefill_userId: userPhoneOrUserName,
            },
        });

        return {
            linkCashBackForm: linkCashBackForm,
            title: `📢 Thông báo: Nhận ngay khuyến mãi hoàn tiền lên đến 5% từ Medpro`,
            message: `Chúc mừng ${booking.patient.surname} ${booking.patient.name} đã sử dụng thành công dịch vụ tại ${partnerName} \nĐể nhận hoàn tiền, bạn vui lòng hoàn tất các bước sau trong vòng 07 ngày kể từ ngày nhận Thông báo này: \n1️⃣ Điền phiếu khảo sát đánh giá dịch vụ \n2️⃣ Cung cấp số tài khoản ngân hàng \n3️⃣ Tải lên hóa đơn đã sử dụng dịch vụ \n👉 Nhấn vào đây để bắt đầu ${linkCashBackForm} \nXem chi tiết các điều kiện và điều khoản về chương trình ${this.cashBackTermAndServiceLink} \n📞 Mọi thắc mắc vui lòng liên hệ tổng đài 1900 2115 để được hỗ trợ. \nCảm ơn bạn đã luôn đồng hành cùng Medpro.`
        }
    }

    private async callApiPushZnsNotification(payload: {
        tenbn: string
        tenbv: string
        maphieu: string
        gioitinh: "Anh" | "Chị"
        userphone: string
    }) {
        let response: { isOK: boolean, message: string };
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/zns/cash-back/failed`
       
        try {   
            const { data } = await this.http.post<{ isOK: boolean }>(url, payload).toPromise();
            response = {
                isOK: data.isOK,
                message: data.isOK ? 'Gửi zns thông báo hoàn tiền cho phiếu khám thành công' : 'Gửi zns thông báo hoàn tiền cho phiếu khám thất bại'
            }
        } catch (error) {
            response = {
                isOK: false,
                message: 'Gửi zns thông báo hoàn tiền cho phiếu khám thất bại'
            }       
            this.logger.error(
                'Có lỗi trong quá trình gửi zns thông báo hoàn tiền cho phiếu khám',
                `${this.repoName} - ${this.serviceName} - ${this.callApiPushZnsNotification.name} - ${error.message}`,
            )
        }
        return response;
    }

    private async callApiPushNotifToUserMessageEvent({
        idBooking,
        bookingCode,
        url,
        titleMessage,
        content
    }: {
        idBooking: string
        bookingCode: string,
        url: string,
        titleMessage: string,
        content: string
    }) {
        try {
            let data = JSON.stringify({
                idBooking,
                bookingCode,
                url,
                titleMessage,
                content
            });
    
            let config: AxiosRequestConfig = {
                method: 'post',
                url: `${this.urlConfigService.getUrlPushNotifV2}/message-event/push-cashback-notification`,
                headers: {
                    'Content-Type': 'application/json',
                },
                data: data,
            };
    
            const response = await this.http
                .request(config).toPromise();

            
            this.logger.log(
                'Gửi thông báo hoàn tiền cho phiếu khám thành công' + JSON.stringify(response.data),
                `${this.repoName} - ${this.serviceName} - ${this.callApiPushNotifToUser.name}`
            )

            return {
                isOK: true,
                message: 'Gửi thông báo hoàn tiền cho phiếu khám thành công'
            }
        } catch (error) {
            this.logger.error(
                'Có lỗi trong quá trình gửi thông báo hoàn tiền cho phiếu khám',
                `${this.repoName} - ${this.serviceName} - ${this.callApiPushNotifToUser.name} - ${error.message}`,
            )
            return {
                isOK: false,
                message: 'Gửi thông báo hoàn tiền cho phiếu khám thất bại'
            }
        }
    }

    private async callApiPushNotifToUser({
        bookingCode,
        url,
        titleMessage,
        content
    }: {
        bookingCode: string,
        url: string,
        titleMessage: string,
        content: string
    }) {
        try {
            let data = JSON.stringify({
                bookingCode,
                url,
                titleMessage,
                content
            });
    
            let config: AxiosRequestConfig = {
                method: 'post',
                url: `${this.urlConfigService.getUrlPushNotifV2}/message-event/push-notification`,
                headers: {
                    'Content-Type': 'application/json',
                },
                data: data,
            };
    
            const response = await this.http
                .request(config).toPromise();

            
            this.logger.log(
                'Gửi thông báo hoàn tiền cho phiếu khám thành công' + JSON.stringify(response.data),
                `${this.repoName} - ${this.serviceName} - ${this.callApiPushNotifToUser.name}`
            )

            return {
                isOK: true,
                message: 'Gửi thông báo hoàn tiền cho phiếu khám thành công'
            }
        } catch (error) {
            this.logger.error(
                'Có lỗi trong quá trình gửi thông báo hoàn tiền cho phiếu khám',
                `${this.repoName} - ${this.serviceName} - ${this.callApiPushNotifToUser.name} - ${error.message}`,
            )
            return {
                isOK: false,
                message: 'Gửi thông báo hoàn tiền cho phiếu khám thất bại'
            }
        }
    }

    

    private async sendCashbackNotification(booking: BookingRelation) : Promise<any> { // ? Gửi thông báo cho khách hàng kèm form hoàn tiền
        let response: {
            isOK: boolean,
            message: string,
        }
        // ? Gửi thông báo cho khách hàng trên app
        try {
            const { title, message, linkCashBackForm } = this.generateNotificationMessage(booking);
           
            await this.callApiPushNotifToUserMessageEvent({
                idBooking: booking._id,
                bookingCode: booking.bookingCode,
                url: linkCashBackForm,
                titleMessage: title,
                content: message
            });

            response = {
                isOK: true,
                message: `Đã gửi thông báo hoàn tiền cho phiếu khám:>>> _id:${booking._id}`
            }
        } catch (error) {   
            response = {
                isOK: false,
                message: `Gửi thông báo hoàn tiền cho phiếu khám:>>> _id:${booking._id} thất bại -> Tiến hành gửi thông báo qua ZNS`
            } 
            
        }

        return response;
    }

    private async sendMobileNotification(booking: BookingRelation) : Promise<any> { // ? Gửi thông báo cho khách hàng kèm form hoàn tiền
        let response: {
            isOK: boolean,
            message: string,
        }
        // ? Gửi thông báo cho khách hàng trên app
        try {
            const { title, message, linkCashBackForm } = this.generateNotificationMessage(booking);
           
            await this.callApiPushNotifToUser({
                bookingCode: booking.bookingCode,
                url: linkCashBackForm,
                titleMessage: title,
                content: message
            });

            response = {
                isOK: true,
                message: `Đã gửi thông báo hoàn tiền cho phiếu khám:>>> _id:${booking._id}`
            }
        } catch (error) {   
            response = {
                isOK: false,
                message: `Gửi thông báo hoàn tiền cho phiếu khám:>>> _id:${booking._id} thất bại -> Tiến hành gửi thông báo qua ZNS`
            } 
            
        }
 
        // ? Kiểm tra cấu hình gửi thông báo qua ZNS có được bật không
        const shouldSendNotificationToZns = this.urlConfigService.isImplCashBackZn
        if(shouldSendNotificationToZns) {
            // ? Nếu gửi thông báo qua app thất bại thì gửi thông báo qua ZNS
            if(!response.isOK) {
                const { isOK, message } = await this.callApiPushZnsNotification({
                    tenbn: `${booking.patient.surname} ${booking.patient.name}`,
                    tenbv: booking.partner.name,
                    maphieu: booking.bookingCode,
                    userphone: booking.user.username,
                    gioitinh: booking.patient.sex === 1 ? "Anh" : "Chị" 
                });
                
                if(!isOK) {
                    response = {
                        isOK: false,
                        message: message,
                    }
                }
            }
        }else {
            this.logger.log(
                'Không gửi thông báo qua ZNS - Cấu hình biến IS_IMPL_CASH_BACK_ZNS trong ENV>>> 1 - enable, 0 - disable', 
                `${this.repoName} - ${this.serviceName} - ${this.sendMobileNotification.name}`
            );
        }
            
        return response
    }
        
    private get yesterdayBookingWhereClause () { // ? Điều kiện lọc các phiếu đã khám trong ngày hôm qua (từ 00:00:00 đến 23:59:59) 
        const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DDTHH:mm:ss.SSSZ'); // ? Format date in collection bookings mongodb
        
        const fromDate = moment(yesterday).startOf('day').set({ // ? Set time to 00:00:00 
            hour: 0,
            minute: 0,
            second: 0,
            millisecond: 0,
        });

        const toDate = moment(yesterday).endOf('day').set({ // ? Set time to 23:59:59
            hour: 23,
            minute: 59,
            second: 59,
            millisecond: 999,
        });

        return {
            status: [1, 2], // ? 1: Chưa khám, 2: Đã khám,
            isCashBack: true,
            date: {
                $gte: fromDate.toDate(),
                $lte: toDate.toDate(),
            },
        }
    }

    private async getBookingIncludePartnerAndUser (): Promise<BookingRelation[]> {
        const bookings = await this.bookingModel
                        .find(this.yesterdayBookingWhereClause)
                        .populate({ 
                            path: 'partner', 
                            select: {
                                name: 1,
                                partnerId: 1,
                            }
                        })
                        .populate({
                            path: 'patient',
                            select: {
                                name: 1,
                                surname: 1,
                                sex: 1
                            }
                        })
                        .exec();

        const bookingWithUser: BookingRelation[] = await Promise.all(
            bookings.map(async booking => {
                const user = await this.userModel.findById(booking.userId).exec();
                
                return {
                    ...booking.toObject(),
                    user
                }
            })
        )

        return bookingWithUser
    }


    public async pushCashBack(): Promise<any> {

        // const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DDTHH:mm:ss.SSSZ'); // ? Format date in collection bookings mongodb

        const fromDate = moment('2024-10-07').startOf('day').set({ // ? Set time to 00:00:00 
            hour: 0,
            minute: 0,
            second: 0,
            millisecond: 0,
        });

        const toDate = moment('2024-10-14').endOf('day').set({ // ? Set time to 23:59:59
            hour: 23,
            minute: 59,
            second: 59,
            millisecond: 999,
        });

        const bookings = await this.bookingModel
            .find({
                status: [1, 2], // ? 1: Chưa khám, 2: Đã khám,
                isCashBack: true,
                date: {
                    $gte: fromDate.toDate(),
                    $lte: toDate.toDate(),
                },
            })
            .populate({
                path: 'partner',
                select: {
                    name: 1,
                    partnerId: 1,
                }
            })
            .populate({
                path: 'patient',
                select: {
                    name: 1,
                    surname: 1,
                    sex: 1
                }
            })
            .exec();

        if (bookings.length === 0)
            return false;
        // xử lý tiếp
        for await (const booking of bookings) {
            const user = await this.userModel.findById(booking.userId).exec();
            const itemNotif = {
                ...booking.toObject(),
                user
            }
            await this.sendCashbackNotification(itemNotif);
        }

    }

    public async sendCashBackNotificationOnSuccessfulBooking() {
        const isON = await this.globalSettingService.findByKeyAndRepoName("ENABLE_CASH_BACK_PUSH_NOTI");

        if(isON === "OFF"){
            this.logger.log('Gửi thông báo hoàn tiền đã bị tắt', `${this.repoName} - ${this.serviceName} - ${this.sendCashBackNotificationOnSuccessfulBooking.name}`);
            return;
        }

        const yesterdayBooking = await this.getBookingIncludePartnerAndUser()

        if(!yesterdayBooking || yesterdayBooking.length === 0) {
            this.logger.log('Không có phiếu khám hoàn tiền nào trong ngày hôm qua', `${this.repoName} - ${this.serviceName} - ${this.sendCashBackNotificationOnSuccessfulBooking.name}`);
            return;
        }
        
        return Promise.all(yesterdayBooking.map(async booking => await this.sendMobileNotification(booking)))
    }   
}