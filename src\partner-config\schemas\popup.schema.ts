import * as mongoose from 'mongoose';
import { POPUP_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PopupSchema = new Schema(
    {
        appId: { type: String },
        status: { type: Number, default: 0 },
        startDate: { type: Date },
        endDate: { type: Date },
        dataType: String,
        type: String,
        url: String,
        platforms: {
            _id: false,
            required: true,
            type: [mongoose.SchemaTypes.Mixed],
            default: [],
        },
    },
    {
        collection: POPUP_COLLECTION_NAME,
        timestamps: true,
    },
);
