import { Document } from 'mongoose';

export interface IPatientInfo extends Document {
    // Thông tin cơ bản
    patientName: string;
    birthDate: Date; // Thay đổi từ dateOfBirth thành birthDate
    gender: 0 | 1; // 0: Nữ, 1: Nam
    parentName?: string;

    // Địa chỉ
    address: string;
    ward_id: string;
    district_id: string;
    city_id: string;
    ward?: any; // ObjectId reference
    district?: any; // ObjectId reference
    city?: any; // ObjectId reference
    phone: string; // Thay đổi từ phoneNumber thành phone

    // Lý do khám
    reasonForVisit: string;

    // Checkbox options
    isXRay: boolean; // Thay đổi từ isXetNghiem thành isXRay
    isHospitalized: boolean; // Thay đổi từ isNhapVien thành isHospitalized

    // Dấu hiệu sinh tồn
    vitalSigns: {
        birthSign: Date; // Thay đổi từ vitalSignsDate thành birthSign
        weight: string; // Thay đổi thành string
        height: string; // Thay đổi thành string
        temperature: string; // Thay đổi thành string
        pulse: string; // Thay đổi thành string
        spO2: string; // Thay đổi thành string
        bloodPressure: string; // Huyết áp (mmHg) - format: "120/80"
    };

    // QR Code information
    qrCodeData?: string; // Encoded patient ID for QR code
    qrCodeImage?: string; // Base64 encoded QR code image



    // Queue information from API
    queueId?: string;
    dateId?: string;
    sequenceNumber?: number;
    queueStatus?: string;
    timeSlotId?: string;
    timeShiftId?: string;
    queueDate?: number;
    blockDate?: number;
    dateInString?: string;
    blockDateInString?: string;
}
