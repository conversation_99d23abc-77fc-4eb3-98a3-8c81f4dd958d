import { IsOptional, IsString, IsN<PERSON>ber, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';

export class AnKhangTransactionsQueryDto {
    @IsOptional()
    @IsString()
    referralCode?: string;

    @IsOptional()
    @IsString()
    partnerId?: string;

    @IsOptional()
    @IsString()
    searchText?: string;

    @IsOptional()
    @IsDateString()
    fromDate?: string;

    @IsOptional()
    @IsDateString()
    toDate?: string;

    @IsOptional()
    @IsNumber()
    pageSize?: number = 20;

    @IsOptional()
    @IsNumber()
    pageIndex?: number = 0;

    @IsOptional()
    @IsDateString()
    fromCreate?: string;

    @IsOptional()
    @IsDateString()
    toCreate?: string;
}
