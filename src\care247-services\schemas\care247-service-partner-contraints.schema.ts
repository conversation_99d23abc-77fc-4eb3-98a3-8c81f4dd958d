import * as mongoose from 'mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { Care247ServicesSchema } from './care247-services.schema';
import { CARE247_SERVICES_PARTNER_CONTRAINTS } from './constants';

const Schema = mongoose.Schema;

export const Care247ServicesPartnerContraintSchema = new Schema(
    {
        hospital: { type: String, required: true, unique: true }
    },
    {
        collection: CARE247_SERVICES_PARTNER_CONTRAINTS,
        timestamps: true,
    },
);
