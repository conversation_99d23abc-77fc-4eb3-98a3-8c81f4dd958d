import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MedproCareInstructorDto {
    @ApiProperty({ description: 'id care247', required: true })
    @IsNotEmpty({ message: 'id is required' })
    id: string;
    
    @ApiProperty({ description: 'id user cs', required: true })
    @IsNotEmpty({ message: 'instructorId is required' })
    instructorId: string;
}
