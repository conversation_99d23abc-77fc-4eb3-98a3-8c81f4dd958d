import { Injectable } from '@nestjs/common';
import { MongooseModuleOptions, MongooseOptionsFactory } from '@nestjs/mongoose';
import * as Joi from 'joi';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ConfigMongoPortalService extends ConfigService implements MongooseOptionsFactory {
    provideConfigSpec() {
        return {
            MONGO_HOST: {
                validate: Joi.string(),
                required: true,
            },
            MONGO_PORT: {
                validate: Joi.number(),
                required: true,
                default: 27018,
            },
            MONGO_DATABASE: {
                validate: Joi.string(),
                required: true,
                default: 'pkhsuper',
            },
            MONGO_SINGLE_OR_CLUSTER: {
                validate: Joi.string(),
                required: true,
                default: 'SINGLE',
            },
            MONGO_CLUSTER_HOST: {
                validate: Joi.string(),
                required: true,
                default: 'mongo-master.medpro.com.vn:27017,mongo-replicas1.medpro.com.vn:27018,mongo-replicas2.medpro.com.vn:27019',
            },
            MONGO_CLUSTER_PORTAL_DATABASE: {
                validate: Joi.string(),
                required: true,
                default: 'PortalDB',
            },
            MONGO_OPTIONS: {
                validate: Joi.string(),
                required: true,
                default: 'medpro',
            },
        };
    }

    getMongoConfig() {
        return {
            username: this.get<string>('MONGO_USERNAME'),
            password: this.get<string>('MONGO_PASSWORD'),
            host: this.get<string>('MONGO_HOST'),
            port: this.get<string>('MONGO_PORT'),
            database: this.get<string>('MONGO_CLUSTER_PORTAL_DATABASE'),
        };
    }

    createMongooseOptions(): MongooseModuleOptions {
        let uri: string;
        if (this.getSwitchMongoMode === 'SINGLE') {
            const { username, password, host, port, database } = this.getMongoConfig();
            uri = `mongodb://${!!username && !!password ? `${username}:${encodeURIComponent(password)}@` : ``}${host}:${port}/${database}${
                this.getMongoOption
            }`;
        } else {
            const { username, password, host, port, database } = this.getMongoCluster;
            uri = `mongodb://${username && password ? `${username}:${password}@` : ''}${host}/${database}${this.getMongoOption}`;
        }
        console.log('uri: ', uri);
        return {
            uri,
            useNewUrlParser: true,
            useUnifiedTopology: true,
            connectionName: 'medproConnection',
        };
    }

    get getSwitchMongoMode(): string {
        return this.get<string>('MONGO_SINGLE_OR_CLUSTER');
    }

    get getMongoCluster(): any {
        return {
            username: this.get<string>('MONGO_USERNAME'),
            password: this.get<string>('MONGO_PASSWORD'),
            host: this.get<string>('MONGO_CLUSTER_HOST'),
            database: this.get<string>('MONGO_CLUSTER_PORTAL_DATABASE'),
        };
    }

    get getMongoOption(): string {
        return this.get<string>('MONGO_OPTIONS');
    }
}
