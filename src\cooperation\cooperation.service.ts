import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LARK_NOTIF_COOPERATE_MEDPRO, LARK_NOTIF_COOPERATE_MEDPRO_LONGVAN, LARK_NOTIF_ENTERPRISE_REGISTRATION } from 'src/message-event/constant';
import { UserService } from '../user/user.service';
import { CooperationDto } from './dto/create-cooperation.dto';
import { ICooperationPackage } from './interfaces/cooperation-package.inteface';
import { ICooperation } from './interfaces/cooperation.inteface';
import { COOPERATION, COOPERATION_DOCTOR, COOPERATION_PACKAGES, COOPERATION_PACKAGES_PRICE, ENTERPRISE_REGISTRATION_COLLECTION_NAME, LOG_LONG_VAN } from './schemas/constants';
import { UrlConfigService } from 'src/config/config.url.service';
import { ICooperationDoctor } from './interfaces/cooperation-doctor.inteface';
import { CooperationDoctorDto } from './dto/create-cooperation-doctor.dto';
import { UpdateCooperationDto } from './dto/update-cooperation.dto';
import { ConfigRecaptchaService } from 'src/config/config.recapcha.service';
import { ICooperationPackagePrice } from './interfaces/cooperation-package-price.inteface';
import { chain, map } from 'lodash';
import { ILogLongVanDocument } from './interfaces/audit-log.interface';
import { EnterpriseRegistrationDto } from './dto/enterprise-registration.dto';
import { IEnterpriseRegistration } from './interfaces/enterprise-registration.interface';
import { CooperationClinicDto } from './dto/create-cooperation-clinic.dto';
import { ConfigRepoService } from '../config/config.repo.service';
import { GlobalSettingService } from '../global-setting/global-setting.service';

@Injectable()
export class CooperationService {
    private logger = new Logger(CooperationService.name);
    private readonly repoName: string;


    constructor(
        @InjectModel(COOPERATION) private CooperationModel: Model<ICooperation>,
        // @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(COOPERATION_PACKAGES) private cooperationPackagesModel: Model<ICooperationPackage>,
        @InjectModel(COOPERATION_DOCTOR) private cooperationDoctorModel: Model<ICooperationDoctor>,
        @InjectModel(COOPERATION_PACKAGES_PRICE) private cooperationPackagePriceModel: Model<ICooperationPackagePrice>,
        @InjectModel(LOG_LONG_VAN) private logsLongVanModel: Model<ILogLongVanDocument>,
        @InjectModel(ENTERPRISE_REGISTRATION_COLLECTION_NAME)
        private enterpriseRegistrationModel: Model<IEnterpriseRegistration>,
        private userService: UserService,
        private readonly urlConfigService: UrlConfigService,
        private eventEmmiter: EventEmitter2,
        private readonly http: HttpService,
        private readonly recaptchaConfig: ConfigRecaptchaService,
        private readonly httpService: HttpService,
        private readonly repoConfigService: ConfigRepoService,
        private globalSettingService: GlobalSettingService,
    ) {
        this.repoName = this.repoConfigService.getRepoName();
    }

    async createCooperation(body: CooperationDto) {
        try {
            // Kiểm tra packageId có tồn tại trong collection CooperationPackages không

            if (body?.captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: body.captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }

            const packageExists = await this.cooperationPackagesModel.findById(body.packageId).exec();
            if (!packageExists) {
                throw new HttpException('Gói hợp tác không tồn tại.', HttpStatus.BAD_REQUEST);
            }
            const packageObj = packageExists.toObject();
            const packagePrice = await this.cooperationPackagePriceModel.findOne({ type: 1, packageId: packageObj._id }).exec();
            const packagePriceObj = packagePrice.toObject();
            // Chỉ lấy các field cần thiết từ body
            const cooperationData = {
                fullname: body.fullname,
                email: body.email,
                phone: body.phone,
                note: body.note,
                packageId: packageObj._id,
                package: packageObj._id,
                typeCooperation: body.typeCooperation,
            };

            // Tạo mới và lưu vào DB
            const newCooperation = await this.CooperationModel.create(cooperationData);
            try {
                this.eventEmmiter.emit(LARK_NOTIF_COOPERATE_MEDPRO, {
                    fullname: body.fullname,
                    email: body.email,
                    phone: body.phone,
                    note: body.note,
                    packageName: packageObj.name,
                    packagePrice: packagePriceObj.price,
                    typeCooperation: body.typeCooperation,
                    type: 1
                });
            } catch (error) {
                this.logger.error(`${error.message}`, error.stack);
            }
            try {
                this.eventEmmiter.emit(LARK_NOTIF_COOPERATE_MEDPRO_LONGVAN, {
                    fullname: body.fullname,
                    email: body.email,
                    phone: body.phone,
                    note: body.note,
                    packageName: packageObj.name,
                    packagePrice: packagePriceObj.price,
                    typeCooperation: body.typeCooperation,
                });
            } catch (error) {
                this.logger.error(`${error.message}`, error.stack);
            }
            return newCooperation;
        } catch (error) {
            this.logger.error(`Error creating cooperation package for user: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi tạo gói hợp tác.', HttpStatus.BAD_REQUEST);
        }
    }

    async createCooperationClinic(body: CooperationClinicDto) {
        try {
            if (body?.captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: body.captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }

            // Chỉ lấy các field cần thiết từ body
            const cooperationData = {
                fullname: body.fullname,
                email: body.email,
                phone: body.phone,
                name: body.name,
                note: body.note,
                typeCooperation: 'Phòng mạch',
            };

            // Tạo mới và lưu vào DB
            const newCooperation = await this.CooperationModel.create(cooperationData);
            try {
                this.eventEmmiter.emit(LARK_NOTIF_COOPERATE_MEDPRO, {
                    fullname: body.fullname,
                    email: body.email,
                    phone: body.phone,
                    note: body.note,
                    packageName: 'Hợp tác phòng mạch',
                    packagePrice: undefined,
                    typeCooperation: body.name,
                    type: 3
                });
            } catch (error) {
                this.logger.error(`${error.message}`, error.stack);
            }
            return newCooperation;
        } catch (error) {
            if (error.code === 11000) {
                throw new HttpException('Email này đã đăng ký trước đó. Vui lòng liên hệ 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
            this.logger.error(`Error creating cooperation package for user: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi tạo gói hợp tác.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCooperations(userId: string) {
        try {
            const services = await this.CooperationModel.find()
                .populate('package')
                .populate('packagePrice')
                .exec();
            return services;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    // async updateCooperation(userMongoId: string, id: string, updateData: UpdateCooperationDto): Promise<any> {
    //     try {
    //         // Kiểm tra packageId có tồn tại trong collection CooperationPackages không
    //         const packageExists = await this.cooperationPackagesModel.findById(updateData.packageId);
    //         if (!packageExists) {
    //             throw new HttpException('Gói hợp tác không tồn tại.', HttpStatus.BAD_REQUEST);
    //         }
    //         const cooperationData = {
    //             ...updateData,
    //             packageInfo: packageExists,
    //         };
    //         const updatedPackage = await this.CooperationModel.findByIdAndUpdate(id, cooperationData, {
    //             new: true, // Trả về bản ghi đã cập nhật
    //             runValidators: true, // Chạy validators của Schema
    //         }).exec();

    //         if (!updatedPackage) {
    //             throw new HttpException('Không tìm thấy gói hợp tác.', HttpStatus.NOT_FOUND);
    //         }

    //         return updatedPackage;
    //     } catch (error) {
    //         this.logger.error(`Error updating package with ID ${id}: ${error.message}`, error.stack);
    //         throw new HttpException('Lỗi khi cập nhật gói hợp tác.', HttpStatus.BAD_REQUEST);
    //     }
    // }

    // async deleteCooperation(userId: string, id: string) {
    //     if (!(await this.userService.isCs(userId))) {
    //         throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
    //     }

    //     try {
    //         const deletedService = await this.CooperationModel.findByIdAndDelete(id).exec();

    //         if (!deletedService) {
    //             throw new HttpException('Dịch vụ không tồn tại.', HttpStatus.NOT_FOUND);
    //         }

    //         return {
    //             message: 'Xóa dịch vụ thành công.',
    //             id,
    //         };
    //     } catch (error) {
    //         this.logger.error(`Error deleting service: ${error.message}`, error.stack);
    //         throw new HttpException(error?.message || 'Lỗi xóa dịch vụ Care247.', HttpStatus.BAD_REQUEST);
    //     }
    // }

    groupPackagePrice(servicePrice: any) {
        const data = chain(servicePrice)
            .groupBy('packageId')
            .map((items) => {
                const itemObj = items[0]?.toObject()
                const priceText = `${itemObj.price}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
                return {
                ...itemObj?.package,
                priceText: itemObj.price === 0 ? 'Miễn phí' : `${priceText}đ/năm`,
                priceOptions: map(items, item => ({
                    id: item._id,
                    originalPrice: item.originalPrice,
                    price: item.price,
                    discountPrice: item.discountPrice,
                    discountPercent: item.discountPercent,
                    duration: item.duration,
                    buttonText: item.price === 0 ? 'Đăng ký ngay' : items.length === 1 ? 'Mua ngay' : `Mua ngay ${item.duration/12} năm`
                })).sort((a, b) => a.duration - b.duration),
            }})
            .sort((a, b) => a.price - b.price)
            .value();
        return data;
    }

    async createRegistration(dto: EnterpriseRegistrationDto): Promise<any> {
        try {

            if (dto?.captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: dto.captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
            // Tạo document mới từ dữ liệu DTO
            const newRegistration = new this.enterpriseRegistrationModel(dto);

            const savedRegistration = await newRegistration.save();
            try {
                this.eventEmmiter.emit(LARK_NOTIF_ENTERPRISE_REGISTRATION, {
                    fullName: dto.fullName,
                    company: dto.company,
                    quantity: dto.quantity,
                    phoneNumber: dto.phoneNumber,
                    note: dto.note || 'Không có ghi chú',
                });
            } catch (error) {
                this.logger.error(`Lỗi khi phát sự kiện LARK_NOTIF_ENTERPRISE_REGISTRATION: ${error.message}`, error.stack);
            }
            return savedRegistration
        } catch (error) {
            // Ghi log lỗi để debug
            this.logger.error(`Lỗi khi đăng ký khám doanh nghiệp: ${error.message}`);
            return { success: false, message: error.message };
        }
    }

    async getCooperationPackages() {
        try {
            const servicesPrice = await this.cooperationPackagePriceModel.find({ type: 1 }).populate('package').exec();
            
            let data = this.groupPackagePrice(servicesPrice);
            data = data.map(item => {
                const originalPrice = (item.priceOptions[0].discountPrice || item.priceOptions[0].discountPercent) ? item.priceOptions[0].originalPrice : null;
                return { ...item, price: item.priceOptions[0].price, originalPrice, discountPercent: item.priceOptions[0].discountPercent }
            })
            return data
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCooperationPackagesClinic() {
        try {
            const cooperationClinic = await this.globalSettingService.findByKeyAndRepoName('COOPERATION_CLINIC');
            const data = cooperationClinic ? JSON.parse(cooperationClinic) : [];
            return data;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách gói hợp tác phòng mạch.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCooperationPackagesDoctor() {
        try {
            const servicesPrice = await this.cooperationPackagePriceModel.find({ type: 2 }).populate('package').exec();
            const data = this.groupPackagePrice(servicesPrice);
            // const services = [
            //     {
            //         name: "Trang thông tin chi tiết bác sĩ",
            //         description: "",
            //         price: 0,
            //         features: [
            //             {
            //                 name: "Trang web thông tin cơ bản",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Tổng đài 19002115",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Công cụ thanh toán trực tuyến",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Medpro support",
            //                 isAvailable: true,
            //                 details: "",
            //             }
            //         ],
            //         isPopular: true,
            //         type: 2
            //     },
            //     {
            //         name: "Gọi video với bác sĩ",
            //         description: "",
            //         price: 799000,
            //         features: [
            //             {
            //                 name: "Trang web thông tin cơ bản",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Tổng đài 19002115",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Công cụ thanh toán trực tuyến",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Medpro support",
            //                 isAvailable: true,
            //                 details: "",
            //             }
            //         ],
            //         isPopular: true,
            //         type: 2
            //     },
            //     {
            //         name: "Đặt khám trực tiếp với bác sĩ (phòng mạch)",
            //         description: "",
            //         price: 1899000,
            //         features: [
            //             {
            //                 name: "Trang web thông tin cơ bản",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Tổng đài 19002115",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Công cụ thanh toán trực tuyến",
            //                 isAvailable: true,
            //                 details: "",
            //             },
            //             {
            //                 name: "Medpro support",
            //                 isAvailable: true,
            //                 details: "",
            //             }
            //         ],
            //         isPopular: true,
            //         type: 2
            //     }
            // ]
            return data;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ bác sĩ hợp tác.', HttpStatus.BAD_REQUEST);
        }
    }
    
    @OnEvent(LARK_NOTIF_COOPERATE_MEDPRO_LONGVAN)
    private async callApiSendNotificationToLongVan(payload: {
        fullname: string;
        email: string;
        phone: string;
        note: string;
        packageId: string;
        packageName: string;
        packagePrice: number;
        typeCooperation: string;
        type: string;
    }) {
        let response = { data: undefined, error: undefined };
        const url = `${this.urlConfigService.getUrlLongVan}/dynamic-collection/public/v2/webhook/work_effort.createOpportunity`;
        try {
            const { data } = await this.http.post<{ isOK: boolean }>(url, payload).toPromise();
            if (data) {
                response = {data: data, error: undefined};
            }
        } catch (error) {
            response = {data: undefined, error: error};
            this.logger.error(`Có lỗi trong quá trình gửi thông tin khách hàng hợp tác Medpro đến Long Vân`, `${error.message}`);
        }
        try {
            await this.logsLongVanModel.create({
                url: this.urlConfigService.getUrlLongVan,
                method: 'POST',
                body: payload,
                nameRepo: 'work_effort.createOppotunity',
                response: JSON.stringify(response?.data || response?.error),
                type: 'cooperate',
                status: response?.data ? true : false,
            });
        } catch (error) {
            this.logger.error(`Error creating log long van: ${error.message}`, error.stack);
        }
        return response;
    }

    @OnEvent(LARK_NOTIF_COOPERATE_MEDPRO)
    private async callApiSendNotificationToLarkGroup(payload: {
        fullname: string;
        email: string;
        phone: string;
        note: string;
        packageId: string;
        packageName: string;
        packagePrice: number;
        typeCooperation: string;
        type: number;
    }) {
        let response: { isOk: boolean; message: string };
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/lark-cooperate-medpro`;

        try {
            const { data } = await this.http.post<{ isOK: boolean }>(url, { ...payload, repoName: this.repoName }).toPromise();
            response = {
                isOk: data.isOK,
                message: data.isOK
                    ? 'Gửi thông tin khách hàng hợp tác Medpro tới lark group thành công'
                    : 'Gửi thông tin khách hàng hợp tác Medpro tới lark group thất bại',
            };
        } catch (error) {
            response = {
                isOk: false,
                message: 'Gửi thông thông tin khách hàng hợp tác Medpro đến lark group thất bại',
            };
            this.logger.error(`Có lỗi trong quá trình gửi thông tin khách hàng hợp tác Medpro đến lark group`, `${error.message}`);
        }
        return response;
    }

    // Gửi thông tin đăng ký khám doanh nghiệp đến nhóm Lark
    @OnEvent(LARK_NOTIF_ENTERPRISE_REGISTRATION, { async: true })
    private async callApiSendNotificationToLarkGroupForEnterpriseRegistration(payload: {
        fullName: string;
        company: string;
        quantity: number;
        phoneNumber: string;
        note: string;
    }) {
        let response: { isOk: boolean; message: string };
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/lark-notif-enterprise-registration`;

        try {
            // Gửi yêu cầu POST đến API trung gian để gửi thông báo đến nhóm Lark
            const { data } = await this.http.post<{ isOK: boolean }>(url, {
                fullname: payload.fullName,
                phone: payload.phoneNumber,
                note: payload.note,
                company: payload.company,
                quantity: payload.quantity,
                type: 'enterprise_registration',
            }).toPromise();

            response = {
                isOk: data.isOK,
                message: data.isOK
                    ? 'Gửi thông tin đăng ký khám doanh nghiệp tới nhóm Lark thành công'
                    : 'Gửi thông tin đăng ký khám doanh nghiệp tới nhóm Lark thất bại',
            };
        } catch (error) {
            response = {
                isOk: false,
                message: 'Gửi thông tin đăng ký khám doanh nghiệp đến nhóm Lark thất bại',
            };
            this.logger.error(
                `Có lỗi trong quá trình gửi thông tin đăng ký khám doanh nghiệp đến nhóm Lark`,
                `${error.message}`,
            );
        }

        return response;
    }

    async createCooperationDoctor(body: CooperationDoctorDto) {
        try {
            if (body?.captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: body.captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
            // Kiểm tra packageId có tồn tại trong collection CooperationPackages không
            const packageExists = await this.cooperationPackagesModel.findById(body.packageId).exec();
            if (!packageExists) {
                throw new HttpException('Gói hợp tác không tồn tại.', HttpStatus.BAD_REQUEST);
            }
            const packageObj = packageExists.toObject();
            // Chỉ lấy các field cần thiết từ body
            const cooperationData = {
                fullname: body.fullname,
                email: body.email,
                phone: body.phone,
                certificateImage: body.certificateImage,
                packageId: packageObj._id,
                package: packageObj._id,
                role: body.role,
                subject: body.subject,
                treatments: body.treatments,
                address: body.address,
                birthDate: body.birthDate,
                sex: body.sex,
                workplace: body.workplace,
                note: body.note,
            };

            // Tạo mới và lưu vào DB
            const newCooperation = await this.cooperationDoctorModel.create(cooperationData);
            try {
                this.eventEmmiter.emit(LARK_NOTIF_COOPERATE_MEDPRO, {
                    ...cooperationData,
                    type: 2,
                    packageName: packageObj.name,
                    packagePrice: packageObj.price,
                });
            } catch (error) {
                this.logger.error(`${error.message}`, error.stack);
            }
            return newCooperation;
        } catch (error) {
            if (error.code === 11000) {
                return { isOk: false, message: 'Email này đã đăng kí liên kết với Medpro!' };
            }
            this.logger.error(`Error creating cooperation package for user: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi tạo gói hợp tác.', HttpStatus.BAD_REQUEST);
        }
    }

    async verifyGoogleRecaptcha(formData: { captchaResponse: string }) {
        try {
            const { data } = await this.httpService
                .post(
                    'https://www.google.com/recaptcha/api/siteverify',
                    {},
                    {
                        params: {
                            secret: this.recaptchaConfig.getSecret(),
                            response: formData.captchaResponse,
                        },
                    },
                )
                .toPromise();

            if (data.success !== true) {
                throw new HttpException({ message: 'Lỗi xác thực reCAPTCHA, vui lòng thử lại!', recaptchaError: true }, HttpStatus.BAD_REQUEST);
            }
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }

            console.error('error verifyGoogleRecapcha: ', err);

            throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
        }
    }
}
