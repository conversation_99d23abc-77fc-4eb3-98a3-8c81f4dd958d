import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LARK_NOTIF_COOPERATE_MEDPRO } from 'src/message-event/constant';
import { UserService } from '../user/user.service';
import { CooperationDto } from './dto/create-cooperation.dto';
import { ICooperationPackage } from './interfaces/cooperation-package.inteface';
import { ICooperation } from './interfaces/cooperation.inteface';
import { COOPERATION, COOPERATION_PACKAGES } from './schemas/constants';
import { UrlConfigService } from 'src/config/config.url.service';
import { UpdateCooperationDto } from './dto/update-cooperation.dto';
import { ConfigRecaptchaService } from 'src/config/config.recapcha.service';

@Injectable()
export class CooperationService {
    private logger = new Logger(CooperationService.name);

    constructor(
        @InjectModel(COOPERATION) private CooperationModel: Model<ICooperation>,
        // @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(COOPERATION_PACKAGES) private cooperationPackagesModel: Model<ICooperationPackage>,
        private userService: UserService,
        private readonly urlConfigService: UrlConfigService,
        private eventEmmiter: EventEmitter2,
        private readonly http: HttpService,
        private readonly recaptchaConfig: ConfigRecaptchaService,
        private readonly httpService: HttpService,
    ) {}

    async createCooperation(body: CooperationDto) {
        try {
            // Kiểm tra packageId có tồn tại trong collection CooperationPackages không

            if (body?.captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: body.captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }

            const packageExists = await this.cooperationPackagesModel.findById(body.packageId);
            if (!packageExists) {
                throw new HttpException('Gói hợp tác không tồn tại.', HttpStatus.BAD_REQUEST);
            }
            // Chỉ lấy các field cần thiết từ body
            const cooperationData = {
                fullname: body.fullname,
                email: body.email,
                phone: body.phone,
                note: body.note,
                packageId: body.packageId,
                package: body.packageId,
                typeCooperation: body.typeCooperation,
            };

            // Tạo mới và lưu vào DB
            const newCooperation = await this.CooperationModel.create(cooperationData);
            try {
                this.eventEmmiter.emit(LARK_NOTIF_COOPERATE_MEDPRO, {
                    fullname: body.fullname,
                    email: body.email,
                    phone: body.phone,
                    note: body.note,
                    packageId: body.packageId,
                    packageName: packageExists.name,
                    packagePrice: packageExists.price,
                    typeCooperation: body.typeCooperation,
                });
            } catch (error) {
                this.logger.error(`${error.message}`, error.stack);
            }
            return newCooperation;
        } catch (error) {
            if (error.code === 11000) {
                return { isOk: false, message: 'Email này đã đăng kí liên kết với Medpro!' };
            }
            this.logger.error(`Error creating cooperation package for user: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi tạo gói hợp tác.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCooperations(userId: string) {
        try {
            const services = await this.CooperationModel.find()
                .populate('package')
                .exec();
            return services;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async updateCooperation(userMongoId: string, id: string, updateData: UpdateCooperationDto): Promise<any> {
        try {
            // Kiểm tra packageId có tồn tại trong collection CooperationPackages không
            const packageExists = await this.cooperationPackagesModel.findById(updateData.packageId);
            if (!packageExists) {
                throw new HttpException('Gói hợp tác không tồn tại.', HttpStatus.BAD_REQUEST);
            }
            const cooperationData = {
                ...updateData,
                packageInfo: packageExists,
            };
            const updatedPackage = await this.CooperationModel.findByIdAndUpdate(id, cooperationData, {
                new: true, // Trả về bản ghi đã cập nhật
                runValidators: true, // Chạy validators của Schema
            }).exec();

            if (!updatedPackage) {
                throw new HttpException('Không tìm thấy gói hợp tác.', HttpStatus.NOT_FOUND);
            }

            return updatedPackage;
        } catch (error) {
            this.logger.error(`Error updating package with ID ${id}: ${error.message}`, error.stack);
            throw new HttpException('Lỗi khi cập nhật gói hợp tác.', HttpStatus.BAD_REQUEST);
        }
    }

    async deleteCooperation(userId: string, id: string) {
        if (!(await this.userService.isCs(userId))) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }

        try {
            const deletedService = await this.CooperationModel.findByIdAndDelete(id).exec();

            if (!deletedService) {
                throw new HttpException('Dịch vụ không tồn tại.', HttpStatus.NOT_FOUND);
            }

            return {
                message: 'Xóa dịch vụ thành công.',
                id,
            };
        } catch (error) {
            this.logger.error(`Error deleting service: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi xóa dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCooperationPackages() {
        try {
            const services = await this.cooperationPackagesModel.find().exec();
            return services;
        } catch (error) {
            this.logger.error(`Error retrieving services: ${error.message}`, error.stack);
            throw new HttpException(error?.message || 'Lỗi lấy danh sách dịch vụ Care247.', HttpStatus.BAD_REQUEST);
        }
    }

    @OnEvent(LARK_NOTIF_COOPERATE_MEDPRO)
    private async callApiSendNotificationToLarkGroup(payload: {
        fullname: string;
        email: string;
        phone: string;
        note: string;
        packageId: string;
        packageName: string;
        packagePrice: number;
        typeCooperation: string;
    }) {
        let response: { isOk: boolean; message: string };
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/lark-cooperate-medpro`;

        try {
            const { data } = await this.http.post<{ isOK: boolean }>(url, payload).toPromise();
            response = {
                isOk: data.isOK,
                message: data.isOK
                    ? 'Gửi thông tin khách hàng hợp tác Medpro tới lark group thành công'
                    : 'Gửi thông tin khách hàng hợp tác Medpro tới lark group thất bại',
            };
        } catch (error) {
            response = {
                isOk: false,
                message: 'Gửi thông thông tin khách hàng hợp tác Medpro đến lark group thất bại',
            };
            this.logger.error(`Có lỗi trong quá trình gửi thông tin khách hàng hợp tác Medpro đến lark group`, `${error.message}`);
        }
        return response;
    }

    async verifyGoogleRecaptcha(formData: { captchaResponse: string }) {
        try {
            const { data } = await this.httpService
                .post(
                    'https://www.google.com/recaptcha/api/siteverify',
                    {},
                    {
                        params: {
                            secret: this.recaptchaConfig.getSecret(),
                            response: formData.captchaResponse,
                        },
                    },
                )
                .toPromise();

            if (data.success !== true) {
                throw new HttpException({ message: 'Lỗi xác thực reCAPTCHA, vui lòng thử lại!', recaptchaError: true }, HttpStatus.BAD_REQUEST);
            }
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }

            console.error('error verifyGoogleRecapcha: ', err);

            throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
        }
    }
}
