import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsIn, IsNotEmpty } from 'class-validator';

export class ICare247ServiceDto {
    @ApiProperty({ description: 'duration' })
    @IsNotEmpty()
    duration: string;

    @ApiProperty({ description: 'originalPrice', default: 0 })
    originalPrice: number = 0;

    @ApiProperty({ description: 'originalPrice' })
    @IsNotEmpty()
    price: Number;

    @ApiProperty({ description: 'name' })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: 'subname' })
    subname?: string;

    @ApiProperty({ description: 'description' })
    @IsNotEmpty()
    description: string;

    @ApiProperty({ description: 'currency' })
    @IsNotEmpty()
    currency: string;

    @ApiProperty({ 
        description: 'Status of the service (0: INACTIVE, 1: ACTIVE, 2: PENDING)', 
        example: 1 
    })
    @IsNotEmpty()
    @Transform(value => Number(value))
    @IsIn([0, 1, 2])
    status: number;

    @ApiProperty({ description: 'locale' })
    @IsNotEmpty()
    locale: String;

    @ApiProperty({ description: 'description_following' })
    description_following: String
}
