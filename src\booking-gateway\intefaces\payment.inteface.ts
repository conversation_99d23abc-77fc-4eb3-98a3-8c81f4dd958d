import { Document } from 'mongoose';

export interface IPayment extends Document {
    bookingId: string;
    booking: string;
    type: number;
    date: string;
    patientId: string;
    patient: string;
    amount: number;
    subTotal: number;
    totalFee: number;
    medproFee: number;
    transferFee: number;
    gatewayId: string;
    paymentMethod: string;
    paymentMethodDetail: string;
    transactionId: string;
    bookingCode: string;
    feeCode: string;
    orderId: string;
    paymentTime: string;
    partnerId: string;
    partner?: string;
    appId: string; // dành cho phần nạp tiền
    bankInfo?: {
        name: string;
        accountHolder: string;
        accountNumber: string;
        bankBranch: string;
    };
    extraInfo?: {
        transactionIdV1: string;
        methodIdV1: number;
        booking: object;

    };
    email: string; // dành cho phần nạp tiền
    platform: string; // dành cho phần nạp tiền
    message: string;
    userId?: string;
    hospitalFee?: string;
    paymentHospitalFeeId?: string;
    paymentHospitalFee?: string;
    reference: number;
    noPayment: boolean;
    sharePayment: boolean;
    plusOrMinus: boolean;
    historyType: number;
    transactionContent?: string;
    status: number;
    bookingOrder?: string;
    bookingOrderId?: string;
    phoneCall1900?: string;
    syncAt?: Date;
    updatedAt?: Date;
    createdAt?: Date;
    chargeFeeInfo?: any;
    groupId?: number;
    gatewayTransactionId: string;
    refundStatus: number;
    medproCareFee?: number;
    refundDate?: number;
    discountInfo?: any;
    care247?: string;
    dealFee?: number;
    deal?: string;
    expiredAt?: string;
    care247Independent?: string;
}
