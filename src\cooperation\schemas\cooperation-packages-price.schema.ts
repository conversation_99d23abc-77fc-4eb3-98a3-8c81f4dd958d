import * as mongoose from 'mongoose';
import { COOPERATION_PACKAGES, COOPERATION_PACKAGES_PRICE } from './constants';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const CooperationPackagesPriceSchema = new Schema(
    {
        packageId:{ type: String, required: true },
        package: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES, required: true },
        defaultPrice: { type: Number },
        originalPrice: { type: Number }, // Gi<PERSON> tiền (VNĐ)
        price: { type: Number, required: true }, // <PERSON>i<PERSON> tiền (VNĐ)
        discountPrice: { type: Number, required: true }, // <PERSON>i<PERSON> khuyến mãi (VNĐ)
        discountPercent: { type: Number, default: 0 }, // <PERSON><PERSON><PERSON> mãi (VNĐ)
        duration: { type: Number, required: true }, // Thời hạn (tháng)
        period: { type: String, default: 'month' }, // Thời hạn (năm)
        createdBy: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        type: { type: Number, required: true }, //1: csyt, 2: doctor
    },
    {
        collection: COOPERATION_PACKAGES_PRICE,
        timestamps: true,
        versionKey: false,
    },
).plugin(jsonMongo);
