import { ApiProperty } from "@nestjs/swagger"
import { IsString } from "class-validator"
export class StorageUserBankingInfoOnSubmitDto {
    @ApiProperty({
        type: String,
        description: "<PERSON>ứ<PERSON> độ hài lòng với cơ sở y tế",
        example: "5"
    })
    @IsString()
    ratingHospital: string
    
    @ApiProperty({
        type: String,
        description: "Mức độ hài lòng với Medpro",
        example: "5"
    })
    @IsString()
    ratingMedpro: string

    @ApiProperty({
        type: String,
        description: "Tên ngân hàng",
        example: "Viettinbank"
    })
    @IsString()
    bankName: string

    @ApiProperty({
        type: String,
        description: "Số tài khoản ngân hàng",
        example: "*********"
    })
    @IsString()
    bankAccountNumber: string
    
    @ApiProperty({
        type: String,
        description: "Tên chủ tài khoản",
        example: "<PERSON><PERSON><PERSON>"
    })
    @IsString()
    bankAccountName: string

    @ApiProperty({
        type: String,
        description: "Tổng tiền thanh toán",
        example: "1000000"
    })
    @IsString()
    totalAmount: string
    
    @ApiProperty({
        type: String,
        description: "Id của bệnh viện",
        example: "umc"
    })
    @IsString()
    prefill_partnerId: string

    @ApiProperty({
        type: String,
        description: "Id của người dùng",
        example: "5f48c55e60d7f72ae4001f79"
    })
    @IsString()
    prefill_userId: string

    @ApiProperty({
        type: String,
        description: "Id của phiếu khám",
        example: "5f48c55e60d7f72ae4001f79"
    })
    @IsString()
    prefill_bookingId: string

    @ApiProperty({
        type: 'array',
        items: {
            type: 'string',
            format: 'binary',
        },
        description: 'Description for file',
    })
    files: string[];
}