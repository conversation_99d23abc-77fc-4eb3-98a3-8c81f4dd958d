import moment = require('moment');

export function notifyDatabaseDisconnectTemplate(larkEnv: string, content: string, stack: string): Object {
    return {
        config: {
            wide_screen_mode: true,
        },
        elements: [
            {
                alt: {
                    content: '',
                    tag: 'plain_text',
                },
                img_key: 'img_v2_cb03ec35-a638-4b93-9e6f-5e2d0e549deg',
                tag: 'img',
            },
            {
                tag: 'hr',
            },
            {
                tag: 'column_set',
                flex_mode: 'none',
                background_style: 'default',
                columns: [
                    {
                        tag: 'column',
                        width: 'weighted',
                        weight: 1,
                        vertical_align: 'top',
                        elements: [
                            {
                                tag: 'div',
                                text: {
                                    tag: 'lark_md',
                                    content: `${content}`,
                                },
                                extra: {
                                    tag: 'img',
                                    img_key: 'img_v2_84f2ebbf-78cc-4b7e-b0b1-bfe820f05edh',
                                    alt: {
                                        tag: 'plain_text',
                                        content: '',
                                    },
                                    mode: 'fit_horizontal',
                                    compact_width: false,
                                },
                            },
                            {
                                tag: 'hr',
                            },
                        ],
                    },
                ],
            },
            {
                elements: [
                    {
                        content: `Detail: ${stack}`,
                        tag: 'plain_text',
                    },
                ],
                tag: 'note',
            },
        ],
        header: {
            template: 'green',
            title: {
                content: `📊 ${larkEnv} Health system report?`,
                tag: 'plain_text',
            },
        },
    };
}

export function larkMsgAddContactUsTemplate(larkEnv: string, time: string, fullname: string, email: string, phone: string, note: string, isMedproId: boolean, typeName: string, company: string, typeBussiness: string, employees: number): Object {
    let thisField = [
        {
            is_short: true,
            text: {
                content: `**👤TÊN ĐƠN VỊ/NGƯỜI LIÊN HỆ**\n${fullname}`,
                tag: 'lark_md',
            },
        },
        {
            is_short: true,
            text: {
                content: `**📅 SỐ ĐIỆN THOẠI**\n${phone}${isMedproId == false ? '\n**(Chưa đăng ký MedproId)**' : ''}`,
                tag: 'lark_md',
            },
        },
        {
            is_short: false,
            text: {
                content: '',
                tag: 'lark_md',
            },
        },
    ];

    if (!!note) {
        thisField.push({
            is_short: true,
            text: {
                content: `**📝GHI CHÚ**\n${note}`,
                tag: 'lark_md',
            },
        });

        if (thisField.length % 3 == 0) {
            thisField.push({
                is_short: false,
                text: {
                    content: '',
                    tag: 'lark_md',
                },
            });
        }
    }

    if (!!email) {
        thisField.push({
            is_short: true,
            text: {
                content: `**🗳EMAIL **\n${email}`,
                tag: 'lark_md',
            },
        });

        if (thisField.length % 3 == 0) {
            thisField.push({
                is_short: false,
                text: {
                    content: '',
                    tag: 'lark_md',
                },
            });
        }
    }

    if (!!company) {
        thisField.push(
            {
                is_short: true,
                text: {
                    content: `**👨‍✈️CÔNG TY**\n${company}`,
                    tag: 'lark_md',
                },
            });

        if (thisField.length % 3 == 0) {
            thisField.push({
                is_short: false,
                text: {
                    content: '',
                    tag: 'lark_md',
                },
            });
        }
    }

    if (!!employees) {
        thisField.push(
            {
                is_short: true,
                text: {
                    content: `**🙎‍♂️SỐ LƯỢNG NHÂN VIÊN**\n${employees}`,
                    tag: 'lark_md',
                },
            });
        if (thisField.length % 3 == 0) {
            thisField.push({
                is_short: false,
                text: {
                    content: '',
                    tag: 'lark_md',
                },
            });
        }
    }

    if (!!typeBussiness) {
        thisField.push(
            {
                is_short: true,
                text: {
                    content: `**🎨LĨNH VỰC KINH DOANH**\n${typeBussiness}`,
                    tag: 'lark_md',
                },
            });
    }

    return {
        config: {
            wide_screen_mode: true,
        },
        elements: [
            {
                tag: 'div',
                text: {
                    content: `Chào bạn,\n\n(${time}) - Hệ thống ghi nhận thông tin có liên hệ hợp tác với Medpro`,
                    tag: 'lark_md',
                },
            },
            {
                fields: thisField,
                tag: 'div',
            },
        ],
        header: {
            template: 'green',
            title: {
                content: `🔔${larkEnv}Liên hệ hợp tác / ${typeName}`,
                tag: 'plain_text',
            },
        },
    };
}

export function larkMsgTrackingPatient({ cskhUser, patientUser, action, patient }: { action: string; cskhUser: any; patientUser: any, patient: any }): Object {
    return {
        config: {
            wide_screen_mode: true,
        },
        elements: [
            {
                tag: 'div',
                text: {
                    content: `Hệ thống ghi nhận CSKH ${cskhUser.fullname} ( MedproId: ${cskhUser.username}) thực hiện thao tác ${action}.`,
                    tag: 'lark_md',
                },
            },
            {
                fields: [
                    {
                        is_short: true,
                        text: {
                            content: `**👤Tên Hồ Sơ**\n${patient.surname} ${patient.name} - ${patient.code}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**👤Hành động**\n${action}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**🗳Tài khoản **\n${patientUser.username}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**👤Thời gian**\n${moment().utc().add(7, 'hours').format('DD-MM-YYYY HH:mm')}`,
                            tag: 'lark_md',
                        },
                    },
                ],
                tag: 'div',
            },
        ],
        header: {
            template: 'green',
            title: {
                content: `TRACKING PATIENT ACTION`,
                tag: 'plain_text',
            },
        },
    };
}

export function messageAddPatientToUserTemplate(actionName: string, userAction: any, patientUser: any, patient: any, hospitalName: string, oldPatientName: string): Object {
    return {
        config: {
            wide_screen_mode: true,
        },
        elements: [
            {
                tag: 'div',
                text: {
                    content: `Hệ thống ghi nhận ${userAction.fullname} (MedproId: ${userAction.username}) thực hiện thao tác ${actionName}`,
                    tag: 'lark_md',
                },
            },
            {
                fields: [
                    {
                        is_short: true,
                        text: {
                            content: `**👤Tên Hồ Sơ**\n${patient.surname} ${patient.name} - ${patient.code}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**🗳Tên Hồ Sơ Cũ **\n${oldPatientName}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**👤Hành động**\n${actionName}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**🏥Cơ sở y tế**\n${hospitalName}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**🗳Tài khoản **\n${patientUser.username}`,
                            tag: 'lark_md',
                        },
                    },
                    {
                        is_short: true,
                        text: {
                            content: `**👤Thời gian**\n${moment().utc().add(7, 'hours').format('DD-MM-YYYY HH:mm')}`,
                            tag: 'lark_md',
                        },
                    },
                ],
                tag: 'div',
            },
        ],
        header: {
            template: 'green',
            title: {
                content: 'TRACKING PATIENT ACTION',
                tag: 'plain_text',
            },
        },
    };
}
