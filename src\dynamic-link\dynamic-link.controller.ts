import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { DynamicLinkService } from './dynamic-link.service';
import { DoctorDynamicLinkQuery } from './dto/doctor-dynamic-link.query';
import {DoctorBookingDynamicLinkQuery} from './dto/doctor-booking-dynamic-link.query';
import { MedproZoneDto } from './dto/medpro-zone.dto';

@Controller('dynamic-link')
export class DynamicLinkController {
    constructor(private service: DynamicLinkService) {}

    @Get('doctor')
    getDoctorDynamicLink(@Query() query: DoctorDynamicLinkQuery) {
        return this.service.getDoctorDynamicLink(query);
    }

    @Get('doctor-booking')
    getDoctorBookingDynamicLink(@Query() query: DoctorBookingDynamicLinkQuery) {
        return this.service.getDoctorBookingDynamicLink(query);
    }

    @Get('tinh-nang')
    getTinhNangDynamicLink(@Query() query: any) {
        return this.service.getTinhNangDynamicLink(query);
    }

    @Get('booking-detail')
    getBookingDetailDynamicLink(@Query() query: any) {
        return this.service.getBookingDetailDynamicLink(query);
    }

    @Post('medpro-zone')
    getMedproZone(@Body() data: MedproZoneDto) {
        return this.service.getMedproZone(data);
    }
}
