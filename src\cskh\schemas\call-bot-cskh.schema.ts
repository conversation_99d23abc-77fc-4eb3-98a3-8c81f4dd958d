import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { AUDIT_LOG_CSKH, CALL_BOT_CSKH } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const CallBotCskhSchema = new Schema({
    phone: { type: String },
    fullData:  { type: Schema.Types.Mixed },
    userResponses: [{ type: Schema.Types.Mixed }],
    timeStart: { type: Date }, 
    timeEnd: { type: Date },
    duration: { type: Number },
    transactionId: { type: String, unique: true, required: true },
}, {
    collection: CALL_BOT_CSKH,
    timestamps: true,
}).plugin(jsonMongo);
