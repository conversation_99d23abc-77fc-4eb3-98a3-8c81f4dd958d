import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString } from 'class-validator';

export class UpdateVitalSignsDto {
    @ApiProperty({ description: '<PERSON><PERSON>y sinh dấu hiệu', example: '2025-07-18T03:15:40.453Z', required: false })
    @IsOptional()
    @IsDateString({}, { message: 'Ngày sinh dấu hiệu phải là ngày hợp lệ' })
    birthSign?: string;

    @ApiProperty({ description: 'Cân nặng (Kg)', example: '65', required: false })
    @IsOptional()
    @IsString({ message: 'Cân nặng phải là chuỗi' })
    weight?: string;

    @ApiProperty({ description: 'Chiều cao (cm)', example: '165', required: false })
    @IsOptional()
    @IsString({ message: 'Chiều cao phải là chuỗi' })
    height?: string;

    @ApiProperty({ description: 'Nhiệt độ (°C)', example: '37', required: false })
    @IsOptional()
    @IsString({ message: 'Nhiệt độ phải là chuỗi' })
    temperature?: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> (lần/phút)', example: '90', required: false })
    @IsOptional()
    @IsString({ message: 'Mạch phải là chuỗi' })
    pulse?: string;

    @ApiProperty({ description: 'SpO2 (%)', example: '36', required: false })
    @IsOptional()
    @IsString({ message: 'SpO2 phải là chuỗi' })
    spO2?: string;

    @ApiProperty({ description: 'Huyết áp (mmHg)', example: '80/80', required: false })
    @IsOptional()
    @IsString({ message: 'Huyết áp phải là chuỗi' })
    bloodPressure?: string;
}

export class ScanQrCodeDto {
    @ApiProperty({ description: 'Dữ liệu QR code đã quét', example: 'encoded_patient_id_123' })
    @IsString({ message: 'Dữ liệu QR code phải là chuỗi' })
    qrCodeData: string;
}

export class UpdateVitalSignsByQrDto {
    @ApiProperty({ description: 'Dữ liệu QR code đã quét', example: 'encoded_patient_id_123' })
    @IsString({ message: 'Dữ liệu QR code phải là chuỗi' })
    qrCodeData: string;

    @ApiProperty({ description: 'Dấu hiệu sinh tồn', type: UpdateVitalSignsDto, required: false })
    @IsOptional()
    vitalSigns?: UpdateVitalSignsDto;

    @ApiProperty({ description: 'Tên bệnh nhân', example: 'Nguyễn Văn A', required: false })
    @IsOptional()
    @IsString({ message: 'Tên bệnh nhân phải là chuỗi' })
    patientName?: string;

    @ApiProperty({ description: 'Ngày sinh', example: '1990-01-01', required: false })
    @IsOptional()
    @IsDateString({}, { message: 'Ngày sinh phải là ngày hợp lệ' })
    birthDate?: string;

    @ApiProperty({ description: 'Giới tính (0: Nữ, 1: Nam)', example: 1, required: false })
    @IsOptional()
    gender?: number;

    @ApiProperty({ description: 'Tên phụ huynh', example: 'Nguyễn Văn B', required: false })
    @IsOptional()
    @IsString({ message: 'Tên phụ huynh phải là chuỗi' })
    parentName?: string;

    @ApiProperty({ description: 'Địa chỉ', example: '123 Đường ABC', required: false })
    @IsOptional()
    @IsString({ message: 'Địa chỉ phải là chuỗi' })
    address?: string;

    @ApiProperty({ description: 'Số điện thoại', example: '**********', required: false })
    @IsOptional()
    @IsString({ message: 'Số điện thoại phải là chuỗi' })
    phone?: string;

    @ApiProperty({ description: 'Lý do khám', example: 'Khám sức khỏe định kỳ', required: false })
    @IsOptional()
    @IsString({ message: 'Lý do khám phải là chuỗi' })
    reasonForVisit?: string;

    @ApiProperty({ description: 'Có chụp X-quang không', example: false, required: false })
    @IsOptional()
    isXRay?: boolean;

    @ApiProperty({ description: 'Có nhập viện không', example: false, required: false })
    @IsOptional()
    isHospitalized?: boolean;

    @ApiProperty({ description: 'ID phường/xã', example: '001', required: false })
    @IsOptional()
    @IsString({ message: 'ID phường/xã phải là chuỗi' })
    ward_id?: string;

    @ApiProperty({ description: 'ID quận/huyện', example: '001', required: false })
    @IsOptional()
    @IsString({ message: 'ID quận/huyện phải là chuỗi' })
    district_id?: string;

    @ApiProperty({ description: 'ID tỉnh/thành phố', example: '01', required: false })
    @IsOptional()
    @IsString({ message: 'ID tỉnh/thành phố phải là chuỗi' })
    city_id?: string;
} 