import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty } from 'class-validator';

export class CooperationDoctorDto {
    @ApiProperty({
        description: 'Họ và tên',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Họ và tên không được để trống' })
    readonly fullname: string;

    @ApiProperty({
        description: 'Email',
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Email không được để trống' })
    @IsEmail()
    readonly email: string;

    @ApiProperty({
        description: 'Điện thoại',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
    readonly phone: string;

    @ApiProperty({
        description: '<PERSON><PERSON> chú',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    readonly note: string;

    @ApiProperty({
        description: 'Id gói hợp tác',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    readonly packageId: string;

    @ApiProperty({
        description: 'Chứng chỉ hành nghề',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
    certificateImage: string[];

    role?: string;
    subject?: string;
    treatments?: string;
    address?: string;
    birthDate?: string;
    sex?: number;
    workplace?: string;

    @ApiProperty({
        description: 'captchaResponse',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'captchaResponse không được để trống' })
    captchaResponse?: string;
}
