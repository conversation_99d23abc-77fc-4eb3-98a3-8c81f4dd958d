import * as mongoose from 'mongoose';
import { BOOKING_COLLECTION_NAME, SECTION_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';
import { SUBJECT_COLLECTION_NAME } from '../../subject-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from '../../room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from '../../doctor-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from '../../service-mongo/schemas/constants';
import { CARE247_SALES } from './constants';

const Schema = mongoose.Schema;

export const Care247SalesSchema = new Schema(
    {
        bookingId: { type: String },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        bookingCode: { type: String },
        userId: { type: String, unique: true, required: true },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        patientId: { type: String },
        patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
        patientVersionId: { type: String },
        partnerId: { type: String },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        appId: { type: String },
        platform: { type: String },
        status: { type: Number },
        date: { type: Date },
        subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
        room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
        section: { type: Schema.Types.ObjectId, ref: SECTION_COLLECTION_NAME },
        trackingTimes: { type: Number },
        isFirstTime: { type: Boolean, default: false },
        note: { type: String }
    },
    {
        collection: CARE247_SALES,
        timestamps: true,
    },
);
