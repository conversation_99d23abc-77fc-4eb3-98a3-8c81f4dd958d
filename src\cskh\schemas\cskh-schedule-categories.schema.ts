import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { CSKH_SCHEDULE_CATEGORIES } from './constants';

const Schema = mongoose.Schema;

export const CskhScheduleCategoriesSchema = new Schema(
    {
        name: { type: String, required: true },
        title: { type: String, required: true },
        fromTime: { type: String, required: true },
        toTime: { type: String, required: true },
        workDay: { type: Boolean, required: true },
        color:{ type: String, required: true, default: '#47bfff' },
    },
    {
        collection: CSKH_SCHEDULE_CATEGORIES,
        timestamps: true,
    },
).plugin(jsonMongo);
