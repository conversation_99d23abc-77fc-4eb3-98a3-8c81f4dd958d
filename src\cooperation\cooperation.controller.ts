import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
// import { CskhGuard } from '../common/guards/cskh.guard';
import { AuthGuard } from '@nestjs/passport';
import { CooperationService } from './cooperation.service';
import { CooperationDto } from './dto/create-cooperation.dto';
import { UpdateCooperationDto } from './dto/update-cooperation.dto';

@Controller('cooperation')
@ApiTags('Cooperation')
export class CooperationController {
    constructor(private readonly service: CooperationService) {}

    @Get()
    @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách khách hàng muốn hợp tác' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getAllCooperations(@Req() req) {
        const userId = req.user.id;
        return await this.service.getCooperations(userId);
    }

    @Post()
    @ApiOperation({ summary: 'Tạo mới khách hàng hợp tác với gói' })
    async createCooperation(@Body() body: CooperationDto) {
        return await this.service.createCooperation(body);
    }

    @Patch('/:id')
    @ApiOperation({ summary: 'Cập nhật khách hàng hợp tác với gói' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async updateCooperation(@Req() req: any, @Param('id') id: string, @Body() body: UpdateCooperationDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateCooperation(userMongoId, id, body);
    }

    @Delete('/:id')
    @ApiOperation({ summary: 'Xóa khách hàng hợp tác với gói' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async deleteCooperation(@Req() req: any, @Param('id') id: string) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.deleteCooperation(userMongoId, id);
    }

    @Get('packages')
    @ApiOperation({ summary: 'Lấy danh sách tất cả các gói hợp tác' })
    async getAllCooperationPackages(@Req() req) {
        return await this.service.getCooperationPackages();
    }
}
