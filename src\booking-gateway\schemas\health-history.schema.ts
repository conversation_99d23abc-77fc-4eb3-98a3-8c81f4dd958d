import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { HEALTH_HISTORY_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const HealthHistorySchema = new Schema({
    id: { type: String },
    userId: { type: String },
    partnerId: { type: String },
    examId: {type: String },
}, {
    collection: HEALTH_HISTORY_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
