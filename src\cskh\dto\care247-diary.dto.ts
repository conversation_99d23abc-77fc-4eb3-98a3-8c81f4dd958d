import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class Care247DiaryQueryDto {
    @ApiProperty({
        description: 'category',
        required: false,
        type: String,
    })
    categoryCode?: string;

    @ApiProperty({
        description: 'action',
        required: false,
        type: String,
    })
    actionCode?: string;

    @ApiProperty({
        description: 'userActionId',
        required: false,
        type: String,
    })
    userActionId?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toDate?: string;

    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @Transform(value => Number(value))
    pageIndex?: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
