import { Document } from 'mongoose';

export interface IReserveBookingLogs extends Document {
    uuid: string;
    userId: string;
    user: string;
    medproId: string;
    username: string;
    action: string;
    timestamp: Date;
    partnerId?: string;
    appId?: string;
    bookingData?: any;
    cskhInfo?: any;
    url?: string;
    method?: string;
    headers?: any;
    params?: any;
    body?: any;
    result?: any;
    error?: any;
    process?: any[];
    query?: any;
    nameRepo?: string;
    amount?: number;
    bookingSlotId?: string;
    doctorId?: string;
    endTime?: Date;
    filterCheckData?: any[];
    groupId?: number;
    hasInsuranceCode?: boolean;
    insuranceChoice?: string;
    insuranceCode?: string;
    maxSlot?: number;
    methodId?: string;
    patientId?: string;
    paymentTypeDetail?: string;
    platform?: string;
    redirectUrl?: string;
    serviceId?: string;
    startTime?: Date;
    startTimeString?: string;
    subTotal?: number;
    subjectId?: string;
    totalFee?: number;
    treeId?: string;
    roomId?: string;
    sectionId?: string;
    status?: number;
}
