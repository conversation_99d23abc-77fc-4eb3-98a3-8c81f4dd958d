import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CskhModule } from 'src/cskh/cskh.module';
import { UserModule } from 'src/user/user.module';
import { CooperationController } from './cooperation.controller';
import { CooperationService } from './cooperation.service';
import { LogLongVanSchema } from './schemas/log-long-van.schema';
import { COOPERATION, COOPERATION_DOCTOR, COOPERATION_PACKAGES, COOPERATION_PACKAGES_PRICE, ENTERPRISE_REGISTRATION_COLLECTION_NAME, LOG_LONG_VAN } from './schemas/constants';
import { CooperationsSchema } from './schemas/cooperation.schema';
import { CooperationPackagesSchema } from './schemas/cooperation-packages.schema';
import { CooperationDoctorSchema } from './schemas/cooperation-doctor.schema';
import { CooperationPackagesPriceSchema } from './schemas/cooperation-packages-price.schema';
import { EnterpriseRegistrationSchema } from './schemas/enterprise-registration.schema';
import { GlobalSettingModule } from '../global-setting/global-setting.module';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: COOPERATION, schema: CooperationsSchema },
            { name: COOPERATION_PACKAGES, schema: CooperationPackagesSchema },
            { name: COOPERATION_DOCTOR, schema: CooperationDoctorSchema },
            { name: COOPERATION_PACKAGES_PRICE, schema: CooperationPackagesPriceSchema },
            { name: LOG_LONG_VAN, schema: LogLongVanSchema },
            { name: ENTERPRISE_REGISTRATION_COLLECTION_NAME, schema: EnterpriseRegistrationSchema }
        ]),
        UserModule,
        CskhModule,
        GlobalSettingModule,
    ],
    controllers: [CooperationController],
    providers: [CooperationService],
    exports: [CooperationService],
})
export class CooperationModule {}
