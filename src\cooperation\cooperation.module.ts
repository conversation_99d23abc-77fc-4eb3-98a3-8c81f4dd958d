import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CskhModule } from 'src/cskh/cskh.module';
import { UserModule } from 'src/user/user.module';
import { CooperationController } from './cooperation.controller';
import { CooperationService } from './cooperation.service';
import { COOPERATION, COOPERATION_PACKAGES } from './schemas/constants';
import { CooperationsSchema } from './schemas/cooperation.schema';
import { CooperationPackagesSchema } from './schemas/cooperation-packages.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: COOPERATION, schema: CooperationsSchema },
            { name: COOPERATION_PACKAGES, schema: CooperationPackagesSchema },
        ]),
        UserModule,
        CskhModule,
    ],
    controllers: [CooperationController],
    providers: [CooperationService],
    exports: [CooperationService],
})
export class CooperationModule {}
