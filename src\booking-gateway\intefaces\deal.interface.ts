import { Document } from 'mongoose';

export interface IDeal extends Document {
    fullname: string;
    phone: string;
    partnerName: string;
    email: string;
    businessImage: string[];
    medicalLicenseImage: string[];
    invoiceInformation?: {
        invoiceRecipient: number;
        taxCode: string;
        cccd: string;
        unitName: string;
        fullname: string;
        countryCode: string;
        cityId: string;
        districtId: string;
        wardId: string;
        addresss: string;
        invoiceEmail: string;
    } 
    status: number;
    transactionId: string;
    payment: string;
    packageId: string;
    package: string;
    packagePriceId: string;
    packagePrice: string;
    typeCooperation: string;
    note: string;
}
