import { ErrorMessage } from './../common/enums/message-error.enum';
import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import { Method } from 'axios';
import * as moment from 'moment';

@Injectable()
export class ClientUtilService {
    constructor(private readonly http: HttpService) {}

    async get<T = any>(api: string, headers?: any): Promise<T> {
        let response: any;
        try {
            response = await this.http
                .get<T>(api, {
                    headers,
                })
                .toPromise();
        } catch (error) {
            console.error(`${moment().format("YYYY-MM-DD HH:mm:ss")} [ClientUtilService]  Error exec GET: `, error.toJSON());
            Logger.error(`Error when exec get()\nError: ${error.message || error?.response.data.message}`);
            const errorMessage = error?.response.data.message || ErrorMessage.BAD_REQUEST;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
        if (response.status < 200 || response.status >= 400 || !response?.data) {
            throw new HttpException(ErrorMessage.BAD_REQUEST, response.status);
        }
        return response.data;
    }

    async post<T = any>(api: string, formData?: any, headers?: any): Promise<T> {
        let response: any;
        try {
            response = await this.http
                .post<T>(api, formData, {
                    headers,
                })
                .toPromise();
        } catch (error) {
            console.error(`${moment().format("YYYY-MM-DD HH:mm:ss")} [ClientUtilService]  Error exec POST: `, error.toJSON());
            const errorMessage = error?.response?.data?.error?.message ||
                            error?.response?.data.message ||
                            error?.response?.message ||
                            error?.message ||
                            ErrorMessage.BAD_REQUEST;
            Logger.error(`Error when exec post()\nError: ${errorMessage}`);
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
        if (response.status < 200 || response.status >= 400 || !response?.data) {
            throw new HttpException(ErrorMessage.BAD_REQUEST, response.status);
        }
        return response.data;
    }

    async postNoContent<T = any>(api: string, formData?: any, headers?: any): Promise<void> {
        let response: any;
        try {
            response = await this.http
                .post<T>(api, formData, {
                    headers,
                })
                .toPromise();
        } catch (error) {
            console.error(`${moment().format("YYYY-MM-DD HH:mm:ss")} [ClientUtilService]  Error exec postNoContent: `, error.toJSON());
            const errorMessage = error?.response?.data.error?.message ||
                            error?.response?.data?.message ||
                            error?.response?.message ||
                            error?.message ||
                            ErrorMessage.BAD_REQUEST;
            Logger.error(`Error when exec post()\nError: ${errorMessage}`);
            const statusCode = error?.status || error?.statusCode || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
        if (response.status < 200 || response.status >= 400) {
            throw new HttpException(ErrorMessage.BAD_REQUEST, response.status);
        }
    }

    async put<T = any>(api: string, formData?: any, headers?: any): Promise<T> {
        let response: any;
        try {
            response = await this.http
                .put<T>(api, formData, {
                    headers,
                })
                .toPromise();
        } catch (error) {
            console.error(`${moment().format("YYYY-MM-DD HH:mm:ss")} [ClientUtilService] Error exec PUT: `, error.toJSON());
            Logger.error(`Error when exec put()\nError: ${error.message || error?.response.data.message}`);
            const errorMessage = error?.response.data.message || ErrorMessage.BAD_REQUEST;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
        if (response.status < 200 || response.status >= 400 || !response?.data) {
            throw new HttpException(ErrorMessage.BAD_REQUEST, response.status);
        }
        return response.data;
    }

    async delete<T = any>(api: string, headers?: any): Promise<T> {
        let response: any;
        try {
            response = await this.http
                .delete<T>(api, {
                    headers,
                })
                .toPromise();
        } catch (error) {
            console.error(`${moment().format("YYYY-MM-DD HH:mm:ss")} [ClientUtilService] Error exec DELETE: `, error.toJSON());
            Logger.error(`Error when exec delete()\nError: ${error.message || error?.response.data.message}`);
            const errorMessage = error?.response.data.message || ErrorMessage.BAD_REQUEST;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
        if (response.status < 200 || response.status >= 400 || !response?.data) {
            throw new HttpException(ErrorMessage.BAD_REQUEST, response.status);
        }
        return response.data;
    }

    async request<T = any>(method: Method, api: string, formData?: any, headers?: any): Promise<T> {
        let response: any;
        try {
            response = await axios.request({
                url: api,
                data: formData,
                method,
                headers
            });
        } catch (error) {
            console.error(`${moment().format("YYYY-MM-DD HH:mm:ss")} [ClientUtilService]: Error exec: `, error.toJSON());
            Logger.error(`Error when exec put()\nError: ${error.message || error?.response.data.message}`);
            const errorMessage = error?.response.data.message || ErrorMessage.BAD_REQUEST;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
        if (response.status < 200 || response.status >= 400) {
            throw new HttpException(ErrorMessage.BAD_REQUEST, response.status);
        }
        return response;
    }
}
