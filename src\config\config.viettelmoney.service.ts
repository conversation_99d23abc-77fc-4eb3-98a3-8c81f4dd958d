import { Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import { ConfigManager } from '@nestjsplus/config';
import * as <PERSON><PERSON> from 'joi';

@Injectable()
export class ViettelMoneyConfigService extends ConfigManager {
    provideConfigSpec() {
        return {};
    }

    getViettelMoneyPublicKey(): string {
        return this.get<string>('VIETTEL_MONEY_PUBLIC_KEY').replace(/\\n/g, '\n'); /* _______________ */;
    }

    createJwtOptions(): JwtModuleOptions {
        return {
            secret: this.get<string>('USER_VIETTEL_MONEY_JWT_SECRET'),
            signOptions: { expiresIn: this.get<string>('USER_VIETTEL_MONEY_JWT_EXPIRES_IN') },
        };
    }

}
