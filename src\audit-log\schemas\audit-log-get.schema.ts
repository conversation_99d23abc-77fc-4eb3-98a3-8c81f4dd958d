import { AUDIT_LOG_GET_NAME, AUDIT_LOG_NAME } from '../constant';
import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';

const Schema = mongoose.Schema;

export const AuditLogGetSchema = new Schema(
    {
        url: { type: String },
        method: { type: String },
        headers: { type: Schema.Types.Mixed },
        params: { type: Schema.Types.Mixed },
        body: { type: Schema.Types.Mixed },
        query: { type: Schema.Types.Mixed },
        nameRepo: { type: String },
    },
    {
        collection: AUDIT_LOG_GET_NAME,
        timestamps: true,
        versionKey: false,
    },
).plugin(jsonMongo);
