import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';

const Schema = mongoose.Schema;

export const AddOnServiceSchema = new Schema({
    id: {type: String},
    code: {type: String},
    name: {type: String},
    infoLine2: {type: Schema.Types.Mixed},
    shortName: {type: String},
    partnerId: {type: String},
    description: {type: String},
    price: {type: Number},
    serviceType: {type: String},
    isRequiredCheckInsurance: { type: Boolean, default: true },
    type: {type: String},
    priceText: { type: String },
}, {
    timestamps: true,
}).plugin(jsonMongo);
