import { Document } from 'mongoose';
import { IFeature } from '../../feature-mongo/interfaces/feature.interface';
import { IPartnerInFeature } from './partner-in-feature.inteface';
export interface IFeatureTranslate extends Document {
    locale: string;
    name: string;
    message: string;
}

export interface IFeatureInApp extends Document {
    isCashBack: boolean;
    contentCashBack: string;
    slug?: string;
    name: string;
    image: string;
    priority: number;
    status: boolean;
    mobileStatus: boolean;
    readonly type: string;
    message?: string;
    warningMessage?: string;
    disabled: boolean;
    parentId: string;
    children: IFeature[];
    mobileIcon: string;
    webRoute?: string;
    mobileRoute?: string;
    bookingLimit: number;
    localeName?: string;
    customUrl?: string;
    position?: string;
    displayIcon?: string;
    translate?: IFeatureTranslate[];
    warning_truoc_ngay?: string;
    // list partnerId
    partners: IPartnerInFeature[]
}
