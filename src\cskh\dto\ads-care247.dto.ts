import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class AdsCare247Dto {
    @ApiProperty({
        description: 'header',
        required: false,
        type: String,
    })
    header?: string;
    
    @ApiProperty({
        description: 'footer',
        required: false,
        type: String,
    })
    footer?: string;

    @ApiProperty({
        description: 'content',
        required: false,
        type: String,
    })
    content?: string;
}
