import { Injectable, HttpService, HttpException, HttpStatus, Logger } from '@nestjs/common';
import * as queryString from 'query-string';
import { ThuDucHospitalConfigService } from 'src/config/config.thuduc.hospital.service';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { PkhHttpService } from 'src/config/config.http.service';
import { ConfigSMSMedproService } from 'src/config/config.sms.medpro.service';
import * as strongSoap from 'strong-soap';
import { SendBookingSMSDTO } from 'src/booking-gateway/dto/send-booking-sms.dto';
import * as crypto from 'crypto';
import { v4 as uuid } from 'uuid';
import { SendSmsMessageHubDTO } from './dto/send-sms.dto';
import { ISms } from './interface/sms.interface';
import { IOtp } from './interface/otp.interface';
import { SendOTPMessageHubDTO } from './dto/send-otp.dto';
import { VerifyOTPDto } from './dto/verify-otp.dto';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { UtilService } from 'src/config/util.service';
import { get } from 'lodash';
import * as moment from 'moment';
import { InjectModel } from '@nestjs/mongoose';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { Model } from 'mongoose';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { REPO_NAME_BETA } from 'src/common/constants';

@Injectable()
export class SmsService {
    private readonly logger: Logger = new Logger(SmsService.name);
    private readonly messageHubUrl: string;
    private readonly messageHubClientId: string;
    private readonly messageHubShareKey: string;
    private readonly isActiveMessageHub: boolean;
    private readonly SMS_BOOKING_INFO_CSKH: string = 'SMS_BOOKING_INFO_CSKH';
    private readonly CSKH_DOMAIN: string = 'CSKH_DOMAIN';
    private readonly repoName: string;

    constructor(
        private readonly thuDucHospitalConfigService: ThuDucHospitalConfigService,
        private readonly smsMedproConfig: ConfigSMSMedproService,
        private readonly httpService2: HttpService,
        private readonly httpService: PkhHttpService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly eventEmitter: EventEmitter2,
        private readonly utilService: UtilService,
        private readonly repoService: ConfigRepoService,
        @InjectModel(BOOKING_COLLECTION_NAME) private readonly bookingModel: Model<IBooking>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private readonly partnerConfigModel: Model<IPartnerConfig>,
    ) {
        this.messageHubUrl = this.smsMedproConfig.getMessageHubUrl();
        this.messageHubClientId = this.smsMedproConfig.getMedproAdminClientId();
        this.messageHubShareKey = this.smsMedproConfig.getMedproAdminShareKey();
        this.isActiveMessageHub = this.smsMedproConfig.isActiveMessageHub();
        this.repoName = this.repoService.getRepoName();
    }

    sendSMSBrandNameThuDuc(phone: string, message: string): Observable<AxiosResponse<any>> {
        const { url, ...rest } = this.thuDucHospitalConfigService.getSMSBrandNameInfo();
        rest.phone = phone;
        rest.sms = message;
        const urlSMSBrandName = queryString.stringifyUrl({
            url,
            query: { ...rest },
        });
        return this.httpService2.get(urlSMSBrandName);
    }

    async getMessageInfoSms(bookingCode: string): Promise<string> {
        try {
            let [switchFormat, templateSms, booking] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CSKH_SWITCH_FORMAT_SMS'),
                this.globalSettingService.findByKeyAndRepoName('CSKH_SEND_BOOKING_SMS'),
                this.bookingModel
                    .findOne(
                        { $or: [{ bookingCode }, { bookingCodeV1: bookingCode }] },
                        {
                            bookingCode: true,
                            smsCode: true,
                            date: true,
                            sequenceNumber: true,
                            partnerId: true,
                            bookingOrder: true,
                            bookingOrderId: true,
                            transactionId: true,
                        },
                    )
                    .populate({ path: 'partner', select: { sms_name: true } })
                    .exec(),
            ]);
            const hospitalName = get(booking?.partner, 'sms_name', '');
            const setReponame = new Set(['api-v2-111', 'api-v2-beta-kaka']);
            switchFormat = setReponame.has(this.repoName) ? 'ON' : switchFormat;

            if (switchFormat === 'ON') {
                //case nhieu phieu
                if (booking?.bookingOrder || booking?.bookingOrderId) {
                    return this.getTempalteSmsForMultipleBooking(booking);
                }

                // override if have partner config
                const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: booking.partnerId }, { templateSmsMessage: true }).exec();
                if (partnerConfig?.templateSmsMessage) {
                    templateSms =  partnerConfig?.templateSmsMessage || templateSms;

                    const getBookingDate = get(booking, 'date', '');
                    
                    const bookingDate = moment(getBookingDate).isValid() ? moment(getBookingDate).utc().add(7, 'hours').format('HH:mm-DD/MM') : '';

                    const message = templateSms
                                .replace('{bookingDate}', bookingDate)
                                .replace('{hospitalName}', hospitalName)
                                .replace('{sequenceNumber}', booking?.sequenceNumber ? `STT: ${booking?.sequenceNumber}.` : '')
                                .replace('{bookingCode}', booking?.smsCode || booking.bookingCode);
                    return message;
                } else {
                    return this.getSmsMessageTemplateDefault(booking, hospitalName);
                }
            } else {
                return this.getSmsMessageTemplateDefault(booking, hospitalName);
            }
        } catch (error) {
            this.logger.error(`Error when exec getMessageInfoSms(${bookingCode}). Cause: ${error.message}`);
            throw error;
        }
    }

    async sendBookingSMS(formData: SendBookingSMSDTO): Promise<any> {
        // return strongSoap.soap.createClient('https://api.aztech.com.vn/api/agentSmsApiSoap?wsdl', {}, function (err, client) {
        //     const method = client.sendSms;
        //     method({
        //         // authenticateUser: 'pkh',
        //         // authenticatePass: 'pkh@2020',
        //         authenticateUser: 'pkh1',
        //         authenticatePass: 'pkh1@api',
        //         brandName: 'Medpro',
        //         message: '123456 la ma xac thuc tai khoan BV Medpro cua ban tren Medpro. Ma nay chi co hieu luc trong 2 phut.',
        //         // message: `Vui long truy cap https://cs.medpro.com.vn/booking/${bookingCode} de xem chi tiet phieu kham.`,
        //         receiver: '0845469282',
        //         type: 1,
        //     }, function (err, result, envelope, soapHeader) {
        //         //response envelope
        //         console.log('Response Envelope: \n' + envelope);
        //         //'result' is the response body
        //         console.log('Result: \n' + JSON.stringify(result));
        //     });
        // });
        const message = await this.getMessageInfoSms(formData.bookingCode);
        if (!this.isActiveMessageHub) {
            return strongSoap.soap.createClient('https://api.aztech.com.vn/api/agentSmsApiSoap?wsdl', {}, function(err, client) {
                const method = client.sendSms;
                // console.log(formData);
                const objParams = {
                    authenticateUser: 'pkh1',
                    authenticatePass: 'pkh1@api',
                    // authenticateUser: 'pkh',
                    // authenticatePass: 'pkh@2020',
                    brandName: 'Medpro',
                    message,
                    receiver: formData.mobile,
                    type: 1,
                };
                // console.log('obj Prams', objParams);
                method(objParams, function(err, result, envelope, soapHeader) {
                    // response envelope
                    console.log('Response Envelope: \n' + envelope);
                    // 'result' is the response body
                    console.log('Result: \n' + JSON.stringify(result));
                });
            });
        }

        const data = {
            receiver: formData.mobile,
            message,
            partnerId: 'medpro',
            messageType: 'TEXT',
        };
        return this.sendSmsMessageHub(data);
    }

    async sendSmsMedpro(phone: string, bookingCode: string = ''): Promise<any> {
        // return strongSoap.createClientAsync('https://api.aztech.com.vn/api/agentSmsApiSoap?wsdl').then((client) => {
        //     const method = client.sendSms;
        //     try {
        //         return method({
        //             authenticateUser: 'pkh',
        //             authenticatePass: 'pkh@2020',
        //             brandName: 'Medpro',
        //             message: '123456 la ma xac thuc tai khoan BV Medpro cua ban tren Medpro. Ma nay chi co hieu luc trong 2 phut.',
        //             receiver: phone,
        //             type: 1,
        //         });
        //     } catch (error) {
        //         console.log(error);
        //     }

        // }).catch(error => {
        //     console.log(error);
        // });
        const message = await this.getMessageInfoSms(bookingCode);
        if (!this.isActiveMessageHub) {
            return strongSoap.soap.createClient('https://api.aztech.com.vn/api/agentSmsApiSoap?wsdl', {}, function(err, client) {
                const method = client.sendSms;
                method(
                    {
                        authenticateUser: 'pkh',
                        authenticatePass: 'pkh@2020',
                        brandName: 'Medpro',
                        // message: '123456 la ma xac thuc tai khoan BV Medpro cua ban tren Medpro. Ma nay chi co hieu luc trong 2 phut.',
                        message,
                        receiver: phone,
                        type: 1,
                    },
                    function(err, result, envelope, soapHeader) {
                        // response envelope
                        console.log('Response Envelope: \n' + envelope);
                        // 'result' is the response body
                        console.log('Result: \n' + JSON.stringify(result));
                    },
                );
            });
        }

        const data = {
            receiver: phone,
            message,
            partnerId: 'medpro',
            messageType: 'TEXT',
        };
        return this.sendSmsMessageHub(data);
    }

    async messageHubSmsSignKey(clientId: string, receiver: string, messageId: string): Promise<string> {
        const message = `${clientId}|${receiver}|${messageId}`;
        const hash = crypto
            .createHmac('sha256', this.messageHubShareKey)
            .update(message)
            .digest('hex');
        return hash;
    }

    async messageHubOtpSignKey(partnerId: string, appId: string, phone: string): Promise<string> {
        const message = `${partnerId}|${appId}|${phone}`;
        const hash = crypto
            .createHmac('sha256', this.messageHubShareKey)
            .update(message)
            .digest('hex');
        return hash;
    }

    async sendSmsMessageHub(formData: SendSmsMessageHubDTO): Promise<any> {
        const { receiver, partnerId } = formData;
        const clientId = this.messageHubClientId;
        const messageId = uuid().replace(/-/g, '');
        try {
            const signKey = await this.messageHubSmsSignKey(clientId, receiver, messageId);
            const smsData: ISms = { ...formData, signKey, messageId, clientId, deviceId: '' };
            return (await this.callSendSmsMessageHub(smsData, partnerId).toPromise()).data;
        } catch (error) {
            const errorData = error?.response?.data || `Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!`;
            const statusCode = error?.response.status || HttpStatus.BAD_REQUEST;
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callSendSmsMessageHub',
                summary: 'Send SMS',
                nameParent: 'sendSmsMessageHub',
                params: { ...formData },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: error?.message || 'Lỗi từ MessageHub',
            });
            this.logger.error(`Error when exec sendSmsMessageHub(). Cause: ${error.message}`);
            throw new HttpException(errorData, statusCode);
        }
    }

    callSendSmsMessageHub(smsData: ISms, partnerId: string) {
        const baseUrl = `${this.messageHubUrl}/sms/send/${partnerId}`;
        return this.httpService.postHttpRequest(baseUrl, { ...smsData });
    }

    async sendOTPMessageHub(formData: SendOTPMessageHubDTO): Promise<any> {
        const { phone, partnerId, appId } = formData;
        const clientId = this.messageHubClientId;
        try {
            const signKey = await this.messageHubSmsSignKey(partnerId, appId, phone);
            const smsData: IOtp = { ...formData, signKey, clientId };
            const configSMSBlacklistPhone = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');
            const splitPhone = new Set(configSMSBlacklistPhone.split(','));
            if(splitPhone.has(phone)){
                throw new HttpException(`Không hỗ trợ số điện thoại này ${phone}`, HttpStatus.FORBIDDEN)
            }
            return (await this.callSendOTPMessageHub(smsData).toPromise()).data;
        } catch (error) {
            const errorData = error?.response?.data || `Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!`;
            const statusCode = error?.response.status || HttpStatus.BAD_REQUEST;
            this.logger.error(`Error when exec sendOTPMessageHub(). Cause: ${error.message}`);
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callSendOTPMessageHub',
                summary: 'Send OTP',
                nameParent: 'sendOTPMessageHub',
                params: { ...formData },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: error?.message || 'Lỗi từ MessageHub',
            });
            throw new HttpException(errorData, statusCode);
        }
    }

    callSendOTPMessageHub(smsOtp: IOtp) {
        const baseUrl = `${this.messageHubUrl}/otp/sms`;
        return this.httpService.postHttpRequest(baseUrl, { ...smsOtp });
    }

    async verifyOTP(formData: VerifyOTPDto): Promise<any> {
        try {
            return (await this.callOTPVerify(formData).toPromise()).data;
        } catch (error) {
            const errorData = error?.response?.data || `Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!`;
            const statusCode = error?.response.status || HttpStatus.BAD_REQUEST;
            this.logger.error(`Error when exec sendOTPMessageHub(). Cause: ${error.message}`);
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callOTPVerify',
                summary: 'Verify OTP',
                nameParent: 'verifyOTP',
                params: { ...formData },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: error?.message || 'Lỗi từ MessageHub',
            });
            throw new HttpException(errorData, statusCode);
        }
    }

    callOTPVerify(formData: VerifyOTPDto) {
        const baseUrl = `${this.messageHubUrl}/otp/verify`;
        return this.httpService.postHttpRequest(baseUrl, { ...formData });
    }

    async getSmsMessageTemplateDefault(booking: any, hospitalName = ''): Promise<any> {
        if (booking?.bookingOrder || booking?.bookingOrderId) {
            return this.getTempalteSmsForMultipleBooking(booking);
        }
        let [messageGlobal, cskhDomain] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName(this.SMS_BOOKING_INFO_CSKH),
            this.globalSettingService.findByKeyAndRepoName(this.CSKH_DOMAIN),
        ])
        /* lấy thông tin partnerId */
        // if(REPO_NAME_BETA.includes(this.repoName)){
        let [messageGlobalBeta, cskhDomainBeta, messageGlobalBetaV2, onOffMessageGlobalBeta] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('SMS_BOOKING_INFO_CSKH_BETA'),
            this.globalSettingService.findByKeyAndRepoName('CSKH_DOMAIN_BETA'),
            this.globalSettingService.findByKeyAndRepoName('SMS_BOOKING_INFO_CSKH_BETA_V2'),
            this.globalSettingService.findByKeyAndRepoName('ON_OFF_SMS_BOOKING_INFO_CSKH_BETA_V2'),
        ])
        if (onOffMessageGlobalBeta === 'ON') {
            messageGlobalBeta = messageGlobalBetaV2
        }
        if (messageGlobalBeta) {
            messageGlobalBeta = messageGlobalBeta.replace('{bookingCode}', (booking?.smsCode || booking.bookingCode));
        }
        if (cskhDomainBeta) {
            messageGlobalBeta = messageGlobalBeta.replace('{cskhDomain}', cskhDomainBeta);
        }
        if (hospitalName) {
            messageGlobalBeta = messageGlobalBeta.replace('{hospitalName}', hospitalName);
        }
        return messageGlobalBeta;
        // }

        if (messageGlobal) {
            return messageGlobal.replace('{bookingCode}', (booking?.smsCode || booking.bookingCode));
        }
        if (!cskhDomain) {
            cskhDomain = 'https://cs.medpro.com.vn/booking';
        }
        return `Vui long truy cap ${cskhDomain}/${booking?.smsCode || booking.bookingCode} de xem chi tiet phieu kham.`;
    }

    async getTempalteSmsForMultipleBooking(booking: any): Promise<string> {
        const configMultiBookingTemplate = await this.globalSettingService.findByKeyAndRepoName('SMS_MULTIPLE_BOOKINGS');

        const message = configMultiBookingTemplate
                    .replace('${transactionId}', booking?.transactionId)

        return message;
    }
}
