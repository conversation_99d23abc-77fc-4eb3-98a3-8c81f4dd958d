import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { RetrySyncBookingDTO } from './retry-sync-booking.dto';

export class RetrySyncBookingDTOV2 extends RetrySyncBookingDTO {

    @ApiProperty({
        description: 'pass code',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng nhập PassCode',
    })
    passCode: string;
}
