import { Injectable, Inject, HttpStatus, HttpException, Logger } from '@nestjs/common';
import { CreatePartnerConfigDTO } from './dto/create-partner-config.dto';
import { PARTNER_CONFIG_COLLECTION_NAME, POPUP_COLLECTION_NAME } from './schemas/constants';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IPartnerConfig } from './intefaces/partner-config.inteface';
import { get, last, first } from 'lodash';
import { v4 as uuid } from 'uuid';
import { UpdatePartnerConfigDTO } from './dto/update-partner-config.dto';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { AddFiltersDTO } from './dto/add-filters.dto';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { FirebaseConfigService } from 'src/firebase-config/firebase-config.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { IGlobalSetting } from 'src/global-setting/interfaces/global-setting.interface';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { ISubject } from 'src/subject-mongo/interfaces/subject.interface';
import { HeadersDto } from './dto/headers.dto';
import { UtilService } from 'src/config/util.service';
import * as moment from 'moment';
import { IPopup } from './intefaces/popup.interface';
import { REPO_NAME_BETA } from '../common/constants';
import { ConfigRepoService } from '../config/config.repo.service';

@Injectable()
export class PartnerConfigService {
    private logger = new Logger(PartnerConfigService.name);
    private bookingTableName = 'booking';
    private readonly WARNING_REGISTER_MESSAGE: string = 'WARNING_REGISTER_MESSAGE';
    repoName: string

    constructor(
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private subjectModel: Model<ISubject>,
        @InjectModel(POPUP_COLLECTION_NAME) private popupModel: Model<IPopup>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly utilService: UtilService,
        private readonly firebaseConfigService: FirebaseConfigService,
        private readonly globalService: GlobalSettingService,
        private readonly repoConfigService: ConfigRepoService,
    ) {
        this.repoName = this.repoConfigService.getRepoName();
    }

    // async getPopup(headers: HeadersDto) {
    //     const { appid: appId, platform = 'web', locale = 'vi' } = headers;

    //     if (!!locale && locale !== 'vi') {
    //         throw new HttpException({ message: `Chưa hỗ trợ locale ${locale}` }, HttpStatus.BAD_REQUEST);
    //     }

    //     const platformType = this.utilService.getPlatformType(platform);

    //     const now = moment();

    //     const condition: any = {
    //         appId,
    //         // status: 1,
    //         startDate: { $lte: now.toDate() },
    //         endDate: { $gt: now.toDate() },
    //         platform: platformType,
    //     };

    //     if (!REPO_NAME_BETA.includes(this.repoName)) {
    //         condition.status = 1;
    //     }

    //     const popups = await this.popupModel
    //         .find(condition)
    //         .sort({ updatedAt: -1 })
    //         .limit(1)
    //         .exec();

    //     const popup = first(popups);
    //     if (popup) {
    //         return popup.toObject();
    //     } else {
    //         throw new HttpException({ message: 'Không tìm thấy thông tin!', data: { appId, platform } }, HttpStatus.NOT_FOUND);
    //     }
    // }

    private checkVersion({
        headers: { version },
        supportedVersion = 14,
    }:{
        headers: HeadersDto,
        supportedVersion?: number
    }): boolean {
        let checkVersion = false;
        if (version && `${version}`.trim() != '') {
            // console.log('version', version);
            const splitVersion = `${version}`.trim().split('.');
            // console.log('splitVersion', splitVersion)
            if (splitVersion.length > 0) {
                // console.log('splitVersion.length', splitVersion.length)
                const [firstV, secondV] = splitVersion;
                // console.log('firstV', firstV)
                // console.log('secondV', secondV)
                const arrayJon = [firstV, secondV];
                const joinVersionNumber = Number(arrayJon.join('')) || 0;
                if (joinVersionNumber > supportedVersion) {
                    checkVersion = true
                }
            }
        }

        return checkVersion;
    }

    async getPopup(headers: HeadersDto) {
        const { appid: appId, platform = 'web', locale = 'vi' } = headers;

        let isON = await this.globalService.findByKeyAndRepoName("ENABLE_CASH_BACK_CASH_BACK_POPUP")

        if (REPO_NAME_BETA.includes(this.repoConfigService.getRepoName())) {
            isON = 'ON';
        }

        if(isON === "OFF"){
            throw new HttpException({ message: 'Không tìm thấy thông tin!', data: { appId: appId, platform: platform } }, HttpStatus.NOT_FOUND);
        }

        // if(platform === "pc" || platform === "web"){
        //     throw new HttpException({ message: 'Không tìm thấy thông tin!', data: { appId: appId, platform: platform } }, HttpStatus.NOT_FOUND);
        // }

        const isRequestByMobile = ['ios', 'android'].includes(platform.toLowerCase());
        const byMedpro = appId?.toUpperCase() === 'MEDPRO';
        const isSupportedVersion = this.checkVersion({
            headers: headers,
            supportedVersion: 14,
        });

        if(isRequestByMobile && (!isSupportedVersion || !byMedpro)){
            throw new HttpException({ message: 'Không tìm thấy thông tin!', data: { appId: appId, platform: platform } }, HttpStatus.NOT_FOUND);
        }

        const _popup = await this.globalService.findByKeyAndRepoName(`DEFAULT_POPUP_${this.repoConfigService.getRepoName()}`);

        if(isRequestByMobile && isSupportedVersion && byMedpro){
            return JSON.parse(_popup)
        }

        return JSON.parse(_popup);

        if (!!locale && locale !== 'vi') {
            throw new HttpException({ message: `Chưa hỗ trợ locale ${locale}` }, HttpStatus.BAD_REQUEST);
        }

        const platformType = this.utilService.getPlatformType(platform);

        const now = moment();

        const condition: any = {
            appId,
            // status: 1,
            startDate: { $lte: now.toDate() },
            endDate: { $gt: now.toDate() },
            platform: platformType,
        };

        if (!REPO_NAME_BETA.includes(this.repoName)) {
            condition.status = 1;
        }

        const popups = await this.popupModel
            .find(condition)
            .sort({ updatedAt: -1 })
            .limit(1)
            .exec();

        const popup = first(popups);
        if (popup) {
            return popup.toObject();
        } else {
            throw new HttpException({ message: 'Không tìm thấy thông tin!', data: { appId, platform } }, HttpStatus.NOT_FOUND);
        }
    }

    async getExtraConfig(partnerId: string): Promise<any> {
        let partnerConfig = await this.partnerConfigModel
            .findOne(
                { partnerId },
                {
                    isVatInvoice: true,
                    reserveMulti: true,
                    shareToPay: true,
                    bookingLimit: true,
                    quet_bhyt_btn: true,
                    da_tung_khamt_btn: true,
                    tao_ho_so_moi_btn: true,
                    tim_ho_so_mo_rong_btn: true,
                    isConfirmDialog: true,
                    ads: true,
                    qrCodeShareToPay: true,
                    isSearchExamInput: true,
                    isSaveExamPatient: true,
                    joinChat: true,
                    joinVideo: true,
                    timeAllowMessage: true,
                    popUpProfile: true,
                    warningRegister: true,
                    warning_trong_ngay: true,
                    warning_truoc_ngay: true,
                    reExamsGreeting: true,
                    reExamsBenefic: true,
                    patientYearOldAccepted: true,
                    reExamsDownloadIntro: true,
                    reExamsDownloadImageApp: true,
                    templateQrCodeContent: true,
                    cskh: true,
                    yearOldAccepted: true,
                    templateSmsMessage: true,
                    qrCodeConfig: true,
                    agreement: true,
                    isConfigNews: true,
                    footerSupport: true,
                    countdown: true,
                    choosePatientStep: true,
                    findPatientCodeImg: true,
                    socialConfig: true,
                    gtag: true,
                    BHYTOptions:true
                },
            )
            .exec();
        partnerConfig = partnerConfig.toObject()
        if (!partnerConfig) {
            throw new HttpException('Không tìm thấy thông tin partner config', HttpStatus.NOT_FOUND);
        }
        const {
            isVatInvoice = false,
            reserveMulti = false,
            shareToPay = false,
            bookingLimit = 1,
            quet_bhyt_btn = true,
            da_tung_khamt_btn = true,
            tao_ho_so_moi_btn = true,
            tim_ho_so_mo_rong_btn = true,
            isConfirmDialog = true,
            ads = {},
            qrCodeShareToPay,
            isSearchExamInput = false,
            isSaveExamPatient = false,
            joinChat = 120,
            joinVideo = 120,
            timeAllowMessage = '',
            popUpProfile = false,
            warningRegister = '',
            warning_truoc_ngay = '',
            reExamsDownloadIntro = '',
            reExamsDownloadImageApp = '',
            templateQrCodeContent = '',
            cskh = [],
            yearOldAccepted = {},
            templateSmsMessage = '',
            qrCodeConfig = {},
            agreement = '',
            isConfigNews,
            countdown = 60,
            choosePatientStep = true,
            findPatientCodeImg,
            socialConfig,
            patientYearOldAccepted,
            gtag,
            BHYTOptions
        } = partnerConfig;
        const [bymedpro, bymedprowhite, hospital, partnerValidateAgeStr, gtmMessage,refundNote, dataComplaint, adsBannerConfig, carePartnerList] = await Promise.all([
            this.globalService.findByKeyAndRepoName('BY_MEDPRO'),
            this.globalService.findByKeyAndRepoName('BY_MEDPRO_WHITE'),
            this.hospitalModel.findOne({ partnerId }),
            this.globalService.findByKeyAndRepoName('PARTNER_VALIDATE_PATIENT_AGE'),
            this.globalService.findByKeyAndRepoName('GTM_MESSAGE'),
            this.globalService.findByKeyAndRepoName('REFUND_NOTE'),
            this.globalService.findByKeyAndRepoName('DATA_COMPLAINT_CONFIG'),
            this.globalService.findByKeyAndRepoName('ADS_BANNER_CONFIG'),
            this.globalService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
        ]);

        const warningRegisterMessage = warningRegister || (await this.getWarningRegisterMessage());

        let warningTruocNgayMessage = warning_truoc_ngay || (await this.globalService.findByKeyAndRepoName('WARNING_TRUOC_NGAY'));
        warningTruocNgayMessage = warningTruocNgayMessage.replace('{hospiltalname}', hospital.name);

        let patientValidateAge: any;
        const partnerHaveAgeConfig = await this.getPartnerConfigAgeLimit(partnerId);
        if (partnerId === 'umc') {
            const [subjectAgeLimits, subjectNhiWarning, subject] = await Promise.all([
                this.globalService.findByKeyAndRepoName('UMC_AGE_LIMIT_WITH_EXAM'),
                this.globalService.findByKeyAndRepoName('UMC_AGE_LT_NHI_MESSAGE'),
                this.subjectModel.findOne({ id: 'umc_ND' }),
            ]);
            const subjectAgeLimitObjs = JSON.parse(subjectAgeLimits);

            patientValidateAge = {
                subjectAgeLimits: subjectAgeLimitObjs,
                warningPatientLessAge: subjectNhiWarning.replace('{SUBJECT}', subject.name),
            };
        } else if (partnerHaveAgeConfig) {
            const { patientYearOldAccepted } = partnerConfig;
            let warningMessage = await this.globalService.findByKeyAndRepoName('WARNING_MESSAGE');
            warningMessage = warningMessage.replace('{PARTNER}', hospital.name) || '';
            patientValidateAge = {
                patientYear: patientYearOldAccepted,
                condition: 'gt',
                warningMessage,
            };
        }

        let reExamsGreeting = partnerConfig?.reExamsGreeting || (await this.globalService.findByKeyAndRepoName('REEXAMS_GREETING'));
        reExamsGreeting = reExamsGreeting.replace('{PARTNER}', hospital.name);

        const reExamsBenefic = partnerConfig?.reExamsBenefic || (await this.globalService.findByKeyAndRepoName('REEXAMS_BENEFIC'));

        // case trả treeId cho partner
        let treeId: any;
        const features = hospital.toObject().features;
        for (const feature of features) {
            const strKeys = feature.type.split('.');
            if (`${first(strKeys)}` === 'booking') {
                const keyTreeId = last(strKeys);
                const rowTreeBookingLimit: Record<string, object> = {
                    [`${keyTreeId}`]: {
                        bookingLimit: feature.bookingLimit,
                    },
                };
                treeId = { ...treeId, ...rowTreeBookingLimit };
            }
        }

        let ovverideagreement = !agreement ? await this.globalService.findByKeyAndRepoName('AGREEMENT_CONFIG_PARTNER') : agreement;
        ovverideagreement = ovverideagreement.replace('{hospitalName}', hospital.name);

        // let overrideSearchHSC = da_tung_khamt_btn;
        // if (partnerId === 'nhidong1') {
        //     overrideSearchHSC = true;
        // }
        const carePartnerListObj = new Set(carePartnerList?.split(','));
        const adsBannerConfigObj =
        carePartnerListObj.has(partnerId) && adsBannerConfig ? JSON.parse(adsBannerConfig)?.care247 : JSON.parse(adsBannerConfig)?.default;
        const dataRefund = refundNote ? JSON.parse(refundNote) : undefined;
        return {
            partnerId,
            isVatInvoice,
            reserveMulti,
            shareToPay,
            bookingLimit,
            quet_bhyt_btn,
            da_tung_khamt_btn,
            tao_ho_so_moi_btn,
            tim_ho_so_mo_rong_btn,
            isConfirmDialog,
            ads,
            qrCodeShareToPay,
            isSearchExamInput,
            isSaveExamPatient,
            joinChat,
            joinVideo,
            timeAllowMessage,
            popUpProfile,
            bymedpro,
            bymedprowhite,
            warningRegister: warningRegisterMessage,
            warning_truoc_ngay: warningTruocNgayMessage,
            patientValidateAge,
            reExamsGreeting,
            reExamsBenefic,
            treeId,
            reExamsDownloadIntro,
            reExamsDownloadImageApp,
            templateQrCodeContent,
            cskh,
            yearOldAccepted,
            templateSmsMessage,
            qrCodeConfig,
            agreement: ovverideagreement,
            isConfigNews,
            footerSupport: partnerConfig?.footerSupport,
            countdown,
            gtmMessage,
            refundNote: dataRefund,
            choosePatientStep,
            hospitalV1s: ['nhidong1', 'umc'],
            discountUMCGroup: ['umc', 'umc2', 'umc3'],
            findPatientCodeImg,
            socialConfig,
            patientYearOldAccepted,
            gtag,
            BHYTOptions,
            dataComplaint:dataComplaint ? JSON.parse(dataComplaint) : undefined,
            adsBannerConfig: adsBannerConfigObj
            // ...(partnerId === 'umc' && { choosePatientStep: true }),
        };
    }

    async getWarningRegisterMessage(): Promise<string> {
        try {
            return this.globalService.findByKeyAndRepoName(this.WARNING_REGISTER_MESSAGE);
        } catch (error) {
            this.logger.error(`Error when exec getWarningRegisterMessage. Cause: ${error.message}`);
            throw error;
        }
    }

    async isVatInvoice(partnerId: string): Promise<any> {
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!partnerConfig) {
            return {
                isVatInvoice: false,
                partnerId,
                message: 'Không tìm thấy thông tin cấu hình bật/tắt thông tin hóa đơn.',
            };
        }
        const { isVatInvoice = false } = partnerConfig;
        return {
            isVatInvoice,
            partnerId,
        };
    }

    async addConstraints(partnerId: string, formData: AddFiltersDTO): Promise<any> {
        const getPartner = await this.partnerConfigModel
            .findOne(
                {
                    partnerId,
                },
                { extra: true },
            )
            .exec();
        if (!getPartner) {
            throw new HttpException('Chưa có config cho partner này.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin extra */
        const partnerObj = getPartner.toObject();
        const getExtra = get(partnerObj, 'extra', {});

        getExtra.constraints = formData.constraints;

        return this.partnerConfigModel
            .findOneAndUpdate(
                {
                    partnerId,
                },
                {
                    extra: getExtra,
                },
                { new: true },
            )
            .exec();
    }

    async updateMaxId(partnerId: string): Promise<any> {
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (partnerConfig) {
            /* tiến hành lấy thông tin từ bookingg */
            const booking = await this.pkhPatientKnex(this.bookingTableName)
                .max('id', { as: 'maxId' })
                .first();
            if (booking) {
                /* cập nhật vào trong partner config */
                return this.partnerConfigModel.findByIdAndUpdate({ _id: partnerConfig._id }, { maxId: booking.maxId }, { new: true }).exec();
            }
        }
    }

    async isExistPartner(partnerId: string): Promise<boolean> {
        try {
            const partner = await this.hospitalModel.findOne({ partnerId }).exec();
            return partner ? true : false;
        } catch (error) {
            this.logger.error(`Error when exec isExistPartner() with partnerId: ${partnerId}\nError: ${error.message}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async isExistPartnerConfig(partnerId: string): Promise<boolean> {
        try {
            return this.partnerConfigModel.exists({ partnerId });
        } catch (error) {
            this.logger.error(`Error when exec isExistPartnerConfig() with partnerId: ${partnerId}\nError: ${error.message}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async list(): Promise<IPartnerConfig[]> {
        return this.partnerConfigModel.find({}).exec();
    }

    async create(formData: CreatePartnerConfigDTO): Promise<IPartnerConfig> {
        const isExistPartner = await this.isExistPartner(formData.partnerId);
        const isExistPartnerConfig = await this.isExistPartnerConfig(formData.partnerId);
        if (!isExistPartner) {
            throw new HttpException(`Partner with partnerId: ${formData.partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (isExistPartnerConfig) {
            throw new HttpException(`PartnerConfig with partnerId: ${formData.partnerId} already exist`, HttpStatus.BAD_REQUEST);
        }
        try {
            const { formData: createData, rules } = await this.handlePartnerConfigForCreate(formData);
            const partnerConfig = new this.partnerConfigModel(createData);
            partnerConfig.bookingRules = rules;
            return (await partnerConfig.save()).toJSON();
        } catch (error) {
            this.logger.error(`Error when exec create() for partnerId: ${formData.partnerId}\nError: ${error.message}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async update(formData: UpdatePartnerConfigDTO): Promise<any> {
        const isExistPartnerConfig = await this.isExistPartnerConfig(formData.partnerId);
        if (!isExistPartnerConfig) {
            throw new HttpException(`PartnerConfig with partnerId: ${formData.partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        try {
            const { ...updateData } = await this.handlePartnerConfigForUpdate(formData);
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: formData.partnerId }).exec();
            partnerConfig.set(updateData);
            if (formData.firebaseId) {
                const firebase = await this.firebaseConfigService.findById(formData.firebaseId);
                partnerConfig.firebase = firebase.id;
            }
            return await partnerConfig.save();
        } catch (error) {
            this.logger.error(`Error when exec update() for partnerId: ${formData.partnerId}\nError: ${error.message}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async handlePartnerConfigForCreate(formData: CreatePartnerConfigDTO): Promise<any> {
        const { bookingReminder, bookingBeforeTest, bookingBHYT } = formData;
        let rules: string[] = [];
        if (bookingReminder) {
            rules = [...rules, 'bookingReminder'];
        } else if (bookingBeforeTest) {
            rules = [...rules, 'bookingBeforeTest'];
        } else if (bookingBHYT) {
            rules = [...rules, 'bookingBHYT'];
        }
        return { formData, rules };
    }

    async handlePartnerConfigForUpdate(formData: UpdatePartnerConfigDTO): Promise<any> {
        const { partnerId, bookingReminder, bookingBeforeTest, bookingBHYT, ...updateData } = formData;
        let bookingRules = (await this.partnerConfigModel.findOne({ partnerId }).exec()).bookingRules;
        const isExistConfig = new Set(bookingRules);
        if (bookingReminder && !isExistConfig.has('bookingReminder')) {
            bookingRules = [...bookingRules, 'bookingReminder'];
        } else if (bookingBeforeTest && !isExistConfig.has('bookingBeforeTest')) {
            bookingRules = [...bookingRules, 'bookingBeforeTest'];
        } else if (bookingBHYT && !isExistConfig.has('bookingBHYT')) {
            bookingRules = [...bookingRules, 'bookingBHYT'];
        } else if (bookingReminder === false && isExistConfig.has('bookingReminder')) {
            const index = bookingRules.indexOf('bookingReminder');
            bookingRules.splice(index, 1);
        } else if (bookingBeforeTest === false && isExistConfig.has('bookingBeforeTest')) {
            const index = bookingRules.indexOf('bookingBeforeTest');
            bookingRules.splice(index, 1);
        } else if (bookingBHYT === false && isExistConfig.has('bookingBHYT')) {
            const index = bookingRules.indexOf('bookingBHYT');
            bookingRules.splice(index, 1);
        }
        return { ...updateData, formData, bookingRules };
    }

    async delete(partnerId: string): Promise<any> {
        try {
            const isExistPartnerConfig = await this.isExistPartnerConfig(partnerId);
            if (!isExistPartnerConfig) {
                throw new HttpException(`PartnerConfig with partnerId: ${partnerId} does not exist`, HttpStatus.NOT_FOUND);
            }
            return this.partnerConfigModel.findOneAndRemove({ partnerId }).exec();
        } catch (error) {
            this.logger.error(`Error delete() with partnerId: ${partnerId}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getPartnerConfigByPartnerId(partnerId: string): Promise<IPartnerConfig> {
        try {
            const partnerConfigInfo = await this.partnerConfigModel.findOne({ partnerId }).exec();
            if (!partnerConfigInfo) {
                throw new HttpException(`PartnerConfig with partnerId: ${partnerId} does not exist`, HttpStatus.NOT_FOUND);
            }
            return partnerConfigInfo;
        } catch (err) {
            this.logger.error(`Error getPartnerConfigByPartnerId() with partnerId: ${partnerId}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async seedData(): Promise<any> {
        try {
            return this.partnerConfigModel
                .updateMany({ $or: [{ bookingRules: '' }, { bookingRules: ' ' }, { bookingRules: null }] }, { bookingRules: [] })
                .exec();
        } catch (error) {
            this.logger.error(`Error when exec seedData()\nError: ${error.message}`);
            throw new HttpException(`Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`, HttpStatus.BAD_REQUEST);
        }
    }

    async seedPartnerReferral(partnerId: string): Promise<any> {
        const data = [
            { partnerId, code: uuid().replace(/-g/, ''), name: 'Chọn nơi giới thiệu' },
            { partnerId, code: uuid().replace(/-g/, ''), name: 'BV Trưng Vương' },
            { partnerId, code: uuid().replace(/-g/, ''), name: 'BV Chợ Rẫy' },
            { partnerId, code: uuid().replace(/-g/, ''), name: 'BV Ung Bướu' },
            { partnerId, code: uuid().replace(/-g/, ''), name: 'BV Nhi Đồng TP HCM' },
            { partnerId, code: uuid().replace(/-g/, ''), name: 'BV ĐH Y Dược' },
            { partnerId, code: uuid().replace(/-g/, ''), name: 'BV ĐK Vạn Hạnh' },
        ];
        return data;
    }

    async getPartnerConfigAgeLimit(partnerId: string): Promise<any> {
        try {
            const ageLimitConfig = await this.globalService.findByKeyAndRepoName('AGE_LIMIT_CONFIG');
            const partnerList = new Set([...ageLimitConfig.split(',')]);
            return partnerList.has(partnerId);
        } catch (error) {
            this.logger.error(`Error when exec getPartnerConfigAgeLimit for partnerId: ${partnerId}. Error: ${error.message}`);
            throw error;
        }
    }

    async getPartnerHaveHisConnector(): Promise<string[]> {
        const list = await this.partnerConfigModel.find({ da_tung_khamt_btn: true }).exec();
        return list.map(element => element.partnerId);
    }
}
