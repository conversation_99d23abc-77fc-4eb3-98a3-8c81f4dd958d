import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';

import { SCHEMA_PRICES_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PricesSchema = new Schema(
    {
        description: {
            type: String,
            required: false,
            trim: true,
        },
        price: {
            type: Number,
            required: true,
        },
        status: {
            type: Boolean,
            default: true,
        },
        hospital: {
            type: Schema.Types.ObjectId,
            ref: 'hospitals',
            required: true,
        },
        hospitalId: {
            type: String,
            required: true,
        },
        name: {
            type: String,
            required: true,
            trim: true,
        },
    },
    {
        collection: SCHEMA_PRICES_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);