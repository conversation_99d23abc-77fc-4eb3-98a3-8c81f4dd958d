import { Modu<PERSON> } from '@nestjs/common';
import { DynamicLinkController } from './dynamic-link.controller';
import { DynamicLinkService } from './dynamic-link.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DoctorSchema } from '../doctor-mongo/schemas/doctor.schema';
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { DOCTOR_DYNAMIC_LINK_COLLECTION_NAME } from './schemas/constants';
import { DoctorDynamicLinkSchema } from './schemas/doctor-dynamic-link.schema';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME } from '../service-mongo/schemas/constants';
import { DoctorDescriptionSchema } from '../doctor-description/schema/doctor-description.schema';
import { ServiceMongoModule } from '../service-mongo/service-mongo.module';
import { GlobalSettingModule } from '../global-setting/global-setting.module';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
            { name: DOCTOR_DESCRIPTION_COLLECTION_NAME, schema: DoctorDescriptionSchema },
            {
                name: DOCTOR_DYNAMIC_LINK_COLLECTION_NAME,
                schema: DoctorDynamicLinkSchema,
            },
        ]),
        ServiceMongoModule,
        GlobalSettingModule,
    ],
    controllers: [DynamicLinkController],
    providers: [DynamicLinkService],
    exports: [DynamicLinkService],
})
export class DynamicLinkModule {}
