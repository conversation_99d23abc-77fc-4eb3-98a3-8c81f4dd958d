import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ICare247ServiceDto } from './create-care247-service.dto';

export class ICare247HospitalDto {
  @ApiProperty({ description: 'hospitalId', example: 'choray' })
  @IsNotEmpty({ message: 'hospitalId ID không được để trống' })
  @IsString({ message: 'hospitalId ID phải là chuỗi' })
  hospital: string;

  @ApiProperty({
    description: 'Trạng thái của dịch vụ',
    example: true,
    default: false,
  })
  @IsBoolean({ message: 'Trạng thái phải là giá trị boolean' })
  @IsOptional()
  status: boolean;

  @ApiProperty({
      description: 'Trạng thái của dịch vụ',
      example: true,
      default: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ICare247ServiceDto)
    addonServices: ICare247ServiceDto[];
}
