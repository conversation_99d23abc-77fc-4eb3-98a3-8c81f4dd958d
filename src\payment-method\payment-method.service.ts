import { CONSTRAINTS_DATE_COLLECTION_NAME, BOOKING_COLLECTION_NAME, BOOKING_ORDER_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { Injectable, Inject, Request, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { PaymentMethodDTO } from './dto/payment-method.dto';
import { UrlConfigService } from 'src/config/config.url.service';
import { PaymentMethodRateConfigService } from 'src/config/config.paymentMethodRate.service';
import { PkhHttpService } from 'src/config/config.http.service';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { first, get, isEmpty, isNil } from 'lodash';
import { BookingSlotFormDTO } from 'src/booking-gateway/dto/booking-slot-form.dto';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { InjectModel } from '@nestjs/mongoose';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { Model } from 'mongoose';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { UtilService } from 'src/config/util.service';
import { GetAllPayment } from './dto/get-all-payment.dto';
import * as moment from 'moment';
import { IConstraintsDate } from 'src/booking-gateway/intefaces/constraints-date.interface';
import { xor, reduce } from 'lodash';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { GetAllPaymentMultiBookingDto } from './dto/get-all-payment-multi-booking.dto';
import { IBookingOrder } from 'src/booking-gateway/intefaces/booking-order.inteface';
import { CSKHTokenVerifyDTO } from '../patient-mongo/dto/cskh-token-verify.dto';
import { PatientMongoService } from '../patient-mongo/patient-mongo.service';
import { isMobilePhone } from 'class-validator';
import { USER_COLLECTION_NAME, VISA_USER_COLLECTION_NAME } from '../user/schemas/constants';
import { IUser } from '../user/interfaces/user.interface';
import { HisGatewayService } from '../his-gateway/his-gateway.service';
import { GetAllPaymentMedproCare } from './dto/get-all-payment-medpro-care.dto';
import { MedproCare } from '../booking-gateway/intefaces/medpro-care.interface';
import { IVisaUser } from '../user/interfaces/visa-user.interface';
import { COOPERATION_PACKAGES } from '../cooperation/schemas/constants';
import { ICooperationPackage } from '../cooperation/interfaces/cooperation-package.inteface';
import { REPO_NAME_BETA } from '../common/constants';

@Injectable()
export class PaymentMethodService {
    private readonly logger: Logger = new Logger(PaymentMethodService.name);

    private readonly repoName: string;

    private readonly SHARE_PAYMENT_SUPPORT_VERSION: string = 'SHARE_PAYMENT_SUPPORT_VERSION';

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(SERVICE_COLLECTION_NAME) private serviceModel: Model<IService>,
        @InjectModel(CONSTRAINTS_DATE_COLLECTION_NAME) private constraintsDateModel: Model<IConstraintsDate>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private readonly partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private readonly hospitalModel: Model<IHospital>,
        @InjectModel(BOOKING_ORDER_COLLECTION_NAME) private bookingOrderModel: Model<IBookingOrder>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(VISA_USER_COLLECTION_NAME) private visaUserModel: Model<IVisaUser>,
        @InjectModel(COOPERATION_PACKAGES) private cooperationPackagesModel: Model<ICooperationPackage>,
        private readonly urlConfigService: UrlConfigService,
        private readonly paymentMethodRateConfigService: PaymentMethodRateConfigService,
        private readonly pkhHttpService: PkhHttpService,
        private readonly restApiMapping: RestfulAPIOldHospitalConfigService,
        @InjectSentry() private readonly clientSentry: SentryService,
        private readonly utilService: UtilService,
        private readonly eventEmitter: EventEmitter2,
        private readonly globalSettingService: GlobalSettingService,
        private readonly repoService: ConfigRepoService,
        private readonly patientMongoService: PatientMongoService,
        private readonly hisGatewayService: HisGatewayService,
    ) {
        this.repoName = this.repoService.getRepoName();
    }

    async getList(): Promise<any> {
        return this.pkhPatientKnex('payment_method')
            .select('id', 'short_name as name')
            .orderBy('id', 'asc');
    }

    async getBaseUrl(): Promise<any> {
        return this.urlConfigService.getBaseUrl();
    }
    async getPaymentMethodRate(): Promise<any> {
        return this.paymentMethodRateConfigService.getPaymentMethodRate();
    }

    isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // async getPaymentMethodByPartnerId(partnerId: string, price: number, groupId: number = 1): Promise<any> {
    //     const params = `${partnerId}`.toLowerCase();
    //     const bookingTree = (await this.callThirdPaymentMethod(params, price, groupId).toPromise()).data;
    //     return bookingTree;
    // }

    // async getAllPaymentMethod(
    //     partnerId: string,
    //     appId: string,
    //     platform: string,
    //     version: string,
    //     price: number,
    //     treeId: string = '',
    //     groupId: number = 1,
    //     cskhtoken?: string,
    // ): Promise<any> {
    //     const group = this.utilService.getGroupIdAfterCheck({cskhtoken});
    //     const partner = `${partnerId}`.toLowerCase();
    //     // const payments = (await this.callThirdPaymentMethod(partner, price, groupId).toPromise()).data;
    //     const payments = (await this.callThirdPaymentMethodWithFilter(partner, price, group, appId, platform, treeId).toPromise()).data;
    //     // If platform is android or ios -> return 1900 payment method
    //     const isMobileApp = platform === 'android' || platform === 'ios';
    //     if (!isMobileApp) {
    //         return payments.filter(payment => payment.methodId !== 'call');
    //     }
    //     return payments;
    // }

    // async caculatePriceSerivceAndServiceAddON(mainService: any, addonServices: any): Promise<number> {
    //     try {
    //         const services = [...addonServices, mainService];
    //         return reduce(
    //             services,
    //             (sum: number, item: any) => {
    //                 return sum + item.price;
    //             },
    //             0,
    //         );
    //     } catch (error) {
    //         throw error;
    //     }
    // }

    async getSharePaymentSupportVersion(partnerId: string): Promise<any> {
        try {
            const sharePayment = await this.globalSettingService.findByKeyAndRepoName(this.SHARE_PAYMENT_SUPPORT_VERSION);

            if (sharePayment) {
                const payload = JSON.parse(sharePayment);
                return first(payload
                    .map(item => {
                        return {
                            partnerId: item?.partnerId,
                            android: item?.android,
                            ios: item?.ios,
                        };
                    })
                    .filter(item => item?.partnerId === partnerId));
            }
            return null;
        } catch (error) {
            this.logger.error(`Error when exec getSharePaymentSupport. Cause: ${error.message}`);

            throw error;
        }
    }

    async verifyCskhToken(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        return this.patientMongoService.verifyCskhToken(cskhToken);
    }

    async getMedproCarePrice(appId: string, partnerId: string, medproCareServiceIds: string[], userId: string, platform?: string, version?: string, date?: any, isPrimary?: boolean): Promise<any> {
        try {
            let medproCare: MedproCare
            medproCare = await this.hisGatewayService.getMedproCareFromPortal(appId, partnerId, userId, platform, version, null, null, date, isPrimary)
            const medproCareServices = medproCare?.addonServices?.filter((item: any) => medproCareServiceIds.includes(item.id))
            medproCare = { ...medproCare, addonServices: medproCareServices }
            const medproCarePrice = reduce(
                medproCareServices,
                (sum: number, item: any) => {
                    return sum + item.price;
                },
                0,
            )
            return { medproCare, medproCarePrice }
        } catch (error) {
            this.logger.error(`Error when exec getMedproCarePrice. Cause: ${error.message}`);
            throw error;
        }
    }

    async getAllPaymentMethod(
        query: GetAllPayment,
        partnerId: string,
        appId: string,
        platform: string,
        version: string,
        userId: string,
        cskhtoken?: string,
    ): Promise<any> {
        let groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, groupId: query.groupId });
        // if (query?.groupId && (query.groupId === 2 || query.groupId === 4)) {
        //     groupId = query.groupId;
        // } else {
        //     groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken });
        // }
        let partner = `${partnerId}`.toLowerCase();
        // add addonService for fixed price
        const { addonServices = '', bookingId = '' } = query;
        let {
            patientId,
            serviceId = '',
            bookingDate = '',
            price,
            treeId = '',
        } = query;
        let addonServiceData: any = [];

        let getBooking: IBooking;
        if (bookingId) {
            getBooking = await this.bookingModel.findOne({ id: bookingId }).exec();
        }

        if (getBooking && getBooking?.bookingOrder) {
            const bookingOrder = await this.bookingOrderModel.findById(getBooking?.bookingOrder).exec();
            const bookings =  await this.bookingModel.find({ bookingOrder: bookingOrder?._id }).exec();
            return this.getAllPaymentMethodMultiBooking(
                {
                    bookings: bookings.map(booking => {
                        return {
                            bookingId: booking.id,
                            price: query?.price,
                            patientId: query?.patientId,
                        };
                    }),
                },
                partnerId,
                appId,
                platform,
                version,
                cskhtoken,
            );
        }

        if (getBooking && getBooking?.treeId && getBooking?.date) {
            groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, booking: getBooking.toObject() });
            treeId = getBooking.treeId;
            patientId = getBooking.patientId;
            bookingDate = getBooking.date.toISOString();
            serviceId = getBooking.serviceId;
            addonServiceData = getBooking?.addonServices;
            partner = getBooking.partnerId;
        } else {
            if (addonServices && addonServices.split(',').length) {
                addonServiceData = await this.serviceModel.find({ id: { $in: [...addonServices.split(',')] } }).exec();
            }
        }

        if (addonServiceData) {
            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                price = await this.checkContraintsDiscount(price, partnerId, treeId, bookingDate, patientId);
                /*Thêm phần addOnService*/
                // check addonServices trong serviceId
                if (bookingId) {
                    price += reduce(
                        addonServiceData,
                        (sum: number, item: any) => {
                            return sum + item.price;
                        },
                        0,
                    );
                } else {
                    const tracking = xor(findService.addonServiceIds, addonServiceData.map(addOn => addOn.id));
                    if (tracking.length < findService?.addonServiceIds.length) {
                        // price = await this.caculatePriceSerivceAndServiceAddON(findService, addonServiceData);
                        price += reduce(
                            addonServiceData,
                            (sum: number, item: any) => {
                                return sum + item.price;
                            },
                            0,
                        );
                    }
                }
            }
        } else {
            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                price = await this.checkContraintsDiscount(price, partnerId, treeId, bookingDate, patientId);
            }
        }
        // const payments = (await this.callThirdPaymentMethod(partner, price, groupId).toPromise()).data;
        let payments: any;
        try {
            payments = (await this.callThirdPaymentMethodWithFilter(partner, price, groupId, appId, platform, treeId).toPromise()).data;
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'RESULT_DATA_PORTAL_111',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, price, groupId, appId, platform, treeId },
                errorBody: {},
                response: payments,
                message: 'data tu portal tra ve',
            });
            if (!payments || isEmpty(payments)) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'callThirdPaymentMethodWithFilter',
                    summary: 'callThirdPaymentMethodWithFilter',
                    nameParent: 'getAllPaymentMethod',
                    params: { partner, price, groupId, appId, platform, treeId },
                    errorBody: {},
                    response: payments,
                    message: 'payments data trống !',
                });
                throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
            }
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdPaymentMethodWithFilter',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, price, groupId, appId, platform, treeId },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: 'callThirdPaymentMethodWithFilter xảy ra lỗi !',
            });
            throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
        }
        if (getBooking && getBooking.status === 6) {
            payments = payments.filter(payment => payment.methodId !== 'THANH_TOAN_TAI_CO_SO');
        }

        const ignoreSharePayment = await this.globalSettingService.findByKeyAndRepoName('REPO_IGNORE_SHAREPAYMENT');
        const setIgnoreSharePayment = new Set(ignoreSharePayment ? ignoreSharePayment.split(',') : []);
        if (setIgnoreSharePayment.has(this.repoName)) {
            payments = payments.filter(payment => payment.methodId !== 'SHARE_PAYMENT');
        }

        // If platform is android or ios -> return 1900 payment method
        const isMobileApp = platform === 'android' || platform === 'ios';
        if (!isMobileApp && payments?.length) {
            payments = payments.filter(payment => payment.methodId !== 'call');
        }

        payments =  await this.handlePaymentFilter(payments, partnerId, version, platform, userId, query);

        payments = await this.addArgeementToPayment(payments, partnerId);

        return payments;
    }

    async getAllPaymentMethodNew(
        query: GetAllPaymentMedproCare,
        partnerId: string,
        appId: string,
        platform: string,
        version: string,
        userId: string,
        cskhtoken?: string,
    ): Promise<any> {
        let groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, groupId: query.groupId });
        // if (query?.groupId && (query.groupId === 2 || query.groupId === 4)) {
        //     groupId = query.groupId;
        // } else {
        //     groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken });
        // }
        let partner = `${partnerId}`.toLowerCase();
        // add addonService for fixed price
        const { addonServices = [], bookingId = '' } = query;
        let {
            patientId,
            serviceId = '',
            bookingDate = '',
            price,
            treeId = '',
        } = query;
        let addonServiceData: any = [];

        let getBooking: IBooking;
        if (bookingId) {
            getBooking = await this.bookingModel.findOne({ id: bookingId }).exec();
        }

        if (getBooking && getBooking?.care247) {
            return this.getAllPaymentMethodMedproCare(query, partnerId, appId, platform, version, userId, cskhtoken)
        }

        if (getBooking && getBooking?.bookingOrder) {
            const bookingOrder = await this.bookingOrderModel.findById(getBooking?.bookingOrder).exec();
            const bookings =  await this.bookingModel.find({ bookingOrder: bookingOrder?._id }).exec();
            return this.getAllPaymentMethodMultiBooking(
                {
                    bookings: bookings.map(booking => {
                        return {
                            bookingId: booking.id,
                            price: query?.price,
                            patientId: query?.patientId,
                        };
                    }),
                },
                partnerId,
                appId,
                platform,
                version,
                cskhtoken,
            );
        }

        if (getBooking && getBooking?.treeId && getBooking?.date) {
            groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, booking: getBooking.toObject() });
            treeId = getBooking.treeId;
            patientId = getBooking.patientId;
            bookingDate = getBooking.date.toISOString();
            serviceId = getBooking.serviceId;
            addonServiceData = getBooking?.addonServices;
            partner = getBooking.partnerId;
        } else {
            if (addonServices && addonServices.length) {
                addonServiceData = await this.serviceModel.find({ id: { $in: [...addonServices] } }).exec();
            }
        }

        if (addonServiceData) {
            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                price = await this.checkContraintsDiscount(price, partnerId, treeId, bookingDate, patientId);
                /*Thêm phần addOnService*/
                // check addonServices trong serviceId
                if (bookingId) {
                    price += reduce(
                        addonServiceData,
                        (sum: number, item: any) => {
                            return sum + item.price;
                        },
                        0,
                    );
                } else {
                    const tracking = xor(findService.addonServiceIds, addonServiceData.map(addOn => addOn.id));
                    if (tracking.length < findService?.addonServiceIds.length) {
                        // price = await this.caculatePriceSerivceAndServiceAddON(findService, addonServiceData);
                        price += reduce(
                            addonServiceData,
                            (sum: number, item: any) => {
                                return sum + item.price;
                            },
                            0,
                        );
                    }
                }
            }
        } else {
            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                price = await this.checkContraintsDiscount(price, partnerId, treeId, bookingDate, patientId);
            }
        }
        // const payments = (await this.callThirdPaymentMethod(partner, price, groupId).toPromise()).data;
        let payments: any;
        try {
            payments = (await this.callThirdPaymentMethodWithFilter(partner, price, groupId, appId, platform, treeId).toPromise()).data;
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'RESULT_DATA_PORTAL_111',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, price, groupId, appId, platform, treeId },
                errorBody: {},
                response: payments,
                message: 'data tu portal tra ve',
            });
            if (!payments || isEmpty(payments)) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'callThirdPaymentMethodWithFilter',
                    summary: 'callThirdPaymentMethodWithFilter',
                    nameParent: 'getAllPaymentMethod',
                    params: { partner, price, groupId, appId, platform, treeId },
                    errorBody: {},
                    response: payments,
                    message: 'payments data trống !',
                });
                throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
            }
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdPaymentMethodWithFilter',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, price, groupId, appId, platform, treeId },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: 'callThirdPaymentMethodWithFilter xảy ra lỗi !',
            });
            throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
        }
        if (getBooking && getBooking.status === 6) {
            payments = payments.filter(payment => payment.methodId !== 'THANH_TOAN_TAI_CO_SO');
        }

        const ignoreSharePayment = await this.globalSettingService.findByKeyAndRepoName('REPO_IGNORE_SHAREPAYMENT');
        const setIgnoreSharePayment = new Set(ignoreSharePayment ? ignoreSharePayment.split(',') : []);
        if (setIgnoreSharePayment.has(this.repoName)) {
            payments = payments.filter(payment => payment.methodId !== 'SHARE_PAYMENT');
        }

        // If platform is android or ios -> return 1900 payment method
        const isMobileApp = platform === 'android' || platform === 'ios';
        if (!isMobileApp && payments?.length) {
            payments = payments.filter(payment => payment.methodId !== 'call');
        }

        payments =  await this.handlePaymentFilter(payments, partnerId, version, platform, userId, query);

        payments = await this.addArgeementToPayment(payments, partnerId);

        if (partnerId === 'umc') {
            const userIdQuery = getBooking?.userId || userId;
            const getVisaUser = await this.visaUserModel.findOne({ userId: userIdQuery }).read('primary').exec();
            payments = payments.map(p => {
                return {
                    ...p,
                    paymentTypes: p.paymentTypes.map(t => {
                        if (t.gatewayId === 'cybersource') {
                            return { ...t, checkEmail: true,
                                userInfo: { fullName: getVisaUser?.fullname, userName: getVisaUser?.phone, email: this.isValidEmail(getVisaUser?.email) ? getVisaUser?.email : '' }
                            }
                        }
                        return { ...t }
                    })
                }
            })
        }

        return payments;
    }

    async getAllPaymentDeal(body: { packageId: string }, platform: string): Promise<any> {
        const { packageId } = body;
        /*Tính lại tiền gói dịch vụ hợp tác*/
        const packageExist = await this.cooperationPackagesModel.findById(packageId);
        if (!packageExist) {
            throw new HttpException('Hệ thống không tìm thấy gói dịch vụ. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }

        const partnerListingConfig = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_LISTING_PARTNER')
        const partnerListingGroupIdConfig = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_LISTING_PARTNER_GROUP_ID')
        let payments: any;
        try {
            payments = (await this.callThirdPaymentMethodWithFilter(partnerListingConfig, packageExist.price, +partnerListingGroupIdConfig, 'medpro', platform, null).toPromise()).data;
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'RESULT_DATA_PORTAL_111',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner: partnerListingConfig, price: packageExist.price, groupId: +partnerListingGroupIdConfig, appId: 'medpro', platform },
                errorBody: {},
                response: payments,
                message: 'data tu portal tra ve',
            });
            if (!payments || isEmpty(payments)) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'callThirdPaymentMethodWithFilter',
                    summary: 'callThirdPaymentMethodWithFilter',
                    nameParent: 'getAllPaymentMethod',
                    params: { partner: partnerListingConfig, price: packageExist.price, groupId: +partnerListingGroupIdConfig, appId: 'medpro', platform },
                    errorBody: {},
                    response: payments,
                    message: 'payments data trống !',
                });
                throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
            }
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdPaymentMethodWithFilter',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner: partnerListingConfig, price: packageExist.price, groupId: +partnerListingGroupIdConfig, appId: 'medpro', platform },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: 'callThirdPaymentMethodWithFilter xảy ra lỗi !',
            });
            throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
        }

        return payments;
    }

    async getAllPaymentMethodMedproCare(
        body: GetAllPaymentMedproCare,
        partnerId: string,
        appId: string,
        platform: string,
        version: string,
        userId: string,
        cskhtoken?: string,
    ): Promise<any> {
        let groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, groupId: body.groupId });
        // if (query?.groupId && (query.groupId === 2 || query.groupId === 4)) {
        //     groupId = query.groupId;
        // } else {
        //     groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken });
        // }
        let partner = `${partnerId}`.toLowerCase();
        // add addonService for fixed price
        const { addonServices = [], bookingId = '' } = body;
        let {
            patientId,
            serviceId = '',
            bookingDate = '',
            price,
            treeId = '',
            medproCareServiceIds, 
        } = body;        
        let addonServiceData: any = [];

        let getBooking: IBooking;
        if (bookingId) {
            getBooking = await this.bookingModel.findOne({ id: bookingId }).exec();
        }

        if (getBooking && getBooking?.bookingOrder) {
            const bookingOrder = await this.bookingOrderModel.findById(getBooking?.bookingOrder).exec();
            const bookings =  await this.bookingModel.find({ bookingOrder: bookingOrder?._id }).exec();
            return this.getAllPaymentMethodMultiBooking(
                {
                    bookings: bookings.map(booking => {
                        return {
                            bookingId: booking.id,
                            price: body?.price,
                            patientId: body?.patientId,
                        };
                    }),
                },
                partnerId,
                appId,
                platform,
                version,
                cskhtoken,
            );
        }

        if (getBooking && getBooking?.treeId && getBooking?.date) {
            groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, booking: getBooking.toObject() });
            treeId = getBooking.treeId;
            patientId = getBooking.patientId;
            bookingDate = getBooking.date.toISOString();
            serviceId = getBooking.serviceId;
            addonServiceData = getBooking?.addonServices;
            partner = getBooking.partnerId;
        } else {
            if (addonServices && addonServices?.length) {
                addonServiceData = await this.serviceModel.find({ id: { $in: [...addonServices] } }).exec();
            }
        }

        if (addonServiceData) {
            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                price = await this.checkContraintsDiscount(price, partnerId, treeId, bookingDate, patientId);
                /*Thêm phần addOnService*/
                // check addonServices trong serviceId
                if (bookingId) {
                    price += reduce(
                        addonServiceData,
                        (sum: number, item: any) => {
                            return sum + item.price;
                        },
                        0,
                    );
                } else {
                    const tracking = xor(findService.addonServiceIds, addonServiceData.map(addOn => addOn.id));
                    if (tracking.length < findService?.addonServiceIds.length) {
                        // price = await this.caculatePriceSerivceAndServiceAddON(findService, addonServiceData);
                        price += reduce(
                            addonServiceData,
                            (sum: number, item: any) => {
                                return sum + item.price;
                            },
                            0,
                        );
                    }
                }
            }
        } else {
            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                price = await this.checkContraintsDiscount(price, partnerId, treeId, bookingDate, patientId);
            }
        }

        /*Tính giá Medpro Care*/
        let medproCareFee = 0
        const medproCarePartner2Bill = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_2_BILL')
        let listPartner2Bill = medproCarePartner2Bill.split(',');
        if (REPO_NAME_BETA.includes(this.repoName)) {
            listPartner2Bill = [...listPartner2Bill, 'umc', 'dalieuhcm']
        }
        if (!listPartner2Bill.includes(partner)) {
            if (getBooking && getBooking?.care247) {
                const medproCare = getBooking?.care247;
                const medproCarePrice = reduce(
                    medproCare?.addonServices,
                    (sum: number, item: any) => {
                        return sum + item.price;
                    },
                    0,
                )
                medproCareFee = medproCarePrice
                price += medproCarePrice
            } else {
                if (partnerId && medproCareServiceIds?.length) {
                    const { medproCarePrice } = await this.getMedproCarePrice(appId, partnerId, medproCareServiceIds, userId)
                    medproCareFee = medproCarePrice
                    price += medproCarePrice
                }
            }
        }
        
        // const payments = (await this.callThirdPaymentMethod(partner, price, groupId).toPromise()).data;
        let payments: any;
        try {
            payments = (await this.callThirdPaymentMethodWithFilter(partner, price, groupId, appId, platform, treeId).toPromise()).data;
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'RESULT_DATA_PORTAL_111',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, price, groupId, appId, platform, treeId },
                errorBody: {},
                response: payments,
                message: 'data tu portal tra ve',
            });
            if (!payments || isEmpty(payments)) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'callThirdPaymentMethodWithFilter',
                    summary: 'callThirdPaymentMethodWithFilter',
                    nameParent: 'getAllPaymentMethod',
                    params: { partner, price, groupId, appId, platform, treeId },
                    errorBody: {},
                    response: payments,
                    message: 'payments data trống !',
                });
                throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
            }
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdPaymentMethodWithFilter',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, price, groupId, appId, platform, treeId },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: 'callThirdPaymentMethodWithFilter xảy ra lỗi !',
            });
            throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
        }
        if (getBooking && getBooking.status === 6) {
            payments = payments.filter(payment => payment.methodId !== 'THANH_TOAN_TAI_CO_SO');
        }

        const ignoreSharePayment = await this.globalSettingService.findByKeyAndRepoName('REPO_IGNORE_SHAREPAYMENT');
        const setIgnoreSharePayment = new Set(ignoreSharePayment ? ignoreSharePayment.split(',') : []);
        if (setIgnoreSharePayment.has(this.repoName)) {
            payments = payments.filter(payment => payment.methodId !== 'SHARE_PAYMENT');
        }

        // If platform is android or ios -> return 1900 payment method
        const isMobileApp = platform === 'android' || platform === 'ios';
        if (!isMobileApp && payments?.length) {
            payments = payments.filter(payment => payment.methodId !== 'call');
        }

        payments =  await this.handlePaymentFilter(payments, partnerId, version, platform, userId, body);

        payments = await this.addArgeementToPayment(payments, partnerId);

        payments = payments.map(p => {
            const subTotalPrice = p.subTotal - medproCareFee
            return {
                ...p,
                subTotal: subTotalPrice,
                medproCareFee,
                paymentTypes: p.paymentTypes.map(t => {
                    return {...t, subTotal: subTotalPrice, medproCareFee}
                })
            }
        })

        if (partnerId === 'umc') {
            const userIdQuery = getBooking?.userId || userId;
            const getVisaUser = await this.visaUserModel.findOne({ userId: userIdQuery }).read('primary').exec();
            payments = payments.map(p => {
                return {
                    ...p,
                    paymentTypes: p.paymentTypes.map(t => {
                        if (t.gatewayId === 'cybersource') {
                            return { ...t, checkEmail: true,
                                userInfo: { fullName: getVisaUser?.fullname, userName: getVisaUser?.phone, email: this.isValidEmail(getVisaUser?.email) ? getVisaUser?.email : '' }
                            }
                        }
                        return { ...t }
                    })
                }
            })
        }

        return payments;
    }
    
    // apply for app version
    async handlePaymentFilter(payments: any, partnerId: string, version: string, platform: string, userId: string, query: any): Promise<any> {
        try {
            // add check version for share payment
            let res = [...payments];
            const sharePaymentSupport = await this.getSharePaymentSupportVersion(partnerId);

            if (sharePaymentSupport) {
                const requiredVersion = `${sharePaymentSupport[platform]}`.replace(/[.]+/g, '');
                const currentVersion = `${version}`.replace(/[.]+/g, '');

                const require = Number(requiredVersion);
                const current = Number(currentVersion);

                if (require > current || isNil(version) || isNil(platform)) {
                    this.logger.warn(`Filter for SHARE_PAYMENT. require: ${requiredVersion}, current: ${currentVersion}`);
                    res = res.filter(payment => `${payment.methodId}`.toUpperCase() !== 'SHARE_PAYMENT');
                }
            }

            const user = await this.userModel.findOne({ _id: userId }).exec();
            // Filter bỏ cổng TT fundiin nếu như username không phải là SĐT
            if (query.shareToPay || (user && !isMobilePhone(user.username, 'vi-VN'))) {
                res = res.map(payment => {
                    const { paymentTypes = [], ...rest } = payment;

                    return {
                        ...rest,
                        paymentTypes: paymentTypes.filter(type => (type.paymentPartnerId || type.gatewayId) !== 'fundiin'),
                    }
                }).filter(p => p.paymentTypes?.length > 0)
            }

            return res;
        } catch (error) {
            this.logger.error(`Error when exec handlePaymentFilter. Cause: ${error.message}`);

            return payments;
        }

    }

    async isRequirePayment(partnerId: string, formData: BookingSlotFormDTO, appId: string, userId: string): Promise<any> {
        const { methodId, paymentTypeDetail: methodDetail, treeId } = formData;
        /* Tìm lại thông tin master */
        if (partnerId === 'umc') {
            return {
                isRequirePayment: true,
            };
        }
        const findService = await this.serviceModel.findOne({ id: formData.serviceId }).exec();
        if (!findService) {
            throw new HttpException('Không tìm thấy thông tin dịch vụ', HttpStatus.BAD_REQUEST);
        }
        let subTotal = findService.price;

        /*Tính giá Medpro Care*/
        const { medproCareServiceIds } = formData;
        if (partnerId && medproCareServiceIds?.length) {
            const { medproCarePrice } = await this.getMedproCarePrice(appId, partnerId, medproCareServiceIds, userId)
            subTotal += medproCarePrice
        }

        if (subTotal === 0) {
            try {
                formData.partnerId = partnerId;
                const groupId = typeof formData.groupId !== typeof undefined && formData.groupId > 0 ? Number(formData.groupId) : 1;
                const checkPayment = (await this.callCheckIsRequiredPayment(partnerId, groupId, treeId).toPromise()).data;
                // console.log('checkPayment', checkPayment)
                const getMethodId = get(checkPayment, 'methodId', '');
                if (getMethodId === 'NO_PAYMENT') {
                    return {
                        isRequirePayment: false,
                    };
                } else {
                    return {
                        isRequirePayment: true,
                    };
                }
            } catch (error) {
                console.log(error);
                // this.clientSentry.instance().captureException(error);
            }
        } else {
            return {
                isRequirePayment: true,
            };
        }
    }

    async checkPaymentZeroDong(partnerId: string, price: number, groupId: number = 1): Promise<any> {
        if (Number(price) === 0) {
            const params = partnerId.toLowerCase();
            const bookingTree = (await this.callThirdPaymentMethod(params, price, groupId).toPromise()).data;
            const listZeroDong = bookingTree.filter(item => item.totalFee === 0);
            if (listZeroDong.length === 0) {
                return {
                    nextStep: true,
                };
            } else {
                const firstMethod = first(listZeroDong);
                return {
                    nextStep: false,
                    methodInfo: firstMethod,
                };
            }
        } else {
            return {
                nextStep: true,
            };
        }
    }

    callCheckIsRequiredPayment(
        partnerId: string,
        groupId: number,
        treeId?: string,
    ): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/payment/v1/groupFee`;
        const body = { partnerId, groupId, treeId };
        return this.pkhHttpService.postHttpRequest(url, body);
    }

    callThirdPaymentMethod(partnerId: string, price: number, groupId: number): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        return this.pkhHttpService.getHttpRequest(`${baseUrl}/payment/v1/charge/${partnerId}/${groupId}/${price}`);
    }

    callThirdPaymentMethodWithFilter(
        partnerId: string,
        amount: number,
        groupId: number,
        appId: string = '',
        platform: string = '',
        treeId: string = '',
    ): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        // console.log(`${baseUrl}/payment/v1/charge`, { partnerId, amount, groupId, appId, platform, treeId })
        return this.pkhHttpService.postHttpRequest(`${baseUrl}/payment/v1/charge`, { partnerId, amount, groupId, appId, platform, treeId });
    }

    async getATMs(paymentMethodDTO: PaymentMethodDTO): Promise<any> {
        const baseUrl = this.urlConfigService.getBaseUrl();
        const { vp_rate_atm, vp_add_atm, vp_sevice_fee } = this.paymentMethodRateConfigService.getPaymentMethodRate();
        const bankTable = 'bank';
        /* Lấy thông tin ATMs */
        const whereATMs = paymentMethodDTO.type === 'alepay' ? `alepay_code != ''` : `payoo_code != ''`;
        const bankData = this.pkhPatientKnex(bankTable)
            .whereRaw(`is_intcard = 0 AND priority != 0 AND hospital_id = 2 AND ${whereATMs}`)
            .where('status', 1)
            .orderBy('priority', 'asc');
        return bankData.map(item => {
            return {
                ...item,
                rate: vp_rate_atm,
                const_rate: vp_add_atm,
                medpro_fee: vp_sevice_fee,
                image: baseUrl + '/st/bank/' + item.image,
            };
        });
    }

    async getVisaMasters(paymentMethodDTO: PaymentMethodDTO): Promise<any> {
        const baseUrl = this.urlConfigService.getBaseUrl();
        const { vp_sevice_fee, vp_rate_int_card, vp_add_int_card } = this.paymentMethodRateConfigService.getPaymentMethodRate();
        const bankTable = 'bank';
        /* Lấy thông tin ATMs */
        const whereATMs = paymentMethodDTO.type === 'alepay' ? `alepay_code != ''` : `payoo_code != ''`;
        /* Lấy thông tin Visa*/
        const VisaMasterData = this.pkhPatientKnex(bankTable)
            .whereRaw(`is_intcard = 1 AND hospital_id = 2 AND ${whereATMs}`)
            .where('status', 1)
            .orderBy('priority', 'asc');
        return VisaMasterData.map(item => {
            return {
                ...item,
                rate: vp_rate_int_card,
                const_rate: vp_add_int_card,
                medpro_fee: vp_sevice_fee,
                image: baseUrl + '/st/bank/' + item.image,
            };
        });
    }

    async checkContraintsDiscount(price: number, partnerId: string, treeId: string, bookingDate: string, patientId: string): Promise<number> {
        if (treeId === 'DATE' || treeId === 'DOCTOR') {
            const formatStringDate = moment(bookingDate)
                .add(7, 'hours')
                .format('DDMMYYYY');
            const constraintsValue = `${partnerId}_${patientId}_${formatStringDate}`;
            const constraint = await this.constraintsDateModel.findOne({ constraintsValue }).exec();
            // console.log(`checkContraintsDiscount paymentmethod ${constraint}`);
            if (constraint) {
                price = price * 0.5;
            }
        }
        return price;
    }

    async addArgeementToPayment(payments: any, partnerId: string): Promise<any> {
        const [partnerConfig, configGlobal, hospital] = await Promise.all([
            this.partnerConfigModel.findOne({ partnerId }, { agreement: true, feeLabel: true}).exec(),
            this.globalSettingService.findByKeyAndRepoName('AGREEMENT_CONFIG_PARTNER'),
            this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
        ]);

        let overrideagreement = partnerConfig?.agreement ==  null ? configGlobal : partnerConfig.agreement;
        overrideagreement = overrideagreement.replace('{hospitalName}', hospital.name);

        return payments.map(payment => {
            return {
                ...payment,
                agreement: overrideagreement,
                feeLabel: partnerConfig.feeLabel,
            };
        });
    }

    async getAllPaymentMethodMultiBooking(
        formData: GetAllPaymentMultiBookingDto,
        partnerId: string,
        appId: string,
        platform: string,
        version: string,
        userMongoId: string,
        cskhtoken?: string,
    ): Promise<any> {
        const mapBookings = formData.bookings.map(async query => {
            let groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken });
            // if (query?.groupId && (query.groupId === 2 || query.groupId === 4)) {
            //     groupId = query.groupId;
            // } else {
            //     groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken });
            // }
            let partner = `${partnerId}`.toLowerCase();
            // add addonService for fixed price
            const { bookingId = '' } = query;
            let {
                patientId,
                serviceId = '',
                bookingDate = '',
                price,
                treeId = '',
            } = query;

            let getBooking: IBooking;
            if (bookingId) {
                getBooking = await this.bookingModel.findOne({ id: bookingId }).exec();
            }
            if (getBooking) {
                groupId = this.utilService.getGroupIdAfterCheck({ cskhtoken, booking: getBooking.toObject() });
                treeId = getBooking?.treeId || treeId;
                patientId = getBooking.patientId;
                bookingDate = getBooking?.date.toISOString() || bookingDate;
                serviceId = getBooking?.serviceId || serviceId;
                partner = `${getBooking.partnerId}`.toLowerCase();
            }

            if (serviceId) {
                const findService = await this.serviceModel.findOne({ id: serviceId }).exec();
                price = findService.price;
                // console.log('price:', price)
            }

            return { partner, price, groupId, treeId, userId: getBooking?.userId };
        });

        const bookingParams = await Promise.all(mapBookings);
        const totalPrice = reduce(
            bookingParams,
            (sum: number, item: any) => {
                return sum + item.price;
            },
            0,
        );
        const { partner, groupId, treeId, userId } = first(bookingParams);
        // console.log('totalPrice:', totalPrice)
        let payments: any;
        try {
            payments = (await this.callThirdPaymentMethodWithFilter(partner, totalPrice, groupId, appId, platform, treeId).toPromise()).data;
            if (!payments || isEmpty(payments)) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'callThirdPaymentMethodWithFilter',
                    summary: 'callThirdPaymentMethodWithFilter',
                    nameParent: 'getAllPaymentMethod',
                    params: { partner, totalPrice, groupId, appId, platform, treeId },
                    errorBody: {},
                    response: payments,
                    message: 'payments data trống !',
                });
                throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
            }
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdPaymentMethodWithFilter',
                summary: 'callThirdPaymentMethodWithFilter',
                nameParent: 'getAllPaymentMethod',
                params: { partner, totalPrice, groupId, appId, platform, treeId },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: 'callThirdPaymentMethodWithFilter xảy ra lỗi !',
            });
            throw new HttpException('Không có phương thức thanh toán nào. Vui lòng thử lại sau', 500);
        }
        // if (getBooking && getBooking.status === 6) {
        //     payments = payments.filter(payment => payment.methodId !== 'THANH_TOAN_TAI_CO_SO');
        // }
        // If platform is android or ios -> return 1900 payment method
        const isMobileApp = platform === 'android' || platform === 'ios';
        if (!isMobileApp && payments && payments.length) {
            payments = payments.filter(payment => payment.methodId !== 'call');
        }
        payments = await this.overridePaymentWithConfig(payments, partnerId);

        payments = await this.addArgeementToPayment(payments, partnerId);

        if (partnerId === 'umc') {
            const userIdQuery = userMongoId || userId;
            const getVisaUser = await this.visaUserModel.findOne({ userId: userIdQuery }).read('primary').exec();
            payments = payments.map(p => {
                return {
                    ...p,
                    paymentTypes: p.paymentTypes.map(t => {
                        if (t.gatewayId === 'cybersource') {
                            return { ...t, checkEmail: true,
                                userInfo: { fullName: getVisaUser?.fullname, userName: getVisaUser?.phone, email: this.isValidEmail(getVisaUser?.email) ? getVisaUser?.email : '' }
                            }
                        }
                        return { ...t }
                    })
                }
            })
        }

        return payments;
    }

    async overridePaymentWithConfig(payments: any, partnerId: string): Promise<any> {
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }, { canSharepayment: true }).exec();
        if (partnerConfig && partnerConfig?.canSharepayment === false) {
            return payments.filter(payment => payment.methodId !== 'SHARE_PAYMENT');
        } else {
            return payments;
        }
    }
}
