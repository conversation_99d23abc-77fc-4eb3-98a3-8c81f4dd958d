import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { DANH_MUC_BANG_GIA_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const DanhMucBangGiaSchema = new Schema(
    {
        partnerId: String,
        name: String,
        price: Number,
        description: String,
        group: String,
    },
    {
        collection: DANH_MUC_BANG_GIA_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
