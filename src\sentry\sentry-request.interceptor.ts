import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import * as Sentry from '@sentry/node';
import { catchError } from "rxjs/operators";
@Injectable()
export class SentryRequestInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    Sentry.setContext('request', {
      method: request.method,
      url: request.url,
      headers: request.headers,
    });
    if (request.user) {
      Sentry.setUser({
        id: request.user._id,
        userMongoId: request.user.userMongoId,
        fullname: request.user.fullname,
        username: request.user.username,
        medproId: request.user.medproId,
      });
    }
    return next.handle().pipe(
      catchError((error) => {
        Sentry.captureException(error);
        throw error;
      }),
    );
  }
}