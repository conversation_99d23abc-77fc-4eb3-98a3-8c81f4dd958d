import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsOptional, IsArray, ArrayMaxSize } from 'class-validator';

export class MedproCareSlotFormDTO {

    @ApiProperty({
        description: 'transactionId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin transactionId',
    })
    @ValidateIf(o => o.platform)
    transactionId: string;

    @ApiProperty({
        description: 'Redirect URL',
        required: true,
        type: String,
    })
    redirectUrl: string;

    @ApiProperty({
        description: 'groupId',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.groupId)
    groupId?: number;

    @ApiProperty({ description: 'medproCare', required: false })
    @IsOptional()
    @IsArray()
    @ArrayMaxSize(1)
    medproCareServiceIds?: string[];
}