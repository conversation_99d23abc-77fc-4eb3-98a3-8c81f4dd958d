import { CreateRatingHospitalDto } from './dto/create-rating-hospital.dto';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { RatingHospitalService } from './rating-hospital.service';

@Controller('rating-hospital')
@ApiTags('Rating Hospital - Đ<PERSON>h gi<PERSON> bệnh viện')
export class  RatingHospitalController {
    constructor(
        private readonly RatingHospitalService: RatingHospitalService,
    ) { }

    @Get()
    async getAllRatingHospital(@Query() query: any): Promise<any> {
        return await this.RatingHospitalService.getAllRatingHospital(query);
    }

    @Post()
    async createRatingHospital(@Body() createSpecialtyDto: CreateRatingHospitalDto): Promise<any> {
        return await this.RatingHospitalService.createRatingHospital(createSpecialtyDto);
    }
}
