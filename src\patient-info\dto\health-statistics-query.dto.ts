import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsIn } from 'class-validator';

export class HealthStatisticsQueryDto {
    @ApiProperty({
        description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
        required: false,
        type: String,
        example: '2025-01-01'
    })
    @IsOptional()
    @IsDateString({}, { message: 'Ng<PERSON>y bắt đầu phải có định dạng YYYY-MM-DD' })
    readonly fromDate?: string;

    @ApiProperty({
        description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
        required: false,
        type: String,
        example: '2025-12-31'
    })
    @IsOptional()
    @IsDateString({}, { message: 'Ng<PERSON>y kết thúc phải có định dạng YYYY-MM-DD' })
    readonly toDate?: string;

    @ApiProperty({
        description: 'Loại khám',
        required: false,
        enum: ['INITIAL', 'CHECKUP', 'EMERGENCY', 'FOLLOW_UP'],
        example: 'INITIAL'
    })
    @IsOptional()
    @IsString()
    @IsIn(['INITIAL', 'CHECKUP', 'EMERGENCY', 'FOLLOW_UP'], {
        message: 'Loại khám phải là một trong: INITIAL, CHECKUP, EMERGENCY, FOLLOW_UP'
    })
    readonly recordType?: string;
}
