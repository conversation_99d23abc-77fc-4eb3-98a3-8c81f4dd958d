import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { ANCHOR_LINK_COLLECTION_NAME } from './constants';
import { IAnchorItem, IAnchorLink } from '../interfaces/anchor-link.interface';

const { Schema } = mongoose;

const AnchorItemSchema = new Schema<IAnchorItem>(
  {
    tabName: { type: String, required: true },
    title: { type: String, required: true },
    href: { type: String, required: true }
  }
);

export const AnchorLinkSchema = new Schema<IAnchorLink>(
  {
    partnerId: { type: String, required: true },
    anchorLink: { type: [AnchorItemSchema], default: [] },
  },
  {
    collection: ANCHOR_LINK_COLLECTION_NAME,
    timestamps: true,
  }
).plugin(jsonMongo);
