import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { CARE247_SERVICE_CHANGES } from './constants';

const Schema = mongoose.Schema;

export const Care247ServiceChangesSchema = new Schema(
    {
        dataBefore: { type: Schema.Types.Mixed },
        dataAfter: { type: Schema.Types.Mixed },
        userId: { type: String },
        userAction: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME, required: true },
    },
    {
        collection: CARE247_SERVICE_CHANGES,
        timestamps: true,
    },
).plugin(jsonMongo);

