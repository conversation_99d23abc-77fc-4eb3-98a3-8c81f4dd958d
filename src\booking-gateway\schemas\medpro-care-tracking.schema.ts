import * as mongoose from 'mongoose';
import { MEDPRO_CARE_TRACKING } from './constants';

const Schema = mongoose.Schema;

export const MedproCareTrackingSchema = new Schema(
    {
        medproCareTrackingId: { type: String },
        userId: { type: String },
        patientId: { type: [String] },
        user: { type: Schema.Types.Mixed },
        patient: { type: [Schema.Types.Mixed] },
        trackingTimes: { type: Number, required: false },
        medproCareServiceId: { type: String, required: false },
    },
    {
        collection: MEDPRO_CARE_TRACKING,
        timestamps: true,
    },
);