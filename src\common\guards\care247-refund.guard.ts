import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { last, size } from 'lodash';
import { JwtUserConfigService } from '../../config/config.user.jwt.service';
import { GlobalSettingService } from '../../global-setting/global-setting.service';

@Injectable()
export class Care247RefundGuard implements CanActivate {
    constructor(
        private jwtUserConfigService: JwtUserConfigService, 
        private readonly globalSettingService: GlobalSettingService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();

        const { authorization } = request.headers;

        if (size(authorization) > 0) {
            try {
                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const care247RefundUsers = await this.globalSettingService.findByKeyAndRepoName('CARE247_REFUND_USER_PERMISSION');
                    const care247RefundUsersList = new Set(care247RefundUsers.split(','));
                    
                    if (care247RefundUsersList.has(userMongoId)) {
                        return true
                    } else {
                        throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!', HttpStatus.FORBIDDEN);
                    }
                }
                return true;
            } catch (error) {
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!', HttpStatus.FORBIDDEN);
            }
        }
    }
}