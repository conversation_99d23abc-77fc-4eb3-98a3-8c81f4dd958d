import * as mongoose from 'mongoose';
import { CATEGORY_SERVICE_IN_MONTH_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const CategoryServiceInMonthSchema = new Schema(
    {
        repo: { type: String, required: true },
        category: { type: String, required: true },
        packageId: { type: String, required: true },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        status: {
            type: Boolean,
            default: false,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: CATEGORY_SERVICE_IN_MONTH_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
