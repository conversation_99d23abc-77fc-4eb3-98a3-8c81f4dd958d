import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { last, size } from 'lodash';
import { JwtUserConfigService } from '../../config/config.user.jwt.service';
import { UserService } from '../../user/user.service';

@Injectable()
export class Care247Guard implements CanActivate {
    constructor(private jwtUserConfigService: JwtUserConfigService, private userService: UserService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();

        const { authorization } = request.headers;

        if (size(authorization) > 0) {
            try {
                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                /* <PERSON>ểm tra lại thông tin cskhInfo */
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const care247User = await this.userService.getUserByMongoUserId(userMongoId);
                    if (!care247User || !care247User?.isCare247) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user care247', HttpStatus.UNAUTHORIZED);
                    }

                    request.user = {
                        ...care247User.toObject(),
                        ...jwtVerify,
                    };

                    return true;
                }
            } catch (error) {
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!', HttpStatus.FORBIDDEN);
            }
        }

        return false;
    }
}
