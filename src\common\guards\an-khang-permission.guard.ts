import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { last, size } from 'lodash';
import { JwtUserConfigService } from '../../config/config.user.jwt.service';
import { UserService } from '../../user/user.service';
import { GlobalSettingService } from '../../global-setting/global-setting.service';

@Injectable()
export class AnKhangPermissionGuard implements CanActivate {
    constructor(
        private jwtUserConfigService: JwtUserConfigService, 
        private userService: UserService,
        private readonly globalSettingService: GlobalSettingService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();

        const { authorization } = request.headers;

        if (size(authorization) > 0) {
            try {
                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                /* Kiểm tra lại thông tin cskhInfo */
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const cskhUser = await this.userService.getUserByMongoUserId(userMongoId);
                    if (!cskhUser || !cskhUser.isCS) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user cskh', HttpStatus.UNAUTHORIZED);
                    }
                    
                    request.user = {
                        ...cskhUser.toObject(),
                        ...jwtVerify,
                    };
                    
                    // Kiểm tra quyền xem referral code An Khang
                    const anKhangRefcodePermission = await this.globalSettingService.findByKeyAndRepoName('AN_KHANG_PERMISSION_VIEW');
                    const anKhangRefcodePermissionList = new Set(anKhangRefcodePermission.split(','));
                    
                    if (anKhangRefcodePermissionList.has(userMongoId)) {
                        return true;
                    }
                }
            } catch (error) {
                const nameJWTError = !!error.name ? error.name : '';
                if (nameJWTError === 'TokenExpiredError') {
                    throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'JsonWebTokenError') {
                    throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'NotBeforeError') {
                    throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
                } else {
                    throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            }
        }

        return false;
    }
}
