import * as mongoose from 'mongoose';
import { CARE247_SERVICES } from './constants';

const Schema = mongoose.Schema;

export const Care247ServicesSchema = new Schema(
    {
        duration: { type: String, required: true  },
        originalPrice: { type: Number },
        price: { type: Number, required: true  },
        name: { type: String, required: true },
        subname: { type: String },
        description: { type: String },
        description_following: { type: String },
        currency: { type: String, required: true  },
        status: { type: Number, required: true  },
        locale: { type: String, required: true  },
    },
    {
        collection: CARE247_SERVICES,
        timestamps: true,
    },
);

