import { Injectable, Inject, HttpException, HttpStatus } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { get } from 'lodash';
import { UrlConfigService } from 'src/config/config.url.service';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { PkhHttpService } from 'src/config/config.http.service';
import { FeatureMongoService } from 'src/feature-mongo/feature-mongo.service';
import { HospitalMongoService } from 'src/hospital-mongo/hospital-mongo.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import {ConfigRepoService} from '../config/config.repo.service';
import { HeadersDto } from '../common/base/headers.dto';
import { InjectModel } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME } from '../hospital-mongo/schemas/constants';
import { Model } from 'mongoose';
import { IHospital } from '../hospital-mongo/interfaces/hospital.interface';
import { UtilService } from '../config/util.service';
import { CacheManagerService } from '../cache-manager/cache-manager.service';

@Injectable()
export class FeatureService {
    private medproFeatureTable = 'medpro_feature';
    private medproHospitalFeature = 'medpro_feature_hospital';
    private IGNORE_DOCTOR_FEATURE = 'IGNORE_DOCTOR_FEATURE';
    private readonly CHO_RAY_DAT_KHAM: string = 'CHO_RAY_DAT_KHAM';
    private repoName: string;

    constructor(
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly urlConfigService: UrlConfigService,
        private readonly restApiMapping: RestfulAPIOldHospitalConfigService,
        private readonly pkhHttpService: PkhHttpService,
        private readonly hospitalMongoService: HospitalMongoService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly repoConfigService: ConfigRepoService,
        private readonly utilService: UtilService,
        private readonly cacheService: CacheManagerService,
    ) {
        this.repoName = this.repoConfigService.getRepoName();
    }

    async getAllFeatureListByPartner(partnerId: string, appId: string = '', type: number = 1, platform: string  = 'pc'): Promise<any> {
        try {
            const hospitalFeatures = await this.hospitalMongoService.getPartnerInfo(partnerId, type);
            const { features = [] } = hospitalFeatures;
            /* kiểm tra trước khi response về */
            let resultData: any = [];
            for (const feature of features) {
                const getChildren = get(feature, 'children', []);
                if (getChildren.length > 0) {
                    resultData = [...resultData, ...getChildren];
                } else {
                    // check case choray datkham
                    const isDisabledBookingChoRay = await this.globalSettingService.findByKeyAndRepoName(this.CHO_RAY_DAT_KHAM);
                    const mobileP = new Set(['android', 'ios']);
                    if (isDisabledBookingChoRay === 'ON' && appId === 'choray' && partnerId === 'choray' && mobileP.has(platform)) {
                        if (feature.type !== 'booking.date') {
                            resultData = [...resultData, feature];
                        }
                    } else {
                        resultData = [...resultData, feature];
                    }
                }
            }

            resultData = resultData.filter(feature => {
                return ['android', 'ios'].includes(platform) ? feature.mobileStatus : feature.status;
            })

            return resultData;
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getFeatureByVersion(partnerId: string, appId: string, version: number = 1, type: number = 1, platform: string = 'pc'): Promise<any> {
        const ignoreDoctorFeatureKey = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_DOCTOR_FEATURE);
        const ignoreDoctorFeature = new Set(ignoreDoctorFeatureKey.split(','));
        let data: any = [];
        switch (+version) {
            case 1:
                data = await this.getAllFeatureListByPartner(partnerId, appId, type, platform);
                data = await this.overrideFeaturesModeBeta(data);
                data = await this.handleFeatureChoRay(data, appId, platform);
                data = await this.overideFeatureModeBetaConfig(data, partnerId, appId, platform);
                break;
            case 2:
                data = await this.getAllFeatureListByPartner(partnerId, appId, type, platform);
                data = await this.overrideFeaturesModeBeta(data);
                data = await this.handleFeatureChoRay(data, appId, platform);
                data = await this.overideFeatureModeBetaConfig(data, partnerId, appId, platform);
                break;
            default:
                break;
        }
        // if (ignoreDoctorFeature.has(partnerId) && appId !== 'medpro') {
        //     return data.filter(item => item.type.toUpperCase() !== 'BOOKING.DOCTOR');
        // }
        return data;
    }

    async getAllFeatureListTree(partnerId: string): Promise<any> {
        try {
            const hospitalFeatures = await this.hospitalMongoService.getPartnerInfo(partnerId);
            const { features = [] } = hospitalFeatures.toJSON();
            return features;
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getAllFeatureList(hosppitalId: number): Promise<any> {
        const data = await this.pkhPatientKnex(this.medproFeatureTable)
            .select(
                `${this.medproFeatureTable}.id`,
                `${this.medproFeatureTable}.name`,
                `${this.medproFeatureTable}.image`,
                `${this.medproFeatureTable}.priority`,
                `${this.medproFeatureTable}.status`,
            )
            .innerJoin(this.medproHospitalFeature, `${this.medproFeatureTable}.id`, `${this.medproHospitalFeature}.feature_id`)
            .where(`${this.medproHospitalFeature}.hospital_id`, hosppitalId)
            .where(`${this.medproFeatureTable}.status`, 1);
        const baseUrl = this.urlConfigService.getBaseUrl();
        return data.map(item => {
            return {
                ...item,
                imageUrl: `${baseUrl}/st/feature/${item.image}`,
            };
        });
    }

    async overrideFeaturesModeBeta(features: any): Promise<any> {
        const setRepoName = new Set(['api-v2-111', 'api-v2-beta']);
        if (setRepoName.has(this.repoName)) {
            return features.map(feature => {
                feature.disabled = false;
                feature.message = '';
                return feature;
            });
        } else {
            return features;
        }
    }

    async handleFeatureChoRay(features: any, appid: string, platform: string): Promise<any> {
        const configRepoGlobal = await this.globalSettingService.findByKeyAndRepoName('REPO_CONFIG_BOOKING_CHORAY');
        const configRepos = new Set(configRepoGlobal ? configRepoGlobal.split(',') : []);
        if (appid === 'choray' && configRepos.has(this.repoName) && !(new Set(['web', 'pc'])).has(platform)) {
            features = features.filter(feature => feature.type !== 'booking.date');
        }
        return features;
    }

    async overideFeatureModeBetaConfig(features: any, partnerId: string, appid: string, platform: string): Promise<any> {
        const [configProductJson, configsJson] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CONFIG_FEATURE_SHOW_IN_PRODUCTION'),
            this.globalSettingService.findByKeyAndRepoName('CONFIG_FEATURE_SHOW_IN_BETA'),
        ]);
        if (configsJson) {
            let configs = JSON.parse(configsJson);
            const configProducts = JSON.parse(configProductJson);
            configs = [...configs, ...configProducts];
            features = this.handleFeatureOverride(features, configs, appid, partnerId, platform);
        }
        return features;
    }

    handleFeatureOverride(features: any, configs: any, appid: string, partnerId: string, platform: string): any {
        for (const config of configs) {
            const repoSet = new Set(config?.repo || []);
            const partnerSet = new Set(config?.partners || []);
            const appSet = new Set(config?.appids || []);
            const platformSet = new Set(config?.platforms || []);
            const featuresType = new Set(features.map(feature => feature.type));
            if (
                repoSet.has(this.repoName) &&
                partnerSet.has(partnerId) &&
                appSet.has(appid) &&
                platformSet.has(platform)
            ) {
                if (featuresType.has(config?.feature.type)) {
                    features = features.map(feature => {
                        if (feature.type === config?.feature.type) {
                            feature = {
                                ...feature,
                                ...config?.feature,
                            };
                        }
                        return feature;
                    });
                } else {
                    features = [...features, config?.feature];
                }
            }
        }

        return features;
    }

    async getPartnerFeatureMap(headers: HeadersDto) {
        const paramsCache = {
            appId: headers.appid,
            platform: headers.platform,
            locale: headers.locale,
        }

        const cacheKey = `partner-feature-map:${this.utilService.sortedStringifyUrl(paramsCache)}`;
        const cachedData = await this.cacheService.get(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        let hospitals = await this.hospitalModel.find({
            status: 1,
            partnerId: { $nin: ['umcmono', 'medpro', 'canthozone'] },
        }, { partnerId: true, features: true }).lean();

        hospitals = await Promise.all(hospitals.map(async hospital => {
            let { features, ...rest } = hospital;

            features = await this.overrideFeaturesModeBeta(features);
            features = await this.overideFeatureModeBetaConfig(features, hospital.partnerId, headers.appid, headers.platform);

            return { ...rest, features }
        }))

        const featureMap = hospitals.reduce((res, partner) => {

            res[partner.partnerId] = {
                partnerId: partner.partnerId,
                features: partner.features.filter((feature) => {
                    const status = ['android', 'ios'].includes(headers.platform) ? feature.mobileStatus : feature.status;

                    return status && feature.type.startsWith('booking.');
                }),
            };

            return res;
        }, {});

        this.cacheService.set(cacheKey, featureMap);
        return featureMap;
    }
}
