import * as mongoose from 'mongoose';
import { ADS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const AdsSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        status: {
            type: Boolean,
            default: true,
        },
        adsContent: {
            type: String,
            default: '',
        },
        adsStyle: {
            type: Schema.Types.Mixed,
            default: {},
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: ADS_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
