import { CacheModule, HttpModule, Module } from '@nestjs/common';
import { HospitalMongoController } from './hospital-mongo.controller';
import { HospitalMongoService } from './hospital-mongo.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    BOOKING_GUIDE_COLLECTION_NAME,
    CAU_HOI_COLLECTION_NAME,
    DANH_MUC_COLLECTION_NAME,
    HOSPITAL_COLLECTION_NAME,
    HOSPITAL_EXTRA_INFO_COLLECTION_NAME,
    MENU_COLLECTION_NAME,
    SEO_PAGE_COLLECTION_NAME,
} from './schemas/constants';
import { HospitalSchema } from './schemas/hospital.schema';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { APP_ID_COLLECTION_NAME } from 'src/app-id/schemas/constants';
import { AppIdSchema } from 'src/app-id/schemas/app-id.schema';
import { PartnerConfigModule } from 'src/partner-config/partner-config.module';
import { PartnerConfigService } from 'src/partner-config/partner-config.service';
import { PARTNER_CONFIG_COLLECTION_NAME, POPUP_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { PopupSchema } from 'src/partner-config/schemas/popup.schema';
import { CacheManagerModule } from '../cache-manager/cache-manager.module';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../hospital-description/schema/constants';
import { HospitalDescriptionSchema } from '../hospital-description/schema/hospital-description.schema';
import { ConfigModule } from '@nestjs/config';
import { ConfigCacheManagerService } from '../config/config.cache-manager.service';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from '../report/schemas/constants';
import { ReportTransactionDailySchema } from '../report/schemas/report-daily.schema';
import { BookingGuideSchema } from './schemas/booking-guide.schema';
import { MenuSchema } from './schemas/menu.schema';
import { CauHoiSchema } from './schemas/cau-hoi.schema';
import { DanhMucSchema } from './schemas/danh-muc.schema';
import { HospitalExtraSchema } from './schemas/hospital-extra.schema';
import { SeoPageSchema } from './schemas/seo-page.schema';
import { HomePageServiceProvider } from 'src/home-page/persistence/home-page.service';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME, SEARCH_KEYWORD_COLLECTION_NAME, SERVICE_COLLECTION_NAME, SERVICE_DESCRIPTION_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceDescriptionSchema } from 'src/service-mongo/schemas/service-description.schema';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { DoctorDescriptionSchema } from 'src/doctor-description/schema/doctor-description.schema';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { FeeRefundConfigsSchema } from 'src/cash-back/schemas/fee-refund-configs.schema';
import { SearchKeywordsSchema } from 'src/service-mongo/schemas/search-keywords.schema';
import { HOME_PAGE } from '../home-page/schemas/constants';
import { HomePageSchema } from '../home-page/schemas/home-page.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: FEATURE_COLLECTION_NAME, schema: FeatureSchema },
            { name: CITY_COLLECTION_NAME, schema: CitySchema },
            { name: APP_ID_COLLECTION_NAME, schema: AppIdSchema },
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: POPUP_COLLECTION_NAME, schema: PopupSchema },
            { name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, schema: HospitalDescriptionSchema },
            { name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, schema: HospitalDescriptionSchema },
            { name: DAILY_TRANSACTION_REPORT_COLLECTION_NAME, schema: ReportTransactionDailySchema },
            { name: SEO_PAGE_COLLECTION_NAME, schema: SeoPageSchema },
            {
                name: SERVICE_DESCRIPTION_COLLECTION_NAME,
                schema: ServiceDescriptionSchema,
            },
            {   name: SERVICE_COLLECTION_NAME, 
                schema: ServiceSchema 
            },
            { 
                name: DOCTOR_DESCRIPTION_COLLECTION_NAME, 
                schema: DoctorDescriptionSchema 
            },
            {
                name: FEE_REFUND_CONFIGS_COLLECTION_NAME,
                schema: FeeRefundConfigsSchema
            },
            { name: HOME_PAGE, schema: HomePageSchema },
        ]),

        MongooseModule.forFeature(
            [
                { name: BOOKING_GUIDE_COLLECTION_NAME, schema: BookingGuideSchema },
                { name: MENU_COLLECTION_NAME, schema: MenuSchema },
                { name: CAU_HOI_COLLECTION_NAME, schema: CauHoiSchema },
                { name: DANH_MUC_COLLECTION_NAME, schema: DanhMucSchema },
                { name: HOSPITAL_EXTRA_INFO_COLLECTION_NAME, schema: HospitalExtraSchema },
            ],
            'backoffice',
        ),
        MongooseModule.forFeature(
            [
                {
                    name: SEARCH_KEYWORD_COLLECTION_NAME,
                    schema: SearchKeywordsSchema,
                },
            ],
            'services', // ? Collection SearchKeyWords nằm trong database services
        ),
        HttpModule,
        PatientMongoModule,
        CacheModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigCacheManagerService],
            useFactory: (config: ConfigCacheManagerService) => config.createCacheOptions,
        }),
        CacheManagerModule,
    ],
    controllers: [HospitalMongoController],
    providers: [HospitalMongoService, PartnerConfigService, GlobalSettingService, HomePageServiceProvider],
    exports: [HospitalMongoService, PartnerConfigService, GlobalSettingService],
})
export class HospitalMongoModule {}
