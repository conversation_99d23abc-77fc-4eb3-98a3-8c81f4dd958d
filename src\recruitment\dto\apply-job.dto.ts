import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

export class ApplyJob<PERSON>to {
  @IsNotEmpty()
  @IsString()
  fullname: string;

  @IsNotEmpty()
  @IsString()
  phone: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  platform: string;

  @IsOptional()
  @IsString()
  coverLetter?: string;

  @IsNotEmpty()
  cvFilePath: string;

  @IsNotEmpty()
  @IsString()
  recruitmentId: string;

  @IsNotEmpty()
  @IsString()
  captchaResponse: string;
}
