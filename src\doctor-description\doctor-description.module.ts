import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { DoctorD<PERSON><PERSON>Controller } from './doctor-description.controller';
import { DoctorDescriptionService } from './doctor-description.service';
import {MongooseModule} from "@nestjs/mongoose";
import {DoctorDescriptionSchema} from "./schema/doctor-description.schema";
import {DOCTOR_DESCRIPTION_COLLECTION_NAME} from "./schema/constants";

@Module({
  imports: [
      MongooseModule.forFeature([
        { name: DOCTOR_DESCRIPTION_COLLECTION_NAME, schema: DoctorDescriptionSchema },
      ]),
  ],
  controllers: [DoctorDescriptionController],
  providers: [DoctorDescriptionService],
})
export class DoctorDescriptionModule { }
