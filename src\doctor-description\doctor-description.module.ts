import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { DoctorD<PERSON><PERSON>Controller } from './doctor-description.controller';
import { DoctorDescriptionService } from './doctor-description.service';
import {MongooseModule} from "@nestjs/mongoose";
import {DoctorDescriptionSchema} from "./schema/doctor-description.schema";
import {DOCTOR_DESCRIPTION_COLLECTION_NAME} from "./schema/constants";
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { DoctorSchema } from '../doctor-mongo/schemas/doctor.schema';

@Module({
  imports: [
      MongooseModule.forFeature([
        { name: DOCTOR_DESCRIPTION_COLLECTION_NAME, schema: DoctorDescriptionSchema },
        { name: DOCTOR_COLLECTION_NAME, schema: DoctorSche<PERSON> },
      ]),
  ],
  controllers: [Doctor<PERSON><PERSON>criptionController],
  providers: [DoctorDescriptionService],
})
export class DoctorDescriptionModule { }
