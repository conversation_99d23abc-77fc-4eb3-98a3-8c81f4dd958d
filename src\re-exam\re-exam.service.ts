import { Injectable, HttpException, HttpStatus, HttpService } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { RE_EXAM_COLLECTION_NAME, RE_EXAM_VERIFY_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import { first, get, groupBy } from 'lodash';
import { IReExam } from './intefaces/re-exam.inteface';
import { ReExamQueryDTO } from './dto/re-exam-query.dto';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { PATIENT_COLLECTION_NAME, PATIENT_CODE_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { USER_COLLECTION_NAME, SIGNIN_PROVIDER_COLLECTION_NAME, CONSTRAINTS_USER_COLLECTION_NAME, CONSTRAINTS_PROVIDER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { UserService } from 'src/user/user.service';
import { JwtService } from '@nestjs/jwt';
import { UrlConfigService } from 'src/config/config.url.service';
import { PkhHttpService } from 'src/config/config.http.service';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import * as moment from 'moment';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { ISubject } from 'src/subject-mongo/interfaces/subject.interface';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { IDoctor } from 'src/doctor-mongo/interfaces/doctor.interface';
import { IRoom } from 'src/room-mongo/interfaces/room.interface';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { isMobilePhone } from 'class-validator';
import * as uuid from 'uuid';
import { ISignInProvider } from 'src/user/interfaces/sign-in-provider.interface';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { PatientFormMongoDTO } from 'src/patient-mongo/dto/patient-form-mongo.dto';
import { IPatientCodes } from 'src/patient-mongo/intefaces/patient-codes.inteface';
import { IUserConstraints } from 'src/user/interfaces/user-constraints.interface';
import { IProviderConstraints } from 'src/user/interfaces/provider-constraints.interface';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { BOOKING_COLLECTION_NAME, SECTION_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { ReExamSearchDTO } from './dto/re-exam-search.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { UtilService } from 'src/config/util.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { ReExamOptionsDto } from './dto/re-exam-options.dto';
import { ISection } from 'src/booking-gateway/intefaces/section.inteface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { PortalPushReExamDataDto } from './dto/portal-push-re-exam-data.dto';
import { IReExamVerify } from './intefaces/re-exam-verify.inteface';
import { ConfigRepoService } from '../config/config.repo.service';
import { REPO_NAME_BETA } from '../common/constants';


@Injectable()
export class ReExamService {
    private repoName: string
    constructor(
        @InjectModel(RE_EXAM_COLLECTION_NAME) private reExamModel: Model<IReExam>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(SERVICE_COLLECTION_NAME) private serviceModel: Model<IService>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private subjectModel: Model<ISubject>,
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
        @InjectModel(ROOM_COLLECTION_NAME) private roomModel: Model<IRoom>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(SIGNIN_PROVIDER_COLLECTION_NAME) private signInProvider: Model<ISignInProvider>,
        @InjectModel(CONSTRAINTS_USER_COLLECTION_NAME) private constraintUser: Model<IUserConstraints>,
        @InjectModel(CONSTRAINTS_PROVIDER_COLLECTION_NAME) private constraintProvider: Model<IProviderConstraints>,
        @InjectModel(SECTION_COLLECTION_NAME) private sectionModel: Model<ISection>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(RE_EXAM_VERIFY_COLLECTION_NAME) private reExamVerifyModel: Model<IReExamVerify>,
        @InjectSentry() private readonly clientSentry: SentryService,
        private patientService: PatientMongoService,
        private userService: UserService,
        private readonly jwtService: JwtService,
        private readonly urlConfigService: UrlConfigService,
        private readonly repoService: ConfigRepoService,
        private readonly httpService: PkhHttpService,
        private readonly http: HttpService,
        private eventEmitter: EventEmitter2,
        private utilService: UtilService,
        private globalSettingService: GlobalSettingService,
    ) {
        this.repoName = this.repoService.getRepoName();
    }

    async getBookingTreeRestApi(partnerId: string): Promise<any> {
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        const { bookingTreeRestApi = '' } = partnerConfig;

        if (REPO_NAME_BETA.includes(this.repoName)) {
            return this.urlConfigService.getBookingTreeUrl();
        }
        
        if (bookingTreeRestApi) {
            return bookingTreeRestApi;
        } else {
            return this.urlConfigService.getBookingTreeUrl();
        }
    }

    async testMoment(): Promise<any> {
        const time = '2020-07-21T18:29:00.000Z';
        const reExamInfo = await this.reExamModel.findOne({ id: 'trungvuong_testTV220720_001' }).exec();
        return {
            value: time,
            isUTC: moment(time).isUTC() ? 'UTC' : 'NON-UTC',
            momentFormat: moment(time).format('DD-MM-YYYY HH:mm:ss'),
            momentFormatLocal: moment(time).local().format('DD-MM-YYYY HH:mm:ss'),
            momentFormatUTC: moment(time).utc().format('DD-MM-YYYY HH:mm:ss'),
            reExam_date: reExamInfo.scheduleDate,
            reExam_IsUTC: moment(reExamInfo.scheduleDate).isUTC() ? 'UTC' : 'NON-UTC',
            reExam_momentFormat: moment(reExamInfo.scheduleDate).format('DD-MM-YYYY HH:mm:ss'),
            reExam_momentFormatLocal: moment(reExamInfo.scheduleDate).local().format('DD-MM-YYYY HH:mm:ss'),
            reExam_momentFormatUTC: moment(reExamInfo.scheduleDate).utc().format('DD-MM-YYYY HH:mm:ss'),
        };
    }

    transformDataPatientV1(url: string, data: any): Observable<AxiosResponse<any>> {
        // console.log(url);
        return this.httpService.postHttpRequest(url, {
            ...data,
        });
    }

    async testTransformTaiKham(): Promise<any> {

        const params = {
            fromPartnerId: 'nhidong1',
            toPartnerId: 'medpro',
            countryId: `VN`,
            cityId: `701`,
            districtId: `70101`,
            wardId: `7010101`,
            nationId: `25`,
            relationTypeId: '2',
            /* thông tin serviceId, subjectId, roomId, doctorId */
            subjectId: '26',
            roomId: '181',
            doctorId: null,
            serviceId: null,
        };

        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const urlTransform = `${baseUrl}/his-connector/api/convertPartnerDataReExam`;
        const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
        return dataKeys;
    }

    async search(appId: string, partnerId: string, data: ReExamSearchDTO): Promise<any> {
        const list = this.reExamModel
            .find({
                phone: data.phone,
                partnerId,
            })
            .sort({ scheduleDate: 'desc' })
            .exec();
        const maps = (await list).map(item => item.toObject());
        /* tiến hành groupby */
        let filterData: any = [];
        if (partnerId === 'leloi') {
            const groupBySubject = groupBy(maps, 'subjectId');
            const keys = Object.entries(groupBySubject);
            for await (const [key, value] of keys) {
                const firstReExam = first(value);
                filterData = [...filterData, firstReExam];
            }
        } else {
            filterData = maps;
        }
        // return filterData;
        let listKhaDung = [];
        const baseUrl = await this.getBookingTreeRestApi(partnerId)
        for await (const item of filterData) {
            /* Kiểm tra tính khả dụng của tái khám */
            try {
                const isValid = (await this.isValidReExam(baseUrl, item.id)
                    .toPromise()).data;
                if (isValid) {
                    listKhaDung = [...listKhaDung, item];
                }
            } catch (error) {
                this.clientSentry.instance().captureException(error);
                // throw new HttpException('Không tìm thấy thông tin lịch khám', HttpStatus.BAD_REQUEST);
            }
        }
        // .filter(item => moment().isBefore(moment(item.scheduleDate).toDate()));
        let listResult = [];
        for await (const item of listKhaDung) {
            /* tiến hành xử lý thông tin tái khám. lấy thông tin booking tree */
            const keysObject = [
                {
                    key: 'serviceId',
                    collectioName: 'services',
                    keyReturn: 'service',
                },
                {
                    key: 'subjectId',
                    collectioName: 'subjects',
                    keyReturn: 'subject',
                },
                {
                    key: 'roomId',
                    collectioName: 'rooms',
                    keyReturn: 'room',
                },
                {
                    key: 'doctorId',
                    collectioName: 'doctors',
                    keyReturn: 'doctor',
                },
            ];
            /* tiếp tục tìm lại trong collection để get data */
            const listKeysReturn: any = {
                service: {},
                subject: {},
                room: {},
                doctor: {},
            };
            let description = '';
            const baseUrlB = await this.getBookingTreeRestApi(partnerId);
            for await (const obj of keysObject) {
                const collectioName = obj.collectioName;
                const key = obj.key;
                const value = item[key];
                const keyReturn = obj.keyReturn;
                if (value) {
                    const id = (await this.getId(baseUrlB, partnerId, collectioName, value)
                        .toPromise()).data;
                    const objectInfo = await this.getObjectInfo(collectioName, id);
                    if (objectInfo) {
                        const toObject = objectInfo.toObject();
                        listKeysReturn[keyReturn] = { ...toObject };
                    }
                }
                /* tiến hành kiểm tra trạng thái tái khám */
                /* kiểm tra xem thông tin tái khám này đã booking chưa */
                const getCountReExam = await this.bookingModel.countDocuments({
                    idReExam: item.id,
                    status: 1,
                });

                if (getCountReExam > 0) {
                    description = 'Tái khám đã đặt lịch và thanh toán thành công.';
                }
            }
            listResult = [...listResult, {
                ...item,
                ...listKeysReturn,
                description,
            }];
        }
        return listResult;
    }

    async process(data: ReExamQueryDTO, appid: string = '', partnerid: string = ''): Promise<any> {
        // const urlEncode = encodeURIComponent(data.url);
        // const url = decodeURIComponent(urlEncode);
        /* tìm lại thông tin tái khám */
        // console.log('data', data)
        const condition: any = {};
        if (data?.url || '') {
            condition.quickBookingUrl = data.url;
        }
        if (data?.urlId || '') {
            condition.urlId = data.urlId;
        }

        if (Object.keys(condition).length === 0) {
            throw new HttpException('Không tìm thấy thông tin tái khám!', HttpStatus.NOT_FOUND);
        }

        const reExamInfo = await this.reExamModel.findOne({ ...condition }).exec();

        if (!reExamInfo) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'reExamInfo',
                nameParent: 'process',
                params: { url: data.url },
                errorCode: 'process_01',
            });
            throw new HttpException('Không tìm thấy thông tin tái khám.', HttpStatus.NOT_FOUND);
        }

        if (appid !== 'medpro' && appid !== reExamInfo.partnerId) {
            throw new HttpException("Link tái khám này không hợp lệ, vui lòng liên hệ 19002115 để được hỗ trợ", HttpStatus.BAD_REQUEST)
        }

        /* lấy thông tin partner config */
        const partnerId = reExamInfo.partnerId;
            /* Kiểm tra tính khả dụng của tái khám */
                const baseUrl = await this.getBookingTreeRestApi(partnerId);
        try {

            const startTime = moment();
            // console.log('start isValidReExam: ', startTime.toISOString());
                const isValid = (await this.isValidReExam(baseUrl, reExamInfo.id)
                    .toPromise()).data;
            const endTime = moment();
            // console.log('end isValidReExam: ', endTime.toISOString());
            // console.log('duration isValidReExam: ', moment.duration(endTime.diff(startTime)).milliseconds());
                if (!isValid) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'isValid',
                        nameParent: 'process',
                        params: { reExamInfoId: reExamInfo.id, baseUrl },
                        errorCode: 'process_02',
                    });
                    throw new HttpException('Thông tin tái khám này không khả dụng.', HttpStatus.BAD_REQUEST);
                }
            } catch (error) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'isValid',
                    nameParent: 'process',
                    params: { reExamInfoId: reExamInfo.id, partnerId },
                    message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                    errorBody: this.utilService.errorHandler(error),
                    errorCode: 'process_03',
                });
            throw new HttpException(error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
            }

            const patientCode = reExamInfo.patientCode;
        const medproPatientId = reExamInfo.medproPatientId;
        // const dataPatients = await this.patientService.searchPatientByMsbn(null, partnerId, partnerId, { msbn: patientCode }, false);
        if (reExamInfo.isVerified) {
            const [partner, userPatientData] = await Promise.all([
                this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
                this.handleUserPatient({ medproPatientId, partnerId, appid }),
                // this.bookingModel.countDocuments({
                //     idReExam: reExamInfo.id,
                //     status: 1,
                // })
            ])

            const { userInfo,patient } = userPatientData;

            /* kiểm tra xem thông tin tái khám này đã booking chưa */
            // const getCountReExam = await this.bookingModel.countDocuments({
            //     idReExam: reExamInfo.id,
            //     status: 1,
            // });

            const { userMongoId: aaaa, userId: bbbb, ...restUserInfo } = userInfo;

            const overrideBirtDate = patient?.birthdate || '';

            return {
                user: { ...restUserInfo },
                partner,
                patient,
                reExam: reExamInfo.toObject(),
                message: '',
                isRequiredBooking: true,
            };
        } else {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getBookingTree',
                nameParent: 'process',
                params: { patientCode, partnerId },
                message: 'Thực hiện tái khám không thành công',
                errorCode: 'process_05',
            });
            throw new HttpException('Thực hiện tái khám không thành công.', HttpStatus.BAD_REQUEST);
        }
    }

    async getObjectInfo(collectioName: string, id: string): Promise<any> {
        switch (collectioName) {
            case 'services':
                return this.serviceModel.findOne({ id }).exec();
            case 'subjects':
                return this.subjectModel.findOne({ id }).exec();
            case 'rooms':
                return this.roomModel.findOne({ id }).exec();
            case 'doctors':
                return this.doctorModel.findOne({ id }).exec();
            default:
                return null;
        }
    }

    // async testGet(): Promise<any> {
    //     /* Lấy thông tin booking tree */
    //     let dataTree = null;
    //     try {
    //         dataTree = (await this.getBookingTree()
    //             .toPromise()).data;
    //         return dataTree;
    //     } catch (error) {
    //         throw new HttpException('Không tìm thấy thông tin lịch khám', HttpStatus.BAD_REQUEST);
    //     }
    // }

    isValidReExam(baseUrl: string, id: string): Observable<AxiosResponse<any>> {
        const url = `${baseUrl}/schedule/v2/booking/validReExam/${id}`;
        return this.httpService.getHttpRequest(url);
    }

    async handleUserPatient(params: any) {
        const { medproPatientId, partnerId, appid } = params;
        const findPatient = await this.patientModel.findOne({ id: medproPatientId }, { mobile: true }).exec();
        if (!findPatient) {
                    throw new HttpException('Không tìm thấy hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
                }

        const patient = await this.patientService.getPatientInfoAndPatientCodeById(partnerId, appid, findPatient._id);

                /* lấy thông tin mobile để check. nếu không có thì tạo tài khoản luôn */
        let mobile = get(patient, 'mobile', '');
        if (partnerId === 'nhidong1') {
            mobile = get(patient, 'relation.relative_mobile', mobile)
        }

                /* Kiểm tra xem mobile có hợp lệ hay ko */
                if (`${mobile}`.length === 9) {
                    mobile = `0${mobile}`;
                }
                const checkPhone = isMobilePhone(mobile, 'vi-VN');
                if (!checkPhone) {
                    throw new HttpException(`Số điện thoại ${mobile} đăng ký không hợp lệ.`, HttpStatus.BAD_REQUEST);
                }

                const yourphone = `${mobile}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
                const userInfo = await this.autoCreateUser(yourphone);
                /* kiểm tra xem hồ sơ bệnh nhân này đã thuộc về user hay chưa */
                const userMongoId = userInfo.userMongoId;

        return {
            userInfo, patient
                }
    }

    async handleGetBookingTree(params: any) {
        const { partnerId, reExamInfo, baseUrl} = params;

        const { service, subject, doctor, room } = reExamInfo.toObject();

                const bookingTreeParams: any = {
            serviceId: service?.id,
            subjectId: subject?.id,
            roomId: room?.id,
            doctorId: doctor?.id,
                };

                let dataTree = null;

                try {
            dataTree = await this.getBookingTree(baseUrl, reExamInfo, bookingTreeParams, partnerId);

            return {
                dataTree,
            }
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'getBookingTree',
                        nameParent: 'process',
                        params: { baseUrl, reExamInfo, bookingTreeParams, partnerId },
                        message: error?.message || 'Không tìm thấy thông tin lịch khám',
                        errorBody: this.utilService.errorHandler(error),
                        errorCode: 'process_04',
                    });
                    throw new HttpException('Không tìm thấy thông tin lịch khám', HttpStatus.BAD_REQUEST);
                }
    }

    async getBookingTree(baseUrl: string, reExamInfo: any, bookingTreeData: any, partnerId: string): Promise<any> {
        const url = `${baseUrl}/schedule/v2/booking/schedule/re-exam`;
        const params = {
            partnerId,
            serviceId: bookingTreeData.serviceId,
            subjectId: bookingTreeData.subjectId,
            roomId: bookingTreeData.roomId,
            doctorId: bookingTreeData.doctorId,
                    idReExam: reExamInfo.id,
        };
        // console.log('parms', params);
        // console.log('moment', moment(reExamInfo.scheduleDate).format('DD-MM-YYYY'));
        // console.log('moment utc', moment(reExamInfo.scheduleDate).utc().format('DD-MM-YYYY'));
        const startTime = moment();
        // console.log('start get booking tree: ', startTime.toISOString());
        const { data } = await this.httpService.postHttpRequest(url, params).toPromise();
        const endTime = moment();
        // console.log('end get booking tree: ', endTime.toISOString());
        // console.log('duration get booking tree: ', moment.duration(endTime.diff(startTime)).milliseconds());

        return data;
    }

    async processBookingTreeData(data: ReExamQueryDTO, partnerId: string, appId: string) {
        // console.log('data', data)
        const condition: any = {};
        if (data?.url || '') {
            condition.quickBookingUrl = data.url;
                }
        if (data?.urlId || '') {
            condition.urlId = data.urlId;
                }

        if (Object.keys(condition).length === 0) {
            throw new HttpException('Không tìm thấy thông tin tái khám!', HttpStatus.NOT_FOUND);
        }

        const reExamInfo = await this.reExamModel.findOne({ ...condition }).exec();
        if (!reExamInfo) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'reExamInfo',
                nameParent: 'processBookingTreeData',
                params: { url: data.url },
                errorCode: 'process_13',
                });
            throw new HttpException('Không tìm thấy thông tin tái khám.', HttpStatus.NOT_FOUND);
        }

        const baseUrl = await this.getBookingTreeRestApi(reExamInfo.partnerId);
        const { dataTree } = await this.handleGetBookingTree({ partnerId: reExamInfo.partnerId, reExamInfo, baseUrl })
        return dataTree;
            }

    getId(baseUrl: string, partnerId: string, collectionName: string, value: string): Observable<AxiosResponse<any>> {
        const url = `${baseUrl}/schedule/v1/booking/getIdByCode/${partnerId}/${collectionName}/${value}`;
        // console.log(url);
        return this.httpService.getHttpRequest(url);
    }

    async autoCreateUser(userName: string): Promise<any> {
        let payload = { username: '', sub: 0, userMongoId: null };
        let resultData = {
                userId: 0,
                userName: '',
                fullName: '',
                number: '',
                email: '',
                token: '',
                historyBookingCount: 0,
                patientCount: 0,
            };
        const checkUserMongo = await this.userService.checkExistsUserByUsernameMongo(userName);
        /* tiến hành tạo tài khoản mongo */
        let userMongoId = 0;
        if (!checkUserMongo) {
            const userSalt = this.userService.generateSalt();
            const userHashPwd = this.userService.generateHashPwd(userSalt, userSalt);
            /* Tạo mới thông tin user trên mongo */
            const userMongo = new this.userModel({
                username: userName,
                password: userHashPwd,
                salt: userSalt,
                email: userName,
                fullname: '',
            });
            const result = (await userMongo.save()).toJSON();
            userMongoId = result.id;
        } else {
            userMongoId = checkUserMongo._id;
        }
        payload = {
            ...payload, username: userName, userMongoId,
        };
        resultData = { ...resultData, email: userName };
        /* Tạo chuỗi Json web token */
        const token = this.jwtService.sign(payload);
        return { ...resultData, token, userMongoId };
    }

    async getReExams(partnerId: string, params: ReExamOptionsDto): Promise<any> {
        const data = await this.reExamModel.find({...params, partnerId }).sort({createTime: 'desc'}).limit(10).exec();
        return Promise.all(data.map(async reExam => {
            let patientOvveride: any;
            let room: any;
            let subject: any;
            let service: any;
            let doctor: any;
            let section: any;

            const reExamObj = reExam.toObject();
            const { patientDetail, serviceId = '', roomId = '', subjectId = '', doctorId = '' } = reExamObj;
            if (patientDetail) {
                patientOvveride = {
                    ...patientDetail,
                    fullname: `${patientDetail?.patientLastName} ${patientDetail?.patientFirstName}`,
                    sex: patientDetail?.patientSex === 'Nam' ? 'NAM' : 'NỮ',
                    address: patientDetail?.addressDetail,
                    birthdate: patientDetail?.patientBirthDay && moment(patientDetail.patientBirthDay, 'YYYY-MM-DD').isValid() ?
                        moment(patientDetail.patientBirthDay, 'YYYY-MM-DD').format('DD/MM/YYYY') : patientDetail?.patientBirthYear,
                    birthyear: patientDetail?.patientBirthYear,
                };
            }

            const [findService, findRoom, findSubject, findDoctor] = await Promise.all([
                this.serviceModel.findOne({ id: serviceId }).exec(),
                this.roomModel.findOne({ id: roomId }).exec(),
                this.subjectModel.findOne({ id: subjectId }).exec(),
                this.doctorModel.findOne({ id: doctorId }).exec(),
            ]);

            if (findService) {
                service = findService.toObject();
            }
            if (findSubject) {
                subject = findSubject.toObject();
            }

            if (findRoom) {
                room = findRoom.toObject();
                /* kiểm tra xem có thông tin khu khám */
                const getSectionId = get(room, 'sectionId', null);
                if (getSectionId) {
                    /* tìm thông tin section */
                    const findSection = await this.sectionModel.findOne({ id: getSectionId }).exec();
                    if (findSection) {
                        section = findSection.toObject();
                    }
                }
            }

            if (findDoctor) {
                doctor = findDoctor.toObject();
            }

            return {
                ...reExamObj,
                patientDetail: patientOvveride,
                room,
                subject,
                service,
                doctor,
                section,
            };
        }));
    }

    async handlePortalPushDataNhidong1(reExamInfo: any) {
                const reExamObj = reExamInfo.toObject();
        const { patientDetail, subjectId, serviceId, doctorId, roomId, partnerId } = reExamObj;
        const appid = partnerId;
        const partnerid = partnerId;
        const reExamQuery: ReExamQueryDTO = { url: reExamObj.quickBookingUrl, urlId: reExamObj.urlId };

                if (patientDetail) {
                    const { relative } = patientDetail;
                    const { phone: phonePrev, relativeType, fullname } = relative;
                    let phone = phonePrev;
                    if (`${phone}`.length === 9) {
                        phone = `0${phone}`;
                    }
                    const checkPhone = isMobilePhone(phone, 'vi-VN');
                    if (checkPhone) {
                        const userSalt = this.userService.generateSalt();
                        const userHashPwd = this.userService.generateHashPwd('xd3fx22dt3', userSalt);
                        const yourPhone = `${phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
                        /* kiểm tra xem username là số điện thoại đã được đăng ký hay chưa */
                        const checkUserMongo: IUser = await this.userService.checkExistsUserByUsernameMedproId(yourPhone);
                        let result: IUser = checkUserMongo;
                        // const fullname = tennguoidung ? tennguoidung : 'Người dùng medpro';
                        if (!checkUserMongo) {
                            try {
                                /* Tiến hành lưu user constraints trước */
                                const newUserConstraints = new this.constraintUser({
                                    medproId: `mp${yourPhone}`,
                                });
                                await newUserConstraints.save();

                                /* Tạo mới thông tin user trên mongo */
                                const userMongo = new this.userModel({
                                    username: yourPhone,
                                    password: userHashPwd,
                                    salt: userSalt,
                                    email: yourPhone,
                                    fullname,
                                    medproId: `mp${yourPhone}`,
                                });
                                result = await userMongo.save();
                            } catch (error) {
                                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                    name: 'newUserConstraints',
                                    nameParent: 'process',
                                    params: {
                                        username: yourPhone,
                                        password: userHashPwd,
                                        salt: userSalt,
                                        email: yourPhone,
                                        fullname,
                                        medproId: `mp${yourPhone}`,
                                    },
                                    message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                                    errorBody: this.utilService.errorHandler(error),
                                    errorCode: 'process_06',
                                });
                                result = await this.userService.checkExistsUserByUsernameMedproId(yourPhone);
                            }
                        }

                        /* kiểm tra type */
                        const type = 'password';
                        /* kiểm tra xem phương thức gửi lên đã có chưa */
                        const checkProvider = await this.signInProvider.findOne({
                            type,
                            user: result._id,
                        }).exec();

                        if (!checkProvider) {
                            try {
                                /* Tiến hành lưu user constraints trước */
                                const newProviderConstraints = new this.constraintProvider({
                                    providerId: `mp${yourPhone}_password`,
                                });
                                await newProviderConstraints.save();

                                /* tiến hành tạo thêm thông tin trong sign_in_provider */
                                const signInProvider = new this.signInProvider({
                                    id: uuid.v4().replace(/-/g, ''),
                                    type,
                                    password: userHashPwd,
                                    salt: userSalt,
                                    user: result._id,
                                    displayName: fullname,
                                    autoCreate: true,
                                });
                                await signInProvider.save();

                            } catch (error) {
                                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                    name: 'newProviderConstraints',
                                    nameParent: 'process',
                                    params: {
                                        type,
                                        password: userHashPwd,
                                        salt: userSalt,
                                        user: result._id,
                                        displayName: fullname,
                                        autoCreate: true,
                                        providerId: `mp${yourPhone}_password`,
                                    },
                                    message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                                    errorBody: this.utilService.errorHandler(error),
                                    errorCode: 'process_07',
                                });
                                this.clientSentry.instance().captureException(error);
                            }
                        }
                        /* gán thông tin */
                        try {
                            await this.userService.getUserIdAndCreateSessionV1(appid, partnerid, result);
                        } catch (error) {
                            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                name: 'getUserIdAndCreateSessionV1',
                                nameParent: 'process',
                                params: {
                                    appid, partnerid, result
                                },
                                message: error?.message || 'Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1',
                                errorBody: this.utilService.errorHandler(error),
                                errorCode: 'process_08',
                            });
                        }
                        /* tiến hành tạo thông tin và sync qua v1 */
                        const { addressDetail, addressStreet, patientBirthDay, patientBirthYear,
                            patientFirstName, patientLastName, patientSex, hospitalPatientId } = patientDetail;

                        const address = `${addressDetail} ${addressStreet}`;
                        /* tiến hành transform data */
                        const {
                            patientEthnicId,
                            professionId,
                            addressCountryId,
                            addressProvinceId,
                            addressDistrictId,
                            addressWardId,
                        } = patientDetail;

                        const params = {
                            fromPartnerId: 'nhidong1',
                            toPartnerId: 'medpro',
                            countryId: `${addressCountryId}`,
                            cityId: `${addressProvinceId}`,
                            districtId: `${addressDistrictId}`,
                            wardId: `${addressWardId}`,
                            nationId: `${patientEthnicId}`,
                            relationTypeId: relativeType,
                            subjectId,
                            serviceId,
                            doctorId,
                            roomId,
                            professionId,
                        };

                        const baseUrl22 = this.urlConfigService.getBookingTreeUrl();
                        const urlTransform = `${baseUrl22}/his-connector/api/convertPartnerDataReExam`;
                        let dataKeys: any = {};
                        try {
                            dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
                            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                name: 'reExam transformDataPatientV1',
                                nameParent: 'process',
                                params: {
                                    urlTransform,
                                    params,
                                    response: dataKeys
                                },
                                response: dataKeys,
                                message: 'Transform Data',
                                errorBody: null,
                                errorCode: 'process_101',
                            });
                        } catch (error) {
                            // service logs
                            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                name: 'reExam transformDataPatientV1',
                                nameParent: 'process',
                                params: {
                                    params,
                                    urlTransform,
                                },
                                response: null,
                                message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau',
                                errorBody: this.utilService.errorHandler(error),
                                errorCode: 'process_100',
                            });
                        }
                        const { districtId, nationId, cityId, wardId, relationTypeId, countryId,
                            subjectId: sub, serviceId: ser, doctorId: doc, roomId: rom, professionId: profession } = dataKeys;
                        /* overide */
                        reExamInfo.subjectId = sub;
                        reExamInfo.serviceId = ser;
                        reExamInfo.doctorId = doc;
                        reExamInfo.roomId = rom;
                        /* tiến hành tạo thông tin để insert */
                        const dataInsert: PatientFormMongoDTO = {
                            name: patientFirstName,
                            surname: patientLastName,
                            email: '',
                            birthdate: moment(`${patientBirthDay} 23:11:11`, 'YYYY-MM-DD HH:mm:ss').toISOString(),
                            birthyear: patientBirthYear,
                            sex: (patientSex === 'Nam' ? 1 : 0),
                            mobile: '',
                            cmnd: '',
                            code: '',
                            profession: null,
                            profession_id: profession,
                            nation: null,
                            dantoc_id: nationId,
                            address,
                            country: '',
                            country_code: 'VIE',
                            country_id: countryId,
                            city: '',
                            city_id: cityId,
                            district: '',
                            district_id: districtId,
                            ward: '',
                            ward_id: wardId,
                            relative_email: relative.email,
                            relative_mobile: relative.phone,
                            relative_name: relative.fullname,
                            relative_type_id: relationTypeId,
                            force: true,
                            id: null,
                            patientCode: hospitalPatientId,
                        };
                        // console.log('params truoc khin', dataInsert);
                        let patientInfoResult = {};
                        /* tìm lại xem hồ sơ này đã có chưa dựa vào thông tin patient code - userId*/
                // console.log('filter patient code: ', { patientCode: dataInsert.patientCode, partnerId });
                        const checkPatientCode = await this.patientCodeModel
                            .findOne({ patientCode: dataInsert.patientCode, partnerId })
                            .exec();
                        /* Tiếp tục check xem patient này có thuộc user này ko */
                        let checkExists = false;
                        let idMongoPatient = '';

                // console.log('checkPatientCode: ', checkPatientCode);
                        if (checkPatientCode) {
                    const checkPatientUser = await this.patientModel.findOne({ id: checkPatientCode.patientId }).exec();
                            if (checkPatientUser) {
                                checkExists = true;
                                idMongoPatient = checkPatientUser._id;
                            }
                        }

                        if (checkExists) {
                    // console.log('checkExists true -> getPatientInfoAndPatientCodeById ');
                            /* lấy lại thông tin patientInfoResult */
                            patientInfoResult = await this.patientService.getPatientInfoAndPatientCodeById('nhidong1', 'nhidong1', idMongoPatient);
                        } else {
                    // console.log('checkExists false -> insertPatient ');
                            try {
                                const patientInfoResult22: any = await this.patientService.insertPatient(partnerId, 'medpro',
                            { id: 0, userMongoId: result._id }, dataInsert, null, reExamQuery);
                                /* tìm lại thông tin */
                                const findPatientaaa = await this.patientModel.findOne({ id: patientInfoResult22.id }).exec();
                                patientInfoResult = await this.patientService.getPatientInfoAndPatientCodeById(
                                    'nhidong1', 'nhidong1', findPatientaaa._id);
                            } catch (error) {
                        // console.log('error insert patient nhidong1: ', error);
                                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                    name: 'insertPatient getPatientInfoAndPatientCodeById',
                                    nameParent: 'process',
                                    params: {
                                        idMongoPatient, userMongoId: result._id, dataInsert,
                                    },
                                    message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau',
                                    errorBody: this.utilService.errorHandler(error),
                                    errorCode: 'process_09',
                                });
                            }
                        }

                // console.log('patientInfoResult: ', patientInfoResult);

                return {
                    patient: patientInfoResult
                };
            } else {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'checkPhone',
                    nameParent: 'process',
                    params: { phone },
                    message:  `Số điện thoại ${phonePrev} đăng ký không hợp lệ.`,
                    errorCode: 'process_11',
                });
                throw new HttpException(`Số điện thoại ${phonePrev} đăng ký không hợp lệ.`, HttpStatus.BAD_REQUEST);
            }
        } else {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'reExamInfo',
                nameParent: 'process',
                params: { quickBookingUrl: reExamQuery.url  },
                message:  `Thiếu thông tin hồ sơ bệnh nhân`,
                errorCode: 'process_12',
            });
            throw new HttpException('Thiếu thông tin hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
        }
    }

    async handlePortalPushData(data: PortalPushReExamDataDto) {
        const { id } = data;
        const reExamInfo = await this.reExamModel.findOne({ id }).exec();

        if (!reExamInfo) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'reExamInfo',
                nameParent: 'handlePortalPushData',
                params: { ...data },
                message: 'Không tìm thấy thông tin tái khám',
                errorCode: 'handlePortalPushData_01',
            });
            throw new HttpException('Không tìm thấy thông tin tái khám.', HttpStatus.NOT_FOUND);
        }

        const { partnerId, patientCode, medproPatientId } = reExamInfo

        let dataPatients;

        if (partnerId === 'nhidong1') {
            const res = await this.handlePortalPushDataNhidong1(reExamInfo);
            dataPatients = [res.patient];
        } else {
            try {
                if (medproPatientId) {
                    dataPatients = [{id: medproPatientId}]
                } else {
                    dataPatients = await this.patientService.searchPatientByMsbn(null, partnerId, partnerId, { msbn: patientCode }, false);
                }
            } catch (err) {
                throw err;
            }
        }

        if (dataPatients && dataPatients.length > 0) {
            /* lấy thông tin mobile */
            const patient: any = first(dataPatients);
            /* lấy thông tin mobile */
            const getPatient = await this.patientModel.findOne({ id: patient.id }).exec();
            if (!getPatient) {
                reExamInfo.set({isVerified: false});
                await reExamInfo.save();
                throw new HttpException('Không tìm thấy hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
            }

            const [
                envCheckConstraintPatient,
                appAcceptConstraint,
            ] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
                this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT')
            ]);
            const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));

            if (envCheckConstraintPatient === 'ON' && setAppAcceptConstraint.has(partnerId)) {
                // console.log('patientService.checkConstrainsPatientDetail');
                const [patientCheckRule] = await this.patientService.checkConstrainsPatientDetail([getPatient], { locale: 'vi', partnerid: partnerId, appid: partnerId });
                if (patientCheckRule.constraintInfo && !patientCheckRule.constraintInfo.isValid) {
                    reExamInfo.patientConstraintInfo = patientCheckRule.constraintInfo;
                    reExamInfo.isVerified = false;
                    await reExamInfo.save()
                    throw new HttpException({message: 'Thông tin bệnh nhân không hợp lệ', constraintInfo: patientCheckRule.constraintInfo}, HttpStatus.BAD_REQUEST);
                }
                // console.log('after patientService.checkConstrainsPatientDetail');
            }

            if (partnerId !== 'nhidong1') {
                // console.log('xu ly user');
                let mobile = get(getPatient, 'mobile', '');
                /* Kiểm tra xem mobile có hợp lệ hay ko */
                if (`${mobile}`.length === 9) {
                    mobile = `0${mobile}`;
                }

                const checkPhone = isMobilePhone(mobile, 'vi-VN');
                if (!checkPhone) {
                    throw new HttpException(`Số điện thoại ${mobile} đăng ký không hợp lệ.`, HttpStatus.BAD_REQUEST);
                }
                const yourphone = `${mobile}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
                const userInfo = await this.autoCreateUser(yourphone);
                // console.log('after autoCreateUser');
                /* kiểm tra xem hồ sơ bệnh nhân này đã thuộc về user hay chưa */
                const userMongoId = userInfo.userMongoId;
                /* tìm lại thông tin user */
                // console.log('checkUMCPatientBelongsToUser');
                const check = await this.patientService.checkUMCPatientBelongsToUser(userMongoId, getPatient._id);
                // console.log('after checkUMCPatientBelongsToUser');

                  if (!check) {
                    /* add hồ sơ bệnh nhân vào trong user. */
                    // console.log('patientService.addUserPatient');
                    await this.patientService.addUserPatient(userMongoId, getPatient._id);
                    getPatient.userId = userMongoId;
                    getPatient.sourceId = partnerId;
                    getPatient.partnerId = partnerId;
                    await getPatient.save();
                    // console.log('after patientService.addUserPatient');
                }

            }

            // transform schedule data
                        const keysObject = [
                            {
                                key: 'serviceId',
                                collectioName: 'services',
                                keyReturn: 'service',
                            },
                            {
                                key: 'subjectId',
                                collectioName: 'subjects',
                                keyReturn: 'subject',
                            },
                            {
                                key: 'roomId',
                                collectioName: 'rooms',
                                keyReturn: 'room',
                            },
                            {
                                key: 'doctorId',
                                collectioName: 'doctors',
                                keyReturn: 'doctor',
                            },
                        ];
                        /* tiếp tục tìm lại trong collection để get data */
                        const listKeysReturn: any = {
                            service: {},
                            subject: {},
                            room: {},
                            doctor: {},
                        };
                        const bookingTreeParams: any = {
                            serviceId: null,
                            subjectId: null,
                            roomId: null,
                            doctorId: null,
                        };

            const bookingTreeRestApi = await this.getBookingTreeRestApi(partnerId);
            // console.log('get booking tree getObjectInfo');
                        for await (const obj of keysObject) {
                            const collectioName = obj.collectioName;
                            const key = obj.key;
                            const value = reExamInfo[key];
                            const keyReturn = obj.keyReturn;
                            if (value) {
                    const id = (await this.getId(bookingTreeRestApi, partnerId, collectioName, value)
                                    .toPromise()).data;
                                const objectInfo = await this.getObjectInfo(collectioName, id);
                                if (objectInfo) {
                                    const toObject = objectInfo.toObject();
                                    bookingTreeParams[key] = id;
                                    listKeysReturn[keyReturn] = { ...toObject };
                                }
                            }
                        }
            // console.log('after get booking tree getObjectInfo');

            reExamInfo.set({isVerified: true, medproPatientId: getPatient.id, ...listKeysReturn });
            await reExamInfo.save();

            const { _id, __v, id, ...rest } = reExamInfo.toObject()
            let reExamVerify = await this.reExamVerifyModel.findOne({reExamId: id}).exec();
            if (!reExamVerify) {
                reExamVerify = await this.reExamVerifyModel.create({reExamId: id });
            }

            reExamVerify.set({...rest});
            await reExamVerify.save();

                        try {
                // console.log('message-event/push-notif-reexam: ');
                await this.http.post(`${this.urlConfigService.getUrlPushNotifV2}/message-event/push-notif-reexam`, { reExamId: reExamInfo.id }).toPromise();

                // console.log('after message-event/push-notif-reexam');
            } catch (err) {
                // console.log('error message-event/push-notif-reexam', err);
                        }

            return { isOk: true }
                } else {
            reExamInfo.set({isVerified: false});
            await reExamInfo.save();

                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'searchPatient',
                nameParent: 'handlePortalPushData',
                params: { id, patientCode, partnerId },
                message: 'Không tìm thây thông tin bệnh nhân',
                errorCode: 'handlePortalPushData_05',
            });
            throw new HttpException('Không tìm thây thông tin bệnh nhân', HttpStatus.BAD_REQUEST);
        }
    }
}
