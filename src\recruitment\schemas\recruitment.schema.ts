import * as mongoose from 'mongoose';
import { RECRUITMENT_COLLECTION_NAME } from './constants';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const recruitmentSchema = new Schema(
    {
        title: { type: String, required: true },
        description: { type: String, required: true },
        cityId: { type: String, required: true },
        city:{ type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
        salary: { type: String, required: true },
        quantity: { type: Number, required: true },
        deadline: { type: String, required: true },
        requirements: { type: String, required: true },
        benefits: { type: String, required: true },
        contactInfo: { type: String, required: true },
        status: { type: Boolean, required: true, default: false },
        jobNature: {
            type: Object,
            required: true,
        },
        workingForm:{
            type: Object,
            required: true,
        },
    },
    {
        collection: RECRUITMENT_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
