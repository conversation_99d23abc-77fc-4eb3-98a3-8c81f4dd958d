import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, BOOKING_SUBJECT_REPORT_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { SUBJECT_COLLECTION_NAME } from '../../subject-mongo/schemas/constants';
import { count } from 'console';

const Schema = mongoose.Schema;

export const BookingSubjectReportSchema = new Schema({
    partnerId: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    subjectId: { type: String },
    subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
    name: { type: String },
    count: { type: Number },
}, {
    collection: BOOKING_SUBJECT_REPORT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
