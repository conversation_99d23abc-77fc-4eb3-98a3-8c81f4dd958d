import { Global, Injectable, Logger } from '@nestjs/common';
import { MongooseModuleOptions, MongooseOptionsFactory } from '@nestjs/mongoose';
import * as Joi from 'joi';
import { ConfigService } from '@nestjs/config';

@Global()
@Injectable()
export class ConfigMongoBackofficeService extends ConfigService implements MongooseOptionsFactory {
    logger = new Logger(ConfigMongoBackofficeService.name);

    provideConfigSpec() {
        return {
            MONGO_BO_HOST: {
                validate: Joi.string(),
                required: true,
            },
            MONGO_BO_PORT: {
                validate: Joi.number(),
                required: true,
                default: 8017,
            },
            MONGO_BO_DATABASE: {
                validate: Joi.string(),
                required: true,
                default: 'StaticResource',
            },
            MONGO_SINGLE_OR_CLUSTER: {
                validate: Joi.string(),
                required: true,
                default: 'SINGLE',
            },
            MONGO_CLUSTER_HOST: {
                validate: Joi.string(),
                required: true,
                default: 'mongo-master.medpro.com.vn:27017,mongo-replicas1.medpro.com.vn:27018,mongo-replicas2.medpro.com.vn:27019',
            },
            MONGO_CLUSTER_DATABASE: {
                validate: Joi.string(),
                required: true,
                default: 'StaticResource',
            },
            MONGO_OPTIONS: {
                validate: Joi.string(),
                required: true,
                default: 'medpro',
            },
        };
    }

    getMongoConfig() {
        return {
            username: this.get<string>('MONGO_BO_USERNAME'),
            password: this.get<string>('MONGO_BO_PASSWORD'),
            host: this.get<string>('MONGO_BO_HOST'),
            port: this.get<string>('MONGO_BO_PORT'),
            database: this.get<string>('MONGO_BO_DATABASE'),
            replicaSet: this.get<string>('MONGO_BO_REPLICASET'),
        };
    }

    getUri(): string {
        const { username, password, host, port, database, replicaSet } = this.getMongoConfig();
        let uri = `mongodb://${!!username && !!password ? `${username}:${encodeURIComponent(password)}@` : ``}${host}:${port}/${database}`;
        if (replicaSet) {
            uri = uri.concat(`&replicaSet=${replicaSet}`);
        }
        return uri;
    }

    createMongooseOptions(): MongooseModuleOptions {
        // const { username, password, host, port, database } = this.getMongoConfig();
        // const uri = `mongodb://${!!username && !!password ? `${username}:${encodeURIComponent(password)}@` : ``}${host}:${port}/${database}?authSource=admin`;
        let uri: string;
        if (this.getSwitchMongoMode === 'SINGLE') {
            const { username, password, host, port, database } = this.getMongoConfig();
            uri = `mongodb://${!!username && !!password ? `${username}:${encodeURIComponent(password)}@` : ``}${host}:${port}/${database}${
                this.getMongoOption
            }`;
        } else {
            const { username, password, host, port, database } = this.getMongoCluster;
            uri = `mongodb://${username && password ? `${username}:${password}@` : ''}${host}/${database}${this.getMongoOption}`;
        }

        console.log(uri);

        return {
            uri,
            useNewUrlParser: true,
            useUnifiedTopology: true,
        };
    }

    get getSwitchMongoMode(): string {
        return this.get<string>('MONGO_BO_SINGLE_OR_CLUSTER');
    }

    get getMongoCluster(): any {
        return {
            username: this.get<string>('MONGO_BO_USERNAME'),
            password: this.get<string>('MONGO_BO_PASSWORD'),
            host: this.get<string>('MONGO_BO_CLUSTER_HOST'),
            database: this.get<string>('MONGO_BO_CLUSTER_DATABASE'),
        };
    }

    get getMongoOption(): string {
        return this.get<string>('MONGO_OPTIONS');
    }
}
