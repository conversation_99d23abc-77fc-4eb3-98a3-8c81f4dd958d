import { Schema } from "mongoose";
import { IFeeRefundConfigs } from "../interfaces/fee-refund-configs.interface";
import { uuidV4Factory } from "../helpers/uuid-v4";
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from "../constants";

export const FeeRefundConfigsSchema = new Schema<IFeeRefundConfigs>(
    {
        _id: { type: String, required: true, default: uuidV4Factory },
        id: { type: String, required: true },
        createTime: { type: Date, required: true },
        feeRefundPartnerId: { type: String, required: true },
        feeRate: { type: Number, required: true },
        hospitalName: { type: String, required: true },
        searchUnicode: { type: String, required: true },
        sourceId: { type: String, required: true },
    },
    {
        collection: FEE_REFUND_CONFIGS_COLLECTION_NAME,
        timestamps: true
    }
)