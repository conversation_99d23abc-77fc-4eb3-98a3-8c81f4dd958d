import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { ClientUtilService } from '../config/client-util.service';
import { ConfigSMSMedproService } from '../config/config.sms.medpro.service';
import { ResetOtpLogDto } from './dto/reset-otp-log.dto';
import { GlobalSettingService } from '../global-setting/global-setting.service';
import { AUDIT_LOG_CSKH, BOOKING_CARE_247_CONSTRAINT, BOOKING_CARE_247_CONSTRAINT_USER, BOOKING_CARE_247_FLAGS, BOOKING_CARE_247_SUCCESS, CALL_BOT_CSKH, CARE247_SALES, CSKH_CONTACT_CUSTOMERS, CSKH_SCHEDULE_CATEGORIES, CSKH_SCHEDULERS, CSKH_SURVEY_CARE247_COLLECTION, INTRODUCTION_CARE247_SERVICE_CHANGES, K<PERSON>_CSKH, REFUND_TRANSACTION_SMS, RESET_OTP_LOGS_COLLECTION_NAME, RETRY_PAYMENT_BOOKING, SETTING_SALARY_CARE247, TRACKING_PUSH_NOTIF_CARE247, TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA, JOB_UPDATE_KPI, CARE247_INDEPENDENT_PAYMENT, AN_KHANG_BRANCHES_COLLECTION, AN_KHANG_USERS_COLLECTION } from './schemas/constants';
import { Model, Types } from 'mongoose';
import { IRestOtpLog } from './interfaces/reset-otp-log.interface';
import { InjectModel } from '@nestjs/mongoose';
import { REPAYMENT_LOG_COLLECTION_NAME, BOOKING_REFERRAL_COLLECTION_NAME } from '../message-event/schemas/constants';
import { IRepaymentLog } from '../message-event/interfaces/repayment-log.interface';
import { IBookingReferral } from '../message-event/interfaces/booking-referral.interface';
import { USER_COLLECTION_NAME, USER_LOGIN_LOG_COLLECTION_NAME } from '../user/schemas/constants';
import { IUser } from '../user/interfaces/user.interface';
import {
    BOOKING_CARE_247,
    BOOKING_COLLECTION_NAME,
    BOOKING_IGNORE_CONVERT,
    BOOKING_LOCKED_COLLECTION_NAME,
    CONVERT_USER_CSKH,
    PAYMENT_COLLECTION_NAME,
    RESERVE_BOOKING_LOGS_COLLECTION_NAME,
    SECTION_COLLECTION_NAME,
    VIEW_BOOKING_LOG_COLLECTION_NAME,
} from '../booking-gateway/schemas/constants';
import * as uuid from 'uuid';
import { IBooking } from '../booking-gateway/intefaces/booking.inteface';
import { CalledInfoDto } from './dto/called-info.dto';
import { IBookingLock } from '../booking-gateway/intefaces/booking-locked.inteface';
import { IViewBookingLog } from '../booking-gateway/intefaces/view-booking-log.inteface';
import { HOSPITAL_COLLECTION_NAME, PUSH_NOTIF_CSKH, PUSH_NOTIF_CSKH_CONSTRAINT } from '../hospital-mongo/schemas/constants';
import { IHospital } from '../hospital-mongo/interfaces/hospital.interface';
import { IPayment } from '../booking-gateway/intefaces/payment.inteface';
import { IPatient } from '../patient-mongo/intefaces/patient.inteface';
import { PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { UserService } from '../user/user.service';
import { IBookingCare247Success } from './interfaces/booking-care-247-success.interface';
import { IBookingCare247 } from '../booking-gateway/intefaces/booking-care-247.interface';
import { BookingCare247QueryDto } from './dto/booking-care247-query.dto';
import { first, groupBy, identity, keyBy, map, maxBy, pickBy, reduce, sortBy } from 'lodash';
import * as moment from 'moment';
import * as Excel from 'exceljs';
import { BookingCare247CskhRevenueQueryDto } from './dto/booking-care247-cskh-revenue-query.dto';
import { IBookingCare247Constraint } from './interfaces/booking-care247-constraint.interface';
import { MedproCareCancelDto } from './dto/medpro-care-cancel.dto';
import { ImportEmployeeExcelBufferDto, ImportResult, ErrorRecord } from './dto/import-employee-excel.dto';
import * as path from 'path';
import * as fs from 'fs';
import { UrlConfigService } from '../config/config.url.service';
import { IPushNotifCskh } from './interfaces/push-notif-cskh.interface';
import { CskhPushNotif } from './dto/cskh-push-notif.dto';
import { IPushNotifCskhConstraint } from './interfaces/push-notif-cskh-constraint.interface';
import { ISettingSalaryCare247 } from './interfaces/setting-salary-care247.interface';
import { USER_PERMISSION_COLLECTION_NAME } from 'src/user-permission/schema/constant';
import { IUserPermission } from 'src/user-permission/interface/user-permission.interface';
import { IUpdateEmployeeCare247Dto } from './dto/update-employee-care247.dto';
import { SearchUserCskh } from './dto/search-user-cskh.dto';
import { IUpdateEmployeeCskhDto } from './dto/update-employee-cskh.dto';
import { TransactionsQueryDto } from './dto/transactions-query.dto';
import { MedproCareRefundDto } from './dto/medpro-care-refund.dto';
import { IUserLoginLog } from '../user/interfaces/user-login-log.interface';
import { AdsCare247Dto } from './dto/ads-care247.dto';
import { UpdateMultipleKey } from './dto/update-multiple-key.dto';
import { ConfigRepoService } from '../config/config.repo.service';
import { IAuditLogCskhDocument } from './interfaces/audit-log-cskh.interface';
import { Care247DiaryQueryDto } from './dto/care247-diary.dto';
import { ActionEnum, CategoryEnum } from './decorator/action-data.enum';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from 'src/report/schemas/constants';
import { IReportTransactionDaily } from 'src/report/interfaces/report-transaction-daily.interface';
import { IDailyTransaction } from './dto/query-daily-transaction-report.dto';
import { ITrackingPushNotifTCKQ } from './interfaces/tracking-push-notif-tckq.interface';
import { IUpdateCare247IntroductionDto } from './dto/update-care247-introduction.dto';
import { IIntroductionCare247Changes } from './interfaces/introduction-care247-changes.inteface';
import { BookingCskhRevenueQueryDto } from './dto/booking-cskh-revenue-query.dto';
import { PatientMongoService } from '../patient-mongo/patient-mongo.service';
import { IBookingCare247ConstraintUser } from './interfaces/booking-care247-constraint-user.interface';
import { Care247ConstraintUserHistoryQueryDto } from './dto/care247-constraint-user-history-query.dto';
import { IBookingCare247Flags } from './interfaces/booking-care247-flags.interface';
import { ITrackingPushNotifCare247 } from './interfaces/tracking-push-notif-care247.interface';
import { IKpiCskh } from './interfaces/kpi-cskh.interface';
import { ICskhContactCustomers } from './interfaces/cskh-contact-customers.interface';
import { CskhContactCustomersDto } from './dto/cskh-contact-customers.dto';
import { ICskhScheduleCategories } from './interfaces/cskh-schedule-categories.inteface';
import { CskhScheduleCategoriesDto } from './dto/cskh-schedule-categories.dto';
import { ICskhSchedulers } from './interfaces/cskh-schedulers.inteface';
import { CskhSchedulersDto } from './dto/cskh-schedulers.dto';
import { ICskhSurveyCare247 } from './interfaces/cskh-survey-care247.inteface';
import { CskhSurveyCare247Dto } from './dto/cskh-survey-care247.dto';
import { ICare247Sales } from './interfaces/care247-sales.interface';
import { UtilService } from '../config/util.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EVENT_RETRY_SYNC_BOOKING } from '../message-event/constant';
import { IConvertUserCskh } from '../booking-gateway/intefaces/convert-user-cskh.interface';
import { IBookingIgnoreConvert } from '../booking-gateway/intefaces/booking-ignore-convert.interface';
import { IReserveBookingLogs } from 'src/booking-gateway/intefaces/reserve-booking.inteface';
import { ISection } from 'src/booking-gateway/intefaces/section.inteface';
import { IDoctor } from 'src/doctor-mongo/interfaces/doctor.interface';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { IRoom } from 'src/room-mongo/interfaces/room.interface';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ISubject } from 'src/subject-mongo/interfaces/subject.interface';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { IRefundTransactionSms } from './interfaces/refund-transaction-sms.interface';
import { SmsService } from '../sms/sms.service';
import { PkhHttpService } from '../config/config.http.service';
import { AnKhangReportQueryDto } from './dto/an-khang-report-query.dto';
import { UploadHealthDataResponseDto } from './dto/upload-health-data.dto';
import { AnKhangTransactionsQueryDto } from './dto/an-khang-transactions-query.dto';
import { HisGatewayService } from '../his-gateway/his-gateway.service';
import { PaymentMethodService } from '../payment-method/payment-method.service';
import { BookingGatewayService } from '../booking-gateway/booking-gateway.service';
import { Observable } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { PrefixTransactionCodeTTConfigService } from '../config/config.prefix.transcode.service';
import * as slugify from 'slugify';
import { REPO_NAME_BETA } from '../common/constants';
import { description } from 'joi';

@Injectable()
export class CskhService {
    private logger = new Logger(CskhService.name);
    private readonly repoName: string;
    constructor(
        @InjectModel(AUDIT_LOG_CSKH) private readonly auditLogCskhModel: Model<IAuditLogCskhDocument>,
        @InjectModel(RESET_OTP_LOGS_COLLECTION_NAME) private resetOtpLogModel: Model<IRestOtpLog>,
        @InjectModel(REPAYMENT_LOG_COLLECTION_NAME) private repaymentLogModel: Model<IRepaymentLog>,
        @InjectModel(BOOKING_REFERRAL_COLLECTION_NAME) private bookingReferralModel: Model<IBookingReferral>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(VIEW_BOOKING_LOG_COLLECTION_NAME) private viewBookingLogModel: Model<IViewBookingLog>,
        @InjectModel(BOOKING_LOCKED_COLLECTION_NAME) private bookingLockedModel: Model<IBookingLock>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(USER_PERMISSION_COLLECTION_NAME) private readonly userPermissionModel: Model<IUserPermission>,
        @InjectModel(PUSH_NOTIF_CSKH) private pushNotifCskhModel: Model<IPushNotifCskh>,
        @InjectModel(PUSH_NOTIF_CSKH_CONSTRAINT) private pushNotifCskhConstrainModel: Model<IPushNotifCskhConstraint>,
        @InjectModel(BOOKING_CARE_247_SUCCESS) private care247SuccessModel: Model<IBookingCare247Success>,
        @InjectModel(BOOKING_CARE_247) private readonly bookingCare247Model: Model<IBookingCare247>,
        @InjectModel(BOOKING_CARE_247_CONSTRAINT) private readonly bookingCare247ConstraintModel: Model<IBookingCare247Constraint>,
        @InjectModel(SETTING_SALARY_CARE247) private readonly settingSalaryCare247Model: Model<ISettingSalaryCare247>,
        @InjectModel(USER_LOGIN_LOG_COLLECTION_NAME) private userLoginLogModel: Model<IUserLoginLog>,
        @InjectModel(DAILY_TRANSACTION_REPORT_COLLECTION_NAME) private transactionDailyModel: Model<IReportTransactionDaily>,
        @InjectModel(TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA) private trackingPushNotifTCKQ: Model<ITrackingPushNotifTCKQ>,
        @InjectModel(TRACKING_PUSH_NOTIF_CARE247) private trackingPushNotifCare247: Model<ITrackingPushNotifCare247>,
        @InjectModel(INTRODUCTION_CARE247_SERVICE_CHANGES) private introductionCare247ChangesModel: Model<IIntroductionCare247Changes>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(BOOKING_CARE_247_CONSTRAINT_USER) private readonly bookingCare247ConstraintUserModel: Model<IBookingCare247ConstraintUser>,
        @InjectModel(BOOKING_CARE_247_FLAGS) private readonly bookingCare247FlagsModel: Model<IBookingCare247Flags>,
        @InjectModel(KPI_CSKH) private kpiCskhModel: Model<IKpiCskh>,
        @InjectModel(CSKH_CONTACT_CUSTOMERS) private cskhContactCustomerModel: Model<ICskhContactCustomers>,
        @InjectModel(CSKH_SCHEDULE_CATEGORIES) private cskhScheduleCategoriesModel: Model<ICskhScheduleCategories>,
        @InjectModel(CSKH_SCHEDULERS) private cskhSchedulersModel: Model<ICskhSchedulers>,
        @InjectModel(CSKH_SURVEY_CARE247_COLLECTION) private cskhSurveyCare247Model: Model<ICskhSurveyCare247>,
        @InjectModel(CARE247_SALES) private care247SalesModel: Model<ICare247Sales>,
        @InjectModel(RETRY_PAYMENT_BOOKING) private retryPaymentBookingModel: Model<any>,
        @InjectModel(CALL_BOT_CSKH) private callBotCskhModel: Model<any>,
        @InjectModel(CONVERT_USER_CSKH) private convertUserCskhModel: Model<IConvertUserCskh>,
        @InjectModel(BOOKING_IGNORE_CONVERT) private bookingIgnoreConvertModel: Model<IBookingIgnoreConvert>,
        @InjectModel(RESERVE_BOOKING_LOGS_COLLECTION_NAME) private reserveBookigLogModel: Model<IReserveBookingLogs>,
        @InjectModel(SERVICE_COLLECTION_NAME) private serviceModel: Model<IService>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private subjectModel: Model<ISubject>,
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
        @InjectModel(ROOM_COLLECTION_NAME) private roomModel: Model<IRoom>,
        @InjectModel(SECTION_COLLECTION_NAME) private sectionModel: Model<ISection>,
        @InjectModel(REFUND_TRANSACTION_SMS) private refundTransactionSmsModel: Model<IRefundTransactionSms>,
        @InjectModel(JOB_UPDATE_KPI) private jobUpdateKpiModel: Model<any>,
        @InjectModel(CARE247_INDEPENDENT_PAYMENT) private care247IndependentPaymentModel: Model<any>,
        @InjectModel(AN_KHANG_BRANCHES_COLLECTION) private anKhangBranchModel: Model<any>,
        @InjectModel(AN_KHANG_USERS_COLLECTION) private anKhangUserModel: Model<any>,
        private readonly clientUtil: ClientUtilService,
        private readonly configSMSMedproService: ConfigSMSMedproService,
        private globalSettingService: GlobalSettingService,
        private userService: UserService,
        private readonly smsService: SmsService,
        private readonly urlConfigService: UrlConfigService,
        private readonly utilService: UtilService,
        private readonly http: HttpService,
        private readonly httpService: PkhHttpService,
        private readonly patientMongoService: PatientMongoService,
        private config: ConfigRepoService,
        private eventEmmiter: EventEmitter2,
        private readonly hisGatewayService: HisGatewayService,
        private readonly paymentMethodService: PaymentMethodService,
        private readonly transactionConfig: PrefixTransactionCodeTTConfigService,
        // private readonly bookingGatewayService: BookingGatewayService,
    ) {
        this.repoName = this.config.getRepoName();

    }

    async createAuditLogCskh(data: any): Promise<any> {
        try {
            if (data.method !== 'GET' && data.url !== '/cskh/care247-diary') {
                return await this.auditLogCskhModel.create({ ...data, nameRepo: this.config.getRepoName() });
            }
        } catch (error) {
            throw error;
        }
    }

    handleDateForFilter(fromDate: string, toDate: string) {
        const dateStart = fromDate ? moment(fromDate) : moment();
        const dateEnd = toDate ? moment(toDate) : moment();
        // Handle UTC time
        const timeStart = dateStart
            .set({
                hours: 0,
                minutes: 0,
                seconds: 0,
            })
            .subtract(7, 'hours')
            .toDate();
        const timeEnd = dateEnd
            .set({
                hours: 23,
                minutes: 59,
                seconds: 59,
            })
            .subtract(7, 'hours')
            .toDate();
        return { timeStart, timeEnd };
    }

    getPartnerName(partnerId: string, partnerName: string) {
        let name: string;
        switch (partnerId) {
            case 'umc':
                name = `${partnerName} - Cơ sở chính`;
                break;
            case 'umc2':
                name = `${partnerName} - Cơ sở 2`;
                break;
            default:
                name = partnerName;
                break;
        }
        return name;
    }

    getPartnerTypeName(type: number) {
        // 1 bệnh viện công | 2 bệnh viện tư | 3 phòng khám | 4 phòng mạch
        switch (type) {
            case 1:
                return 'Bệnh viện công';
            case 2:
                return 'Bệnh viện tư';
            case 3:
                return 'Phòng khám';
            case 4:
                return 'Phòng mạch';
            default:
                break;
        }
    }

    getJobTitleName(jobTitle: string) {
        let name: string;
        switch (jobTitle) {
            case 'manager':
                name = `Quản lý Care247`;
                break;
            case 'employee':
                name = `Nhân viên Care247`;
                break;
            case 'ctv':
                name = `CTV Care247`;
                break;
            default:
                break;
        }
        return name;
    }

    handleStatusDescription(status: number) {
        switch (status) {
            case -2:
                return 'Đã hủy';
            case 0:
                return 'Chưa thanh toán';
            case 6:
                return 'Thanh toán hộ';
            case 1:
                return 'Đã thanh toán';
            case 2:
                return 'Đã khám';
            default:
                return '';
        }
    }

    handlePaymentStatusDescription(status: number) {
        switch (status) {
            case 1:
                return 'Chưa thanh toán';
            case 2:
                return 'Đã thanh toán';
            default:
                return '';
        }
    }

    handleCategoryKpiCskh(treeId: string) {
        switch (treeId) {
            case 'DATE':
            case 'DOCTOR':
            case 'CSKH':
                return 'Booking đặt khám';
            case 'PACKAGE':
                return 'Gói khám';
            case 'TELEMED':
            case 'TELEMEDNOW':
                return 'Telemed';
            default:
                return '';
        }
    }

    getPriceText(price: number) {
        let priceText: number | string = price;
        if (price > 0) {
            priceText = `${price}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }
        return priceText;
    }

    async transaction(transactionId: string, isCallback: number = 1): Promise<any> {
        const paymentHubData = { transactionId, isCallback };
        const urlPaymentHubTransaction = this.urlConfigService.getPaymentTransactionUrl();
        /* tìm lại bên payment hub */
        let result = { data: { TransactionInfo: {} } };
        try {
            result = (await this.httpService.postHttpRequest(urlPaymentHubTransaction, { ...paymentHubData }).toPromise()).data;
            const { data: { TransactionInfo = {} } } = result;
            return TransactionInfo;
        } catch (error) {
            // this.clientSentry.instance().captureException(error);
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    async getTypeSalaryCare247(userId: string): Promise<string> {
        const [care247ManagerSalaryList, care247EmployeeSalaryList] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('MANAGER_CARE247_SALARY_LIST'),
            this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
        ]);
        const listManagerSalary = new Set(care247ManagerSalaryList.split(','));
        const listEmployeeSalary = new Set(care247EmployeeSalaryList.split(','));
        let typeSalary: string;
        switch (true) {
            case listManagerSalary.has(userId):
                typeSalary = 'manager';
                break;
            case listEmployeeSalary.has(userId):
                typeSalary = 'employee';
                break;
            default:
                typeSalary = 'ctv';
                break;
        }
        return typeSalary;
    }

    async resetOtp(userId: string, body: any) {
        this.resetOtpLogModel.create({ userId, phone: body.phone });
        return this.clientUtil.post(`${this.configSMSMedproService.getMessageHubUrl()}/otp/reset-otp-log`, body);
    }

    async addPhoneBlacklistSms(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');

        const phoneArray = phones.split(',').filter(p => !!p);
        const phoneSet = new Set(phoneArray);
        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);
        for (let p of phoneSplits) {
            phoneSet.add(p);
        }

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'SMS_BLACKLIST_PHONE' }, Array.from(phoneSet).join(','));

        return { isOk: true };
    }

    async removePhoneBlacklistSms(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');

        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);
        const phoneArray = phones.split(',').filter(p => !phoneSplits.includes(p));

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'SMS_BLACKLIST_PHONE' }, phoneArray.join(','));

        return { isOk: true };
    }

    async getPhoneBlacklistSms() {
        const phones = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');
        return { phones };
    }

    async addBlockedPhone(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('BLOCKED_USER_LIST');

        const phoneArray = phones.split(',').filter(p => !!p);
        const phoneSet = new Set(phoneArray);
        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);
        for (let p of phoneSplits) {
            phoneSet.add(p);
        }

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'BLOCKED_USER_LIST' }, Array.from(phoneSet).join(','));

        return { isOk: true };
    }

    async removeBlockedPhone(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('BLOCKED_USER_LIST');

        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);

        const phoneArray = phones.split(',').filter(p => !phoneSplits.includes(p));

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'BLOCKED_USER_LIST' }, phoneArray.join(','));

        return { isOk: true };
    }

    async getBlockedPhone() {
        const phones = await this.globalSettingService.findByKeyAndRepoName('BLOCKED_USER_LIST');
        return { phones };
    }

    async unlockLogin(body: ResetOtpLogDto) {
        try {
            const { phone } = body;
            const yourphone = `${phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            const checkUserMongo: IUser = await this.userService.checkExistsUserByUsernameMedproId(yourphone);
            await this.userLoginLogModel.deleteMany({ userId: checkUserMongo._id });
            return { isOk: true };
        } catch (error) {}
    }

    async getRepaymentLog(query: any) {
        const { pageSize = 10, pageIndex = 0 } = query;

        let condition = {
            callStatus: { $ne: 1 },
        };

        const [rows, totalRows] = await Promise.all([
            this.repaymentLogModel
                .find(condition)
                .skip(pageSize * pageIndex)
                .limit(Number(pageSize))
                .exec(),
            this.repaymentLogModel.countDocuments(condition),
        ]);

        const data = await Promise.all(
            rows.map(async row => {
                const booking = await this.bookingModel
                    .findOne(
                        { bookingCode: row.bookingCode },
                        {
                            userId: true,
                            date: true,
                            createdAt: true,
                            transactionId: true,
                            partnerId: true,
                        },
                    )
                    .exec();
                const user = await this.userModel.findOne(
                    { _id: booking?.userId },
                    {
                        fullname: true,
                        username: true,
                        isCS: true,
                        email: true,
                        createdAt: true,
                    },
                );
                return {
                    ...row.toObject(),
                    user: user?.toObject(),
                    booking: booking?.toObject(),
                };
            }),
        );

        return {
            rows: data,
            totalRows,
        };
    }

    async updateRepaymentLog(data: any) {
        const { _id, ...rest } = data;
        return this.repaymentLogModel.findByIdAndUpdate(_id, { ...rest }, { new: true }).exec();
    }

    async addCalledInfo(body: CalledInfoDto, user: any) {
        const booking = await this.bookingModel.findById({ _id: body.bookingId }, { confirmCalledInfo: true }).exec();
        booking.set({
            confirmCalledInfo: {
                ...booking.confirmCalledInfo,
                callList: [
                    ...(booking.confirmCalledInfo?.callList || []),
                    {
                        callSuccess: body.callSuccess,
                        note: body.note,
                        userId: user._id,
                        createTime: new Date(),
                    },
                ],
                updateTime: new Date(),
                callSuccess: body.callSuccess,
            },
        });

        await booking.save();
        return booking.confirmCalledInfo;
    }

    async getBookingLocked(query: any) {
        const { pageSize = 10, pageIndex = 0, ...restQuery } = query;

        const condition = {
            ...restQuery,
        };

        const [rows, totalRows] = await Promise.all([
            this.bookingLockedModel
                .find(condition)
                .skip(pageSize * pageIndex)
                .limit(Number(pageSize))
                .exec(),
            this.bookingLockedModel.countDocuments(condition),
        ]);

        return {
            rows,
            totalRows,
        };
    }

    async unlockBooking(body: any) {
        const bookingLock = await this.bookingLockedModel.findOneAndDelete({ _id: body.id }).exec();

        if (bookingLock?.transactionId) {
            await this.viewBookingLogModel.deleteMany({ transactionId: bookingLock.transactionId }).exec();
        }

        return {
            isOk: true,
        };
    }

    async getUserByBookingDate(fromDate: string, toDate: string) {
        if (fromDate && toDate && fromDate === toDate) {
            // Xử lý thời gian từ `handleDateForFilter`
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);
            const condition = {
                date: { $gte: timeStart, $lte: timeEnd },
                status: 1,
            };

            // Lấy danh sách người dùng dựa trên booking
            const getBooking = await this.bookingCare247Model
                .find(condition)
                .populate({ path: 'user', select: { fullname: true, username: true } })
                .sort({ date: 1 })
                .exec();

            // Trích xuất `userId` từ kết quả
            const listUser = getBooking?.map((u: any) => u.userId);

            // Kiểm tra nếu danh sách không rỗng
            if (listUser && listUser.length > 0) {
                // Lấy danh sách booking theo userId
                const bookingData = await this.bookingCare247Model.find({ userId: { $in: listUser }, bookingStatus: 1,  date: { $gte: timeStart, $lte: timeEnd } }).exec();
                // Nhóm dữ liệu theo userId
                const groupedData = bookingData.reduce((acc: any, booking: any) => {
                    const userId = `${booking.userId}`;
                    const findBookingCare = getBooking.find((b: any) => `${b.userId}` === userId)
                    if (!acc[userId]) {
                        acc[userId] = [];
                    }
                    if (`${booking._id}` !== String(findBookingCare?._id)) {
                        acc[userId].push(booking._id);
                    }
                    return acc;
                }, {});
                return groupedData;
            } else {
                return {};
            }
        }
        return {};
    }
    
    async getBookingMedproCare(userId: string, body: BookingCare247QueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { transactionId, cskhUserIdGuide, partnerId, status, type, fromDate, toDate, pageSize, pageIndex } = body;

            const [care247EmployeeList, medproCarePartnerList, care247UserRoleList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
            ]);
            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);

            const listIdCs = care247EmployeeList.split(',');

            let listPartnerIds = medproCarePartnerList.split(',');
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm'];

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const query: any = {
                partnerId,
                status: !!status && ![-3, -4, 3, 4].includes(status) ? status : { $in: [1, -2] },
                type,
                date: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                bookingStatus: status === -3 ? -2 : null,
                cskhUserIdGuide: cskhUserIdGuide === '-1' ? { $eq: null } : cskhUserIdGuide === '-2' ? null : cskhUserIdGuide,
                refundCare247: status === -4 ? true : null,
                isCSKHConfirmCare247: cskhUserIdGuide === '-2' ? false : null
            };

            let condition = transactionId ? { transactionId } : pickBy(query, identity);

            const flags = await this.bookingCare247FlagsModel.find({ userId }).exec();
            const care247FlagIds = flags.map(f => f.care247Id);

            if (status === 3) {
                condition = { _id: { $in: care247FlagIds } }
            }

            if (status === 4) {
                if (listUserRole.has(userId)) {
                    condition = { ...condition, isCSKHConfirmCare247: true }
                } else {
                    condition = { cskhConfirmedCare247: userId }
                }
            }

            const [rows, totalRows, care247Cs, hospital, userByBooking] = await Promise.all([
                this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'cskhConfirmedCare247', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .populate({ path: 'payment', select: { subTotal: true, medproCareFee: true, medproFee: true,
                         transferFee: true, totalFee: true, amount: true, chargeFeeInfo: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments(condition),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
                this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec(),
                this.getUserByBookingDate(fromDate, toDate),
            ]);
            const data = rows.map((r: any) => {
                const item = r.toObject();
                return { 
                    ...item, 
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) },
                    isFlag: care247FlagIds.includes(`${item._id}`),
                    showMore: userByBooking[item.userId.toString()]?.length > 0,
                };
            });

            const partners = hospital.map((h: any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) };
            });

            const listCsGuide = [
                {
                    id: '',
                    fullname: 'Tất cả',
                },
                {
                    id: '-1',
                    fullname: 'Chưa điều phối',
                },
                ...care247Cs,
                {
                    id: '-2',
                    fullname: 'Chưa xác nhận Care247',
                }
            ];

            return {
                rows: data,
                totalRows,
                hospital: partners,
                listCsGuide,
                role: 'admin',
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách phiếu Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getBookingMedproCareCskhRevenue(userId: string, body: BookingCare247CskhRevenueQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { cskhUserId, partnerId, month, pageSize, pageIndex } = body;

            const [care247UserRoleList, cskhEmployeeList, medproCarePartnerList, csCommission] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CSKH'),
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);

            const listIdCs = cskhEmployeeList.split(',');

            let listPartnerIds = medproCarePartnerList.split(',');
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm'];

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            let query: any = {
                partnerId,
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };
            let cskhUserCondition: any;
            let userRole: string;
            if (listUserRole.has(userId)) {
                query = { ...query, cskhUserId };
                cskhUserCondition = { $in: listIdCs };
                userRole = 'admin';
            } else {
                query = { ...query, cskhUserId: userId };
                cskhUserCondition = userId;
                userRole = 'user';
            }

            const condition = pickBy(query, identity);

            if (!condition?.cskhUserId) {
                throw new HttpException('Không tìm thấy thông tin nhân viên CSKH cần thống kê.', HttpStatus.BAD_REQUEST);
            }

            const [rows, totalRows, cskhEmployee, hospital] = await Promise.all([
                this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments(condition),
                this.userModel.find({ _id: cskhUserCondition, isCS: true }, { fullname: true, username: true }).exec(),
                this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec(),
            ]);

            const data = rows.map((r: any) => {
                const item = r.toObject();
                return {
                    ...item,
                    csCommission: Number(csCommission),
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) },
                };
            });

            const partners = hospital.map((h: any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) };
            });

            return {
                rows: data,
                totalRows,
                hospital: partners,
                listCs: sortBy(cskhEmployee, [item => item.fullname.toLowerCase()]),
                revenue: totalRows * Number(csCommission),
                role: userRole,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi thống kê.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getBookingMedproCareCare247Revenue(userId: string, body: BookingCare247CskhRevenueQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { cskhUserIdGuide, partnerId, month, pageSize, pageIndex } = body;

            const [care247UserRoleList, care247EmployeeList, medproCarePartnerList, targetAndRange] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
                this.globalSettingService.findByKeyAndRepoName('TARGET_AND_RANGE_CONFIG'),
            ]);

            const listIdCs = care247EmployeeList.split(',');
            const listIdCare247Role = care247UserRoleList.split(',');

            const listUserRole = new Set(listIdCare247Role);

            let listPartnerIds = medproCarePartnerList.split(',');
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm'];

            const targetAndRangeObj = targetAndRange ? JSON.parse(targetAndRange) : {};

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);
            const typeSalary = await this.getTypeSalaryCare247(cskhUserIdGuide);

            let query: any = {
                partnerId,
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };
            let care247UserCondition: any;
            let userRole: string;
            if (listUserRole.has(userId)) {
                query = { ...query, cskhUserIdGuide: typeSalary === 'manager' ? null : cskhUserIdGuide };
                care247UserCondition = { $in: listIdCs };
                userRole = 'admin';
            } else {
                query = { ...query, cskhUserIdGuide: typeSalary === 'manager' ? null : userId };
                care247UserCondition = userId;
                userRole = 'user';
            }

            const condition = pickBy(query, identity);

            const [rows, totalRows, care247Employee, hospital] = await Promise.all([
                this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments(condition),
                this.userModel.find({ _id: care247UserCondition, isCS: true }, { fullname: true, username: true }).exec(),
                this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec(),
            ]);

            const data = rows.map((r: any) => {
                const item = r.toObject();
                return { ...item, partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) } };
            });

            const partners = hospital.map((h: any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) };
            });

            const { salary } = await this.calculateSalaryCare247(totalRows, typeSalary);

            let target: [];
            let range: [];
            if (typeSalary === 'manager') {
                target = targetAndRangeObj?.targetBooking || [0, 600];
                range = targetAndRangeObj?.rangeBooking || [0, 2000];
            } else {
                target = targetAndRangeObj?.target || [0, 25];
                range = targetAndRangeObj?.range || [0, 100];
            }

            const userStatistic = {
                bookings: totalRows,
                username: care247Employee.filter(e => e.id === cskhUserIdGuide)[0]?.username,
                fullname: care247Employee.filter(e => e.id === cskhUserIdGuide)[0]?.fullname,
                target,
                range,
                month,
            };

            return {
                rows: data,
                totalRows,
                hospital: partners,
                listCs: sortBy(care247Employee, [item => item.fullname.toLowerCase()]),
                revenue: salary,
                role: userRole,
                statistic: userStatistic,
                jobTitle: this.getJobTitleName(typeSalary),
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi thống kê.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async cancelBookingMedproCare(cskhUserId: string, body: MedproCareCancelDto) {
        try {
            const isCS = await this.userService.isCs(cskhUserId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { id, cancelReason } = body;
            const bookingCare247 = await this.bookingCare247Model.findOne({ _id: id }).exec();
            if (!bookingCare247) {
                throw new HttpException(`Không tìm thấy thông tin Care247.`, HttpStatus.BAD_REQUEST);
            }

            const currentTime = moment().utc();
            const checkTime = moment(bookingCare247.date).add(2, 'days').utc().set({ hours: 10, minutes: 0, seconds: 0 }); //17h

            if (currentTime.isAfter(checkTime) && cskhUserId !== '60371c4055614d001a24ef46') {
                throw new HttpException(`Quá thời gian hủy phiếu.`, HttpStatus.BAD_REQUEST);
            }

            const userCs = await this.userModel.findById({ _id: cskhUserId });
            let userCsObj = userCs.toObject();

            let cancelInfo = {
                fullname: userCsObj.fullname,
                mobile: userCsObj.username,
                cancelReason,
            };

            const newData = await this.bookingCare247Model.findOneAndUpdate({ _id: id }, { status: -2, cancelInfo }, { new: true }).exec();
            //ban lark group khi cskh huy DV Care247
            try {
                const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/notification/cancel-care247`;
                await this.http.post(url, { id }).toPromise();
            } catch (error) {
                this.logger.error(`care247-cancel ${id}`);
            }

            return newData;
        } catch (error) {
            const message = error?.message || 'Hủy dịch vụ Medpro Care gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async testTimeCare247(body: any) {
        try {
            const { id } = body;
            const bookingCare247 = await this.bookingCare247Model.findOne({ _id: id }).exec();
            if (!bookingCare247) {
                throw new HttpException(`Không tìm thấy thông tin Care247.`, HttpStatus.BAD_REQUEST);
            }

            const currentTime = moment().utc();
            const checkTime = moment(bookingCare247.date).utc().set({ hours: 10, minutes: 0, seconds: 0 }); //17h
            console.log('currentTime', currentTime);
            console.log('checkTime', checkTime);

            return currentTime.isAfter(checkTime);
        } catch (error) {
            const message = error?.message || 'testTimeCare247 gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    
    async testTimeSchedulersCskh(body: any) {
        try {
            const { time, partnerId, cskhUserId } = body;
            const createDate = moment(time).utc().format('YYYY-MM-DD');
            const timeStart = moment(createDate, 'YYYY-MM-DD').set({
                hours: 0,
                minutes: 0,
                seconds: 0,
            }).toDate();
            const timeEnd = moment(createDate, 'YYYY-MM-DD').set({
                hours: 23,
                minutes: 59,
                seconds: 59,
            }).toDate();
            const condition = {
                cskhUserId,
                partnerId,
                fromDate: { $gte: timeStart, $lte: timeEnd }
            }
            const schedulers = await this.cskhSchedulersModel.find(condition).populate({ path: 'schedule' }).exec();

            let countDoanhSo = 1;
            for await (const schedule of schedulers) {
                const compareTime = moment(time).utc().add(7, 'hours');
                if (moment(compareTime).isAfter(moment(schedule.fromDate))
                    && moment(compareTime).isBefore(moment(schedule.toDate))
                ) {
                    countDoanhSo = 0
                }
            }
            return {
                schedulers,
                countDoanhSo
            }
        } catch (error) {
            const message = error?.message || 'testTimeCare247 gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async refundBookingMedproCare(cskhUserId: string, body: MedproCareRefundDto) {
        try {
            const isCS = await this.userService.isCs(cskhUserId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { id, refundNote, refundCare247Date } = body;
            const bookingCare247 = await this.bookingCare247Model.findById({ _id: id }).exec();
            if (!bookingCare247) {
                throw new HttpException(`Không tìm thấy thông tin Care247.`, HttpStatus.BAD_REQUEST);
            }

            const newData = await this.bookingCare247Model
                .findOneAndUpdate(
                    { _id: id, status: -2, refundCare247: { $ne: true } },
                    {
                        refundCare247: true,
                        refundCare247Date: refundCare247Date ? moment(refundCare247Date)
                            .utc()
                            .toDate() : moment().utc().toDate(),
                        refundCare247User: cskhUserId,
                        refundCare247Note: refundNote,
                    },
                    { new: true },
                )
                .exec();

            if (newData) {
                return newData;
            } else {
                throw new HttpException('Hệ thống không xử lý được thao tác này.', HttpStatus.BAD_REQUEST);
            }
        } catch (error) {
            const message = error?.message || 'Hoàn tiền Medpro Care gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    // async insertBookingCare247Success() {
    //     const condition = {
    //         $or: [{ medproCare: { $exists: true, $ne: null } }, { care247: { $exists: true, $ne: null } }],
    //         status: 1,
    //         'medproCare.status': { $ne: 0 },
    //     };

    //     const data = await this.bookingModel.find(condition).exec();

    //     const care247Success = data.map(d => {
    //         const bookingCode = d?.bookingCodeV1 || d.bookingCode;
    //         return {
    //             userId: d.userId,
    //             booking: d._id,
    //             transactionId: d?.transactionId,
    //             bookingCode: bookingCode,
    //             patient: d?.patient,
    //             patientVersion: d?.patientVersion,
    //             subject: d?.subject,
    //             service: d?.service,
    //             doctor: d?.doctor,
    //             room: d?.room,
    //             date: d?.date,
    //             partner: d?.partner,
    //         };
    //     });

    //     await this.care247SuccessModel.insertMany(care247Success);
    // }

    async seedDataBookingCare247() {
        try {
            const condition = {
                medproCare: { $exists: true, $ne: null },
                // status: { $in: [1, -2, 2] },
                status: 2,
                paymentStatus: 2,
            };
            const data = await this.bookingModel.find(condition).exec();
            const care247Info = await this.globalSettingService.findByKeyAndRepoName('CARE_247_INFO');
            const care247InfoJson = JSON.parse(care247Info);

            for await (const booking of data) {
                const bookingObj = booking.toObject();
                try {
                    const bookingCare247Constraint = new this.bookingCare247ConstraintModel({
                        transactionId: bookingObj.transactionId,
                    });
                    await bookingCare247Constraint.save();

                    const { medproCare } = bookingObj;
                    const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
                    const newBookingCare247 = new this.bookingCare247Model({
                        bookingId: bookingObj._id,
                        booking: bookingObj._id,
                        bookingCode: bookingCode,
                        bookingStatus: bookingObj.status,
                        patient: bookingObj.patient,
                        patientId: bookingObj.patientId,
                        patientVerSion: bookingObj.patientVerSion,
                        patientVersionId: bookingObj.patientVersionId,
                        userId: bookingObj.userId,
                        user: bookingObj.userId,
                        appId: bookingObj.appId,
                        partnerId: bookingObj.partnerId,
                        partner: bookingObj.partner,
                        subject: bookingObj.subject,
                        service: bookingObj.service,
                        doctor: bookingObj.doctor,
                        room: bookingObj.room,
                        section: bookingObj.section,
                        date: bookingObj.date,
                        cskhUserId: bookingObj.cskhUserId,
                        cskh: bookingObj.cskhUserId,
                        platform: bookingObj.platform,
                        name: medproCare.name,
                        id: uuid.v4().replace(/-/g, ''),
                        addonServices: medproCare.addonServices,
                        note: medproCare.note,
                        medproCareNote: medproCare.medproCareNote,
                        status: 1,
                        transactionId: bookingObj.transactionId,
                        type: 'original',
                        provider: care247InfoJson,
                    });
                    await newBookingCare247.save();
                } catch (error) {
                    console.log('error seed data', error);
                }
            }
            return {
                isOk: true,
            };
        } catch (error) {
            console.log('error', error);
        }
    }

    async updatePaymentCare247(): Promise<any> {
        try {
            const care247List = await this.bookingCare247Model.find({ status: { $in: [1, -2] } }).exec();
            for await (const bookingCare247 of care247List) {
                try {
                    if (bookingCare247?.transactionId || '') {
                        console.log(`bookingCare247: ${bookingCare247._id}`)
                        const payment: any = await this.paymentModel.findOne({ transactionId: bookingCare247.transactionId }).exec();
                        if (payment) {
                            await this.bookingCare247Model
                                .findByIdAndUpdate(
                                    { _id: bookingCare247._id },
                                    { payment: payment._id },
                                    {
                                        new: true,
                                    },
                                )
                                .exec();

                            // console.log(updaetData.toObject());
                        }
                    }
                    
                } catch (error) {
                    console.log('error update data', error);
                }
            }
            return {
                isOk: true,
            };
        } catch (error) {
            console.log('error', error);
        }
    }

    async cskhRevenueReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month } = formData;

            const [care247UserRoleList, cskhEmployeeList, csCommission] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CSKH'),
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }
            const listIdCs = cskhEmployeeList.split(',');

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            const [dataCare247, cskhEmployees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
            ]);

            const convertData = map(groupBy(dataCare247, 'cskhUserId'), (items, cskhUserId) => ({
                cskhUserId,
                bookings: items.length,
                commission: items.length * Number(csCommission),
            }));
            const convertDataByUser = keyBy(convertData, 'cskhUserId');
            const result = cskhEmployees.map(employee => ({
                ...employee.toObject(),
                bookings: convertDataByUser[employee._id]?.bookings || 0,
                commission: convertDataByUser[employee._id]?.commission || 0,
                month: moment(month).format('MM/YYYY'),
            }));

            return result.sort((a, b) => b.commission - a.commission);
        } catch (error) {
            const message = error?.message || 'Báo cáo doanh thu nhân viên CSKH gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async care247RevenueReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month, cskhUserIdGuide } = formData;

            const [care247UserRoleList, care247EmployeeList, care247ManagerSalaryList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MANAGER_CARE247_SALARY_LIST'),
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);

            const listIdManagerSalary = care247ManagerSalaryList.split(',');
            const listManagerSalary = new Set(listIdManagerSalary);

            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }
            const listIdCs = care247EmployeeList.split(',');

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            const [dataCare247, care247Employees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel
                    .find({ _id: cskhUserIdGuide ? cskhUserIdGuide : { $in: listIdCs }, isCS: true }, { fullname: true, username: true })
                    .exec(),
            ]);

            const convertData = map(groupBy(dataCare247, 'cskhUserIdGuide'), (items, cskhUserIdGuide) => ({
                cskhUserIdGuide,
                bookings: listManagerSalary.has(cskhUserIdGuide) ? dataCare247.length : items.length,
            }));

            const convertDataByUser = keyBy(convertData, 'cskhUserIdGuide');

            const result = await Promise.all(
                care247Employees.map(async employee => {
                    const employeeObj = employee.toObject();
                    const typeSalary = await this.getTypeSalaryCare247(employeeObj._id.toString());
                    const monthFormat = moment(month).format('MM/YYYY');
                    const bookings = (listManagerSalary.has(employeeObj._id.toString()) ? dataCare247.length : convertDataByUser[employee._id]?.bookings) || 0;
                    const { salary, kpiMonth, kpiOver, kpiOverBonus, kpiMonthBonus } = await this.calculateSalaryCare247(bookings, typeSalary);
                    return {
                        ...employeeObj,
                        bookings,
                        salary,
                        kpiMonth,
                        kpiOver,
                        kpiOverBonus,
                        kpiMonthBonus,
                        month: monthFormat,
                        jobTitle: this.getJobTitleName(typeSalary),
                    };
                }),
            );
            return result.filter(r => r.bookings !== 0).sort((a: any, b: any) => b.salary - a.salary);
        } catch (error) {
            const message = error?.message || 'Báo cáo doanh thu nhân viên Care247 gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    mappingPermission(data: any[], userPermissions: any[]) {
        return data.map(item => {
            const record = item.toJSON();
            const thisPermission = userPermissions.find(u => String(u.user) === String(record.id))?.permissions || [];
            return {
                ...record,
                permissions: thisPermission,
            };
        });
    }

    async getListUser(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const [care247EmployeeList, salaryConfig] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
            ]);
            const listIdCs = care247EmployeeList.split(',');
            const salaryList = new Set(salaryConfig.split(','));
            const [care247Employees, userPermissions] = await Promise.all([
                this.userModel
                    .find({ _id: { $in: listIdCs }, isCS: true })
                    .select(['username', 'fullname', 'permissions', 'isCS', 'isCare247', 'email', 'medproId'])
                    .exec(),
                this.userPermissionModel
                    .find()
                    .populate('permissions')
                    .select(['permissions', 'user'])
                    .exec(),
            ]);
            const employeeCare247 = [];
            const employeeCskh = [];
            for (const employee of care247Employees) {
                if (salaryList.has(`${employee._id}`)) {
                    employeeCare247.unshift(employee);
                } else {
                    employeeCskh.unshift(employee);
                }
            }
            return {
                employeeCare247: this.mappingPermission(employeeCare247, userPermissions),
                employeeCskh: this.mappingPermission(employeeCskh, userPermissions),
            };
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getListUserCs(userId: string, type: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const [userCs, userPermissions] = await Promise.all([
                this.userModel
                    .find({ isCS: true })
                    .select(['username', 'fullname', 'permissions', 'isCS', 'isCare247', 'email', 'medproId'])
                    .exec(),
                this.userPermissionModel
                    .find()
                    .populate('permissions')
                    .select(['permissions', 'user'])
                    .exec(),
            ]);
            const kumhoUserList = await this.globalSettingService.findByKeyAndRepoName('KUMHO_USER');
            const kumhoUserIds = kumhoUserList.split(',');
            const userKumho = userCs.filter(u => kumhoUserIds.includes(u.id))
            if (type === 'kumho') {
                return this.mappingPermission(userKumho, userPermissions);
            } else {
                return this.mappingPermission(userCs, userPermissions);
            }
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async updateListUserCare247(userId: string, formData: IUpdateEmployeeCare247Dto) {
        const { userCare247 } = formData;
        const [care247EmployeeListConfig, salaryConfig, isCS, care247UserRoleListArr] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
            this.userService.isCs(userId),
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
        ]);

        const listUserRole = new Set(care247UserRoleListArr.split(','));
        if (!isCS) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }
        const care247EmployeeList = new Set(care247EmployeeListConfig.split(','));
        const salaryList = new Set(salaryConfig.split(','));
        if (care247EmployeeList.has(`${userCare247}`)) {
            if (!salaryList.has(`${userCare247}`)) {
                salaryList.add(userCare247);
                await this.globalSettingService.updateByKeyAndRepoName({ key: 'EMPLOYEE_CARE247_SALARY_LIST' }, Array.from(salaryList).join(','));
                return { isOk: true, message: 'Update user care247 thành công!' };
            } else {
                throw new HttpException(`Người dùng đã thuộc danh sách nhân viên chăm sóc 247.`, HttpStatus.FORBIDDEN);
            }
        } else {
            throw new HttpException(`Người dùng không thuộc danh sách nhân viên CSKH.`, HttpStatus.FORBIDDEN);
        }
    }

    async updateListUserCskh(userId: string, formData: IUpdateEmployeeCskhDto) {
        const { userCskh } = formData;
        const [detailUser, care247EmployeeListConfig, care247UserRoleListArr] = await Promise.all([
            this.userModel
                .findOne({ _id: userCskh, isCS: true })
                .select(['username', 'fullname', 'permissions', 'isCS', 'email', 'medproId'])
                .exec(),
            this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
        ]);
        if (detailUser) {
            const listUserRole = new Set(care247UserRoleListArr.split(','));
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }
            const care247EmployeeList = new Set(care247EmployeeListConfig.split(','));
            if (!care247EmployeeList.has(`${userCskh}`)) {
                care247EmployeeList.add(userCskh);
                await this.globalSettingService.updateByKeyAndRepoName({ key: 'CARE247_EMPLOYEE_LIST' }, Array.from(care247EmployeeList).join(','));
                return { isOk: true, message: 'Update danh sách nhân viên chăm sóc khách hàng thành công!' };
            } else {
                throw new HttpException(`Người dùng đã thuộc danh sách nhân viên chăm sóc khách hàng.`, HttpStatus.FORBIDDEN);
            }
        } else {
            throw new HttpException('Không tìm thấy thông tin nhân viên chăm sóc khách hàng', HttpStatus.FORBIDDEN);
        }
    }

    async removeListUserCskh(userId: string, formData: IUpdateEmployeeCskhDto) {
        const { userCskh } = formData;
        const [care247EmployeeListConfig, care247UserRoleListArr] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
        ]);
        const listUserRole = new Set(care247UserRoleListArr.split(','));
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }
        const data = new Set(care247EmployeeListConfig.split(','));

        try {
            if (data.has(`${userCskh}`)) {
                const care247EmployeeList = care247EmployeeListConfig.split(',').filter(p => userCskh !== p);
                await this.globalSettingService.updateByKeyAndRepoName({ key: 'CARE247_EMPLOYEE_LIST' }, care247EmployeeList.join(','));
                return { isOk: true, message: 'Remove user chăm sóc khách hàng thành công!' };
            }
            return { isOk: true, message: 'User này không thuộc chăm sóc khách hàng !' };
        } catch (err) {
            return { isOk: false, message: err };
        }
    }

    async removeListUserCare247(userId: string, formData: IUpdateEmployeeCare247Dto) {
        const { userCare247 } = formData;
        const [salaryConfig, care247UserRoleListArr] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
        ]);
        const listUserRole = new Set(care247UserRoleListArr.split(','));
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }

        const salaryList = salaryConfig.split(',').filter(p => userCare247 !== p);
        try {
            await this.globalSettingService.updateByKeyAndRepoName({ key: 'EMPLOYEE_CARE247_SALARY_LIST' }, salaryList.join(','));
            return { isOk: true, message: 'Remove user care247 thành công!' };
        } catch (err) {
            return { isOk: false, message: err };
        }
    }

    async updateListUserKumho(userId: string, formData: { id: string }) {
        const { id } = formData;
        const [kumhoConfig, isCS] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('KUMHO_USER'),
            this.userService.isCs(userId),
        ]);
        if (!isCS) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }
        const kumhoList = new Set(kumhoConfig.split(','));
        if (!kumhoList.has(`${id}`)) {
            kumhoList.add(id);
            await this.globalSettingService.updateByKeyAndRepoName({ key: 'KUMHO_USER' }, Array.from(kumhoList).join(','));
            return { isOk: true, message: 'Update user kumho thành công!' };
        } else {
            throw new HttpException(`Người dùng đã thuộc danh sách nhân viên chăm sóc 247.`, HttpStatus.FORBIDDEN);
        }
    }

    async removeListUserKumho(userId: string, formData: { id: string }) {
        const { id } = formData;
        const [kumhoConfig, isCS] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('KUMHO_USER'),
            this.userService.isCs(userId),
        ]);
        if (!isCS) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }
        const kumhoList = kumhoConfig.split(',').filter(p => id !== p);
        try {
            await this.globalSettingService.updateByKeyAndRepoName({ key: 'KUMHO_USER' }, kumhoList.join(','));
            return { isOk: true, message: 'Remove user kumho thành công!' };
        } catch (err) {
            return { isOk: false, message: err };
        }
    }

    async searchUserCskhByFullname(userId: string, search: SearchUserCskh): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { phone } = search;
            const text = {
                username: {
                    $regex: phone?.startsWith('0') ? phone.slice(1) : phone,
                    $options: 'i',
                },
            };
            const filter = { ...text };

            const [listUser, userPermissions] = await Promise.all([
                this.userModel
                    .find(filter)
                    .select(['username', 'fullname', 'permissions', 'isCS', 'isCare247', 'email', 'medproId'])
                    .exec(),
                this.userPermissionModel
                    .find()
                    .populate('permissions')
                    .select(['permissions', 'user'])
                    .exec(),
            ]);
            return this.mappingPermission(listUser, userPermissions);
        } catch (error) {
            this.logger.error(`Error when exec searchUserCskhByFullname()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getStatisticsEmployeesByMotnh(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month } = formData;

            const [care247UserRoleList, care247EmployeeList, salaryList, targetAndRange] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
                this.globalSettingService.findByKeyAndRepoName('TARGET_AND_RANGE_CONFIG'),
            ]);

            let targetAndRangeObj = targetAndRange ? JSON.parse(targetAndRange) : {};
            const salaryListArr = new Set(salaryList.split(','));
            // const care247UserRoleListArr = care247UserRoleList.split(',');
            // const listUserRole = new Set(care247UserRoleListArr);

            // if (!listUserRole.has(userId)) {
            //     throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            // }

            const listIdCs = care247EmployeeList.split(',');

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            const [dataCare247, care247Employees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
            ]);

            const convertData = map(groupBy(dataCare247, 'cskhUserIdGuide'), (items, cskhUserIdGuide) => ({
                cskhUserIdGuide,
                bookings: items.length,
            }));
            const convertDataByUser = keyBy(convertData, 'cskhUserIdGuide');

            const result = care247Employees.map(employee => ({
                ...employee.toObject(),
                bookings: convertDataByUser[employee._id]?.bookings || 0,
                target: targetAndRangeObj?.target || [0, 25],
                range: targetAndRangeObj?.range || [0, 100],
                month,
            }));
            result.sort((a, b) => b.bookings - a.bookings);

            const employeeCare247 = [];
            const employeeCskh = [];

            for (const employee of result) {
                if (salaryListArr.has(`${employee._id}`)) {
                    employeeCare247.unshift(employee);
                } else {
                    employeeCskh.unshift(employee);
                }
            }
            return {
                data: {
                    employeeCare247,
                    employeeCskh,
                    admin: {
                        fullname: 'Dịch vụ care247',
                        bookings: dataCare247?.length || 0,
                        target: targetAndRangeObj?.targetBooking || [0, 600],
                        range: targetAndRangeObj?.rangeBooking || [0, 100],
                    },
                },
            };
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getStatisticsEmployeesCskhByMotnh(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month } = formData;

            const [care247UserRoleList, cskhEmployeeList, salaryList, targetAndRange] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
                this.globalSettingService.findByKeyAndRepoName('TARGET_AND_RANGE_CONFIG'),
            ]);

            let targetAndRangeObj = targetAndRange ? JSON.parse(targetAndRange) : {};
            const salaryListArr = new Set(salaryList.split(','));
            // const care247UserRoleListArr = care247UserRoleList.split(',');
            // const listUserRole = new Set(care247UserRoleListArr);

            // if (!listUserRole.has(userId)) {
            //     throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            // }

            const listIdCs = cskhEmployeeList.split(',');

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            const [dataCare247, cskhEmployees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
            ]);

            const convertData = map(groupBy(dataCare247, 'cskhUserId'), (items, cskhUserId) => ({
                cskhUserId,
                bookings: items.length,
            }));
            const convertDataByUser = keyBy(convertData, 'cskhUserId');

            const result = cskhEmployees.map(employee => ({
                ...employee.toObject(),
                bookings: convertDataByUser[employee._id]?.bookings || 0,
                target: targetAndRangeObj?.target || [0, 25],
                range: targetAndRangeObj?.range || [0, 100],
                month,
            }));
            result.sort((a, b) => a.bookings - b.bookings);

            return result;
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getAnnualBookingReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { year, partnerId } = formData;

            const [care247UserRoleList, care247EmployeeList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            ]);

            // const care247UserRoleListArr = care247UserRoleList.split(',');
            // const listUserRole = new Set(care247UserRoleListArr);
            // if (!listUserRole.has(userId)) {
            //     throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            // }

            // Khởi tạo mảng kết quả để lưu tổng số booking theo tháng và từng bệnh viện
            const result: Array<{ month: string; total: number; partner?: string; partnerName?: string }> = [];

            // Lặp qua từng tháng trong năm
            for (let month = 1; month <= 12; month++) {
                const monthString = month.toString().padStart(2, '0');
                const fromDate = `${year}-${monthString}-01`;
                const daysInMonth = moment(fromDate, 'YYYY-MM-DD').daysInMonth();
                const toDate = `${year}-${monthString}-${daysInMonth}`;

                const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

                const condition: any = pickBy(
                    {
                        status: 1,
                        date: { $gte: timeStart, $lte: timeEnd },
                        bookingStatus: { $in: [1, 2] },
                        partnerId,
                    },
                    identity,
                );

                // Lấy dữ liệu booking trong tháng
                const dataCare247 = await this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'partner', select: 'name' })
                    .exec();

                // Tổng số booking trong tháng
                const totalBookings = dataCare247.length;

                // Nhóm booking theo partnerId (bệnh viện)
                const hospitalTotals = dataCare247.reduce((acc: any[], booking: any) => {
                    const partnerId = booking.partnerId;
                    const existingHospital = acc.find(item => item.partner === partnerId);

                    if (existingHospital) {
                        existingHospital.total += 1;
                    } else {
                        acc.push({
                            total: 1,
                            partner: partnerId,
                            partnerName: booking?.partner?.name,
                        });
                    }

                    return acc;
                }, [] as { total: number; partner: string; partnerName: string }[]);
                // Nếu không có booking nào trong tháng, thêm kết quả tháng với total = 0
                if (totalBookings === 0) {
                    result.push({
                        month: `${month}`,
                        total: 0,
                    });
                } else {
                    // Thêm kết quả cho mỗi bệnh viện trong tháng
                    hospitalTotals.forEach(hospital => {
                        result.push({
                            month: `${month}`,
                            total: hospital.total,
                            partner: hospital.partner,
                            partnerName: hospital?.partnerName,
                        });
                    });
                }
            }

            return result;
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getMonthBookingReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { month } = formData;

            const [care247UserRoleList, care247EmployeeList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            ]);

            // const care247UserRoleListArr = care247UserRoleList.split(',');
            // const listUserRole = new Set(care247UserRoleListArr);
            // if (!listUserRole.has(userId)) {
            //     throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            // }

            // Tính ngày bắt đầu và ngày kết thúc của tháng
            const currentMonth = moment().format('YYYY-MM');
            const selectedMonth = month || currentMonth;
            const daysInMonth = moment(selectedMonth, 'YYYY-MM').daysInMonth();
            const fromDate = `${selectedMonth}-01`;
            const toDate = `${selectedMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            // Lấy dữ liệu booking trong khoảng thời gian của tháng
            const dataCare247 = await this.bookingCare247Model
                .find(condition)
                .populate({ path: 'partner', select: 'name' })
                .exec();

            // Khởi tạo mảng kết quả để lưu tổng số booking theo từng ngày và từng bệnh viện
            const dailyTotals: Array<{ day: string; total: number; hospitals: Record<string, { total: number }> }> = [];

            // Duyệt qua từng ngày trong tháng để đếm số booking cho từng ngày
            for (let day = 1; day <= daysInMonth; day++) {
                const dayString = day.toString().padStart(2, '0');
                const currentDate = `${selectedMonth}-${dayString}`;
                const startOfDay = moment(currentDate)
                    .startOf('day')
                    .toDate();
                const endOfDay = moment(currentDate)
                    .endOf('day')
                    .toDate();

                // Lọc các booking trong ngày hiện tại
                const dailyBookings = dataCare247.filter(booking => booking.date >= startOfDay && booking.date <= endOfDay);

                // Tổng số booking trong ngày
                const dailyBookingCount = dailyBookings.length;

                // Nhóm booking theo partnerId (bệnh viện)
                const hospitalTotals = dailyBookings.reduce((acc: any, booking: any) => {
                    const partnerId = booking.partnerId;
                    if (!acc[partnerId]) {
                        acc[partnerId] = { total: 0 };
                    }
                    acc[partnerId].total += 1;
                    acc[partnerId].name = booking?.partner?.name;

                    return acc;
                }, {} as Record<string, { total: number }>);

                // Thêm dữ liệu của ngày vào mảng kết quả
                dailyTotals.push({
                    day: `${day}`,
                    total: dailyBookingCount,
                    hospitals: hospitalTotals,
                });
            }

            return dailyTotals;
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async bookingMedproCareExportExcel(userId: string, formData: any): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        if (!isCS) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }
        const { transactionId, cskhUserIdGuide, partnerId, status, fromDate, toDate } = formData;

        const medproCarePartnerList = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST')
        let listPartnerIds = medproCarePartnerList.split(',');
        listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm'];

        const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

        const query: any = {
            partnerId,
            status: !!status && status !== -3 ? status : { $in: [1, -2] },
            date: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            bookingStatus: status === -3 ? -2 : status === 1 ? { $in: [1, 2] } : null,
            cskhUserIdGuide: cskhUserIdGuide === '-1' ? { $eq: null } : cskhUserIdGuide,
        };

        const condition = transactionId ? { transactionId } : pickBy(query, identity);

        console.log('condition', condition);

        let dataCare247: any = await this.bookingCare247Model
            .find(condition)
            .populate({ path: 'booking', select: 'status' })
            .populate({ path: 'partner', select: 'name' })
            .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true, } })
            .populate({ path: 'user', select: { fullname: true, username: true } })
            .populate({ path: 'cskh', select: { fullname: true, username: true } })
            .populate({ path: 'subject', select: { name: true } })
            .populate({ path: 'service', select: { name: true } })
            .populate({ path: 'room', select: { name: true } })
            .populate({ path: 'doctor', select: { name: true } })
            .populate({
                path: 'payment',
                select: {
                    subTotal: true,
                    medproCareFee: true,
                    medproFee: true,
                    transferFee: true,
                    totalFee: true,
                    amount: true,
                    gatewayId: true,
                    paymentMethod: true,
                    paymentMethodDetail: true,
                    gatewayTransactionId: true,
                    paymentTime: true,
                },
            })
            .sort({ date: 1 })
            .exec();
        
        let dataMapPatient = []
        for await (const item of dataCare247) {
            const itemObj = item.toObject();
            const patients = await this.patientModel
                        .find({ id: item.patientId })
                        .populate('profession')
                        .populate('country')
                        .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .populate('bookingComplain')
                        .exec();
            const firstPatient = first(patients);
            const patientObj = firstPatient.toObject();
            const fullAddress = this.patientMongoService.getFullAddress(patientObj);

            dataMapPatient = [...dataMapPatient, {
                ...itemObj,
                patientAddress: fullAddress
            }]
        }

        //Create data excel
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet('Danh sách DV Care247');
        worksheet.columns = [
            { header: 'STT', key: 'stt', width: 25 },
            { header: 'Bệnh nhân', key: 'patient', width: 40 },
            { header: 'Tài khoản', key: 'user', width: 25 },
            { header: 'Địa chỉ', key: 'address', width: 40 },
            { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
            { header: 'Trạng thái', key: 'bookingStatus', width: 25 },
            { header: 'Cơ sở y tế', key: 'partner', width: 35 },
            { header: 'partnerId', key: 'partnerId', width: 20 },
            { header: 'Dịch vụ', key: 'service', width: 45 },
            { header: 'Chuyên khoa', key: 'subject', width: 45 },
            { header: 'Phòng khám', key: 'room', width: 45 },
            { header: 'Ngày khám', key: 'date', width: 20 },
            { header: 'Ngày đặt', key: 'createdAt', width: 20 },
            { header: 'Platform', key: 'platform', width: 20 },
            { header: 'DV Care247', key: 'status', width: 25 },
            { header: 'Gói DV', key: 'care247Service', width: 35 },
            { header: 'Nhân viên CS', key: 'cskh', width: 35 },
            { header: 'Nhân viên Care247', key: 'instructor', width: 35 },
            { header: 'Mã giao dịch', key: 'transactionId', width: 25 },
            { header: 'Tiền khám', key: 'subTotal', width: 25 },
            { header: 'Tiền dịch vụ Care247', key: 'medproCareFee', width: 25 },
            { header: 'Phí tiện ích Medpro', key: 'medproFee', width: 25 },
            { header: 'Phí chuyển đổi', key: 'transferFee', width: 25 },
            { header: 'Tổng Phí', key: 'totalFee', width: 25 },
            { header: 'Tổng tiền', key: 'amount', width: 25 },
            { header: 'Cổng thanh toán', key: 'gatewayId', width: 25 },
            { header: 'ID riêng của cổng', key: 'gatewayTransactionId', width: 25 },
            { header: 'Phương thức thanh toán', key: 'paymentMethod', width: 25 },
            { header: 'Phương thức thanh toán chi tiết', key: 'paymentMethodDetail', width: 25 },
            { header: 'Thời gian thanh toán', key: 'paymentTime', width: 25 },
        ];

        const dataMap = dataMapPatient.map((item: any, index: number) => ({
            stt: index + 1,
            patient: `${item?.patient?.surname} ${item?.patient?.name} - ${item?.patient?.mobile}`,
            user: `${item?.user?.fullname} ${item?.user?.username}`,
            address: item.patientAddress,
            bookingCode: item.bookingCode,
            bookingStatus: this.handleStatusDescription(item.bookingStatus),
            partner: this.getPartnerName(item.partnerId, item?.partner?.name),
            partnerId: item.partnerId,
            service: item?.service?.name,
            subject: item?.subject?.name,
            room: item?.room?.name,
            date: moment(item.date).utc().add(7, 'hours').format('HH:mm DD/MM/YYYY'),
            createdAt: moment(item.createdAt).utc().add(7, 'hours').format('HH:mm DD/MM/YYYY'),
            serviceName: item.addonServices[0].name,
            platform: item?.platform,
            status: this.handleStatusDescription(item.status),
            care247Service: item.addonServices[0].name,
            cskh: `${item?.cskh?.fullname} ${item?.cskh?.username}`,
            instructor: `${item?.instructor?.fullname} ${item?.instructor?.username}`,
            transactionId: item.transactionId,
            subTotal: item?.payment?.subTotal,
            medproCareFee: item?.payment?.medproCareFee,
            medproFee: item?.payment?.medproFee,
            transferFee: item?.payment?.transferFee,
            totalFee: item?.payment?.totalFee,
            amount: item?.payment?.amount,
            gatewayId: item?.payment?.gatewayId,
            gatewayTransactionId: item?.payment?.gatewayTransactionId,
            paymentMethod: item?.payment?.paymentMethod,
            paymentMethodDetail: item?.payment?.paymentMethodDetail,
            paymentTime: item?.payment?.paymentTime ? moment(item?.payment?.paymentTime).utc().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss') : '',
        }));

        dataMap.forEach(row => {
            worksheet.addRow(row);
        });

        worksheet.getColumn('subTotal').alignment = { horizontal: 'right' };
        worksheet.getColumn('subTotal').numFmt = '#,##0';
        worksheet.getColumn('medproCareFee').alignment = { horizontal: 'right' };
        worksheet.getColumn('medproCareFee').numFmt = '#,##0';
        worksheet.getColumn('medproFee').alignment = { horizontal: 'right' };
        worksheet.getColumn('medproFee').numFmt = '#,##0';
        worksheet.getColumn('transferFee').alignment = { horizontal: 'right' };
        worksheet.getColumn('transferFee').numFmt = '#,##0';
        worksheet.getColumn('totalFee').alignment = { horizontal: 'right' };
        worksheet.getColumn('totalFee').numFmt = '#,##0';
        worksheet.getColumn('amount').alignment = { horizontal: 'right' };
        worksheet.getColumn('amount').numFmt = '#,##0';

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async cskhRevenueExportExcel(userId: string, formData: any): Promise<any> {
        const { month, cskhUserId } = formData;
        const currentMonth = moment().format('YYYY-MM');
        const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
        const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
        const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;
        const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

        const [care247UserRoleList, cskhEmployeeList, csCommission] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CARE247_SALARY_PERMISSION_VIEW'),
            this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            this.globalSettingService.findByKeyAndRepoName('COMMISSION_CSKH'),
        ]);
        const care247UserRoleListArr = care247UserRoleList.split(',');
        const listUserRole = new Set(care247UserRoleListArr);
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }
        const listIdCs = cskhEmployeeList.split(',');
        const [dataCare247, cskhEmployees] = await Promise.all([
            this.bookingCare247Model
                .find({
                    status: 1,
                    date: { $gte: timeStart, $lte: timeEnd },
                    bookingStatus: { $in: [1, 2] },
                })
                .populate({ path: 'partner', select: 'name' })
                .populate({ path: 'cskh', select: { fullname: true, username: true } })
                .sort({ date: 1 })
                .exec(),
            this.userModel.find({ _id: cskhUserId ? cskhUserId : { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
        ]);

        if (!cskhUserId) {
            return this.cskhRevenueExportExcelAll(dataCare247, cskhEmployees, +csCommission);
        }

        //Create data excel
        const workbook = new Excel.Workbook();
        for await (const user of cskhEmployees) {
            const userObj = user.toObject();

            const worksheet = workbook.addWorksheet(userObj.fullname);
            worksheet.columns = [
                { header: 'STT', key: 'stt', width: 25 },
                { header: 'Nhân viên CSKH', key: 'name', width: 40 },
                { header: 'SĐT', key: 'phone', width: 25 },
                { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
                { header: 'Trạng thái', key: 'status', width: 25 },
                { header: 'Cơ sở y tế', key: 'partner', width: 35 },
                { header: 'partnerId', key: 'partnerId', width: 20 },
                { header: 'Dịch vụ Care247', key: 'serviceName', width: 45 },
                { header: 'Thời gian khám', key: 'date', width: 20 },
                { header: 'Hoa hồng', key: 'commission', width: 25 },
            ];

            const dataMap = dataCare247
                .filter(e => e.cskhUserId === `${userObj._id}`)
                .map((item: any, index: number) => ({
                    stt: index + 1,
                    name: userObj.fullname,
                    phone: userObj.username,
                    bookingCode: item.bookingCode,
                    status: 'Đã thanh toán',
                    partner: this.getPartnerName(item.partnerId, item.partner.name),
                    partnerId: item.partnerId,
                    serviceName: item.addonServices[0].name,
                    date: moment(item.date).format('YYYY-MM-DD'),
                    commission: +csCommission,
                }));

            dataMap.forEach(row => {
                worksheet.addRow(row);
            });

            worksheet.getColumn('commission').alignment = { horizontal: 'right' };
            worksheet.getColumn('commission').numFmt = '#,##0';
        }

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async cskhRevenueExportExcelAll(dataCare247: any, cskhEmployees: any, csCommission: number): Promise<any> {
        //Create data excel
        let dataExcel = [];
        for await (const user of cskhEmployees) {
            const userObj = user.toObject();
            const dataMap = dataCare247
                .filter(e => e.cskhUserId === `${userObj._id}`)
                .map((item: any, index: number) => ({
                    stt: index + 1,
                    name: userObj.fullname,
                    phone: userObj.username,
                    bookingCode: item.bookingCode,
                    status: 'Đã thanh toán',
                    partner: this.getPartnerName(item.partnerId, item.partner.name),
                    partnerId: item.partnerId,
                    serviceName: item.addonServices[0].name,
                    date: moment(item.date).format('YYYY-MM-DD'),
                    commission: csCommission,
                }));
            dataMap.forEach(row => {
                dataExcel.push(row);
            });
        }
        const workbook = new Excel.Workbook();

        const worksheet = workbook.addWorksheet('Tất cả');
        worksheet.columns = [
            { header: 'STT', key: 'stt', width: 25 },
            { header: 'Nhân viên CSKH', key: 'name', width: 40 },
            { header: 'SĐT', key: 'phone', width: 25 },
            { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
            { header: 'Trạng thái', key: 'status', width: 25 },
            { header: 'Cơ sở y tế', key: 'partner', width: 35 },
            { header: 'partnerId', key: 'partnerId', width: 20 },
            { header: 'Dịch vụ Care247', key: 'serviceName', width: 45 },
            { header: 'Thời gian khám', key: 'date', width: 20 },
            { header: 'Hoa hồng', key: 'commission', width: 25 },
        ];

        dataExcel.forEach(row => {
            worksheet.addRow(row);
        });

        worksheet.getColumn('commission').alignment = { horizontal: 'right' };
        worksheet.getColumn('commission').numFmt = '#,##0';

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async care247RevenueExportExcel(userId: string, formData: any): Promise<any> {
        // const { month, cskhUserIdGuide } = formData;
        // const currentMonth = moment().format('YYYY-MM');
        // const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
        // const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
        // const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;
        // const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

        // const [care247UserRoleList, care247EmployeeList, care247Commission] = await Promise.all([
        //     this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
        //     this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
        //     this.globalSettingService.findByKeyAndRepoName('COMMISSION_CARE247'),
        // ]);
        // const care247UserRoleListArr = care247UserRoleList.split(',');
        // const listUserRole = new Set(care247UserRoleListArr);
        // if (!listUserRole.has(userId)) {
        //     throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        // }
        // const listIdCs = care247EmployeeList.split(',');
        // const [dataCare247, care247Employees] = await Promise.all([
        //     this.bookingCare247Model
        //         .find({
        //             status: 1,
        //             date: { $gte: timeStart, $lte: timeEnd },
        //             bookingStatus: { $in: [1, 2] },
        //         })
        //         .populate({ path: 'partner', select: 'name' })
        //         .populate({ path: 'cskh', select: { fullname: true, username: true } })
        //         .sort({ date: 1 })
        //         .exec(),
        //     this.userModel
        //         .find({ _id: cskhUserIdGuide ? cskhUserIdGuide : { $in: listIdCs }, isCS: true }, { fullname: true, username: true })
        //         .exec(),
        // ]);
        const { month, cskhUserIdGuide } = formData;
        const data = await this.care247RevenueReport(userId, formData);
        const title = cskhUserIdGuide ? data[0].fullname : 'Tất cả';
        //Create data excel
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet(title);
        worksheet.columns = [
            { header: 'STT', key: 'stt', width: 25 },
            { header: 'Họ và tên', key: 'name', width: 40 },
            { header: 'SĐT', key: 'phone', width: 40 },
            { header: 'Vị trí', key: 'jobTitle', width: 25 },
            { header: 'Trong tháng', key: 'month', width: 25 },
            { header: 'Số ca', key: 'bookings', width: 25 },
            { header: 'Thu nhập', key: 'salary', width: 35 },
            { header: 'Số ca KPI', key: 'kpiMonth', width: 20 },
            { header: 'Số ca vượt KPI', key: 'kpiOver', width: 20 },
            { header: 'Thưởng KPI', key: 'kpiOverBonus', width: 20 },
            { header: 'Thưởng chất lượng dịch vụ', key: 'kpiMonthBonus', width: 25 },
        ];

        const dataMap = data.map((item: any, index: number) => ({
            stt: index + 1,
            name: item.fullname,
            phone: item.username,
            jobTitle: item.jobTitle,
            month: item.month,
            bookings: item.bookings,
            salary: item.salary,
            kpiMonth: item.kpiMonth,
            kpiOver: item.kpiOver,
            kpiOverBonus: item.kpiOverBonus,
            kpiMonthBonus: item.kpiMonthBonus,
        }));

        dataMap.forEach(row => {
            worksheet.addRow(row);
        });

        worksheet.getColumn('salary').alignment = { horizontal: 'right' };
        worksheet.getColumn('salary').numFmt = '#,##0';
        worksheet.getColumn('kpiOverBonus').alignment = { horizontal: 'right' };
        worksheet.getColumn('kpiOverBonus').numFmt = '#,##0';
        worksheet.getColumn('kpiMonthBonus').alignment = { horizontal: 'right' };
        worksheet.getColumn('kpiMonthBonus').numFmt = '#,##0';

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async pushNotifCskh(userMongoId: string, formData: CskhPushNotif) {
        const { onesignalId } = formData;

        try {
            const notifCskhConstraint = new this.pushNotifCskhConstrainModel({
                onesignalId,
                userId: userMongoId,
            });

            await notifCskhConstraint.save();

            const newOnesignalId = new this.pushNotifCskhModel({
                onesignalId,
                userId: userMongoId,
            });

            await newOnesignalId.save();
            return { isOk: true, message: 'Lưu onesignalId thành công' };
        } catch (err) {
            console.log('err', err);
            if (err.code === 11000) {
                return { isOk: false, message: 'OnesignalId đã tồn tại cho user này' };
            }
            return { isOk: false, message: 'Có lỗi xãy ra vui lòng thử lại sau!', error: err };
        }
    }

    async createSettingSalaryCare247() {
        try {
            const commission = await this.globalSettingService.findByKeyAndRepoName('COMMISSION_CARE247');
            const data = [
                { salary: 1000000, kpiMonth: 25, kpiOverBonus: 0, kpiMonthBonus: 0, total: 1000000, type: 'employee' },
                { salary: 1000000, kpiMonth: 50, kpiOverBonus: 1250000, kpiMonthBonus: 500000, total: 1000000, type: 'employee' },
                { salary: 1000000, kpiMonth: 70, kpiOverBonus: 2250000, kpiMonthBonus: 700000, total: 1000000, type: 'employee' },
                { salary: 1000000, kpiMonth: 80, kpiOverBonus: 2750000, kpiMonthBonus: 800000, total: 1000000, type: 'employee' },
                { salary: 1000000, kpiMonth: 90, kpiOverBonus: 3250000, kpiMonthBonus: 900000, total: 1000000, type: 'employee' },
                { salary: 1000000, kpiMonth: 100, kpiOverBonus: 3750000, kpiMonthBonus: 1000000, total: 1000000, type: 'employee' },
                { salary: 2000000, kpiMonth: 600, kpiOverBonus: 0, kpiMonthBonus: 0, total: 1000000, type: 'manager' },
                { salary: 2000000, kpiMonth: 800, kpiOverBonus: 0, kpiMonthBonus: 1000000, total: 1000000, type: 'manager' },
                { salary: 2000000, kpiMonth: 1000, kpiOverBonus: 0, kpiMonthBonus: 1200000, total: 1000000, type: 'manager' },
                { salary: 2000000, kpiMonth: 1500, kpiOverBonus: 0, kpiMonthBonus: 1600000, total: 1000000, type: 'manager' },
                { salary: 2000000, kpiMonth: 2000, kpiOverBonus: 0, kpiMonthBonus: 2000000, total: 1000000, type: 'manager' },
            ];
            const settingSalary = data.map(s => {
                if (s.type === 'employee') {
                    const kpiOverBonus = (s.kpiMonth - data[0].kpiMonth) * Number(commission);
                    return { ...s, kpiOverBonus, total: s.salary + kpiOverBonus + s.kpiMonthBonus };
                } else {
                    return { ...s, total: s.salary + s.kpiMonthBonus };
                }
            });
            await this.settingSalaryCare247Model.insertMany(settingSalary);
            return settingSalary;
        } catch (error) {
            console.log('error', error);
        }
    }

    async getSettingSalaryCare247() {
        try {
            const data = await this.settingSalaryCare247Model.find().exec();
            return {
                employee: data.filter(item => item.type === 'employee').sort((a, b) => a.kpiMonth - b.kpiMonth),
                manager: data.filter(item => item.type === 'manager').sort((a, b) => a.kpiMonth - b.kpiMonth),
            };
        } catch (error) {
            console.log('error', error);
        }
    }

    async calculateSalaryCare247(bookings: number, type: string) {
        try {
            const [settingSalary, care247Commission] = await Promise.all([
                this.settingSalaryCare247Model.find({ type }).exec(),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CARE247'),
            ]);
            let salary: number = 0;
            let kpiMonth: number = 0;
            let kpiOver: number = 0;
            let kpiOverBonus: number = 0;
            let kpiMonthBonus: number = 0;
            if (type === 'ctv') {
                salary = bookings * Number(care247Commission);
                return { salary, kpiMonth, kpiOver, kpiOverBonus, kpiMonthBonus };
            }
            const sortSettingSalary = settingSalary.sort((a, b) => b.kpiMonth - a.kpiMonth);
            for (const kpi of sortSettingSalary) {
                const kpiObj = kpi.toObject();
                if (bookings >= kpiObj.kpiMonth) {
                    kpiMonthBonus = kpiObj.kpiMonthBonus;
                    kpiMonth = kpiObj.kpiMonth;
                    kpiOver = bookings - sortSettingSalary[sortSettingSalary.length - 1].kpiMonth;
                    switch (type) {
                        case 'employee':
                            kpiOverBonus = kpiObj.kpiOverBonus + (bookings - kpiMonth) * Number(care247Commission);
                            salary = kpiObj.salary + kpiOverBonus + kpiMonthBonus;
                            break;
                        case 'manager':
                            kpiOverBonus = kpiObj.kpiOverBonus;
                            salary = kpiObj.salary + kpiOverBonus + kpiMonthBonus;
                            break;
                        default:
                            break;
                    }
                    return { salary, kpiMonth, kpiOver, kpiOverBonus, kpiMonthBonus };
                } else {
                    kpiMonth = sortSettingSalary[sortSettingSalary.length - 1].kpiMonth;
                    salary = settingSalary[0].salary;
                }
            }
            return { salary, kpiMonth, kpiOver, kpiOverBonus, kpiMonthBonus };
        } catch (error) {
            console.log('error', error);
        }
    }

    async countBookingCskh(cskhUserId: string, dateQuery: any, partnerId: string) {
        // booking DATE
        const qtyBookingQuery = pickBy({
            status: { $in: [0, 1, 2, -2, -3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'booking',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyBookingPaySuccessQuery = pickBy({
            status: { $in: [1, 2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'booking',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyBookingPayPendingQuery = pickBy({
            status: { $in: [0, -3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'booking',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyBookingCancelQuery = pickBy({
            status: { $in: [-2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'booking',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const [qtyBooking, qtyBookingPaySuccess, qtyBookingPayPending, qtyBookingCancel] = await Promise.all([
            this.kpiCskhModel.countDocuments(qtyBookingQuery),
            this.kpiCskhModel.countDocuments(qtyBookingPaySuccessQuery),
            this.kpiCskhModel.countDocuments(qtyBookingPayPendingQuery),
            this.kpiCskhModel.countDocuments(qtyBookingCancelQuery),
        ]);
        const booking = { 
            qtyBooking: qtyBooking, 
            qtyPaySuccess: qtyBookingPaySuccess, 
            qtyPayPending: qtyBookingPayPending, 
            qtyCancel: qtyBookingCancel 
        }

        //booking Care247
        const qtyCare247Query = pickBy({
            status: { $in: [1, -2, -3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'care247',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyCare247PaySuccessQuery = pickBy({
            status: { $in: [1] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'care247',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyCare247PayPendingQuery = pickBy({
            status: { $in: [-3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'care247',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyCare247CancelQuery = pickBy({
            status: { $in: [-2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'care247',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const [qtyCare247, qtyCare247PaySuccess, qtyCare247PayPending, qtyCare247Cancel] = await Promise.all([
            this.kpiCskhModel.countDocuments(qtyCare247Query),
            this.kpiCskhModel.countDocuments(qtyCare247PaySuccessQuery),
            this.kpiCskhModel.countDocuments(qtyCare247PayPendingQuery),
            this.kpiCskhModel.countDocuments(qtyCare247CancelQuery),
        ]);
        const care247 = { 
            qtyBooking: qtyCare247,
            qtyPaySuccess: qtyCare247PaySuccess, 
            qtyPayPending: qtyCare247PayPending, 
            qtyCancel: qtyCare247Cancel 
        }

        //booking Package
        const qtyPackageQuery = pickBy({
            status: { $in: [0, 1, 2, -2, -3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'package',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyPackagePaySuccessQuery = pickBy({
            status: { $in: [1, 2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'package',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyPackagePayPendingQuery = pickBy({
            status: { $in: [0, -3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'package',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const qtyPackageCancelQuery = pickBy({
            status: { $in: [-2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'package',
            countDoanhSo: !!partnerId ? { $in: [0] } : 1,
            partnerId
        }, identity);
        const [qtyPackage, qtyPackagePaySuccess, qtyPackagePayPending, qtyPackageCancel] = await Promise.all([
            this.kpiCskhModel.countDocuments(qtyPackageQuery),
            this.kpiCskhModel.countDocuments(qtyPackagePaySuccessQuery),
            this.kpiCskhModel.countDocuments(qtyPackagePayPendingQuery),
            this.kpiCskhModel.countDocuments(qtyPackageCancelQuery),
        ]);
        const examPackage = { 
            qtyBooking: qtyPackage, 
            qtyPaySuccess: qtyPackagePaySuccess, 
            qtyPayPending: qtyPackagePayPending, 
            qtyCancel: qtyPackageCancel 
        }

        //booking Telemed
        const qtyTelemedQuery = pickBy({
            status: { $in: [0, 1, 2, -2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'telemed',
            countDoanhSo: 1,
            partnerId
        }, identity);
        const qtyTelemedPaySuccessQuery = pickBy({
            status: { $in: [1, 2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'telemed',
            countDoanhSo: 1,
            partnerId
        }, identity);
        const qtyTelemedPayPendingQuery = pickBy({
            status: { $in: [0, -3] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'telemed',
            countDoanhSo: 1,
            partnerId
        }, identity);
        const qtyTelemedCancelQuery = pickBy({
            status: { $in: [-2] },
            createdDate: dateQuery,
            cskhUserId,
            mode: 'telemed',
            countDoanhSo: 1,
            partnerId
        }, identity);
        const [qtyTelemed, qtyTelemedPaySuccess, qtyTelemedPayPending, qtyTelemedCancel] = await Promise.all([
            this.kpiCskhModel.countDocuments(qtyTelemedQuery),
            this.kpiCskhModel.countDocuments(qtyTelemedPaySuccessQuery),
            this.kpiCskhModel.countDocuments(qtyTelemedPayPendingQuery),
            this.kpiCskhModel.countDocuments(qtyTelemedCancelQuery),
        ]);
        const telemed = { 
            qtyBooking: qtyTelemed, 
            qtyPaySuccess: qtyTelemedPaySuccess, 
            qtyPayPending: qtyTelemedPayPending, 
            qtyCancel: qtyTelemedCancel 
        }

        return { booking, care247, examPackage, telemed }
    }

    async notifyViewExamResultChoray(formData: any) {
        const { startDate, endDate } = formData;

        const startD = startDate ? startDate : '2024-01-01';
        const endD = endDate ? endDate : '2024-10-10';

        const fromDate = moment(startD).set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const toDate = moment(endD).set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        });

        const condition = {
            partnerId: 'choray',
            appId: 'medpro',
            status: {
                $in: [1, 2],
            },
            platform: {
                $in: ['android', 'ios'],
            },
            date: { $gte: fromDate, $lt: toDate },
        };

        const [rows, totalRows] = await Promise.all([
            this.bookingModel
                .find(
                    {
                        ...condition,
                    },
                    { date: true, bookingCode: true, userId: true, _id: false, locale: true, appId: true, partnerId: true, platform: true },
                )
                .exec(),
            this.bookingModel.countDocuments(condition),
        ]);

        const groupByUser = groupBy(rows, 'userId');
        const bookings = map(groupByUser, records => maxBy(records, record => moment(record.date).valueOf()));

        return {
            data: bookings,
            totalData: bookings.length,
            totalRows,
        };
    }

    async getTransactions(userId: string, body: TransactionsQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { transactionId, bookingCode, partnerId, status, type, fromDate, toDate, pageSize, pageIndex } = body;

            if (!fromDate || !toDate) {
                throw new HttpException('Vui lòng chọn khoảng thời gian.', HttpStatus.BAD_REQUEST);
            }

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const diffTimes = moment(timeEnd).utc().diff(moment(timeStart).utc(), 'months');

            if (diffTimes > 6) {
                throw new HttpException(`Khoảng thời gian không được quá 6 tháng.`, HttpStatus.BAD_REQUEST)
            }

            let query: any = {
                partnerId,
                status,
                type,
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            };

            let booking: any;
            if (bookingCode) {
                booking = await this.bookingModel
                    .findOne({
                        $or: [{ bookingCode: `${bookingCode}`.trim() }, { bookingCodeV1: `${bookingCode}`.trim() }],
                    })
                    .exec();
                query = { ...query, bookingCode: booking.bookingCode };
            }

            if (transactionId) {
                const payment: any = await this.paymentModel.findOne({ transactionId }).exec();
                booking = await this.bookingModel.findOne({ $or: [
                    { bookingCode: payment.bookingCode },
                    { bookingCodeV1: payment.bookingCode },
                ]}).exec();
                if (!booking) {
                    const bookingCare247 = await this.bookingCare247Model.findOne({ transactionId }).exec();
                    booking = await this.bookingModel.findOne({ _id: bookingCare247?.bookingId }).exec();
                    query = { ...query, transactionId };
                } else {
                    if (booking.partnerId === 'umc') {
                        query = { ...query, transactionId };
                    } else {
                        query = { ...query, bookingCode: booking.bookingCode };
                    }
                }
            }

            //type 17 (phiếu khám + Care247) or type 18 (phiếu khám + Care247 & đặt sau Care247 & đặt thêm giờ Care247)
            if (type && type === 17) {
                query = { ...query, type: 1, medproCareFee: { $gt: 0 } };
            } else if (type && type === 18) {
                query = { ...query, type: { $in: [1, 7, 8] }, medproCareFee: { $gt: 0 } };
            }

            const condition = pickBy(query, identity);

            const [rows, totalRows] = await Promise.all([
                this.paymentModel
                    .find(condition)
                    .sort({ createdAt: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.paymentModel.countDocuments(condition),
            ]);

            let paymentsRelation: any = [];
            if (booking) {
                const bookingsCare247 = await this.bookingCare247Model.find({ bookingId: booking._id }).exec();
                const care247Transactions = bookingsCare247.map(b => b.transactionId);
                if (query.transactionId) {
                    const transactionsList = [...care247Transactions, booking.transactionId].filter(t => t !== transactionId);
                    paymentsRelation = await this.paymentModel.find({ transactionId: { $in: transactionsList } }).exec();
                } else {
                    paymentsRelation = await this.paymentModel.find({ transactionId: { $in: care247Transactions } }).exec();
                }
            }
            const listRowsTransactions = rows.map(r => r.transactionId);
            paymentsRelation = paymentsRelation.filter(p => !listRowsTransactions.includes(p.transactionId))

            return {
                rows: [...rows, ...paymentsRelation],
                totalRows: totalRows + paymentsRelation.length,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách phiếu Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getAdsCare247(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const [headerAdsConfig, contentAdsConfig, footerAdsConfig] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ADVERTISEMENT_HEADER'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_ADVERTISEMENT_CONTENT'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_ADVERTISEMENT_FOOTER'),
            ]);
            return {
                headerAdsConfig,
                contentAdsConfig,
                footerAdsConfig,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách quảng cáo Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async updateAdsCare247(userId: string, body: AdsCare247Dto) {
        try {
            // Kiểm tra quyền hạn người dùng
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN,
                );
            }
    
            // Xác thực giá trị đầu vào (nếu cần)
            if (!body?.header && !body?.content && !body?.footer) {
                throw new HttpException(
                    'Dữ liệu cập nhật quảng cáo không hợp lệ. Vui lòng kiểm tra lại.',
                    HttpStatus.BAD_REQUEST,
                );
            }
    
            // Cập nhật cấu hình quảng cáo
            const updates = [];
            if (body?.header) {
                updates.push(
                    this.globalSettingService.updateByKeyAndRepoName(
                        { key: 'CARE247_ADVERTISEMENT_HEADER' },
                        body.header,
                    )
                );
            }
            if (body?.content) {
                updates.push(
                    this.globalSettingService.updateByKeyAndRepoName(
                        { key: 'CARE247_ADVERTISEMENT_CONTENT' },
                        body.content,
                    )
                );
            }
            if (body?.footer) {
                updates.push(
                    this.globalSettingService.updateByKeyAndRepoName(
                        { key: 'CARE247_ADVERTISEMENT_FOOTER' },
                        body.footer,
                    )
                );
            }
    
            // Thực hiện các cập nhật đồng thời
            const [headerAdsConfig, contentAdsConfig, footerAdsConfig] = await Promise.all(updates);
    
            // Trả về kết quả
            return {
                headerAdsConfig: headerAdsConfig || null,
                contentAdsConfig: contentAdsConfig || null,
                footerAdsConfig: footerAdsConfig || null,
            };
        } catch (error) {
            // Log lỗi với ngữ cảnh rõ ràng hơn
            console.error(`Error updating ads for userId ${userId}:`, error);
    
            // Xác định thông báo lỗi
            const message =
                error instanceof HttpException
                    ? error.message
                    : 'Lỗi không xác định khi cập nhật quảng cáo Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    
    async updateMultipleKeyGlobalSetting(userId: string, settings: UpdateMultipleKey) {
        try {
            // Kiểm tra quyền hạn
            if (!(await this.userService.isCs(userId))) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN,
                );
            }
    
            // Chuyển DTO sang dạng key-value để cập nhật
            const updates = Object.entries(settings)
                .filter(([_, value]) => value !== undefined) // Loại bỏ các key không được truyền
                .map(([key, value]) =>
                    this.globalSettingService.updateByKeyAndRepoName({ key }, value)
                );
    
            if (!updates.length) {
                throw new HttpException(
                    'Không có key hợp lệ để cập nhật. Vui lòng kiểm tra lại.',
                    HttpStatus.BAD_REQUEST,
                );
            }
            const updatedSettings = await Promise.all(updates);
            console.log('updatedSettings', updatedSettings)
            return {
                message: 'Cập nhật cấu hình thành công.',
                updatedSettings: Object.entries(settings)
                    .filter(([_, value]) => value !== undefined)
                    .map(([key, _], index) => ({
                        key,
                        value: updatedSettings[index],
                    })),
            };
        } catch (error) {
            console.error(`Error updating settings for userId ${userId}:`, error);
            throw new HttpException(
                error?.message || 'Lỗi không xác định khi cập nhật cấu hình Care247.',
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async getMultipleKeyGlobalSetting() {
        try {
            // Lấy danh sách key hợp lệ từ DTO
            const keys = UpdateMultipleKey.VALID_KEYS;
    
            // Lấy giá trị của từng key song song
            const settings = await Promise.all(
                keys.map(key => this.globalSettingService.findByKeyAndRepoName(key))
            );
    
            // Tạo đối tượng cấu hình từ keys và values
            const settingsObject = keys.reduce((acc, key, index) => {
                acc[key] = settings[index];
                return acc;
            }, {});
    
            return {
                message: 'Lấy cấu hình Care247 thành công.',
                settings: settingsObject,
            };
        } catch (error) {
            console.error('Error retrieving Care247 settings:', error);
            throw new HttpException(
                error?.message || 'Lỗi khi lấy cấu hình Care247.',
                HttpStatus.BAD_REQUEST,
            );
        }
    }
    

    async getCare247Diary(userId: string, body: Care247DiaryQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { categoryCode, actionCode, userActionId, fromDate, toDate, pageSize, pageIndex } = body;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const query: any = {
                categoryCode,
                actionCode,
                user: userActionId,
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            };

            const condition = pickBy(query, identity);

            const [rows, totalRows] = await Promise.all([
                this.auditLogCskhModel
                    .find(condition)
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .sort({ createdAt: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .lean()
                    .exec(),
                this.auditLogCskhModel.countDocuments(condition),
            ]);

            const filteredData = rows.map(item => {
                const { headers, ...rest } = item; 
                return rest;
            });

            return {
                rows: filteredData,
                totalRows,
                categoryList: Object.entries(CategoryEnum).map(([code, value]) => ({code, value})),
                actionList: Object.entries(ActionEnum).map(([code, value]) => ({code, value})),
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy nhật ký Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    

    async getCare247Services(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            
            const data = [
                {
                    "duration": "3 giờ",
                    "originalPrice": null,
                    "price": 399000,
                    "name": "Gói Trợ Lý cơ bản",
                    "subname": "",
                    "description": `<p>
                        <ul style='list-style-type: disc'>
                        <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                        <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                        <li>Tổng thời gian dịch vụ: 3 giờ</li>
                        <li>Hotline hỗ trợ: 19002115</li>
                        </ul>
                        <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                        <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                        </p>`,
                    "description_following": "",
                    "currency": "đ",
                    "id": "95066bbb72da4a8eaf6cc08df97c92a8",
                    "status": true,
                    "locale": "vi"
                },
                {
                    "duration": "3 giờ",
                    "originalPrice": null,
                    "price": 699000,
                    "name": "Gói Trợ Lý có hỗ trợ tiếng Anh / Hoa",
                    "subname": "",
                    "description": `<p>
                        <ul style='list-style-type: disc'>
                        <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                        <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                        <li>Tổng thời gian dịch vụ: 3 giờ</li>
                        <li>Hotline hỗ trợ: 19002115</li>
                        </ul>
                        <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc.</p>
                        <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                        </p>`,
                    "description_following": "Hỗ trợ Tiếng Anh / Hoa mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                    "currency": "đ",
                    "id": "665c4f03849c4379802bd4da346bf83d",
                    "status": true,
                    "locale": "en"
                },
                {
                    "duration": "3 giờ",
                    "originalPrice": null,
                    "price": 799000,
                    "name": "Gói Trợ Lý có hỗ trợ tiếng Khmer",
                    "subname": "",
                    "description": `<p>
                        <ul style='list-style-type: disc'>
                        <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                        <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                        <li>Tổng thời gian dịch vụ: 3 giờ</li>
                        <li>Hotline hỗ trợ: 19002115</li>
                        </ul>
                        <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc.</p>
                        <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                        </p>`,
                    "description_following": "Hỗ trợ Khmer mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                    "currency": "đ",
                    "id": "9ad7034872c3485b8aad87d8a408f3f5",
                    "status": true,
                    "locale": "km"
                }
            ]

            return data;
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy dịch vụ Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getCare247ServicesPartner(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            
            const data = [
                {
                    "partnerId": "nhidong1",
                    "name": "Bệnh viện Nhi Đồng 1",
                    "care247Services": [
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 399000,
                            "name": "Gói Trợ Lý cơ bản",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "",
                            "currency": "đ",
                            "id": "95066bbb72da4a8eaf6cc08df97c92a8",
                            "status": true,
                            "locale": "vi"
                        },
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 699000,
                            "name": "Gói Trợ Lý có hỗ trợ tiếng Anh / Hoa",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc.</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "Hỗ trợ Tiếng Anh / Hoa mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                            "currency": "đ",
                            "id": "665c4f03849c4379802bd4da346bf83d",
                            "status": true,
                            "locale": "en"
                        }
                    ],
                    "customPrice": false,
                    "customPriceRule": "",
                    "isOpenCare247": true,
                },
                {
                    "partnerId": "choray",
                    "name": "Bệnh viện Chợ Rẫy",
                    "care247Services": [
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 399000,
                            "name": "Gói Trợ Lý cơ bản",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "",
                            "currency": "đ",
                            "id": "95066bbb72da4a8eaf6cc08df97c92a8",
                            "status": true,
                            "locale": "vi"
                        },
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 699000,
                            "name": "Gói Trợ Lý có hỗ trợ tiếng Anh / Hoa",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc.</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "Hỗ trợ Tiếng Anh / Hoa mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                            "currency": "đ",
                            "id": "665c4f03849c4379802bd4da346bf83d",
                            "status": true,
                            "locale": "en"
                        }
                    ],
                    "customPrice": false,
                    "customPriceRule": "",
                    "isOpenCare247": true,
                },
                {
                    "partnerId": "bvmathcm",
                    "name": "Bệnh Viện Mắt",
                    "care247Services": [
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 399000,
                            "name": "Gói Trợ Lý cơ bản",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "",
                            "currency": "đ",
                            "id": "95066bbb72da4a8eaf6cc08df97c92a8",
                            "status": true,
                            "locale": "vi"
                        },
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 699000,
                            "name": "Gói Trợ Lý có hỗ trợ tiếng Anh / Hoa",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc.</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "Hỗ trợ Tiếng Anh / Hoa mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                            "currency": "đ",
                            "id": "665c4f03849c4379802bd4da346bf83d",
                            "status": true,
                            "locale": "en"
                        },
                        {
                            "duration": "3 giờ",
                            "originalPrice": null,
                            "price": 799000,
                            "name": "Gói Trợ Lý có hỗ trợ tiếng Khmer",
                            "subname": "",
                            "description": `<p>
                                <ul style='list-style-type: disc'>
                                <li>Đối tượng: Người nước ngoài/ Việt kiều/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                                <li>Nội dung dịch vụ: Phiên dịch, trợ lý giúp việc trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                                <li>Tổng thời gian dịch vụ: 3 giờ</li>
                                <li>Hotline hỗ trợ: 19002115</li>
                                </ul>
                                <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc.</p>
                                <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                                </p>`,
                            "description_following": "Hỗ trợ Khmer mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                            "currency": "đ",
                            "id": "9ad7034872c3485b8aad87d8a408f3f5",
                            "status": true,
                            "locale": "km"
                        }
                    ],
                    "customPrice": false,
                    "customPriceRule": "",
                    "isOpenCare247": true,
                }
            ]

            return data;
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy dịch vụ Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getDailyTransactionReport(query: IDailyTransaction) {
        try {
            if (!query) {
                throw new Error('Input query is undefined or null.');
            }
    
            const { page = 0, limit = 10, ...queryData } = query;
            const { timeStart, timeEnd } = this.handleDateForFilter(queryData.from, queryData.to);

            const options: any = queryData?.from || queryData?.to ? {
                createdAt: {
                    ...(queryData?.from && { $gte: timeStart }),
                    ...(queryData?.to && { $lte: timeEnd }),
                },
            } : null;
            
            const currentPage = Math.max(Number(page - 1) || 0, 0);
            const pageSize = Math.max(Number(limit) || 10, 1);
    
            const [rows, totalRows] = await Promise.all([
                this.transactionDailyModel
                    .find(options)
                    .skip(currentPage * pageSize)
                    .sort({ createdAt: -1 })
                    .limit(pageSize)
                    .exec(),
                this.transactionDailyModel.countDocuments(options),
            ]);
    
            return {
                page: page,
                limit: pageSize,
                totalRows,
                rows,
            };
        } catch (error) {
            console.error('Error in getDailyTransactionReport:', error.message);
            throw new Error(`Lỗi getDailyTransactionReport: ${error.message}`); // Custom error message
        }
    }     
    async getNotifTckqChoray(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { fromDate, toDate, status, pageSize, pageIndex } = body
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const query: any = {
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                status
            };

            const condition = pickBy(query, identity);

            const [rows, totalRows] = await Promise.all([
                this.trackingPushNotifTCKQ.find(condition)
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'booking', select: { bookingCode: true, partnerId: true, appId: true, transactionId: true } })
                    .sort({ createdAt: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.trackingPushNotifTCKQ.countDocuments(condition),
            ]);

            const filteredData = rows.map(item => {
                const { notifAppId, notifApiKey, clientIds, ...rest } = item.toObject(); 
                return rest;
            });

            return {
                rows: filteredData,
                totalRows,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi getNotifTckqChoray.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getNotifCare247(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { fromDate, toDate, status, pageSize, pageIndex } = body
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const query: any = {
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                status
            };

            const condition = pickBy(query, identity);

            const [rows, totalRows] = await Promise.all([
                this.trackingPushNotifCare247.find(condition)
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'booking', select: { bookingCode: true, partnerId: true, appId: true, transactionId: true } })
                    .sort({ createdAt: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.trackingPushNotifCare247.countDocuments(condition),
            ]);

            const filteredData = rows.map(item => {
                const { notifAppId, notifApiKey, clientIds, ...rest } = item.toObject(); 
                return rest;
            });

            return {
                rows: filteredData,
                totalRows,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi getNotifTckqChoray.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async reportNotifTckqChoray(userId: string, body: any): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            const { month, status } = body;
            if (!month) {
                throw new HttpException('Tháng là bắt buộc.', HttpStatus.BAD_REQUEST);
            }
    
            const timeStart = moment(month, 'YYYY-MM').startOf('month').toDate();
            const timeEnd = moment(month, 'YYYY-MM').endOf('month').toDate();
            const daysInMonth = moment(month, 'YYYY-MM').daysInMonth();
    
            const match = pickBy({ createdAt: { $gte: timeStart, $lte: timeEnd }, status }, identity);
            const dataObj = await this.trackingPushNotifTCKQ.find(match).exec();
    
            const result = Array.from({ length: daysInMonth }, (_, index) => {
                const day = `${month}-${String(index + 1).padStart(2, '0')}`;
                const { timeStart: dayStart, timeEnd: dayEnd } = this.handleDateForFilter(day, day);
    
                const dayData = dataObj.filter((item: any) =>
                    moment(item?.createdAt).isBetween(dayStart, dayEnd, null, '[]')
                );
    
                const delivered = dayData.filter((item: any) => item.status === 'Delivered' && item.viewNotif !== 1).length;
                const converted = dayData.filter((item: any) => item.status === 'Delivered' && item.viewNotif === 1).length;
    
                return {
                    date: day,
                    total: dayData.length,
                    Delivered: delivered,
                    Converted: converted,
                    'Failed/Unsubscribed': dayData.length - delivered - converted,
                };
            });
    
            return result;
        } catch (error) {
            console.log('error', error);
            throw new HttpException(error?.message || 'Lỗi reportNotifTckqChoray.', HttpStatus.BAD_REQUEST);
        }
    }
     
    async getReportNotifCare247(userId: string, body: any): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            const { month } = body;
            if (!month) {
                throw new HttpException('Tháng là bắt buộc.', HttpStatus.BAD_REQUEST);
            }
    
            const timeStart = moment(month, 'YYYY-MM').startOf('month').toDate();
            const timeEnd = moment(month, 'YYYY-MM').endOf('month').toDate();
            const daysInMonth = moment(month, 'YYYY-MM').daysInMonth();
    
            const match = pickBy({ createdAt: { $gte: timeStart, $lte: timeEnd } }, identity);
            const dataObj = await this.trackingPushNotifCare247.find(match).exec();
    
            const result = Array.from({ length: daysInMonth }, (_, index) => {
                const day = `${month}-${String(index + 1).padStart(2, '0')}`;
                const { timeStart: dayStart, timeEnd: dayEnd } = this.handleDateForFilter(day, day);
    
                const dayData = dataObj.filter((item: any) =>
                    moment(item?.createdAt).isBetween(dayStart, dayEnd, null, '[]')
                );
    
                const delivered = dayData.filter((item: any) => item.status === 'Delivered' && item.viewNotif !== 1).length;
                const converted = dayData.filter((item: any) => item.status === 'Delivered' && item.viewNotif === 1).length;
    
                return {
                    date: day,
                    total: dayData.length,
                    Delivered: delivered,
                    Converted: converted,
                    'Failed/Unsubscribed': dayData.length - delivered - converted,
                };
            });
    
            return result;
        } catch (error) {
            console.log('error', error);
            throw new HttpException(error?.message || 'Lỗi reportNotifTckqChoray.', HttpStatus.BAD_REQUEST);
        }
    }
    
    async syncNotifTckqChoray(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { fromDate, toDate } = body;
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const url = `${this.urlConfigService.getUrlPushNotifV2}/event/sync-notification-tckq-choray`;
            await this.http.post(url, { timeStart, timeEnd }).toPromise();
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách phiếu Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getCare247Introduction(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const care247IntroductionConfig = await this.globalSettingService.findByKeyAndRepoName('CARE247_INTRODUCTION')
            let care247Introduction = {}
            if(care247IntroductionConfig){
                care247Introduction = JSON.parse(care247IntroductionConfig);
            }
            return care247Introduction
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy Care247 Introduction.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async updateCare247Introduction(userId, updatedDataDto: IUpdateCare247IntroductionDto) {
        try {
            // Kiểm tra quyền của user
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            const dataTracking: any = {
                dataBefore: {},
                dataAfter: {},
                userId,
                userAction: userId
            };
    
            // Chỉ lấy các trường hợp lệ từ DTO
            const { name, subname, medproCareNote, note } = updatedDataDto;
    
            // Lấy cấu hình hiện tại
            const care247IntroductionConfig = await this.globalSettingService.findByKeyAndRepoName('CARE247_INTRODUCTION');
            let care247Introduction = {};
    
            if (care247IntroductionConfig) {
                care247Introduction = JSON.parse(care247IntroductionConfig);
                dataTracking.dataBefore = JSON.parse(care247IntroductionConfig);
            }
    
            // Cập nhật dữ liệu mới vào cấu hình hiện tại
            care247Introduction = {
                ...care247Introduction,
                name,
                subname,
                medproCareNote,
                note
            };
    
            // Lưu cấu hình đã cập nhật
            await this.globalSettingService.updateByKeyAndRepoName(
                {key: 'CARE247_INTRODUCTION'},
                JSON.stringify(care247Introduction)
            );

            dataTracking.dataAfter = care247Introduction;
            await this.introductionCare247ChangesModel.create(dataTracking)
    
            return { isOk: true, message: 'Update Care247 Introduction thành công!' };
        } catch (error) {
            console.error('Error updating Care247 Introduction:', error);
            const message = error?.message || 'Lỗi cập nhật Care247 Introduction.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getCare247IntroductionChanges(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            return await this.introductionCare247ChangesModel.find()
                    .populate({ path: 'userAction', select: { fullname: true, username: true } })
                    .sort({ createdAt: -1 })
                    .exec()
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy Care247 Introduction.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    async getCskhsRevenue(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const cskhEmployeeList = await this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST');
            const listIdCs = cskhEmployeeList.split(',');
            const cskhList = await this.userModel.find({ _id: { $in: listIdCs }, isCS: true  }, { fullname: true, username: true }).exec();
            return cskhList;
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách doanh số nhân viên CSKH.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
            
        }
        
    }

    async bookingCskhRevenueExportExcel(userId: string, formData: any): Promise<any> {
        const { cskhUserId, fromDate, toDate } = formData;

        const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

        const timeConfig = await this.globalSettingService.findByKeyAndRepoName('TIME_LIMIT_CSKH_REVENUE');

        const diffTimes = moment(timeEnd).utc().diff(moment(timeStart).utc(), 'days');

        if (diffTimes > +timeConfig) {
            throw new HttpException(`Thời gian không được quá ${+timeConfig} ngày.`, HttpStatus.BAD_REQUEST)
        }

        const now = moment().utc().toDate();
        const pastDays = moment().utc().subtract(+timeConfig, 'days').toDate();

        const dateQuery = fromDate && toDate ? { $gte: timeStart, $lte: timeEnd } : { $gte: pastDays, $lte: now };

        const conditionBooking = pickBy({
            status: { $in: [0, 1, 2, -2] },
            createdAt: dateQuery,
            cskhUserId,
        }, identity);

        const dataBooking = await this.kpiCskhModel.find(conditionBooking)
            .populate('payment')
            .populate({ path: 'cskh', select: { fullname: true, username: true } })
            .populate({ path: 'booking', select: { status: true, bookingCode: true, bookingCodeV1: true, transactionId: true, paymentStatus: true } })
            .populate({ path: 'partner', select: 'name' })
            .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
            .populate({ path: 'subject', select: { name: true } })
            .populate({ path: 'service', select: { name: true } })
            .populate({ path: 'room', select: { name: true } })
            .populate({ path: 'doctor', select: { name: true } })
            .exec();

        //Create data excel
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet('Tất cả');
        worksheet.columns = [
            { header: 'STT', key: 'stt', width: 25 },
            { header: 'Họ và tên', key: 'name', width: 40 },
            { header: 'SĐT', key: 'phone', width: 40 },
            { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
            { header: 'Mode', key: 'mode', width: 25 },
            { header: 'category', key: 'category', width: 35 },
            { header: 'treeId', key: 'treeId', width: 25 },
            { header: 'Bệnh nhân', key: 'patient', width: 40 },
            { header: 'Trạng thái', key: 'status', width: 25 },
            { header: 'partnerId', key: 'partnerId', width: 20 },
            { header: 'Cơ sở y tế', key: 'partnerName', width: 20 },
            { header: 'Dịch vụ', key: 'service', width: 45 },
            { header: 'Chuyên khoa', key: 'subject', width: 45 },
            { header: 'Phòng khám', key: 'room', width: 45 },
            { header: 'Ngày khám', key: 'date', width: 20 },
            { header: 'Giờ khám', key: 'time', width: 20 },
            { header: 'Ngày đặt', key: 'createdDate', width: 20 },
            { header: 'Giờ đặt', key: 'createdTime', width: 20 },
            { header: 'Mã giao dịch', key: 'transactionId', width: 25 },
            { header: 'Trạng thái giao dịch', key: 'paymentStatus', width: 25 },
            { header: 'Tiền khám', key: 'subTotal', width: 25 },
            { header: 'Tiền Care247', key: 'medproCareFee', width: 25 },
            { header: 'Phí tiện ích Medpro', key: 'medproFee', width: 25 },
            { header: 'Tổng tiền', key: 'amount', width: 25 },
        ];

        const dataMap = dataBooking.map((item: any, index: number) => ({
            stt: index + 1,
            name: item?.cskh?.fullname,
            phone: item?.cskh?.username,
            bookingCode: item.booking.bookingCodeV1 || item.booking.bookingCode,
            mode: item.mode || '-',
            category: this.handleCategoryKpiCskh(item.treeId),
            treeId: item.treeId,
            patient: `${item?.patient?.surname} ${item?.patient?.name} - ${item?.patient?.mobile}`,
            status: this.handleStatusDescription(item.status),
            partnerId: item.partnerId,
            partnerName: this.getPartnerName(item.partnerId, item?.partner?.name),
            service: item?.service?.name,
            subject: item?.subject?.name,
            room: item?.room?.name,
            date: moment(item.date).utc().add(7, 'hours').format('DD/MM/YYYY'),
            time: moment(item.date).utc().add(7, 'hours').format('HH:mm'),
            createdDate: moment(item.createdDate).utc().add(7, 'hours').format('DD/MM/YYYY'),
            createdTime: moment(item.createdDate).utc().add(7, 'hours').format('HH:mm'),
            transactionId: item?.booking?.transactionId,
            paymentStatus: this.handlePaymentStatusDescription(item?.booking?.paymentStatus),
            subTotal: item?.payment?.subTotal || '-',
            medproCareFee: item?.payment?.medproCareFee || '-',
            medproFee: item?.payment?.medproFee || '-',
            amount: item?.payment?.amount || '-',
        }));

        dataMap.forEach(row => {
            worksheet.addRow(row);
        });

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async countBookingCskhAggregated(
        cskhUserIds: any,
        dateQuery: any,
        primaryPartnerIdFilter?: string,
    ) {
        const cskhUserIdsArray = Array.isArray(cskhUserIds) ? cskhUserIds : [cskhUserIds];
        const matchCriteria: any = {
            cskhUserId: { $in: cskhUserIdsArray },
            createdDate: dateQuery,
        };

        if (primaryPartnerIdFilter) {
            matchCriteria.partnerId = primaryPartnerIdFilter;
        } 
        const partnerCountZero = ['binhthanhhcm', 'bvmathcm'].includes(primaryPartnerIdFilter)

        const pipeline = [
            {
                $match: matchCriteria,
            },
            {
                $group: {
                    _id: {
                        cskhUserId: '$cskhUserId',
                        mode: '$mode',
                    },
                    qtyBooking: { 
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$countDoanhSo', partnerCountZero ? 0 : 1] },
                                        { $in: ['$status', [0, 1, 2, -2, -3]] }
                                    ]
                                },
                                then: 1,
                                else: 0,
                            },
                        }, 
                    },
                    qtyPaySuccess: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$countDoanhSo', partnerCountZero ? 0 : 1] },
                                        { $in: ['$status', [1, 2]] }
                                    ]
                                },
                                then: 1,
                                else: 0,
                            },
                        },
                    },
                    qtyPayPending: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$countDoanhSo', partnerCountZero ? 0 : 1] },
                                        { $in: ['$status', [0, -3]] }
                                    ]
                                },
                                then: 1,
                                else: 0,
                            },
                        },
                    },
                    qtyCancel: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$countDoanhSo', partnerCountZero ? 0 : 1] },
                                        { $in: ['$status', [-2]] }
                                    ]
                                },
                                then: 1,
                                else: 0,
                            },
                        },
                    },

                    // --- ADDITION FOR bookingBinhThanh ---
                    qtyBookingBinhThanh: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$mode', 'booking'] },
                                        { $eq: ['$countDoanhSo', 0] },
                                        { $eq: ['$partnerId', 'binhthanhhcm'] },
                                        { $in: ['$status', [0, 1, 2, -2, -3]] }
                                    ]
                                },
                                then: 1,
                                else: 0
                            }
                        }
                    },
                    qtyBookingPaySuccessBinhThanh: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$mode', 'booking'] },
                                        { $eq: ['$countDoanhSo', 0] },
                                        { $eq: ['$partnerId', 'binhthanhhcm'] },
                                        { $in: ['$status', [1, 2]] }
                                    ]
                                },
                                then: 1,
                                else: 0
                            }
                        }
                    },
                    qtyBookingPayPendingBinhThanh: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$mode', 'booking'] },
                                        { $eq: ['$countDoanhSo', 0] },
                                        { $eq: ['$partnerId', 'binhthanhhcm'] },
                                        { $in: ['$status', [0, -3]] }
                                    ]
                                },
                                then: 1,
                                else: 0
                            }
                        }
                    },
                    qtyBookingCancelBinhThanh: {
                        $sum: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $eq: ['$mode', 'booking'] },
                                        { $eq: ['$countDoanhSo', 0] },
                                        { $eq: ['$partnerId', 'binhthanhhcm'] },
                                        { $in: ['$status', [-2]] }
                                    ]
                                },
                                then: 1,
                                else: 0
                            }
                        }
                    },
                },
            },
            {
                // Regroup by cskhUserId to gather all metrics for that user
                $group: {
                    _id: '$_id.cskhUserId',
                    metrics: {
                        $push: {
                            mode: '$_id.mode',
                            qtyBooking: '$qtyBooking',
                            qtyPaySuccess: '$qtyPaySuccess',
                            qtyPayPending: '$qtyPayPending',
                            qtyCancel: '$qtyCancel',
                        },
                    },
                    metricsBinhThanh: {
                        $push: {
                            mode: '$_id.mode',
                            qtyBookingBinhThanh: '$qtyBookingBinhThanh',
                            qtyBookingPaySuccessBinhThanh: '$qtyBookingPaySuccessBinhThanh',
                            qtyBookingPayPendingBinhThanh: '$qtyBookingPayPendingBinhThanh',
                            qtyBookingCancelBinhThanh: '$qtyBookingCancelBinhThanh',
                        },
                    },
                },
            },
            {
                // Project to reshape the output and create the final objects
                $project: {
                    _id: 0,
                    cskhUserId: '$_id',
                    booking: {
                        $first: { $filter: { input: '$metrics', as: 'm', cond: { $eq: ['$$m.mode', 'booking'] } } },
                    },
                    care247: {
                        $first: { $filter: { input: '$metrics', as: 'm', cond: { $eq: ['$$m.mode', 'care247'] } } },
                    },
                    examPackage: {
                        $first: { $filter: { input: '$metrics', as: 'm', cond: { $eq: ['$$m.mode', 'package'] } } },
                    },
                    telemed: {
                        $first: { $filter: { input: '$metrics', as: 'm', cond: { $eq: ['$$m.mode', 'telemed'] } } },
                    },
                    // --- NEW: Project bookingBinhThanh separately ---
                    bookingBinhThanh: {
                        qtyBooking: { $sum: '$metricsBinhThanh.qtyBookingBinhThanh' },
                        qtyPaySuccess: { $sum: '$metricsBinhThanh.qtyBookingPaySuccessBinhThanh' },
                        qtyPayPending: { $sum: '$metricsBinhThanh.qtyBookingPayPendingBinhThanh' },
                        qtyCancel: { $sum: '$metricsBinhThanh.qtyBookingCancelBinhThanh' },
                    }
                },
            },
        ];

        const results = await this.kpiCskhModel.aggregate(pipeline).exec();
        
        // Format results into a map for easy lookup
        const formattedResults = results.reduce((acc, current) => {
            const defaultMetrics = { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 };
            // Default BinhThanh metrics
            const defaultBinhThanhMetrics = { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 };

            acc[current.cskhUserId.toString()] = {
                booking: current.booking || defaultMetrics,
                care247: current.care247 || defaultMetrics,
                examPackage: current.examPackage || defaultMetrics,
                telemed: current.telemed || defaultMetrics,
                bookingBinhThanh: current.bookingBinhThanh || defaultBinhThanhMetrics, // Add BinhThanh to the map
            };
            return acc;
        }, {});

        return formattedResults;
    }

    async getBookingCskhRevenue(userId: string, body: BookingCskhRevenueQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { cskhUserId, partnerId, fromDate, toDate } = body;

            const [care247UserRoleList, cskhEmployeeList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('DS_CARE247_EXPORT_PERMISSION_VIEW'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            ]);

            // const care247UserRoleListArr = care247UserRoleList.split(',');
            // const listUserRole = new Set(care247UserRoleListArr);

            const listIdCs = cskhEmployeeList.split(',');
            
            // let cskhUserCondition: any;
            // if (listUserRole.has(userId)) {
            //     cskhUserCondition = cskhUserId ? cskhUserId : { $in: listIdCs };
            // } else {
            //     cskhUserCondition = userId;
            // }
            let cskhUserCondition = cskhUserId ? cskhUserId : { $in: listIdCs };


            const cskhListFilter = await this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec();

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const timeConfig = await this.globalSettingService.findByKeyAndRepoName('TIME_LIMIT_CSKH_REVENUE');

            const diffTimes = moment(timeEnd).utc().diff(moment(timeStart).utc(), 'days');

            if (diffTimes > +timeConfig) {
                throw new HttpException(`Thời gian không được quá ${+timeConfig} ngày.`, HttpStatus.BAD_REQUEST)
            }

            const now = moment().utc().toDate();
            const pastDays = moment().utc().subtract(+timeConfig, 'days').toDate();

            const dateQuery = fromDate && toDate ? { $gte: timeStart, $lte: timeEnd } : { $gte: pastDays, $lte: now };

            const cskhList = await this.userModel.find({ _id: cskhUserCondition, isCS: true }, { fullname: true, username: true }).exec();

            let qtyBookingTotal = 0;
            let qtyBookingPaySuccessTotal = 0;
            let qtyBookingPayPendingTotal = 0;
            let qtyBookingCancelTotal = 0;
            let qtyBookingTotalBinhThanh = 0;
            let qtyBookingPaySuccessTotalBinhThanh = 0;
            let qtyBookingPayPendingTotalBinhThanh = 0;
            let qtyBookingCancelTotalBinhThanh = 0;
            let qtyCare247Total = 0;
            let qtyCare247PaySuccessTotal = 0;
            let qtyCare247PayPendingTotal = 0;
            let qtyCare247CancelTotal = 0;
            let qtyPackageTotal = 0;
            let qtyPackagePaySuccessTotal = 0;
            let qtyPackagePayPendingTotal = 0;
            let qtyPackageCancelTotal = 0;
            let qtyTelemedTotal = 0;
            let qtyTelemedPaySuccessTotal = 0;
            let qtyTelemedPayPendingTotal = 0;
            let qtyTelemedCancelTotal = 0;

            const data = await Promise.all(
                cskhList.map(async employee => {
                    const employeeObj = employee.toObject();
                    const { booking, care247, examPackage, telemed } = await this.countBookingCskh(`${employeeObj._id}`, dateQuery, partnerId);
                    // const { booking: bookingBinhThanh } = await this.countBookingCskh(`${employeeObj._id}`, dateQuery, 'binhthanhhcm');
                    qtyBookingTotal += booking.qtyBooking;
                    qtyBookingPaySuccessTotal += booking.qtyPaySuccess;
                    qtyBookingPayPendingTotal += booking.qtyPayPending;
                    qtyBookingCancelTotal += booking.qtyCancel;
                    // qtyBookingTotalBinhThanh += bookingBinhThanh.qtyBooking;
                    // qtyBookingPaySuccessTotalBinhThanh += bookingBinhThanh.qtyPaySuccess;
                    // qtyBookingPayPendingTotalBinhThanh += bookingBinhThanh.qtyPayPending;
                    // qtyBookingCancelTotalBinhThanh += bookingBinhThanh.qtyCancel;
                    qtyCare247Total += care247.qtyBooking;
                    qtyCare247PaySuccessTotal += care247.qtyPaySuccess;
                    qtyCare247PayPendingTotal += care247.qtyPayPending;
                    qtyCare247CancelTotal += care247.qtyCancel;
                    qtyPackageTotal += examPackage.qtyBooking;
                    qtyPackagePaySuccessTotal += examPackage.qtyPaySuccess;
                    qtyPackagePayPendingTotal += examPackage.qtyPayPending;
                    qtyPackageCancelTotal += examPackage.qtyCancel;
                    qtyTelemedTotal += telemed.qtyBooking;
                    qtyTelemedPaySuccessTotal += telemed.qtyPaySuccess;
                    qtyTelemedPayPendingTotal += telemed.qtyPayPending;
                    qtyTelemedCancelTotal += telemed.qtyCancel;
                    
                    return {
                        staffNo: employeeObj.username,
                        agentName: employeeObj.fullname,
                        booking,
                        // bookingBinhThanh,
                        care247,
                        examPackage,
                        telemed,
                    };
                }),
            );

            const total = { 
                qtyBookingTotal,
                qtyBookingPaySuccessTotal,
                qtyBookingPayPendingTotal,
                qtyBookingCancelTotal,
                qtyBookingTotalBinhThanh,
                qtyBookingPaySuccessTotalBinhThanh,
                qtyBookingPayPendingTotalBinhThanh,
                qtyBookingCancelTotalBinhThanh,
                qtyCare247Total,
                qtyCare247PaySuccessTotal,
                qtyCare247PayPendingTotal,
                qtyCare247CancelTotal,
                qtyPackageTotal,
                qtyPackagePaySuccessTotal,
                qtyPackagePayPendingTotal,
                qtyPackageCancelTotal,
                qtyTelemedTotal,
                qtyTelemedPaySuccessTotal,
                qtyTelemedPayPendingTotal,
                qtyTelemedCancelTotal
            };

            const dataRowTotal = {
                staffNo: "",
                agentName: "Tổng cộng",
                booking: {
                    qtyBooking: qtyBookingTotal,
                    qtyPaySuccess: qtyBookingPaySuccessTotal,
                    qtyPayPending: qtyBookingPayPendingTotal,
                    qtyCancel: qtyBookingCancelTotal
                },
                bookingBinhThanh: {
                    qtyBooking: qtyBookingTotalBinhThanh,
                    qtyPaySuccess: qtyBookingPaySuccessTotalBinhThanh,
                    qtyPayPending: qtyBookingPayPendingTotalBinhThanh,
                    qtyCancel: qtyBookingCancelTotalBinhThanh
                },
                care247: {
                    qtyBooking: qtyCare247Total,
                    qtyPaySuccess: qtyCare247PaySuccessTotal,
                    qtyPayPending: qtyCare247PayPendingTotal,
                    qtyCancel: qtyCare247CancelTotal
                },
                examPackage: {
                    qtyBooking: qtyPackageTotal,
                    qtyPaySuccess: qtyPackagePaySuccessTotal,
                    qtyPayPending: qtyPackagePayPendingTotal,
                    qtyCancel: qtyPackageCancelTotal
                },
                telemed: {
                    qtyBooking: qtyTelemedTotal,
                    qtyPaySuccess: qtyTelemedPaySuccessTotal,
                    qtyPayPending: qtyTelemedPayPendingTotal,
                    qtyCancel: qtyTelemedCancelTotal
                },
            }

            const result = cskhUserId ? data : [dataRowTotal, ...data]

            let userBookings
            if (cskhUserId) {
                userBookings = await this.kpiCskhModel.find({ cskhUserId, createdDate: { $gte: timeStart, $lte: timeEnd } }).exec();
            }


            return {
                data: result,
                total,
                listCs: sortBy(cskhListFilter, [item => item?.fullname?.toLowerCase()]),
                userBookings,
                userBookingsLenght: userBookings?.length
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách doanh số nhân viên CSKH.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getBookingCskhRevenueV2(userId: string, body: BookingCskhRevenueQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { cskhUserId, partnerId, fromDate, toDate } = body;

            // Fetch global settings concurrently
            const [cskhEmployeeListSetting, timeConfigSetting] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('TIME_LIMIT_CSKH_REVENUE'),
            ]);

            const listIdCs = cskhEmployeeListSetting.split(',')
            const timeConfig = +timeConfigSetting;

            // Determine the actual list of CSKH users to fetch data for
            let targetCskhUserIds: string[];
            if (cskhUserId) {
                // If a specific user is requested, override with that user
                targetCskhUserIds = [cskhUserId];
            } else {
                // Otherwise, use the full list from global settings
                targetCskhUserIds = listIdCs;
            }

            // Fetch user details for display, optimized to only fetch once
            const cskhListDetails = await this.userModel.find({ _id: { $in: targetCskhUserIds }, isCS: true }, { fullname: true, username: true }).exec();

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const diffTimes = moment(timeEnd).utc().diff(moment(timeStart).utc(), 'days');

            if (diffTimes > timeConfig) {
                throw new HttpException(`Thời gian không được quá ${timeConfig} ngày.`, HttpStatus.BAD_REQUEST)
            }

            // Determine the final date query for MongoDB
            const now = moment().utc().toDate();
            const pastDays = moment().utc().subtract(timeConfig, 'days').toDate();
            const dateQuery = fromDate && toDate ? { $gte: timeStart, $lte: timeEnd } : { $gte: pastDays, $lte: now };

            // --- OPTIMIZATION HERE ---
            // Call the aggregated function once for all target CSKH users
            const aggregatedMetricsMap = await this.countBookingCskhAggregated(targetCskhUserIds, dateQuery, partnerId);


            let qtyBookingTotal = 0;
            let qtyBookingPaySuccessTotal = 0;
            let qtyBookingPayPendingTotal = 0;
            let qtyBookingCancelTotal = 0;
            let qtyBookingTotalBinhThanh = 0;
            let qtyBookingPaySuccessTotalBinhThanh = 0;
            let qtyBookingPayPendingTotalBinhThanh = 0;
            let qtyBookingCancelTotalBinhThanh = 0;
            let qtyCare247Total = 0;
            let qtyCare247PaySuccessTotal = 0;
            let qtyCare247PayPendingTotal = 0;
            let qtyCare247CancelTotal = 0;
            let qtyPackageTotal = 0;
            let qtyPackagePaySuccessTotal = 0;
            let qtyPackagePayPendingTotal = 0;
            let qtyPackageCancelTotal = 0;
            let qtyTelemedTotal = 0;
            let qtyTelemedPaySuccessTotal = 0;
            let qtyTelemedPayPendingTotal = 0;
            let qtyTelemedCancelTotal = 0;

            const data = cskhListDetails.map(employee => {
                const employeeObj = employee.toObject();
                const metrics = aggregatedMetricsMap[employeeObj._id.toString()] || {
                    booking: { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 },
                    bookingBinhThanh: { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 },
                    care247: { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 },
                    examPackage: { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 },
                    telemed: { qtyBooking: 0, qtyPaySuccess: 0, qtyPayPending: 0, qtyCancel: 0 },
                }; // Get metrics or default to zero

                // Accumulate totals
                qtyBookingTotal += metrics.booking.qtyBooking;
                qtyBookingPaySuccessTotal += metrics.booking.qtyPaySuccess;
                qtyBookingPayPendingTotal += metrics.booking.qtyPayPending;
                qtyBookingCancelTotal += metrics.booking.qtyCancel;

                qtyBookingTotalBinhThanh += metrics.bookingBinhThanh.qtyBooking;
                qtyBookingPaySuccessTotalBinhThanh += metrics.bookingBinhThanh.qtyPaySuccess;
                qtyBookingPayPendingTotalBinhThanh += metrics.bookingBinhThanh.qtyPayPending;
                qtyBookingCancelTotalBinhThanh += metrics.bookingBinhThanh.qtyCancel;

                qtyCare247Total += metrics.care247.qtyBooking;
                qtyCare247PaySuccessTotal += metrics.care247.qtyPaySuccess;
                qtyCare247PayPendingTotal += metrics.care247.qtyPayPending;
                qtyCare247CancelTotal += metrics.care247.qtyCancel;

                qtyPackageTotal += metrics.examPackage.qtyBooking;
                qtyPackagePaySuccessTotal += metrics.examPackage.qtyPaySuccess;
                qtyPackagePayPendingTotal += metrics.examPackage.qtyPayPending;
                qtyPackageCancelTotal += metrics.examPackage.qtyCancel;

                qtyTelemedTotal += metrics.telemed.qtyBooking;
                qtyTelemedPaySuccessTotal += metrics.telemed.qtyPaySuccess;
                qtyTelemedPayPendingTotal += metrics.telemed.qtyPayPending;
                qtyTelemedCancelTotal += metrics.telemed.qtyCancel;

                // Return formatted data for each employee
                return {
                    staffNo: employeeObj.username,
                    agentName: employeeObj.fullname,
                    booking: metrics.booking,
                    bookingBinhThanh: metrics.bookingBinhThanh,
                    care247: metrics.care247,
                    examPackage: metrics.examPackage,
                    telemed: metrics.telemed,
                };
            });

            // Totals object
            const total = {
                qtyBookingTotal,
                qtyBookingPaySuccessTotal,
                qtyBookingPayPendingTotal,
                qtyBookingCancelTotal,
                qtyBookingTotalBinhThanh,
                qtyBookingPaySuccessTotalBinhThanh,
                qtyBookingPayPendingTotalBinhThanh,
                qtyBookingCancelTotalBinhThanh,
                qtyCare247Total,
                qtyCare247PaySuccessTotal,
                qtyCare247PayPendingTotal,
                qtyCare247CancelTotal,
                qtyPackageTotal,
                qtyPackagePaySuccessTotal,
                qtyPackagePayPendingTotal,
                qtyPackageCancelTotal,
                qtyTelemedTotal,
                qtyTelemedPaySuccessTotal,
                qtyTelemedPayPendingTotal,
                qtyTelemedCancelTotal
            };

            // Data row for totals
            const dataRowTotal = {
                staffNo: "",
                agentName: "Tổng cộng",
                booking: {
                    qtyBooking: qtyBookingTotal,
                    qtyPaySuccess: qtyBookingPaySuccessTotal,
                    qtyPayPending: qtyBookingPayPendingTotal,
                    qtyCancel: qtyBookingCancelTotal
                },
                bookingBinhThanh: { // Placeholder, needs specific calculation if active
                    qtyBooking: qtyBookingTotalBinhThanh,
                    qtyPaySuccess: qtyBookingPaySuccessTotalBinhThanh,
                    qtyPayPending: qtyBookingPayPendingTotalBinhThanh,
                    qtyCancel: qtyBookingCancelTotalBinhThanh
                },
                care247: {
                    qtyBooking: qtyCare247Total,
                    qtyPaySuccess: qtyCare247PaySuccessTotal,
                    qtyPayPending: qtyCare247PayPendingTotal,
                    qtyCancel: qtyCare247CancelTotal
                },
                examPackage: {
                    qtyBooking: qtyPackageTotal,
                    qtyPaySuccess: qtyPackagePaySuccessTotal,
                    qtyPayPending: qtyPackagePayPendingTotal,
                    qtyCancel: qtyPackageCancelTotal
                },
                telemed: {
                    qtyBooking: qtyTelemedTotal,
                    qtyPaySuccess: qtyTelemedPaySuccessTotal,
                    qtyPayPending: qtyTelemedPayPendingTotal,
                    qtyCancel: qtyTelemedCancelTotal
                },
            }

            const resultDataArray = cskhUserId ? data : [dataRowTotal, ...data]; // Use a distinct variable name

            let userBookings;
            if (cskhUserId) {
                userBookings = await this.kpiCskhModel.find({ cskhUserId: new Types.ObjectId(cskhUserId), createdDate: dateQuery }).exec();
            }

            return {
                data: resultDataArray, // Return the main data array
                total, // The aggregated totals object
                listCs: sortBy(cskhListDetails, [item => item?.fullname?.toLowerCase()]), // Sort the fetched user details for the listCs field
                userBookings,
                userBookingsLenght: userBookings?.length
            };
        } catch (error) {
            console.error('Error in getBookingCskhRevenue:', error); // Use console.error for errors
            const message = error instanceof HttpException ? error.message : 'Lỗi lấy danh sách doanh số nhân viên CSKH.';
            const status = error instanceof HttpException ? error.getStatus() : HttpStatus.BAD_REQUEST;
            throw new HttpException(message, status);
        }
    }

    async getCare247UserRebooking(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { sortTimes, pageSize, pageIndex, partnerId } = body;

            let sortCondition: any = { createdAt: -1 }
            if (sortTimes === 1) {
                sortCondition = { times: 1 }
            } else if (sortTimes === -1 ) {
                sortCondition = { times: -1 }
            }


            const [rows, totalRows] = await Promise.all([
                this.bookingCare247ConstraintUserModel.find({ multiple: 2 })
                    .populate('care247')
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'latestPartner', select: { name: true, partnerId: true }  })
                    .sort(sortCondition)
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247ConstraintUserModel.countDocuments({ multiple: 2 }),
            ]);

            let data = rows.map((r: any) => {
                const item = r.toObject();
                return { 
                    ...item, 
                    latestPartner: { ...item.latestPartner, name: this.getPartnerName(item.latestPartner.partnerId, item.latestPartner.name) },
                };
            });

            if (partnerId) {
                data = data.filter((item: any) => {
                    const partnerList = item.care247.map((c: any) => c.partnerId);
                    if (partnerList.includes(partnerId)) {
                        return item;
                    }
                })
            }

            return {
                rows: data,
                totalRows
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách khách hàng đặt lại dịch vụ Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getCare247UserRebookingHistory(userId: string, body: Care247ConstraintUserHistoryQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { id, pageSize, pageIndex } = body;
            
            const constraintUser = await this.bookingCare247ConstraintUserModel.findById({ _id: id }).read('primary').exec();

            const [rows, totalRows] = await Promise.all([
                this.bookingCare247Model.find({ _id: { $in: constraintUser.care247 } })
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments({ _id: { $in: constraintUser.care247 } }),
            ]);

            const data = rows.map((r: any) => {
                const item = r.toObject();
                return { 
                    ...item, 
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) },
                };
            });

            return {
                rows: data,
                totalRows
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách lịch sử dịch vụ Care247 khách hàng đã đặt.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async addFlagBookingCare247(userId: string, body: { id: string }) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { id } = body;

            const bookingCare247 = await this.bookingCare247Model.findById({ _id: id }).exec();

            if (!bookingCare247) {
                throw new HttpException(`Không tìm thấy thông tin Care247.`, HttpStatus.BAD_REQUEST);
            }
            
            const bookingCare247Flag = new this.bookingCare247FlagsModel({
                userId,
                user: userId,
                care247Id: bookingCare247._id,
                care247: bookingCare247._id,
                constraint: `${userId}_${bookingCare247._id}`
            });
            await bookingCare247Flag.save();

            return {
                isOk: true,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi add flag.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async removeFlagBookingCare247(userId: string, body: { id: string }) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { id } = body;

            await this.bookingCare247FlagsModel.deleteOne({ userId, care247Id: id });

            return {
                isOk: true,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi remove flag.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async findBookingByMedproCareId(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            const { id } = body;
            // console.log('id:', id);
            const getBooking = await this.bookingCare247Model.findById(id).exec();
            // console.log('getBooking:', JSON.stringify(getBooking, null, 2));
            if (!getBooking) {
                return {};
            }
    
            const bookingObj = getBooking.toObject();
            const date = moment(bookingObj.date).format('YYYY-MM-DD');
            const { timeStart, timeEnd } = this.handleDateForFilter(date, date);
    
            // Tìm tất cả các đặt chỗ trong khoảng thời gian
            const condition = { 
                userId: bookingObj.userId, 
                // partnerId: bookingObj.partnerId,
                bookingStatus: 1,
                date: { $gte: timeStart, $lte: timeEnd } 
            };
            // console.log('condition:', JSON.stringify(condition, null, 2));
            const bookingData = await this.bookingCare247Model
                .find(condition)
                .populate({ path: 'partner', select: 'name' })
                .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                .populate({ path: 'user', select: { fullname: true, username: true } })
                .populate({ path: 'cskh', select: { fullname: true, username: true } })
                .populate({ path: 'subject', select: { name: true } })
                .populate({ path: 'service', select: { name: true } })
                .populate({ path: 'room', select: { name: true } })
                .populate({ path: 'doctor', select: { name: true } })
                .exec();
            
            const result = bookingData.map(c => {
                const item = c.toObject();
                return { ...item,
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) },
                    status: item?.status || 0 
                }
            })

            return result;
            
        } catch (error) {
            const message = error?.message || 'Lỗi lấy danh sách phiếu theo booking id care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    private transformContacts(contacts:any[]) {
        return contacts.map((contact) => {
            const transformedContact = contact.toObject();
    
            if (transformedContact.userCreateInfo) {
                transformedContact.userCreateInfo = {
                    ...transformedContact.userCreateInfo,
                    date: transformedContact.dateCreate || null,
                };
            }
    
            if (transformedContact.userUpdateInfo) {
                transformedContact.userUpdateInfo = {
                    ...transformedContact.userUpdateInfo,
                    date: transformedContact.dateUpdate || null,
                };
            }
    
            return transformedContact;
        });
    }

    async createContactCustomers(userId: string, body: CskhContactCustomersDto) {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN,
                );
            }
    
            const getBooking = await this.bookingCare247Model.findById(body.bookingCare247).exec();
            if (!getBooking) {
                throw new HttpException('Không tìm thấy thông tin booking.', HttpStatus.NOT_FOUND);
            }
    
            let newContactCustomer;
            if (body?.id) {
                const firstContact = await this.cskhContactCustomerModel
                    .findOne({ bookingCare247: body.bookingCare247 })
                    .sort({ createdAt: 1 })
                    .exec();
                
                const firstContactObj = firstContact?.toObject();
                const firstUserCreate = firstContactObj?.userCreate || userId;
    
                // Tạo dữ liệu mới để thêm vào bản ghi
                newContactCustomer = {
                    userCreate: firstUserCreate,
                    userCreateInfo: firstUserCreate,
                    dateCreate: firstContactObj?.createdAt || moment().toDate(), 
                    dateUpdate: moment().toDate(),
                    userUpdate: userId,
                    userUpdateInfo: userId,
                    bookingCare247: body.bookingCare247,
                    bookingCare247Info: body.bookingCare247,
                    bookingCount: body.bookingCount || 0,
                    onePerson: body.onePerson || false,
                    callResults: body.callResults || '',
                    description: body.description || '',
                };
            } else {
                // Trường hợp tạo mới bản ghi
                newContactCustomer = {
                    userCreate: userId,
                    userCreateInfo: userId,
                    dateCreate: moment().toDate(),
                    dateUpdate: moment().toDate(),
                    userUpdate: userId,
                    userUpdateInfo: userId,
                    bookingCare247: body.bookingCare247,
                    bookingCare247Info: body.bookingCare247,
                    bookingCount: body.bookingCount || 0,
                    onePerson: body.onePerson || false,
                    callResults: body.callResults || '',
                    description: body.description || '',
                };

                await this.bookingCare247Model.findOneAndUpdate(
                    { _id: getBooking._id, isCSKHConfirmCare247: { $ne: true } },
                    { isCSKHConfirmCare247: true },
                    { new: true },
                );
            }
    
            // Lưu vào cơ sở dữ liệu
            const createdContact = await this.cskhContactCustomerModel.create(newContactCustomer);
    
            // Trả về kết quả
            return createdContact;
        } catch (error) {
            // Xử lý lỗi và trả về thông báo phù hợp
            const message = error?.message || 'Lỗi khi tạo thông tin liên hệ CSKH. Vui lòng thử lại.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    
    async historyContactCustomers(userMongoId: string, id: string) {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            // Tìm thông tin liên hệ CSKH theo bookingId và userMongoId
            const contacts = await this.cskhContactCustomerModel
                .find({bookingCare247: id })
                .populate({ path: 'userCreateInfo', select: { fullname: true, username: true } })
                .populate({ path: 'userUpdateInfo', select: { fullname: true, username: true } })
                .populate({
                    path: 'bookingCare247Info',
                    select: {
                        id: true,
                        status: true,
                        bookingCode: true,
                        bookingCodeV1: true,
                        appId: true,
                        partnerId: true,
                        date: true,
                        createdAt: true,
                        transactionId: true,
                        isCSKHConfirmCare247: true
                    },
                })
                .sort({ updatedAt: -1 })
                .exec();

            if (!contacts) {
                return {};
            }
            return this.transformContacts(contacts);;
        } catch (error) {
            throw new Error('Lỗi khi lấy thông tin liên hệ.');
        }
    }

    async getContactCustomers(userMongoId: string, id: string) {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            // Tìm thông tin liên hệ CSKH theo bookingId và userMongoId
            const contacts = await this.cskhContactCustomerModel
                .find({ bookingCare247: id })
                .populate({ path: 'userCreateInfo', select: { fullname: true, username: true } })
                .populate({ path: 'userUpdateInfo', select: { fullname: true, username: true } })
                .populate({
                    path: 'bookingCare247Info',
                    select: {
                        id: true,
                        status: true,
                        bookingCode: true,
                        bookingCodeV1: true,
                        appId: true,
                        partnerId: true,
                        date: true,
                        createdAt: true,
                        transactionId: true,
                        isCSKHConfirmCare247: true
                    },
                })
                .sort({ updatedAt: -1 })
                .exec();
            if (!contacts) {
                return {};
            }

            const transformedContacts = this.transformContacts(contacts);
            
            return transformedContacts[0];
        } catch (error) {
            throw new Error('Lỗi khi lấy thông tin liên hệ.');
        }
    }

    validateTimeFormat(time: string): boolean {
        const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
        return timeRegex.test(time);
    }

    async createCskhScheduleCategory(userMongoId: string,
        categoryDto: CskhScheduleCategoriesDto,
    ): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            if (!this.validateTimeFormat(categoryDto?.fromTime)) {
                throw new HttpException('Thời gian bắt đầu không đúng định dạng HH:mm',HttpStatus.BAD_REQUEST,);
            }
            if (!this.validateTimeFormat(categoryDto?.toTime)) {
                throw new HttpException('Thời gian kết thúc không đúng định dạng HH:mm',HttpStatus.BAD_REQUEST,);
            }
            const from = categoryDto?.fromTime.split(':').map(Number);
            const to = categoryDto?.toTime.split(':').map(Number);
        
            // So sánh thời gian bắt đầu và kết thúc
            if (from[0] > to[0] || (from[0] === to[0] && from[1] >= to[1])) {
                throw new HttpException('Thời gian kết thúc phải lớn hơn thời gian bắt đầu',HttpStatus.BAD_REQUEST);
            }
            const newCategory = new this.cskhScheduleCategoriesModel({
                ...categoryDto
            });
    
            // Lưu vào cơ sở dữ liệu
            await newCategory.save();
            return {
                isOk: true,
                message: 'Tạo lịch trình CSKH thành công !'
            }
        } catch (error) {
            throw new HttpException(`${error.message}`, HttpStatus.BAD_REQUEST);
        }
    }

    async updateCskhScheduleCategory(
        userMongoId: string,
        id: string,
        updateData: CskhScheduleCategoriesDto,
    ): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            if (!this.validateTimeFormat(updateData?.fromTime)) {
                throw new HttpException('Thời gian bắt đầu không đúng định dạng HH:mm',HttpStatus.BAD_REQUEST,);
            }
            if (!this.validateTimeFormat(updateData?.toTime)) {
                throw new HttpException('Thời gian kết thúc không đúng định dạng HH:mm',HttpStatus.BAD_REQUEST,);
            }
            const from = updateData?.fromTime.split(':').map(Number);
            const to = updateData?.toTime.split(':').map(Number);
        
            // So sánh thời gian bắt đầu và kết thúc
            if (from[0] > to[0] || (from[0] === to[0] && from[1] >= to[1])) {
                throw new HttpException('Thời gian kết thúc phải lớn hơn thời gian bắt đầu',HttpStatus.BAD_REQUEST);
            }
            const newCategory = {
                ...updateData
            };
            const updatedCategory = await this.cskhScheduleCategoriesModel.findByIdAndUpdate(
                id,
                { $set: newCategory },
                { new: true },
            ).exec();

            if (!updatedCategory) {
                throw new Error(`Không tìm thấy lịch trình CSKH : ${id}`);
            }

            return updatedCategory;
        } catch (error) {
            throw new HttpException(`${error.message}`, HttpStatus.BAD_REQUEST);
        }
    }

    async getAllCategories(userMongoId: string): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            const categories = await this.cskhScheduleCategoriesModel.find().exec();
            return categories;
        } catch (error) {
            throw new HttpException(
                'Không thể lấy danh sách lịch CSKH.',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async getCategoryById(userMongoId: string, id: string): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            const category = await this.cskhScheduleCategoriesModel.findById(id).exec();
            if (!category) {
                throw new HttpException(
                    'Không tìm thấy lịch CSKH với ID này.',
                    HttpStatus.NOT_FOUND,
                );
            }
            return category;
        } catch (error) {
            throw new HttpException(
                'Không thể lấy lịch CSKH theo ID.',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async deleteCategoryById(userMongoId: string, id: string): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            // Kiểm tra xem lịch CSKH này có đang được sử dụng trong cskhSchedulersModel hay không
            const isBeingUsed = await this.cskhSchedulersModel.findOne({ scheduleId: id }).exec();
            if (isBeingUsed) {
                throw new HttpException(
                    'Lịch CSKH này đang được sử dụng trong hệ thống. Không thể xóa.',
                    HttpStatus.BAD_REQUEST
                );
            }
    
            // Tìm và xóa mục theo ID
            const deletedCategory = await this.cskhScheduleCategoriesModel.findByIdAndDelete(id).exec();
    
            if (!deletedCategory) {
                throw new HttpException(
                    'Không tìm thấy lịch CSKH với ID này để xóa.',
                    HttpStatus.NOT_FOUND,
                );
            }
    
            return { message: 'Xóa lịch CSKH thành công.' };
        } catch (error) {
            throw new HttpException(
                error.message || 'Không thể xóa lịch CSKH.',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
    

    async createCskhScheduler(userMongoId: string, data: CskhSchedulersDto): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const [isCS, care247UserRoleListArr] = await Promise.all([
                this.userService.isCs(userMongoId),
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
            ]);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const listUserRole = new Set(care247UserRoleListArr.split(','));
            if (!listUserRole.has(userMongoId)) {
                if(userMongoId !== data.cskhUserId){
                    throw new HttpException('Vui lòng chỉ tạo lịch cho chính bạn!', HttpStatus.FORBIDDEN);
                }
            }
            // Lấy thông tin lịch trình từ `cskhScheduleCategoriesModel`
            const schedule = await this.cskhScheduleCategoriesModel.findById(data.scheduleId).exec();
            if (!schedule) {
                throw new HttpException('Không tìm thấy thông tin lịch trình.', HttpStatus.NOT_FOUND);
            }
    
            // Xử lý định dạng ngày giờ cho `fromDate` và `toDate`
            const startDateTime = 
                data.fromDate && schedule?.fromTime
                    ? moment(data.fromDate)
                          .set({
                              hour: moment(schedule.fromTime, 'HH:mm').hour(),
                              minute: moment(schedule.fromTime, 'HH:mm').minute(),
                          })
                          .toISOString()
                    : null;
    
            const endDateTime = 
                data.toDate && schedule?.toTime
                    ? moment(data.toDate)
                          .set({
                              hour: moment(schedule.toTime, 'HH:mm').hour(),
                              minute: moment(schedule.toTime, 'HH:mm').minute(),
                          })
                          .toISOString()
                    : null;
    
            if (!startDateTime || !endDateTime) {
                throw new HttpException(
                    'Dữ liệu ngày giờ không hợp lệ. Vui lòng kiểm tra lại.',
                    HttpStatus.BAD_REQUEST,
                );
            }
    
            // Tạo dữ liệu mới với thời gian đã định dạng
            const newData = {
                ...data,
                user: data.cskhUserId,
                schedule: data.scheduleId,
                fromDate: startDateTime,
                toDate: endDateTime,
            };
    
            // Lưu vào cơ sở dữ liệu
            const newScheduler = new this.cskhSchedulersModel(newData);
            const savedScheduler = await newScheduler.save();
    
            return {
                isOk: true,
                message: 'Tạo lịch trình CSKH thành công!',
                data: savedScheduler,
            };
        } catch (error) {
            console.error('Error creating CSKH scheduler:', error.message);
            throw new HttpException(
                `${error.message}` || 'Không thể tạo lịch trình CSKH. Vui lòng thử lại.',
                HttpStatus.BAD_REQUEST,
            );
        }
    }
    

    async updateCskhScheduler(userMongoId: string, id: string, data: CskhSchedulersDto): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const [isCS, care247UserRoleListArr] = await Promise.all([
                this.userService.isCs(userMongoId),
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
            ]);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
            const listUserRole = new Set(care247UserRoleListArr.split(','));
            if (!listUserRole.has(userMongoId)) {
                if (userMongoId !== data.cskhUserId) {
                    throw new HttpException('Vui lòng chỉ cập nhật lịch cho chính bạn!', HttpStatus.FORBIDDEN);
                }
            }
    
            // Kiểm tra trạng thái "locked" của lịch trình
            const existingScheduler = await this.cskhSchedulersModel.findById(id).exec();
            if (!existingScheduler) {
                throw new HttpException('Không tìm thấy lịch trình CSKH.', HttpStatus.NOT_FOUND);
            }
            const existingSchedulerObj = existingScheduler.toObject();
            if (existingSchedulerObj?.locked && !listUserRole.has(userMongoId)) {
                throw new HttpException('Lịch trình đã bị khóa và không thể cập nhật.', HttpStatus.FORBIDDEN);
            }
    
            // Lấy thông tin lịch trình từ `cskhScheduleCategoriesModel`
            const schedule = await this.cskhScheduleCategoriesModel.findById(data.scheduleId).exec();
            if (!schedule) {
                throw new HttpException('Không tìm thấy thông tin lịch trình.', HttpStatus.NOT_FOUND);
            }
    
            // Xử lý định dạng ngày giờ cho `fromDate` và `toDate`
            const startDateTime =
                data.fromDate && schedule?.fromTime
                    ? moment(data.fromDate)
                          .set({
                              hour: moment(schedule.fromTime, 'HH:mm').hour(),
                              minute: moment(schedule.fromTime, 'HH:mm').minute(),
                          })
                          .toISOString()
                    : null;
    
            const endDateTime =
                data.toDate && schedule?.toTime
                    ? moment(data.toDate)
                          .set({
                              hour: moment(schedule.toTime, 'HH:mm').hour(),
                              minute: moment(schedule.toTime, 'HH:mm').minute(),
                          })
                          .toISOString()
                    : null;
    
            if (!startDateTime || !endDateTime) {
                throw new HttpException(
                    'Dữ liệu ngày giờ không hợp lệ. Vui lòng kiểm tra lại.',
                    HttpStatus.BAD_REQUEST
                );
            }
    
            // Chuẩn bị dữ liệu cập nhật
            const updateData = {
                ...data,
                user: data.cskhUserId,
                schedule: data.scheduleId,
                fromDate: startDateTime,
                toDate: endDateTime,
            };
    
            // Tìm và cập nhật lịch trình theo ID
            const updatedScheduler = await this.cskhSchedulersModel
                .findByIdAndUpdate(id, { $set: updateData }, { new: true })
                .exec();
    
            if (!updatedScheduler) {
                throw new HttpException(`Không tìm thấy lịch trình CSKH với ID: ${id}`, HttpStatus.NOT_FOUND);
            }
    
            // Trả về kết quả thành công
            return {
                isOk: true,
                message: 'Cập nhật lịch trình CSKH thành công!',
                data: updatedScheduler,
            };
        } catch (error) {
            console.error('Error updating CSKH scheduler:', error.message);
            throw new HttpException(
                `${error.message}` || `Không thể cập nhật lịch trình CSKH`,
                HttpStatus.BAD_REQUEST
            );
        }
    }
    
    
    async updateLockedScheduler(userMongoId: string, id: string, data: { locked: boolean }): Promise<any> {
        // Kiểm tra quyền của người dùng
        try {
            const [isCS, care247UserRoleListArr] = await Promise.all([
                this.userService.isCs(userMongoId),
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
            ]);

            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const listUserRole = new Set(care247UserRoleListArr.split(','));
            if (!listUserRole.has(userMongoId)) {
                throw new HttpException('Bạn không có quyền khóa lịch!', HttpStatus.FORBIDDEN);
            }

            const updatedScheduler = await this.cskhSchedulersModel.findByIdAndUpdate(id, { $set: { locked: data?.locked } }, { new: true }).exec();;
            if (!updatedScheduler) {
                throw new HttpException('Không tìm thấy lịch cần cập nhật.', HttpStatus.NOT_FOUND);
            }

            return {
                message: 'Cập nhật trạng thái khóa lịch thành công.',
                isOk: true,
            };
        } catch (error) {
            console.error('Error updating CSKH scheduler:', error.message);
            throw new HttpException(`${error.message}`, HttpStatus.BAD_REQUEST);
        }
    }

    async deleteCskhScheduler(userMongoId: string, id: string): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            // Tìm và xóa lịch trình CSKH theo ID
            const deletedScheduler = await this.cskhSchedulersModel.findByIdAndDelete(id).exec();

            // Xử lý trường hợp không tìm thấy bản ghi cần xóa
            if (!deletedScheduler) {
                throw new HttpException(`Không tìm thấy lịch trình CSKH với ID: ${id}`, HttpStatus.NOT_FOUND);
            }

            // Trả về kết quả xóa thành công
            return {
                isOk: true,
                message: 'Xóa lịch trình CSKH thành công!',
            };
        } catch (error) {
            console.error('Error deleting CSKH scheduler:', error.message);

            // Xử lý và trả về lỗi
            throw new HttpException('Không thể xóa lịch trình CSKH. Vui lòng thử lại.', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getCskhSchedulers(userMongoId: string, query: any): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            // Tạo bộ lọc từ query (nếu có)
            const filters: any = {};
            if (query.partnerId) {
                filters.partnerId = query.partnerId;
            }
            if (query.scheduleId) {
                filters.scheduleId = query.scheduleId;
            }
            if(query.cskhUserId){
                filters.cskhUserId = query.cskhUserId;
            }
            // Lấy danh sách từ cơ sở dữ liệu
            const schedulers = await this.cskhSchedulersModel
                .find(filters)
                .populate({ path: 'partner', select: 'name' })
                .populate({ path: 'user', select: { fullname: true, username: true } })
                .populate({ path: 'schedule' })
                .exec();
            const formattedData = schedulers.map((item: any) => {
                return {
                    Id: item._id,
                    Subject: item?.schedule?.title,
                    StartTime: item.fromDate,
                    EndTime: item.toDate,
                    IsAllDay: false, // Nếu cần flag cả ngày
                    CskhUserId: item?.cskhUserId,
                    PartnerId: item?.partnerId,
                    PartnerName: item?.partner?.name,
                    partner: item?.partner,
                    ScheduleId: item?.schedule?.id,
                    user: item?.user,
                    schedule: item?.schedule,
                    locked: item?.locked,
                };
            });

            return formattedData;
        } catch (error) {
            throw new HttpException('Không thể lấy danh sách lịch trình CSKH. Vui lòng thử lại.', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    async medproCareCskhConfirm(data: any, userId: string): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const care247UserRoleList = await this.globalSettingService.findByKeyAndRepoName('CSKH_ASSIGNMENTS_CONTACT_CARE247_PERMISSION_ROLE');
            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }

            const { id, cskhConfirmedCare247 } = data;

            const userCs = await this.userModel.findOne(
                { _id: cskhConfirmedCare247 },
                { id: true, username: true, fullname: true, email: true, medproId: true },
            );
            if (!userCs) {
                throw new HttpException('Không tìm thấy thông tin người xác nhận', HttpStatus.BAD_REQUEST);
            }

            const care247Confirm = await this.bookingCare247Model
                .findOneAndUpdate(
                    { _id: id },
                    { cskhConfirmedCare247: cskhConfirmedCare247 },
                    { new: true },
                )
                .exec();
            return care247Confirm;
        } catch (e) {
            console.error('error: ', e);
            throw new HttpException(e.response, HttpStatus.BAD_REQUEST);
        }
    }

    async getPartnerCare247(userId: string) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const medproCarePartnerList = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST');

            let listPartnerIds = medproCarePartnerList.split(',');
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm'];

            const hospital = await this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec();

            const partners = hospital.map((h: any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) };
            });

            return [
                {
                    _id: '5ee7089f54419e0019192011',
                    partnerId: 'medpro',
                    name: 'MEDPRO - ĐẶT LỊCH KHÁM BỆNH',
                },
                ...partners
            ];
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách partner triển khai care247';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async createCallFeedbackSurvey(userMongoId: string, body: CskhSurveyCare247Dto): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            // Kiểm tra dữ liệu đầu vào
            const {
                surveyDate,
                callOutcome,
                isServiceUsed,
                supportDuration,
                serviceEase,
                scheduleConfirmation,
                notes,
                bookingCare247,
                staffAttitudeRating,
            } = body;
    
            const getBooking = await this.bookingCare247Model.findById(bookingCare247).exec();
            if (!getBooking) {
                throw new HttpException('Không tìm thấy thông tin booking.', HttpStatus.NOT_FOUND);
            }
            // Chuẩn bị dữ liệu để lưu vào cơ sở dữ liệu
            const surveyData = {
                userId: userMongoId,
                user: userMongoId,
                surveyDate: new Date(surveyDate),
                callOutcome,
                isServiceUsed: isServiceUsed ?? null,
                supportDuration: supportDuration ?? null,
                serviceEase: serviceEase ?? null,
                scheduleConfirmation: scheduleConfirmation ?? null,
                bookingCare247,
                bookingCare247Info: bookingCare247,
                staffAttitudeRating: staffAttitudeRating ?? null,
                notes: notes ?? '',
            };
    
            // Tạo khảo sát mới
            const savedSurvey = await this.cskhSurveyCare247Model.create(surveyData);
    
            // Trả về kết quả
            return {
                message: 'Khảo sát đã được lưu thành công.',
                surveyId: savedSurvey._id,
            };
        } catch (error) {
            console.error('Error creating survey:', error.message);
    
            // Xử lý lỗi và trả về thông báo thích hợp
            throw new HttpException(
                'Không thể lưu khảo sát. Vui lòng thử lại sau.',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
    
    async updateCallFeedbackSurvey(
        userId: string,
        surveyId: string,
        body: Partial<CskhSurveyCare247Dto>
    ): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            // Tìm khảo sát theo ID
            const existingSurvey = await this.cskhSurveyCare247Model.findById(surveyId).exec();
            if (!existingSurvey) {
                throw new HttpException(
                    'Không tìm thấy khảo sát với ID đã cung cấp.',
                    HttpStatus.NOT_FOUND
                );
            }
    
            // Cập nhật các trường được cung cấp trong body
            const updateData = {
                ...(body.surveyDate && { surveyDate: new Date(body.surveyDate) }),
                ...(body.callOutcome && { callOutcome: body.callOutcome }),
                ...(body.isServiceUsed !== undefined && { isServiceUsed: body.isServiceUsed }),
                ...(body.supportDuration && { supportDuration: body.supportDuration }),
                ...(body.serviceEase && { serviceEase: body.serviceEase }),
                ...(body.scheduleConfirmation !== undefined && { scheduleConfirmation: body.scheduleConfirmation }),
                ...(body.staffAttitudeRating && { staffAttitudeRating: body.staffAttitudeRating }),
                ...(body.notes && { notes: body.notes }),
                userId,
                user:userId,
                updatedAt: new Date(), // Cập nhật thời gian sửa đổi
            };
    
            // Thực hiện cập nhật
            const updatedSurvey = await this.cskhSurveyCare247Model
                .findByIdAndUpdate(surveyId, updateData, { new: true })
                .exec();
    
            // Trả về kết quả
            return {
                message: 'Cập nhật khảo sát thành công.',
                updatedSurvey,
            };
        } catch (error) {
            console.error('Error updating survey:', error.message);
    
            // Xử lý lỗi và trả về thông báo thích hợp
            throw new HttpException(
                'Không thể cập nhật khảo sát. Vui lòng thử lại sau.',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
    
    async getCallFeedbackSurveys(userId: string, bookingCare247: string): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN,
                );
            }
    
            // Kiểm tra đầu vào `bookingCare247`
            if (!bookingCare247) {
                throw new HttpException(
                    'Vui lòng cung cấp bookingCare247 để lấy danh sách khảo sát.',
                    HttpStatus.BAD_REQUEST,
                );
            }
    
            // Xây dựng bộ lọc theo `bookingCare247`
            const filter = { bookingCare247 };
    
            // Truy vấn danh sách khảo sát và sắp xếp theo ngày giảm dần
            const surveys = await this.cskhSurveyCare247Model
                .find(filter)
                .sort({ surveyDate: -1 })
                .populate({ path: 'user', select: { fullname: true, username: true } })
                .populate({
                    path: 'bookingCare247Info',
                    select: {
                        id: true,  
                        status: true,
                        bookingCode: true,
                        bookingCodeV1: true,
                        appId: true,
                        partnerId: true,
                        date: true,
                        createdAt: true,
                        transactionId: true
                    },
                })
                .exec();
    
            // Kiểm tra nếu không có khảo sát nào
            if (!surveys || surveys.length === 0) {
                return []; // Trả về mảng rỗng nếu không có khảo sát nào
            }
    
            // Trả về kết quả
            return surveys;
        } catch (error) {
            console.error('Error fetching surveys:', error.message);
    
            // Xử lý lỗi và trả về thông báo thích hợp
            throw new HttpException(
                'Không thể lấy danh sách khảo sát. Vui lòng thử lại sau.',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }     
    
    async deleteCallFeedbackSurvey(userId: string, surveyId: string): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN
                );
            }
    
            // Tìm và kiểm tra khảo sát tồn tại
            const existingSurvey = await this.cskhSurveyCare247Model.findById(surveyId).exec();
            if (!existingSurvey) {
                throw new HttpException(
                    'Không tìm thấy khảo sát với ID đã cung cấp.',
                    HttpStatus.NOT_FOUND
                );
            }
    
            // Thực hiện xóa khảo sát
            await this.cskhSurveyCare247Model.findByIdAndDelete(surveyId).exec();
    
            // Trả về kết quả
            return {
                message: 'Khảo sát đã được xóa thành công.',
            };
        } catch (error) {
            console.error('Error deleting survey:', error.message);
    
            // Xử lý lỗi và trả về thông báo thích hợp
            throw new HttpException(
                'Không thể xóa khảo sát. Vui lòng thử lại sau.',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
    
    async getBookingMedproCareSales(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { fromDate, toDate, pageSize, pageIndex } = body;

            const [rows, totalRows] = await Promise.all([
                this.care247SalesModel
                    .find()
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.care247SalesModel.countDocuments(),
            ]);
            const data = rows.map((r: any) => {
                const item = r.toObject();
                return { 
                    ...item, 
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) },
                };
            });

            return {
                rows: data,
                totalRows,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách phiếu Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getCskhReport(userMongoId: string, query: any): Promise<any> {
        try {
            // Kiểm tra quyền của người dùng
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
    
            // Lấy danh sách nhân viên CSKH từ hàm getCskhsRevenue
            const cskhEmployeeList = await this.getCskhsRevenue(userMongoId);
    
            const employeeMap = cskhEmployeeList.reduce((map, employee) => {
                map[employee._id] = { fullname: employee.fullname, username: employee.username, scheduleCounts: {}, total: 0 };
                return map;
            }, {});
    
            // Lấy danh sách loại schedule từ cskhScheduleCategoriesModel
            const scheduleCategories = await this.cskhScheduleCategoriesModel.find({}).exec();
            const scheduleCategoryMap = scheduleCategories.reduce((map, category) => {
                map[category.id] = { 
                    name: category.name, 
                    count: 0, 
                    color: category.color, 
                    workDay: category.workDay
                };
                return map;
            }, {});
    
            // Khởi tạo cấu trúc đếm lịch trình theo loại cho mỗi nhân viên
            for (const employee of Object.values(employeeMap)) {
                Object.keys(scheduleCategoryMap).forEach((id: string) => {
                    (employee as any).scheduleCounts[id] = { 
                        name: scheduleCategoryMap[id].name, 
                        color: scheduleCategoryMap[id].color, 
                        count: 0 
                    };
                });
            }
    
            // Xây dựng bộ lọc
            const filters: any = {};
    
            // Lọc theo tháng/năm với định dạng MM/YYYY
            if (query.monthYear) {
                const date = moment(query.monthYear, 'MM/YYYY', true); // Kiểm tra định dạng chính xác
                if (!date.isValid()) {
                    throw new HttpException('Định dạng tháng/năm không hợp lệ. Vui lòng sử dụng định dạng MM/YYYY.', HttpStatus.BAD_REQUEST);
                }
    
                const startOfMonth = date.startOf('month').toDate(); // Ngày đầu tháng
                const endOfMonth = date.endOf('month').toDate(); // Ngày cuối tháng
                filters.fromDate = { $gte: startOfMonth, $lte: endOfMonth };
            }
    
            if (query.scheduleId) {
                filters.scheduleId = query.scheduleId;
            }
    
            // Lấy danh sách lịch trình theo bộ lọc
            const schedulers = await this.cskhSchedulersModel.find(filters).exec();
    
            // Đếm số lượng lịch trình theo nhân viên và loại
            schedulers.forEach((schedule: any) => {
                const cskhUserId = schedule.cskhUserId;
                const scheduleId = schedule.scheduleId;
    
                if (scheduleId && employeeMap[cskhUserId]) {
                    const employeeScheduleCounts = employeeMap[cskhUserId].scheduleCounts;
    
                    if (employeeScheduleCounts[scheduleId]) {
                        employeeScheduleCounts[scheduleId].count += 1;
    
                        // Chỉ cộng vào tổng nếu workDay là true
                        if (scheduleCategoryMap[scheduleId]?.workDay) {
                            employeeMap[cskhUserId].total += 1;
                        }
                    }
                }
            });
    
            // Chuyển đổi dữ liệu thành mảng
            const reportArray = Object.keys(employeeMap).map((key) => {
                return {
                    cskhUserId: key,
                    fullname: employeeMap[key].fullname,
                    username: employeeMap[key].username,
                    scheduleCounts: employeeMap[key].scheduleCounts,
                    total: employeeMap[key].total, // Thêm trường total
                };
            });
    
            return reportArray;
        } catch (error) {
            throw new HttpException('Không thể lấy báo cáo CSKH. Vui lòng thử lại.', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getRetryPaymentBooking(userId: string, body: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { partnerId, fromDate, toDate, pageSize, pageIndex } = body;
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const query: any = {
                partnerId,
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                date: { $gte: timeStart },
            };

            let condition = pickBy(query, identity);

            const [rows, totalRows] = await Promise.all([
                this.retryPaymentBookingModel.find(condition)
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .populate({ path: 'userAction.user', select: { fullname: true, username: true } })
                    .sort({ createdAt: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.retryPaymentBookingModel.countDocuments(condition),
            ]);

            const data = rows.map((r: any) => {
                const item = r.toObject();
                const description = this.utilService.getBookingText(item.booking);
                return { 
                    ...item, 
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) },
                    description,
                    status: item.booking.status
                };
            });

            return {
                rows: data,
                totalRows
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách phiếu khám thanh toán lỗi.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async updateStatusBookingChoray(userId: string, body: any) {
        try {
            const { transactionId } = body;
            const bookingNew = await this.bookingModel.findOneAndUpdate(
                { transactionId, partnerId: 'choray', status: -2, cancelledBy: 'SYSTEM' },
                { paymentStatus: 2, status: 1 },
                { new: true },
            ).exec();

            const bookingObj = bookingNew.toObject();

            this.eventEmmiter.emit(EVENT_RETRY_SYNC_BOOKING, { transactionId: bookingObj.transactionId, userId: bookingObj.userId, userActionId: userId });

            return {
                isOk: true
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi cập nhật trạng thái phiếu khám Chợ Rẫy.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async callBotCskh(appid: string, secret: string, data: any) {
        try {
            const secretKey = await this.globalSettingService.findByKeyAndRepoName('CALL_BOT_SECRET_KEY');
            if (appid === 'callbot' && secret === secretKey) {
                if (data?.collect_info_variables?.length) {
                    const userResponses = data?.collect_info_variables?.map(v => {
                        return {
                            entireUserSentence: v.entire_user_sentence,
                            variableValue: v.variable_value,
                            variableName: v.variable_name,
                        }
                    })
                    const timeStart = data?.time_start_call ? moment.unix(data?.time_start_call).toISOString() : null
                    const timeEnd = data?.time_end_call ? moment.unix(data?.time_end_call).toISOString() : null
                    const callBotCskh = new this.callBotCskhModel({
                        phone: data.phone_number,
                        fullData: data,
                        userResponses,
                        timeStart,
                        timeEnd,
                        duration: data.duration,
                        transactionId: data.transaction_id
                    });
                    
                    const dataCreated = await callBotCskh.save();
                    return {
                        isOk: true,
                        data: dataCreated
                    };
                }
                return {
                    isOk: false
                };
            } else {
                throw new HttpException('Thông tin secret key hoặc appid không đúng. Vui lòng thử lại.', HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (error) {
            const message = error?.message || 'callBotCskh gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    getLabelFromCallBot(variableName: string) {
        if (variableName.includes('patientName')) {
            return 'Tên bệnh nhân'
        }
        if (variableName.includes('birthdate') || variableName.includes('Birthdate')) {
            return 'Ngày sinh'
        }
        if (variableName.includes('indentity') || variableName.includes('Indentity')) {
            return 'CCCD'
        }
        if (variableName.includes('hospitalName')) {
            return 'Tên bệnh viện'
        }
        if (variableName.includes('subject') || variableName.includes('Subject')) {
            return 'Chuyên khoa'
        }
        if (variableName.includes('bookingDate')) {
            return 'Ngày khám'
        }
        return ''
    }

    getUniqueByVariableValueAndLabel(arr) {
        const data = arr.map((item) => {
            return {
                ...item,
                label: this.getLabelFromCallBot(item.variableName)
            }
        })

        const uniqueVariableValues = new Set();
        const uniqueLabels = new Set();
        const result = [];
      
        for (const item of data) {
          if (item.label === "") {
            if (!uniqueVariableValues.has(item.variableValue)) {
              result.push(item);
              uniqueVariableValues.add(item.variableValue);
            }
          } else {
            if (!uniqueLabels.has(item.label)) {
              if (!uniqueVariableValues.has(item.variableValue)) {
                  result.push(item);
                  uniqueVariableValues.add(item.variableValue);
                  uniqueLabels.add(item.label);
              }
            }
          }
        }
        return result;
    }

    async getCallBotCskhByPhone(userMongoId: string, body: any): Promise<any> {
        try {
            return { isOk: true }
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { phone } = body;

            const callBotCskh = await this.callBotCskhModel.findOne({ phone }).exec();

            console.log('callBotCskh', callBotCskh);

            if (!callBotCskh) {
                throw new HttpException('Không tìm thấy thông tin callbot.', HttpStatus.NOT_FOUND);
            }
            const callBotObj = callBotCskh.toObject();

            const { fullData, userResponses, transactionId, ...remain } = callBotObj;

            const data = this.getUniqueByVariableValueAndLabel(userResponses)

            return {
                ...remain,
                data
            };
        } catch (error) {
            throw new HttpException('Không thể lấy thông tin callbot. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async seedDataBookingIgnoreConvert() {
        try {
            const condition = {
                checkConvertUser: false,
            };
            const data = await this.bookingModel.find(condition).exec();

            for await (const booking of data) {
                const bookingObj = booking.toObject();
                try {
                    const convertUser = await this.convertUserCskhModel.findOne({ userId: bookingObj?.userId }).exec();

                    const bookingIgnoreConvert = new this.bookingIgnoreConvertModel({
                        convertUserCskh: convertUser._id,
                        user: bookingObj.userId,
                        userId: bookingObj.userId,
                        booking: bookingObj._id,
                        bookingId: bookingObj._id,
                        bookingCode: bookingObj.bookingCode,
                        transactionId: bookingObj.transactionId,
                        date: bookingObj.date,
                    });
                    await bookingIgnoreConvert.save();
                } catch (error) {
                    this.logger.error(`${error.message}`, error.stack);
                }
            }

            return { isOK: true }
        } catch (error) {
            return { isOK: false }
        }
    }

    async getBookingIgnoreConvert(userMongoId: string, body: any): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { transactionId, partnerId, status, fromDate, toDate, pageSize, pageIndex } = body;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            let query: any = {
                partnerId,
                status,
                date: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            };

            if (transactionId) {
                if (transactionId.startsWith('A') || transactionId.startsWith('W') || transactionId.length === 13) {
                    query = { bookingCode: transactionId }
                } else {
                    query = { transactionId }
                }
            }

            const condition = pickBy(query, identity);

            const bookings = await this.bookingIgnoreConvertModel.find(condition)
                    .populate('user')
                    .populate('booking')
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec();

            return bookings;
        } catch (error) {
            console.log('error', error);
            throw new HttpException('Không thể lấy thông tin booking ignore convert. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getUserConvertCskh(userMongoId: string, body: any): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { isConverted, fromDate, toDate, pageSize, pageIndex } = body;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const query: any = {
                isConverted,
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            };

            const condition = pickBy(query, identity);

            const users = await this.convertUserCskhModel
                        .find(condition)
                        .populate('user')
                        .sort({ createdAt: 1 })
                        .skip(pageSize * pageIndex)
                        .limit(Number(pageSize))
                        .exec();

            return users;
        } catch (error) {
            console.log('error', error);
            throw new HttpException('Không thể lấy thông tin convert users. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getReserverBookingLogs(userMongoId: string, body: any): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userMongoId);
            if (!isCS) {
                throw new HttpException(
                    'Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.',
                    HttpStatus.FORBIDDEN,
                );
            }
    
            const { fromDate, toDate, pageSize, pageIndex, partnerId, phone, type, repo, status } = body;
            const { timeStart, timeEnd } = this.utilService.handleDateTimeForFilter(fromDate, toDate);
            
            let medproId: string;
            if(phone){
                medproId = phone?.replace(/^(\+84|0)/, '+84');
            }

            const query: any = {
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                medproId,
                partnerId: partnerId,
                type,
                nameRepo: repo === 'LIVE' ? { $in: ['api-v2', 'api-v2-120', 'api-v2-122']} 
                        : repo === 'BETA' ? { $in: ['api-v2-beta', 'api-v2-111']}
                        : null,
                status: status === 0 ? { $in: [0] } : status
            };
            const condition = pickBy(query, identity);
            const [logs, totalRows] =await Promise.all([this.reserveBookigLogModel
                .find(condition)
                .populate('user', { fullname: true, username: true , email: true })
                .populate('patient', { surname: true, name: true, birthdate: true, sex: true, cmnd: true, email: true, insuranceId: true })
                .sort({ createdAt: 1 })
                .skip(pageSize * pageIndex)
                .limit(Number(pageSize))
                .exec(),
                this.reserveBookigLogModel.countDocuments(condition).exec()])
    
            const enrichedLogs = await Promise.all(
                logs.map(async (dataItem) => {
                    const { serviceId, subjectId, roomId, doctorId, sectionId, partnerId } = dataItem;
    
                    const [
                        findService,
                        findSubject,
                        findRoom,
                        findDoctor,
                        findSectionDirect,
                        findPartner
                    ] = await Promise.all([
                        serviceId ? this.serviceModel.findOne({ id: serviceId }).exec() : null,
                        subjectId ? this.subjectModel.findOne({ id: subjectId }).exec() : null,
                        roomId ? this.roomModel.findOne({ id: roomId }).exec() : null,
                        doctorId ? this.doctorModel.findOne({ id: doctorId }).exec() : null,
                        sectionId ? this.sectionModel.findOne({ id: sectionId }).exec() : null,
                        partnerId ? this.hospitalModel.findOne({ partnerId: partnerId }).select('name address partnerId ').exec() : null,
                    ]);
    
                    // Clone để không ảnh hưởng tới doc gốc (nếu cần)
                    const logWithExtras = dataItem.toObject?.() ?? { ...dataItem };
    
                    // Service
                    if (findService) {
                        const serviceObj = findService.toObject();
                        logWithExtras.service = serviceObj;
                        logWithExtras.serviceId = serviceObj.id;
                        logWithExtras.serviceType = serviceObj.type;
                        logWithExtras.serviceName = serviceObj.name || '';
                    }
    
                    // Subject
                    if (findSubject) {
                        logWithExtras.subject = findSubject.toObject();
                    }
    
                    // Room
                    if (findRoom) {
                        const roomObj = findRoom.toObject();
                        logWithExtras.room = roomObj;
    
                        const getSectionId = roomObj?.sectionId;
                        if (getSectionId) {
                            const findSectionFromRoom = await this.sectionModel.findOne({ id: getSectionId }).exec();
                            if (findSectionFromRoom) {
                                logWithExtras.sectionId = getSectionId;
                                logWithExtras.section = findSectionFromRoom.toObject();
                            }
                        }
                    }
    
                    // Doctor
                    if (findDoctor) {
                        logWithExtras.doctor = findDoctor.toObject();
                    }
    
                    // Section trực tiếp (nếu chưa lấy từ room)
                    if (findSectionDirect && !logWithExtras.section) {
                        logWithExtras.sectionId = sectionId;
                        logWithExtras.section = findSectionDirect.toObject();
                    }

                    if(findPartner){
                        logWithExtras.partner = findPartner.toObject();
                    }
    
                    return logWithExtras;
                })
            );
    
            return {rows:enrichedLogs, totalRows};
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể lấy thông tin logs. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async clearReserverBookingLogs(userMongoId: string, body: any): Promise<any> {
        try {
            const { fromDate, toDate, phone, type, partnerId } = body;
            const { timeStart, timeEnd } = this.utilService.handleDateTimeForFilter(fromDate, toDate);
    
            let medproId: string;
            if (phone){
                medproId = phone?.replace(/^(\+84|0)/, '+84');
            }
            
            const query: any = {
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                medproId,
                partnerId,
                type
            };
            const condition = pickBy(query, identity);
            const result = await this.reserveBookigLogModel.deleteMany(condition).exec();
            
            return { isOk: true, deletedCount: result.deletedCount }
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể xóa thông tin logs. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async searchRefundTransaction(userMongoId: string, body: any): Promise<any> {
        try {
            const { transactionId } = body;

            const payment = await this.paymentModel.findOne({ transactionId }).exec();
            
            if (!payment) {
                throw new HttpException('Không tìm thấy thông tin giao dịch.', HttpStatus.BAD_REQUEST);
            }

            const booking = await this.bookingModel.findOne({ bookingId: payment.bookingId })
                            .populate({ path: 'user', select: { fullname: true, username: true } })
                            .populate({ path: 'partner', select: 'name' })
                            .exec()
            
            const paymentObj = payment.toObject();
            const bookingObj = booking?.toObject() || {};

            return {
                payment: paymentObj,
                booking: { ...bookingObj, partner: { ...bookingObj.partner, name: this.getPartnerName(bookingObj.partnerId, bookingObj.partner.name) }}
            }
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể tra cứu thông tin hoàn tiền giao dịch. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getRefundTransactionSms(userMongoId: string, body: any): Promise<any> {
        try {
            const { fromDate, toDate, pageSize, pageIndex, partnerId, type, smsStatus } = body;
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            let query: any = {
                partnerId,
                smsStatus: smsStatus === 0 ? { $in: [0] } : smsStatus,
                type,
                createdAt: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            };
            const condition = pickBy(query, identity);
            const [rows, totalRows] =await Promise.all([
                this.refundTransactionSmsModel
                    .find(condition)
                    .populate('user', { fullname: true, username: true , email: true })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'partner', select: 'name' })
                    .populate('payment')
                    .populate('booking')
                    .sort({ createdAt: -1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.refundTransactionSmsModel.countDocuments(condition).exec()
            ])
            
            return { rows, totalRows }
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể tạo thông tin hoàn tiền phiếu khám. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async insertRefundTransactionSms(userMongoId: string, body: any): Promise<any> {
        try {
            const { transactionId } = body;

            let [payment, booking] =await Promise.all([
                this.paymentModel.findOne({ transactionId }).exec(),
                this.bookingModel.findOne({ transactionId }).populate({ path: 'user', select: { fullname: true, username: true } }).exec()
            ])
            
            if (!payment) {
                throw new HttpException('Không tìm thấy thông tin giao dịch.', HttpStatus.BAD_REQUEST);
            }
            if (!booking) {
                throw new HttpException('Không tìm thấy thông tin phiếu khám.', HttpStatus.BAD_REQUEST);
            }
            
            const paymentObj = payment.toObject();
            let bookingObj = booking.toObject();

            if (!bookingObj?.user?.username) {
                await this.bookingModel.findByIdAndUpdate({ _id: bookingObj._id }, { 
                    user: bookingObj.userId
                }, { new: true }).exec();
                booking = await this.bookingModel.findOne({ transactionId }).populate({ path: 'user', select: { fullname: true, username: true } }).exec();
                bookingObj = booking.toObject();
            }

            if (paymentObj.status === 2) {
                const newRefundTransactionSms = new this.refundTransactionSmsModel({
                    user: bookingObj.userId,
                    userId: bookingObj.userId,
                    medproId: bookingObj.user.username,
                    fullname: bookingObj.user.fullname,
                    transactionId: paymentObj.transactionId,
                    payment: paymentObj._id,
                    booking: bookingObj._id,
                    patient: bookingObj.patient,
                    type: paymentObj.type,
                    bookingCode: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                    partnerId: paymentObj.partnerId,
                    partner: bookingObj.partner,
                    subTotal: paymentObj.subTotal,
                });
                const data = await newRefundTransactionSms.save();
                
                return { isOk: true, data }
            } else if (paymentObj.status === 1) {
                const transactionInfo = await this.transaction(transactionId, 0);
                if (transactionInfo.status === 2) {
                    const newRefundTransactionSms = new this.refundTransactionSmsModel({
                        user: bookingObj.userId,
                        userId: bookingObj.userId,
                        medproId: bookingObj.user.username,
                        fullname: bookingObj.user.fullname,
                        transactionId: paymentObj.transactionId,
                        payment: paymentObj._id,
                        booking: bookingObj._id,
                        patient: bookingObj.patient,
                        type: paymentObj.type,
                        bookingCode: bookingObj.bookingCodeV1 || bookingObj.bookingCode,
                        partnerId: paymentObj.partnerId,
                        partner: bookingObj.partner,
                        subTotal: paymentObj.subTotal,
                    });
                    const data = await newRefundTransactionSms.save();
                    
                    return { isOk: true, data }
                } else {
                    throw new HttpException('Vui lòng kiểm tra lại giao dịch hoặc phiếu khám.', HttpStatus.BAD_REQUEST);
                }
            } else {
                throw new HttpException('Vui lòng kiểm tra lại giao dịch hoặc phiếu khám.', HttpStatus.BAD_REQUEST);
            }
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể tạo thông tin hoàn tiền giao dịch. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async sendRefundTransactionSms(userMongoId: string, body: any): Promise<any> {
        try {
            const { ids } = body;
            const refundTransactionSms = await this.refundTransactionSmsModel.find({ _id: { $in: ids } }).exec();

            if (!refundTransactionSms.length) {
                throw new HttpException('Không tìm thấy thông tin giao dịch hoàn tiền sms.', HttpStatus.BAD_REQUEST);
            }

            for await (const refund of refundTransactionSms) {
                const refundObj = refund.toObject();
    
                const formData = {
                    mobile: refundObj.medproId, 
                    bookingCode: refundObj. bookingCode
                }
                try {
                    await this.userService.sendBookingSMSHoanTien(formData);
        
                    const message = await this.smsService.getMessageInfoSmsHoanTien(formData.bookingCode);
        
                    await this.refundTransactionSmsModel.findByIdAndUpdate({ _id: refundObj._id }, { 
                        smsStatus: 1, smsTimeSent: moment().utc().toDate(), userAction: userMongoId, smsContent: message
                    }, { new: true }).exec();
                    
                } catch (error) {
                    await this.refundTransactionSmsModel.findByIdAndUpdate({ _id: refundObj._id }, { 
                        smsStatus: -2, smsTimeSent: moment().utc().toDate(), userAction: userMongoId
                    }, { new: true }).exec();
                }
            }
            
            return { isOk: true }

        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể gửi sms hoàn tiền phiếu khám. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async updateRefundTransactionSms(userMongoId: string, id: string, body: any): Promise<any> {
        try {
            const { note, ...remain } = body;

            await this.refundTransactionSmsModel.findByIdAndUpdate(id, { 
                refundBankUser: remain, note
            }, { new: true }).exec();
            
            return { isOk: true }
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể cập nhật thông tin hoàn tiền phiếu khám. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }
    
    async deleteRefundTransactionSms(userMongoId: string, id: string): Promise<any> {
        try {
            await this.refundTransactionSmsModel.findByIdAndDelete(id).exec();
            return { isOk: true };
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Có lỗi khi xóa giao dịch hoàn tiền sms.', HttpStatus.BAD_REQUEST);
        }
    }

    async exportExcelHospital(userId: string, formData: any): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        if (!isCS) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }
        const { isContractSigned } = formData;

        const query: any = {
            isContractSigned: isContractSigned ? true : { $ne: true },
            newHospitalTypes: { $in: [2, 3, 4] }
        };

        const dataHospital: any = await this.hospitalModel.find(query).exec();
        
        //Create data excel
        const workbook = new Excel.Workbook();
        const title = isContractSigned ? 'CSYT tư nhân đã ký' : 'CSYT tư nhân chưa ký'
        const worksheet = workbook.addWorksheet(title);
        worksheet.columns = [
            { header: 'STT', key: 'stt', width: 25 },
            { header: 'Tên bệnh viện', key: 'name', width: 40 },
            { header: 'Địa chỉ', key: 'address', width: 40 },
            { header: 'Loại hình bệnh viện', key: 'newHospitalType', width: 35 },
            { header: 'Mua gói Listing', key: 'listingPackagePaid', width: 35 },
            { header: 'Ký hợp đồng', key: 'isContractSigned', width: 35 },
        ];

        const dataMap = dataHospital.map((item: any, index: number) => {
            const newHospitalType = item.newHospitalTypes.map(t => this.getPartnerTypeName(t)).join(',')
            return {
                stt: index + 1,
                name: item.name,
                address: item.address,
                newHospitalType,
                listingPackagePaid: item?.listingPackagePaid ? 'Đã mua' : 'Chưa mua',
                isContractSigned: item?.isContractSigned ? 'Đã ký' : 'Chưa ký',
            }
        });

        dataMap.forEach(row => {
            worksheet.addRow(row);
        });

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async seedUpdateKpiCskhPayment(body: any): Promise<any> {
        try {
            const { timeStart, timeEnd } = body;

            const timeStartQuery = moment(timeStart, 'YYYY-MM-DD').set({
                hours: 0,
                minutes: 0,
                seconds: 0,
            });
            const timeEndQuery = moment(timeEnd, 'YYYY-MM-DD').set({
                hours: 23,
                minutes: 59,
                seconds: 59,
            });

            const condition = {
                createdAt: timeStart && timeEnd && { $gte: timeStartQuery, $lte: timeEndQuery },
                countDoanhSo: 1
            };

            const kpis = await this.kpiCskhModel.find(condition).exec();

            for await (const kpi of kpis) {
                try {
                    const kpiObj = kpi.toObject();
                    const jobKpi = new this.jobUpdateKpiModel({
                        kpiId: kpiObj._id
                    });
                    await jobKpi.save();
                } catch (error) {
                    console.log('error', error);
                }
            }
            return "Đã tạo job cập nhật KPI thành công.";

        } catch (error) {
            throw new HttpException(`Lỗi : ${error.message}`, HttpStatus.NOT_FOUND);
        }
    }
    async getAnKhangTransactions(userId: string, body: AnKhangTransactionsQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { partnerId, pageSize = 20, pageIndex = 0, fromDate, toDate, referralCode, searchText, fromCreate, toCreate } = body;

            let query: any = {};

            if (partnerId) {
                query.partnerId = partnerId;
            }

            if (referralCode) {
                query.referralCode = referralCode;
            } else {
                query.referralCode = { $regex: '^[aA][kK]', $options: '' };
            }

            if (fromDate && toDate) {
                const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);
                query.date = { $gte: timeStart, $lte: timeEnd };
            }

            if (fromCreate && toCreate) {
                const { timeStart, timeEnd } = this.handleDateForFilter(fromCreate, toCreate);
                query.createdAt = { $gte: timeStart, $lte: timeEnd };
            }

            let referrals: any[] = [];
            let totalReferrals = 0;

            // Luôn lấy toàn bộ bản ghi phù hợp query
            referrals = await this.bookingReferralModel
                .find(query)
                .sort({ createdAt: -1 })
                .populate({
                    path: 'booking',
                    match: { status: { $in: [1, 2] } },
                    populate: [
                        { path: 'partner', select: 'name partnerId' },
                        { path: 'patient', select: 'name surname mobile code' },
                        { path: 'user', select: 'fullname username' },
                        { path: 'cskh', select: 'fullname username' },
                        { path: 'subject', select: 'name' },
                        { path: 'service', select: 'name' },
                        { path: 'room', select: 'name' },
                        { path: 'doctor', select: 'name' },
                    ],
                })
                .lean();
            // Lọc các referral có booking hợp lệ
            let filteredReferrals = referrals.filter(ref => ref.booking !== null);

            if (searchText && searchText.trim()) {
                // Áp dụng tìm kiếm text (bao gồm cả số điện thoại đầu 0)
                const searchTerm = searchText.trim().toLowerCase();
                let normalizedPhone = searchTerm;
                if (/^0\d{9,10}$/.test(searchTerm)) {
                    normalizedPhone = '+84' + searchTerm.substring(1);
                }
                filteredReferrals = filteredReferrals.filter(ref => {
                    const bookingCode = ref.bookingCode || '';
                    const transactionId = ref.transactionId || '';
                    const username = ref.booking?.user?.username || '';
                    return (
                        bookingCode.toLowerCase().includes(searchTerm) ||
                        transactionId.toLowerCase().includes(searchTerm) ||
                        username.toLowerCase().includes(searchTerm) ||
                        username.toLowerCase().includes(normalizedPhone)
                    );
                });
            }
            totalReferrals = filteredReferrals.length;
            // Phân trang lại ở backend
            referrals = filteredReferrals.slice(pageIndex * pageSize, (pageIndex + 1) * pageSize);

            // Lấy danh sách mã giới thiệu An Khang để hiển thị trong dropdown (unique, theo query hiện tại)
            const referralCodes = await this.bookingReferralModel.distinct('referralCode', query);

            // Chuyển đổi dữ liệu sang định dạng mong muốn
            const data = referrals.map(referral => {
                return {
                    referralInfo: {
                        referralCode: referral.referralCode,
                        referralCreatedAt: referral.createdAt,
                        referralUpdatedAt: referral.updatedAt,
                    },
                    ...referral.booking,
                    partner: referral.booking?.partner
                        ? {
                              ...referral.booking.partner,
                              name: this.getPartnerName(referral.booking.partnerId, referral.booking.partner.name),
                          }
                        : null,
                };
            });

            return {
                rows: data,
                totalRows: totalReferrals,
                role: 'user',
                listReferral: referralCodes,
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi thống kê.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    async getAnKhangReport(userId: string, body: AnKhangReportQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { fromDate, toDate, referralCode, partnerId, fromCreate, toCreate, searchText } = body;

            // Xây dựng query cho booking referral
            let referralQuery: any = {};

            // Lọc theo partnerId nếu có
            if (partnerId) {
                referralQuery.partnerId = partnerId;
            }

            // Lọc theo referralCode
            if (referralCode) {
                referralQuery.referralCode = referralCode;
            } else {
                referralQuery.referralCode = { $regex: '^[aA][kK]', $options: '' };
            }

            // Lọc theo ngày đặt chỗ
            if (fromDate && toDate) {
                const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);
                referralQuery.date = { $gte: timeStart, $lte: timeEnd };
            }

            // Lọc theo ngày tạo referral
            if (fromCreate && toCreate) {
                const { timeStart, timeEnd } = this.handleDateForFilter(fromCreate, toCreate);
                referralQuery.createdAt = { $gte: timeStart, $lte: timeEnd };
            }

            // Loại bỏ các trường undefined/null
            const referralCondition = pickBy(referralQuery, identity);

            // Lấy dữ liệu từ database với populate
            const referrals = await this.bookingReferralModel
                .find(referralCondition)
                .populate({
                    path: 'booking',
                    match: { status: { $in: [1, 2] } }, // Chỉ lấy booking có status 1 hoặc 2
                    populate: [
                        { path: 'partner', select: 'name partnerId' },
                        { path: 'service', select: 'name price category' },
                        { path: 'user', select: 'fullname username' },
                        { path: 'patient', select: 'name surname mobile' }, // Thêm mobile để có thông tin SĐT
                    ],
                })
                .exec();

            // Lọc các referral có booking hợp lệ (không null)
            let validReferrals = referrals.filter(referral => referral.booking !== null);
            
            // Nếu có searchText thì filter lại ở backend (giống logic getAnKhangTransactions)
            if (searchText && searchText.trim()) {
            let searchTerm = searchText.trim().toLowerCase();
            let normalizedPhone = searchTerm;
            if (/^0\d{9,10}$/.test(searchTerm)) {
            normalizedPhone = '+84' + searchTerm.substring(1);
            }
            validReferrals = validReferrals.filter(ref => {
            const bookingCode = ref.bookingCode || '';
            const transactionId = ref.transactionId || '';
            const username = ref.booking?.user?.username || '';
            return (
            bookingCode.toLowerCase().includes(searchTerm) ||
            transactionId.toLowerCase().includes(searchTerm) ||
            username.toLowerCase().includes(searchTerm) ||
            username.toLowerCase().includes(normalizedPhone)
            );
            });
            }
            
            // Map để lưu trữ dữ liệu theo referralCode
            const referralCodeMap = {};

            // Xử lý dữ liệu
            for (const referral of validReferrals) {
                const referralObj = referral.toObject();
                const booking = referralObj.booking; // Sửa từ bookingId thành booking
                const transReferralCode = referralObj.referralCode;

                if (transReferralCode && booking) {
                    // Khởi tạo entry cho referralCode nếu chưa tồn tại
                    if (!referralCodeMap[transReferralCode]) {

                        referralCodeMap[transReferralCode] = {
                        referralCode: transReferralCode,
                        totalBookings: 0,
                        revenue: 0,
                        fromDate: fromDate || '',
                        toDate: toDate || '',
                        fromCreate: fromCreate || '',
                        toCreate: toCreate || '',
                        transactions: [],
                        };
                    }

                    // Tạo dữ liệu giao dịch
                    const transactionData = {
                        ...booking,
                        referralInfo: {
                            referralCode: transReferralCode,
                            createdAt: referralObj.createdAt,
                            updatedAt: referralObj.updatedAt,
                        },
                    };

                    // Thêm vào danh sách giao dịch
                    referralCodeMap[transReferralCode].transactions.push(transactionData);
                    referralCodeMap[transReferralCode].totalBookings += 1;

                    // Tính doanh thu nếu có thông tin giá dịch vụ
                    if (booking.service && booking.service.price) {
                        const revenue = this.calculateRevenue(booking.service.price, '0'); // Hoa hồng mặc định 0%
                        referralCodeMap[transReferralCode].revenue += revenue;
                    }
                }
            }

            // Chuyển đổi sang định dạng mảng
            const reportArray = Object.values(referralCodeMap);

            // Sắp xếp theo doanh thu giảm dần
            return reportArray.sort((a: any, b: any) => b.revenue - a.revenue);
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi tạo báo cáo An Khang.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    private calculateRevenue(servicePrice: number, commission: string): number {
        if (!servicePrice || !commission) return 0;
        const commissionRate = Number(commission) / 100;
        return Math.round(servicePrice * commissionRate);
    }

    async anKhangReportExportExcel(userId: string, formData: AnKhangReportQueryDto): Promise<any> {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            // Lấy dữ liệu báo cáo
            const reportData = await this.getAnKhangReport(userId, formData);

            // Tạo workbook Excel
            const workbook = new Excel.Workbook();

            // Nếu có referralCode cụ thể, chỉ xuất dữ liệu cho mã đó
            if (formData.referralCode) {
                return this.anKhangReportExportExcelByReferralCode(workbook, reportData, formData.referralCode);
            } else {
                // Nếu không có referralCode, xuất tất cả dữ liệu
                return this.anKhangReportExportExcelAll(workbook, reportData);
            }
        } catch (error) {
            const message = error?.message || 'Lỗi xuất Excel báo cáo An Khang.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    private async anKhangReportExportExcelAll(workbook: Excel.Workbook, reportData: any[]): Promise<any> {
        // Tạo worksheet tổng quan
        const overviewWorksheet = workbook.addWorksheet('Tổng quan');
        overviewWorksheet.columns = [
            { header: 'STT', key: 'stt', width: 10 },
            { header: 'Mã giới thiệu', key: 'referralCode', width: 25 },
            { header: 'Số lượng phiếu', key: 'totalBookings', width: 20 },
            { header: 'Từ ngày', key: 'fromDate', width: 20 },
            { header: 'Đến ngày', key: 'toDate', width: 20 },
        ];

        // Tìm ngày đầu tiên và ngày cuối cùng trong dữ liệu
        const allDates = reportData.reduce((acc, report) => {
            const dates = report.transactions.map(trans => (trans.date ? new Date(trans.date) : null)).filter(date => date !== null);
            return [...acc, ...dates];
        }, []);

        const fromDate = allDates.length > 0 ? moment(Math.min(...allDates)).format('DD/MM/YYYY') : '';
        const toDate = allDates.length > 0 ? moment(Math.max(...allDates)).format('DD/MM/YYYY') : '';

        // Thêm dữ liệu tổng quan
        reportData.forEach((item, index) => {
            overviewWorksheet.addRow({
                stt: index + 1,
                referralCode: item.referralCode,
                totalBookings: item.totalBookings,
                fromDate,
                toDate,
            });
        });

        // Định dạng header
        const headerRow = overviewWorksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.alignment = { horizontal: 'center' };
        headerRow.height = 25;

        // Thêm border cho tất cả các ô có dữ liệu trong worksheet tổng quan
        overviewWorksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            row.eachCell({ includeEmpty: false }, cell => {
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' },
                };

                // Căn giữa cột STT
                if (typeof cell.col === 'number' &&cell.col === 1) {
                    cell.alignment = { horizontal: 'center' };
                }
            });
        });

        // Tạo một worksheet duy nhất chứa tất cả các giao dịch
        const allTransactionsWorksheet = workbook.addWorksheet('Tất cả giao dịch');
        allTransactionsWorksheet.columns = [
            { header: 'STT', key: 'stt', width: 10 },
            { header: 'Mã giới thiệu', key: 'referralCode', width: 25 },
            { header: 'Mã đặt chỗ', key: 'bookingCode', width: 20 },
            { header: 'Mã giao dịch', key: 'transactionId', width: 25 },
            { header: 'Bệnh nhân', key: 'patient', width: 30 },
            { header: 'SĐT bệnh nhân', key: 'patientMobile', width: 20 },
            { header: 'Dịch vụ', key: 'service', width: 30 },
            { header: 'Cơ sở y tế', key: 'partner', width: 25 },
            { header: 'Ngày đặt', key: 'date', width: 20 },
            { header: 'Giá dịch vụ', key: 'price', width: 20 },
        ];

        // Định dạng header
        const transHeaderRow = allTransactionsWorksheet.getRow(1);
        transHeaderRow.font = { bold: true };
        transHeaderRow.alignment = { horizontal: 'center' };
        transHeaderRow.height = 25;

        // Thêm tất cả giao dịch vào một worksheet
        let rowIndex = 1;
        for (const report of reportData) {
            report.transactions.forEach((transaction: any) => {
                const price = transaction.service?.price || 0;

                // Ẩn một phần số điện thoại bệnh nhân
                let patientMobile = transaction.patient?.mobile || '';
                if (patientMobile && patientMobile.length > 3) {
                    const lastThreeDigits = patientMobile.slice(-3);
                    const maskedPart = patientMobile.slice(0, -3).replace(/\d/g, 'x');
                    patientMobile = maskedPart + lastThreeDigits;
                }

                // Ẩn 3 số cuối của mã phiếu
                let maskedBookingCode = transaction.bookingCode || '';
                if (maskedBookingCode && maskedBookingCode.length > 3) {
                    maskedBookingCode = maskedBookingCode.slice(0, -3) + 'XXX';
                }

                // Ẩn 3 số cuối của mã giao dịch
                let maskedTransactionId = transaction.transactionId || '';
                if (maskedTransactionId && maskedTransactionId.length > 3) {
                    maskedTransactionId = maskedTransactionId.slice(0, -3) + 'XXX';
                }

                allTransactionsWorksheet.addRow({
                    stt: rowIndex++,
                    referralCode: report.referralCode,
                    bookingCode: maskedBookingCode,
                    transactionId: maskedTransactionId,
                    patient: transaction.patient?.name ? `${transaction.patient.surname} ${transaction.patient.name}` : 'N/A',
                    patientMobile,
                    service: transaction.service?.name || 'N/A',
                    partner: this.getPartnerName(transaction.partnerId, transaction.partner?.name),
                    date: moment(transaction.date).format('DD/MM/YYYY HH:mm'),
                    price: price,
                });
            });
        }

        // Định dạng cột giá
        allTransactionsWorksheet.getColumn('price').numFmt = '#,##0';

        // Thêm border cho tất cả các ô có dữ liệu
        allTransactionsWorksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            row.eachCell({ includeEmpty: false }, cell => {
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' },
                };

                // Căn giữa cột STT
                if (typeof cell.col === 'number' && cell.col === 1) {
                    cell.alignment = { horizontal: 'center' };
                }

                // Căn phải cột giá
                if (typeof cell.col === 'number' && cell.col === 10) {
                    cell.alignment = { horizontal: 'right' };
                }
            });
        });

        // Xuất buffer
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }


    private async anKhangReportExportExcelByReferralCode(workbook: Excel.Workbook, reportData: any[], referralCode: string): Promise<any> {
        // Tìm dữ liệu cho referralCode cụ thể
        const report = reportData.find(item => item.referralCode === referralCode);

        if (!report) {
            throw new HttpException(`Không tìm thấy dữ liệu cho mã giới thiệu ${referralCode}`, HttpStatus.NOT_FOUND);
        }

        // Tìm ngày đầu tiên và ngày cuối cùng trong dữ liệu
        const allDates = report.transactions.map(trans => (trans.date ? new Date(trans.date) : null)).filter(date => date !== null);

        const fromDate = allDates.length > 0 ? moment(Math.min(...allDates)).format('DD/MM/YYYY') : '';
        const toDate = allDates.length > 0 ? moment(Math.max(...allDates)).format('DD/MM/YYYY') : '';

        // 1. Tạo worksheet thông tin tổng quan
        const overviewWorksheet = workbook.addWorksheet('Tổng quan');

        // Thêm tiêu đề
        overviewWorksheet.mergeCells('A1:B1');
        const titleCell = overviewWorksheet.getCell('A1');
        titleCell.value = `Báo cáo chi tiết mã giới thiệu: ${report.referralCode}`;
        titleCell.font = { bold: true, size: 14 };
        titleCell.alignment = { horizontal: 'left' };

        // Thêm thông tin tổng quan
        overviewWorksheet.addRow([]);

        const infoRows = [
            ['Mã giới thiệu:', report.referralCode],
            ['Tổng số phiếu:', report.totalBookings],
            ['Từ ngày:', fromDate],
            ['Đến ngày:', toDate],
        ];

        infoRows.forEach(row => {
            const newRow = overviewWorksheet.addRow(row);
            newRow.getCell(1).font = { bold: true };
        });

        // Định dạng cột
        overviewWorksheet.getColumn('A').width = 20;
        overviewWorksheet.getColumn('B').width = 30;

        // 2. Tạo worksheet chi tiết giao dịch
        const detailWorksheet = workbook.addWorksheet('Chi tiết giao dịch');
        detailWorksheet.columns = [
            { header: 'STT', key: 'stt', width: 10 },
            { header: 'Mã phiếu', key: 'bookingCode', width: 20 },
            { header: 'Mã giao dịch', key: 'transactionId', width: 25 },
            { header: 'Bệnh nhân', key: 'patient', width: 30 },
            { header: 'SĐT bệnh nhân', key: 'patientMobile', width: 20 },
            { header: 'Dịch vụ', key: 'service', width: 30 },
            { header: 'Cơ sở y tế', key: 'partner', width: 25 },
            { header: 'Ngày đặt', key: 'date', width: 20 },
            { header: 'Giá dịch vụ', key: 'price', width: 20 },
        ];

        // Định dạng header
        const headerRow = detailWorksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.alignment = { horizontal: 'center' };
        headerRow.height = 25;

        // Thêm dữ liệu chi tiết
        report.transactions.forEach((transaction: any, index: number) => {
            const price = transaction.service?.price || 0;

            // Ẩn một phần số điện thoại bệnh nhân
            let patientMobile = transaction.patient?.mobile || '';
            if (patientMobile && patientMobile.length > 3) {
                const lastThreeDigits = patientMobile.slice(-3);
                const maskedPart = patientMobile.slice(0, -3).replace(/\d/g, 'x');
                patientMobile = maskedPart + lastThreeDigits;
            }

            // Ẩn 3 số cuối của mã phiếu
            let maskedBookingCode = transaction.bookingCode || '';
            if (maskedBookingCode && maskedBookingCode.length > 3) {
                maskedBookingCode = maskedBookingCode.slice(0, -3) + 'XXX';
            }

            // Ẩn 3 số cuối của mã giao dịch
            let maskedTransactionId = transaction.transactionId || '';
            if (maskedTransactionId && maskedTransactionId.length > 3) {
                maskedTransactionId = maskedTransactionId.slice(0, -3) + 'XXX';
            }

            detailWorksheet.addRow({
                stt: index + 1,
                bookingCode: maskedBookingCode,
                transactionId: maskedTransactionId,
                patient: transaction.patient?.name ? `${transaction.patient.surname} ${transaction.patient.name}` : 'N/A',
                patientMobile,
                service: transaction.service?.name || 'N/A',
                partner: this.getPartnerName(transaction.partnerId, transaction.partner?.name),
                date: moment(transaction.date).format('DD/MM/YYYY HH:mm'),
                price: price,
            });
        });

        // Định dạng cột giá
        detailWorksheet.getColumn('price').numFmt = '#,##0';

        // Thêm border cho tất cả các ô có dữ liệu
        detailWorksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            row.eachCell({ includeEmpty: false }, cell => {
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' },
                };

                // Căn giữa cột STT
                if (typeof cell.col === 'number' && cell.col === 1) {
                    cell.alignment = { horizontal: 'center' };
                }

                // Căn phải cột giá
                if (typeof cell.col === 'number' && cell.col === 9) {
                    cell.alignment = { horizontal: 'right' };
                }
            });
        });

        // Xuất buffer
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
    }

    async validatePaymentMethodData(paymentMethod: any, appId: string, platform: string): Promise<boolean> {
        const appIdInfo = paymentMethod?.appIds || '';
        const platformInfo = paymentMethod?.platform || '';
        const appIdFormat = appIdInfo.split(',').map((element: any) => element.trim()).filter((item: any) => item !== '');
        const isValidAppId = appIdFormat.length === 0 ? true : new Set(appIdFormat).has(appId);
        const isValidPlatform = platformInfo ? platform === platformInfo : true;
        return isValidAppId && isValidPlatform;
    }

    async chargeFee(
        appId: string,
        partnerId: string,
        methodId: string,
        detail: string,
        price: number,
        groupId: number,
        platform: string,
        treeId?: string,
    ): Promise<Observable<AxiosResponse<any>>> {
        try {
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            let url = `${baseUrl}/payment/v1/charge/${partnerId}/${groupId}/${methodId}/${detail}/${price}`;
            console.log('url', url);
            if (treeId) {
                url = url.concat(`/${treeId}`);
            }
            // this.logger.log(`Params chargeFee ${url}`);
            const paymentMethod = await (await this.httpService.getHttpRequest(url).toPromise()).data;
            // this.logger.log(`chargeFeeData: ${JSON.stringify(paymentMethod, null, 2)}`);
            // validate return data
            const isValidPaymentMethod = await this.validatePaymentMethodData(paymentMethod, appId, platform);
            // this.logger.log(`isValidPaymentMethod: ${isValidPaymentMethod}`);

            if (!isValidPaymentMethod) {
                throw new HttpException(`Phương thức thanh toán không tồn tại vui lòng kiểm tra lại!`, HttpStatus.BAD_REQUEST);
            }
            return paymentMethod;
        } catch (err) {
            // this.logger.error(`Error when exec chargeFree with appId: ${appId}, partnerId: ${partnerId}\nError: ${err.message}`);
            throw new HttpException(`Phương thức thanh toán không tồn tại vui lòng kiểm tra lại!`, HttpStatus.BAD_REQUEST);
        }
    }

    // async handlePaymentCountdown(paymentTime: Date, bookingDate: Date, partnerId: string, chageFreeValue = null) {
    //     try {
    //         let countdown = 60;
    //         const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }, { countdown: true, countdownSameDay: true }).exec();

    //         const checkTrongNgay = bookingDate && moment(bookingDate).isSame(paymentTime, 'day');

    //         if (chageFreeValue && chageFreeValue?.paymentPartnerId?.toUpperCase() === 'MOMO'){
    //             countdown = 15
    //         } else if (checkTrongNgay && partnerConfig?.countdownSameDay) {
    //             countdown = partnerConfig?.countdownSameDay;
    //         } else if (!partnerConfig?.countdown) {
    //             countdown = Number(await this.globalSettingService.findByKeyAndRepoName(this.COUNTDOWN));
    //         } else {
    //             countdown = partnerConfig.countdown;
    //         }
    //         // check handle endTime
    //         const res = moment.utc(paymentTime).add(Number(countdown), 'minutes').toDate();
    //         // this.logger.debug(`Payment countdown: ${res}`);
    //         return res;
    //     } catch (error) {
    //         this.logger.error(`Error when exec handlePaymentCountdown. Cause: ${error.message}`);
    //     }
    // }

    async submitPayment(
        partnerid: string, userId: string, patientObje: any,
        chageFreeValue: any, redirectUrl: string, transactionCode: string, platform: string, description?: string, cbWebView?: number, extraInfo?: any,
        medproCareFee?: any, userInfo?: any): Promise<any> {

        const { medproId, serviceName, serviceId, smsCode, isPaymentFee = false, isMultipleBooking = false } = extraInfo || {};

        let diaChi = [];
        if (typeof patientObje?.address !== typeof undefined) {
            diaChi = [...diaChi, patientObje.address];
        }
        if (typeof patientObje.ward !== typeof undefined) {
            const ward = patientObje.ward;
            if (typeof ward?.name !== typeof undefined) {
                diaChi = [...diaChi, ward?.name];
            }
        }
        if (typeof patientObje?.district !== typeof undefined) {
            const district = patientObje.district;
            if (typeof district?.name !== typeof undefined) {
                diaChi = [...diaChi, district?.name];
            }
        }

        let cityName = '';
        if (typeof patientObje?.city !== typeof undefined) {
            const city = patientObje.city;
            if (typeof city?.name !== typeof undefined) {
                cityName = city?.name;
            }
        }
        let content = '';

        if (chageFreeValue?.paymentPartnerId?.toUpperCase() === 'MOMO') {
            content = `Thanh toan ${smsCode}`;
        } else if (description) {
            content = description;
        } else {
            content = `PKH - Thanh toan Phieu kham benh ${transactionCode}`;
        }

        if (isPaymentFee) {
            content = description;
        }

        if(isMultipleBooking){
            content = `${transactionCode}`;
        }

        const objcbWebView: any = {};
        if (cbWebView) {
            objcbWebView.cbWebView = cbWebView;
        }
        if (patientObje?.bill_code) {
            objcbWebView.bill_code = patientObje.bill_code;
        }
        /* tiến hành ascii name */
        const fullnameOrigin = `${!!patientObje.surname ? patientObje.surname : ''} ${patientObje.name}`.trim();
        const removeDuplicateSpace = `${fullnameOrigin
            .replace(/[0-9]/g, '').replace(/[\/|*\\/}|{|?|<|>|\]|\[|.]/g, '').replace(/ +(?= )/g, '')}`.trim();
        const slugName = slugify.default(removeDuplicateSpace, {
            replacement: ' ',
            // lower: true,
            locale: 'vi',
        });

        let yourPhone = '';
        if (patientObje?.phoneCall1900 || '') {
            yourPhone = `${patientObje.phoneCall1900}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        } else {
            yourPhone = !!patientObje.mobile ? patientObje.mobile : '';
        }
        // const callbackUrl = await this.globalSettingService.findByKeyAndRepoName('PAYMENT_HUB_CALLBACK_URL');
        let paramsEndtime: any;
        let booking = null;
        if (patientObje?.idBooking) {
            booking = await this.bookingModel.findById({ _id: patientObje.idBooking }, { endTime: true }).exec();
        } else {
            booking = await this.bookingModel.findOne({ transactionId: transactionCode }, { endTime: true }).exec();
        }

        if (booking && booking?.endTime) {
            const bookingObj = booking.toObject();
            paramsEndtime = {
                endTime: bookingObj?.endTime,
            };
        } else {
            // const endTime = await this.handlePaymentCountdown(moment().toDate(), patientObje.bookingDate, partnerid, chageFreeValue);
            const endTime = moment.utc(moment().toDate()).add(Number(60), 'minutes').toDate();
            paramsEndtime = {
                endTime: endTime,
            };
        }

        const paymentHubData = {
            amount: chageFreeValue.grandTotal,
            description: content,
            redirectUrl: `${redirectUrl}?mpTransaction=${transactionCode}`,
            gatewayId: chageFreeValue.gatewayId,
            name: slugName,
            partnerId: partnerid,
            paymentType: chageFreeValue.type,
            transactionId: transactionCode,
            userId,
            /* Truyền thêm thông tin  */
            bankCode: chageFreeValue.code,
            email: !!patientObje.email ? patientObje.email : '',
            country: 'Việt Nam',
            city: cityName,
            phone: yourPhone,
            medproId,
            address: diaChi.join(', '), // `${patientObje.address}, ${patientObje.ward.name}, ${patientObje.district.name}`,
            platform,
            systemId: 'v2', // bổ sung thêm
            ...objcbWebView,
            feeInfo: chageFreeValue,
            ...paramsEndtime,
            serviceName,
            serviceId,
            medproCareFee,
            userInfo
            // callbackUrl
        };
        // console.log(paymentHubData);
        const urlPaymentHubPay = this.urlConfigService.getPaymentHubPayUrl();
        try {
            // this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
            //     name: 'SubmitDataToPaymentHub',
            //     summary: 'Submit Payment Hub',
            //     nameParent: 'submitPayment',
            //     params: {
            //         urlPaymentHubPay,
            //         paymentHubData
            //     },
            //     errorBody: null,
            //     response: {},
            //     message: '',
            // });
            const StartCreateOrder = moment().toDate();
            const result = (await this.httpService.postHttpRequest(urlPaymentHubPay, { ...paymentHubData }).toPromise()).data;
            const EndCreateOrder = moment().toDate();
            this.logger.log(`createOrderPaymentHub: ${moment(EndCreateOrder).diff(StartCreateOrder, 'milliseconds')} ms`)
            return { ...result, ...paramsEndtime };
        } catch (error) {
            // await this.sentryService.captureError(error, {
            //     url: urlPaymentHubPay,
            //     method:'POST',
            //     user:{...userInfo},
            //     body: {
            //         ...paymentHubData,
            //         // ? Gửi thông báo lark group 
            //         patient: patientObje
            //     },
            // }, 'high');
            // this.eventEmmiter.emit(THIRD_PARTY_ERROR_LOG, {
            //     urlPaymentHubPay,
            //     paymentHubData: {
            //         ...paymentHubData,
            //         // ? Gửi thông báo lark group 
            //         patient: patientObje
            //     },
            //     errorBody: this.utilService.errorHandler(error),
            //     message: error?.message || 'Lỗi từ paymenthub',
            //     subjectMail: 'PaymentHub Error - Có lỗi xảy ra khi gọi sang payment gateway. Vui lòng check gấp....'
            // });

            // this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
            //     name: 'paymentHub',
            //     summary: 'Submit Payment Hub',
            //     nameParent: 'submitPayment',
            //     params: {
            //         urlPaymentHubPay,
            //         paymentHubData,
            //     },
            //     errorBody: this.utilService.errorHandler(error),
            //     response: {},
            //     message: error?.message || 'Lỗi từ paymenthub',
            // });
            // const { response = null } = error;
            // if (response) {
            //     console.log('response lỗi từ paymenthub', response);
            // }
            // this.clientSentry.instance().captureException(error);
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    getQueryParamsAsObject(url) {
        const urlObj = new URL(url);
        const params = urlObj.searchParams;

        const queryObject = {};

        params.forEach((value, key) => {
            queryObject[key] = value;
        });

        return queryObject;
    }

    async createCare247PaymentIndependent(body: { bookingId: string }): Promise<any> {
        try {
            throw new HttpException(`Tíng năng đang được cập nhật và phát triển.`, HttpStatus.BAD_REQUEST);
            const { bookingId } = body;

            const care247Payment = await this.care247IndependentPaymentModel.findOne({ bookingId }).exec();
            if (care247Payment) {
                return { link: care247Payment.link, token: care247Payment.token };
            }

            let [booking, bookingCare247] = await Promise.all([
                this.bookingModel.findById({ _id: bookingId }).exec(),
                this.bookingCare247Model.findOne({ bookingId }).exec()
            ])
            if (!booking) {
                throw new HttpException(`Không tìm thấy thông tin phiếu khám.`, HttpStatus.BAD_REQUEST);
            }

            const bookingObj = booking.toObject();
            const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
            let medproCare = bookingObj?.care247;

            const medproCarePartnerList = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST')
            let medproCarePartnerListArr = new Set(medproCarePartnerList.split(',')).add('umc').add('dalieuhcm')
            try {
                const onOffChoRay = await this.globalSettingService.findByKeyAndRepoName('ON_OFF_CHORAY_CARE247')
                if (onOffChoRay === 'ON') {
                    medproCarePartnerListArr = medproCarePartnerListArr.add('choray');
                }
            } catch (error) {}
            // const kenhPhanPhoi = new Set().add('medpro').add('viettelpay');
            if (!bookingCare247 && medproCarePartnerListArr.has(bookingObj.partnerId)) {
                try {
                    const isPrimary = true;
                    // const { medproCare } = await this.paymentMethodService.getMedproCarePrice(appid, 
                    //     bookingObj.partnerId, medproCareServiceIds, userId, platform, version, bookingObj?.date, isPrimary)

                    medproCare = await this.hisGatewayService.getMedproCareFromPortal('medpro', bookingObj.partnerId, 
                        bookingObj.userId, null, null, null, null, bookingObj.date, isPrimary)
                    const medproCareServices = medproCare?.addonServices?.slice(0,1);
                    medproCare = { ...medproCare, addonServices: medproCareServices }
                    
                    const newBookingCare247 = new this.bookingCare247Model({
                        bookingId: bookingObj._id,
                        booking: bookingObj._id,
                        bookingCode: bookingCode,
                        bookingStatus: bookingObj.status,
                        patient: bookingObj.patient,
                        patientId: bookingObj.patientId,
                        patientVersion: bookingObj.patientVersion,
                        patientVersionId: bookingObj.patientVersionId,
                        userId: bookingObj.userId,
                        user: bookingObj.userId,
                        appId: bookingObj.appId,
                        partnerId: bookingObj.partnerId,
                        partner: bookingObj.partner,
                        subject: bookingObj.subject,
                        service: bookingObj.service,
                        doctor: bookingObj.doctor,
                        room: bookingObj.room,
                        section: bookingObj.section,
                        date: bookingObj.date,
                        cskhUserId: bookingObj.cskhUserId,
                        cskh: bookingObj.cskhUserId,
                        platform: bookingObj.platform,
                        name: medproCare.name,
                        id: uuid.v4().replace(/-/g, ''),
                        addonServices: medproCare.addonServices,
                        note: medproCare.note,
                        medproCareNote: medproCare.medproCareNote,
                        status: 0,
                        type: 'independent',
                        provider: medproCare.provider,
                    });
                    bookingCare247 = await newBookingCare247.save();
                } catch (error) {}
            }

            if(!bookingCare247) {
                throw new HttpException(`Không tìm thấy thông tin Care247. Vui lòng thử lại sau.`, HttpStatus.BAD_REQUEST);
            }

            const bookingCare247Obj = bookingCare247.toObject();
            
            const getUserInfo = await this.userModel.findById({ _id: bookingObj.userId }).exec();

            if (!getUserInfo) {
                throw new HttpException('Hệ thống không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.', HttpStatus.UNAUTHORIZED);
            }

            const findPatients = await this.patientModel
                .find({ id: bookingObj.patientId })
                .populate('city')
                .populate('district')
                .populate('ward')
                .limit(1)
                .exec();
            const findPatient = first(findPatients);
            if (!findPatient) {
                throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.BAD_REQUEST);
            }
            const patientObje = findPatient.toObject();

            let medproCareFee = reduce(
                bookingCare247Obj.addonServices,
                (sum: number, item: any) => {
                    return sum + item.price;
                },
                0,
            )

            if (REPO_NAME_BETA.includes(this.repoName)) {
                medproCareFee = 5000;
            }

            //
            let chageFeeValue: any = {};
            let amount = 0;
            let subTotal = 0;
            let totalFee = 0;
            let medproFee = 0;
            let transferFee = 0;
            let gatewayId = 'zalo';
            let paymentType = '';
            let code = '';
            const bankAccountInfo: any = {};

            let paymentsCare247: any
            
            try {
                paymentsCare247 = await this.paymentMethodService.getAllPaymentMethodNew(
                    { price: medproCareFee, patientId: bookingObj.patientId }, 'care247', 'medpro', bookingObj.platform, null, bookingCare247Obj.userId, null
                )
                
                const StartChargeFee = moment().toDate();
                chageFeeValue = await this.chargeFee(
                    'medpro',
                    'care247',
                    paymentsCare247?.[0]?.methodId,
                    paymentsCare247?.[0]?.paymentTypes?.[0]?.code,
                    medproCareFee,
                    1,
                    bookingObj.platform,
                    bookingObj.treeId,
                );
                const EndChargeFee = moment().toDate();
                this.logger.log(`Charge Fee DIFF: ${moment(EndChargeFee).diff(StartChargeFee, 'milliseconds')} ms`)
                amount = Number(chageFeeValue.grandTotal);
                totalFee = Number(chageFeeValue.totalFee);
                medproFee = Number(chageFeeValue.medproFee);
                transferFee = totalFee - medproFee;
                gatewayId = chageFeeValue.gatewayId;
                paymentType = chageFeeValue.type;
                code = chageFeeValue.code;
                /* lấy lại thông tin bankAccount */
                if (typeof chageFeeValue.bankAccount !== typeof undefined) {
                    // console.log(chageFreeValue);
                    bankAccountInfo.name = typeof chageFeeValue?.name !== typeof undefined ? chageFeeValue?.name : '';
                    if (typeof chageFeeValue.bankAccount !== typeof undefined) {
                        const { accountHolder = '', accountNumber = '', bankBranch = '' } = chageFeeValue.bankAccount;
                        bankAccountInfo.accountHolder = accountHolder;
                        bankAccountInfo.accountNumber = accountNumber;
                        bankAccountInfo.bankBranch = bankBranch;
                    }
                }
            } catch (error) {
                console.log('error', JSON.stringify(error, null, 2))
                // this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
                //     name: 'chargeFee',
                //     summary: 'Charge Fee',
                //     nameParent: 'reserveMedproCare',
                //     params: {
                //         partnerId,
                //         methodId: paymentsCare247?.[0]?.methodId,
                //         paymentTypeDetail: paymentsCare247?.[0]?.paymentTypes?.[0]?.code,
                //         medproCareFee,
                //         medproCareWithBooking,
                //     },
                //     errorBody: this.utilService.errorHandler(error),
                //     response: {},
                //     message: error?.message || `Không tìm thấy thông tin phí.`,
                //     userId,
                //     ...logTracking,
                // });
                throw new HttpException('Không tìm thấy thông tin phí.', HttpStatus.BAD_REQUEST);
            }

            let newPaymentCare247: any;
            let expiredAt;
            let bankInfo;
            try {
                // tạo transaction code
                const rand9Char = this.utilService.randomText(12).toUpperCase();
                const transactionCode = this.transactionConfig.getPrefixTransactionCodeTT(rand9Char);

                const objUserCallPhone: any = {};
                if (bookingObj?.phone || '') {
                    /* cập nhật phoneCall1900 */
                    objUserCallPhone.phoneCall1900 = bookingObj.phone;
                }
                const cbWebView = bookingObj?.cbWebView || null;
                const description = 'Thanh toan Dich vu dat rieng Care247'
                const redirectUrl = 'https://care247.asia/payment';

                const result = await this.submitPayment(
                    'care247', bookingCare247Obj.userId, { ...patientObje, ...objUserCallPhone, bookingDate: bookingObj.date }, chageFeeValue, redirectUrl,
                    transactionCode, bookingObj.platform, description, cbWebView,
                    {
                        medproId: getUserInfo.username,
                        serviceName: bookingCare247Obj.addonServices[0].name,
                        serviceId: bookingCare247Obj.addonServices[0].id,
                        smsCode: bookingObj.smsCode,
                    },
                    medproCareFee,
                );

                const { data: { OrderInfo }, endTime } = result;
                const gatewayStatus = Number(OrderInfo.status);
                expiredAt = endTime;
                bankInfo = this.getQueryParamsAsObject(OrderInfo.qrCodeUrl || '');

                const paymentM = new this.paymentModel({
                    bookingId: bookingObj.bookingId,
                    booking: bookingObj._id,
                    date: bookingObj.date,
                    patientId: bookingObj.patientId,
                    patient: bookingObj.patient,
                    amount,
                    subTotal,
                    totalFee,
                    medproFee,
                    transferFee,
                    gatewayId,
                    paymentMethod: code,
                    paymentMethodDetail: paymentType,
                    transactionId: transactionCode,
                    bookingCode,
                    orderId: OrderInfo.orderId,
                    partnerId: 'care247',
                    paymentTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                    status: gatewayStatus,
                    bankInfo: { ...bankAccountInfo },
                    noPayment: false,
                    type: 9,
                    groupId: 1,
                    gatewayTransactionId: OrderInfo?.gatewayTransactionId || '',
                    medproCareFee,
                    care247: bookingCare247Obj._id,
                    expiredAt
                });
                paymentM.id = uuid.v4().replace(/-/g, '');
                newPaymentCare247 = await paymentM.save();

                await this.bookingModel.findByIdAndUpdate({ _id: bookingObj._id }, 
                { care247: { ...medproCare, transactionId: transactionCode, type: 'independent' } }).exec();
        
                await this.bookingCare247Model.findByIdAndUpdate({ _id: bookingCare247Obj._id, status: { $ne: 1 } }, 
                    { 
                        transactionId: transactionCode,
                        type: 'independent',
                    }).exec();
            } catch (error) {
                // this.logger.error(`Error when exec createPayment for bookingId: ${bookingId}. Cause: ${error.message}`);
                throw error;
            }

            const configCare247Payment = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CARE247_PAYMENT_TOKEN');
            const configCare247PaymentObj = configCare247Payment ? JSON.parse(configCare247Payment) : {};
            const token = uuid.v4().replace(/-/g, '');
            const link = configCare247PaymentObj.url.replace('{token}', token);
            
            const care247PaymentIndependent = new this.care247IndependentPaymentModel({
                user: bookingCare247Obj.userId,
                userId: bookingCare247Obj.userId,
                patient: bookingCare247Obj.patient,
                bookingId: bookingCare247Obj.bookingId,
                booking: bookingCare247Obj.booking,
                care247: bookingCare247Obj._id,
                payment: newPaymentCare247._id,
                token,
                link,
                bankInfo
            });
            const data = await care247PaymentIndependent.save();

            await this.paymentModel.findByIdAndUpdate({ _id: newPaymentCare247._id }, { care247Independent: data._id }).exec();
            
            return {
                link: data.link,
                token: data.token,
            }
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Tạo payment Care247 độc lập bị lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async sendSmsCare247PaymentIndependent(body: { token: string, mobile: string }, userId: string): Promise<any> {
        try {
            const { token, mobile } = body;
            const care247Independent = await this.care247IndependentPaymentModel.findOne({ token, expiredAt: { $gte: moment().toDate() } }).exec();

            if (!care247Independent) {
                throw new HttpException('Mã thanh toán này đã hết hiệu lực.', HttpStatus.NOT_FOUND);
            }

            const care247IndependentObj = care247Independent.toObject();

            let template = await this.globalSettingService.findByKeyAndRepoName('CARE247_REGISTER_SMS');
            if (!template) {
                throw new HttpException('Không tìm thấy nội dung sms.', HttpStatus.NOT_FOUND);
            }
            if (template) {
                template = template.replace('{dd/mm/yyyy}', moment().format('DD/MM/YYYY'));
                template = template.replace('{link}', care247IndependentObj.link);
            }

            const data = {
                receiver: mobile,
                message: template,
                partnerId: 'medpro',
                messageType: 'TEXT',
            };

            try {
                await this.smsService.sendSmsMessageHub(data);

                await this.care247IndependentPaymentModel.findByIdAndUpdate({ _id: care247IndependentObj._id }, { 
                    smsStatus: 1, smsTimeSent: moment().utc().toDate(), userAction: userId, smsContent: template
                }, { new: true }).exec();

                return { isOk: true }
            } catch (error) {
                await this.care247IndependentPaymentModel.findByIdAndUpdate({ _id: care247IndependentObj._id }, { 
                    smsStatus: -2, smsTimeSent: moment().utc().toDate(), userAction: userId
                }, { new: true }).exec();
                console.error('error', error);
                throw new HttpException('Không thể gửi sms đăng ký Care247 thành công. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
        } catch (error) {
            console.error('error', error);
            throw new HttpException('Không thể gửi sms đăng ký Care247 thành công. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getCare247PaymentIndependent(body: any): Promise<any> {
        try {
            const care247Independent = await this.care247IndependentPaymentModel
                .findOne({ token: body.token })
                .populate({ path: 'user', select: { fullname: true, username: true, email: true } })
                .populate({ path: 'patient', select: { name: true, surname: true, address: true } })
                .populate('care247')
                .populate('payment')
                .exec();
            if (!care247Independent) {
                throw new HttpException('Không tìm thấy giao dịch thanh toán Care247', HttpStatus.NOT_FOUND);
                // const care247Independent = await this.care247IndependentPaymentModel.findOne({ token: body.token }).exec();
                // const care247IndependentObj = care247Independent.toObject();
                // const { user, patient, care247, payment, ...rest } = care247IndependentObj;
                // return { user, patient, care247, payment, isValid: false, ...rest }
            }
            const care247IndependentObj = care247Independent.toObject();
            const today = moment();
            if (moment(care247Independent.care247.date).isBefore(today)) {
                throw new HttpException('Phiếu care247 này đã qua ngày khám.', HttpStatus.BAD_REQUEST);
            }
            const { user, patient, care247, payment, bankInfo, ...rest } = care247IndependentObj;
            let paymentData = payment;
            let bankInfoData = bankInfo;
            if (payment.status === 2) {
                const { expiredAt, ...restPayment } = payment;
                const { expiredTime, ...restBank } = bankInfo;
                paymentData = restPayment;
                bankInfoData = restBank;
                return { user, patient, care247, payment: paymentData, bankInfo: bankInfoData, isValid: true, ...rest }
            }
            if (moment(care247Independent.payment.expiredAt).isBefore(today)) {
                return { user, patient, care247, payment: paymentData, bankInfo: bankInfoData, isValid: false, ...rest }
            }

            return { user, patient, care247, payment: paymentData, bankInfo: bankInfoData, isValid: true, ...rest }
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lấy thông tin thanh toán Care247 bị lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async createPaymentCare247PaymentIndependent(body: any): Promise<any> {
        try {
            const care247Independent = await this.care247IndependentPaymentModel
                .findOne({ token: body.token })
                .populate({ path: 'user', select: { fullname: true, username: true, email: true } })
                .populate({ path: 'patient', select: { name: true, surname: true, address: true } })
                .populate('care247')
                .populate('booking')
                .populate('payment')
                .exec();
            if (!care247Independent) {
                throw new HttpException('Không tìm thấy giao dịch thanh toán Care247', HttpStatus.NOT_FOUND);
            }
            const care247IndependentObj = care247Independent.toObject();

            const today = moment();
            if (moment(care247Independent.payment.expiredAt).isAfter(today)) {
                return this.getCare247PaymentIndependent({ token: body.token });
            }

            const bookingObj = care247IndependentObj.booking;
            const bookingCare247Obj = care247IndependentObj.care247;
            const paymentObj = care247IndependentObj.payment;
            let medproCareFee = paymentObj.medproCareFee
            const getUserInfo = await this.userModel.findById({ _id: care247IndependentObj.userId }).exec();

            if (!getUserInfo) {
                throw new HttpException('Hệ thống không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
            }

            const findPatients = await this.patientModel
                .find({ id: bookingObj.patientId })
                .populate('city')
                .populate('district')
                .populate('ward')
                .limit(1)
                .exec();
            const findPatient = first(findPatients);
            if (!findPatient) {
                throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.BAD_REQUEST);
            }
            const patientObje = findPatient.toObject();
            
            let chageFeeValue: any = {};
            let amount = 0;
            let subTotal = 0;
            let totalFee = 0;
            let medproFee = 0;
            let transferFee = 0;
            let gatewayId = 'zalo';
            let paymentType = '';
            let code = '';
            const bankAccountInfo: any = {};
            try {
                const paymentsCare247 = await this.paymentMethodService.getAllPaymentMethodNew(
                    { price: medproCareFee, patientId: bookingObj.patientId }, 'care247', 'medpro', bookingObj.platform, null, bookingCare247Obj.userId, null
                )
                
                const StartChargeFee = moment().toDate();
                chageFeeValue = await this.chargeFee(
                    'medpro',
                    'care247',
                    paymentsCare247?.[0]?.methodId,
                    paymentsCare247?.[0]?.paymentTypes?.[0]?.code,
                    medproCareFee,
                    1,
                    bookingObj.platform,
                    bookingObj.treeId,
                );
                const EndChargeFee = moment().toDate();
                this.logger.log(`Charge Fee DIFF: ${moment(EndChargeFee).diff(StartChargeFee, 'milliseconds')} ms`)
                amount = Number(chageFeeValue.grandTotal);
                totalFee = Number(chageFeeValue.totalFee);
                medproFee = Number(chageFeeValue.medproFee);
                transferFee = totalFee - medproFee;
                gatewayId = chageFeeValue.gatewayId;
                paymentType = chageFeeValue.type;
                code = chageFeeValue.code;
                /* lấy lại thông tin bankAccount */
                if (typeof chageFeeValue.bankAccount !== typeof undefined) {
                    // console.log(chageFreeValue);
                    bankAccountInfo.name = typeof chageFeeValue?.name !== typeof undefined ? chageFeeValue?.name : '';
                    if (typeof chageFeeValue.bankAccount !== typeof undefined) {
                        const { accountHolder = '', accountNumber = '', bankBranch = '' } = chageFeeValue.bankAccount;
                        bankAccountInfo.accountHolder = accountHolder;
                        bankAccountInfo.accountNumber = accountNumber;
                        bankAccountInfo.bankBranch = bankBranch;
                    }
                }
            } catch (error) {
                console.log('error', JSON.stringify(error, null, 2))
                throw new HttpException('Không tìm thấy thông tin phí.', HttpStatus.BAD_REQUEST);
            }
            
            let newPaymentCare247: any;
            let expiredAt;
            let bankInfo;
            try {
                // tạo transaction code
                const rand9Char = this.utilService.randomText(12).toUpperCase();
                const transactionCode = this.transactionConfig.getPrefixTransactionCodeTT(rand9Char);

                const objUserCallPhone: any = {};
                if (bookingObj?.phone || '') {
                    /* cập nhật phoneCall1900 */
                    objUserCallPhone.phoneCall1900 = bookingObj.phone;
                }
                const cbWebView = bookingObj?.cbWebView || null;
                const description = 'PKH - Thanh toan Dich vu dat rieng Care247'
                const redirectUrl = 'https://care247.asia/payment';

                const result = await this.submitPayment(
                    'care247', bookingCare247Obj.userId, { ...patientObje, ...objUserCallPhone, bookingDate: bookingObj.date }, chageFeeValue, redirectUrl,
                    transactionCode, bookingObj.platform, description, cbWebView,
                    {
                        medproId: getUserInfo.username,
                        serviceName: bookingCare247Obj.addonServices[0].name,
                        serviceId: bookingCare247Obj.addonServices[0].id,
                        smsCode: bookingObj.smsCode,
                    },
                    medproCareFee,
                );

                const { data: { OrderInfo }, endTime } = result;
                const gatewayStatus = Number(OrderInfo.status);
                expiredAt = endTime;
                bankInfo = this.getQueryParamsAsObject(OrderInfo.qrCodeUrl || '');

                const paymentM = new this.paymentModel({
                    bookingId: bookingObj.bookingId,
                    booking: bookingObj._id,
                    date: bookingObj.date,
                    patientId: bookingObj.patientId,
                    patient: bookingObj.patient,
                    amount,
                    subTotal,
                    totalFee,
                    medproFee,
                    transferFee,
                    gatewayId,
                    paymentMethod: code,
                    paymentMethodDetail: paymentType,
                    transactionId: transactionCode,
                    bookingCode: bookingCare247Obj.bookingCode,
                    orderId: OrderInfo.orderId,
                    partnerId: 'care247',
                    paymentTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                    status: gatewayStatus,
                    bankInfo: { ...bankAccountInfo },
                    noPayment: false,
                    type: 9,
                    groupId: 1,
                    gatewayTransactionId: OrderInfo?.gatewayTransactionId || '',
                    medproCareFee,
                    care247: bookingCare247Obj._id,
                    expiredAt,
                    care247Independent: care247IndependentObj._id
                });
                paymentM.id = uuid.v4().replace(/-/g, '');
                newPaymentCare247 = await paymentM.save();

                await this.bookingModel.findByIdAndUpdate({ _id: bookingObj._id }, 
                { care247: { ...bookingObj.care247, transactionId: transactionCode } }).exec();
        
                await this.bookingCare247Model.findByIdAndUpdate({ _id: bookingCare247Obj._id, status: { $ne: 1 } }, 
                    { 
                        transactionId: transactionCode,
                    }).exec();

                await this.care247IndependentPaymentModel.findByIdAndUpdate({ _id: care247IndependentObj._id }, 
                    { 
                        payment: newPaymentCare247._id,
                        bankInfo
                    }).exec();

                const data = await this.care247IndependentPaymentModel
                    .findById(care247IndependentObj._id)
                    .populate({ path: 'user', select: { fullname: true, username: true, email: true } })
                    .populate({ path: 'patient', select: { name: true, surname: true, address: true } })
                    .populate('care247')
                    .populate('payment')
                    .exec();
                
                const dataObj = data.toObject();

                const { user, patient, care247, payment, ...rest } = dataObj;
                return { user, patient, care247, payment, isValid: true, ...rest }
            } catch (error) {
                // this.logger.error(`Error when exec createPayment for bookingId: ${bookingId}. Cause: ${error.message}`);
                throw error;
            }
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Tạo lại giao dịch thanh toán Care247 bị lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async uploadHealthData(file: Express.Multer.File): Promise<any> {
        try {
            const FormData = require('form-data');
            const formData = new FormData();
            formData.append('file', file.buffer, {
                filename: file.originalname,
                contentType: file.mimetype,
            });
            let url = this.urlConfigService.getAppMedproApi();
            if (REPO_NAME_BETA.includes(this.repoName)) {
                url = 'https://api-medpro-care-app-beta.medpro.com.vn';
            }
            const config: AxiosRequestConfig = {
                method: 'post',
                url: `${url}/patient-files/health-data`,
                headers: {
                    ...formData.getHeaders(),
                    'appid': 'medpro',
                    'locale': 'vi'
                },
                data: formData,
                responseType: 'arraybuffer',
            };

            const apiResponse = await this.http.request(config).toPromise();
            const contentType = apiResponse.headers['content-type'];
            
            if (contentType && contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
                return {
                    success: false,
                    message: 'Import có lỗi. File báo cáo lỗi đã được tạo.',
                    fileInfo: {
                        filename: file.originalname,
                        size: file.size,
                        mimetype: file.mimetype,
                        uploadedAt: new Date(),
                    },
                    errorFile: {
                        buffer: apiResponse.data,
                        filename: 'examination-error-data.xlsx',
                        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                };
            } else {
                const responseData = JSON.parse(apiResponse.data.toString());
                
                return {
                    success: responseData.success || false,
                    message: responseData.message || 'File đã được xử lý',
                    fileInfo: {
                        filename: file.originalname,
                        size: file.size,
                        mimetype: file.mimetype,
                        uploadedAt: new Date(),
                    },
                    data: responseData
                };
            }

        } catch (error) {
            if (error.response) {
                const errorData = error.response.data;
                let errorMessage = 'Lỗi khi xử lý file dữ liệu sức khỏe.';
                
                if (errorData) {
                    try {
                        const parsedError = JSON.parse(errorData.toString());
                        errorMessage = parsedError.message || errorMessage;
                    } catch (parseError) {
                        errorMessage = errorData.toString() || errorMessage;
                    }
                }
                
                throw new HttpException(errorMessage, error.response.status || HttpStatus.BAD_REQUEST);
            }
            
            const message = error?.message || 'Lỗi khi upload file dữ liệu sức khỏe.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getPendingHealthData(): Promise<any> {
        try {
            let url = this.urlConfigService.getAppMedproApi();
            if (REPO_NAME_BETA.includes(this.repoName)) {
                url = 'https://api-medpro-care-app-beta.medpro.com.vn';
            }
            const config: AxiosRequestConfig = {
                method: 'get',
                url: `${url}/patient-files/pending-data`,
                headers: {
                    'appid': 'medpro',
                    'locale': 'vi'
                }
            };

            const apiResponse = await this.http.request(config).toPromise();
            if( !apiResponse || !apiResponse.data) {
                throw new HttpException('Không có dữ liệu chờ xử lý.', HttpStatus.NOT_FOUND);
            }
            return apiResponse?.data;
        } catch (error) {
            console.error('Error parsing pending data:', error);
            return null;
        }
    }

    /**
     * Import employee list from Excel buffer (không lưu file)
     * @param formData - Import configuration with buffer
     * @returns Import result with success/error details
     */
    async importEmployeeFromExcelBuffer(formData: ImportEmployeeExcelBufferDto): Promise<ImportResult> {
        try {
            const { fileBuffer, originalName = 'import.xlsx' } = formData;

            // Đọc file Excel từ buffer
            const workbook = new Excel.Workbook();
            await workbook.xlsx.load(fileBuffer);

            // Lấy sheet đầu tiên
            const worksheet = workbook.worksheets[0];

            if (!worksheet) {
                throw new HttpException('File Excel không có sheet nào', HttpStatus.BAD_REQUEST);
            }

            const phoneNumbers: string[] = [];
            const errors: ErrorRecord[] = [];
            const phoneToEmployeeMap = new Map(); // Map để lưu thông tin nhân viên theo phone

            // Đọc dữ liệu từ Excel (bỏ qua header row)
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber === 1) return; // Skip header

                // Đọc thông tin từ các cột
                const sttCell = row.getCell(1); // STT
                const maSieuThiCell = row.getCell(2); // MÃ SIÊU THỊ
                const tenSieuThiCell = row.getCell(3); // TÊN SIÊU THỊ
                const maNhanVienCell = row.getCell(4); // MÃ NHÂN VIÊN
                const tenNhanVienCell = row.getCell(5); // TÊN NHÂN VIÊN
                const chucVuCell = row.getCell(6); // CHỨC VỤ
                const phoneCell = row.getCell(7); // SĐT (cột G)

                // Helper function để lấy giá trị thực tế từ cell
                const getCellValue = (cell) => {
                    if (!cell || cell.value === null || cell.value === undefined) return '';

                    // Nếu là object (formula result, hyperlink, etc.), lấy result hoặc text
                    if (typeof cell.value === 'object') {
                        return cell.value.result?.toString().trim() ||
                               cell.value.text?.toString().trim() ||
                               cell.text?.toString().trim() || '';
                    }

                    return cell.value.toString().trim();
                };

                const phoneValue = getCellValue(phoneCell);

                // Lấy thông tin nhân viên từ các cột
                const employeeInfo = {
                    stt: getCellValue(sttCell),
                    maSieuThi: getCellValue(maSieuThiCell),
                    tenSieuThi: getCellValue(tenSieuThiCell),
                    maNhanVien: getCellValue(maNhanVienCell),
                    tenNhanVien: getCellValue(tenNhanVienCell),
                    chucVu: getCellValue(chucVuCell),
                };

                if (!phoneValue) {
                    errors.push({
                        row: rowNumber,
                        phone: '',
                        error: 'Số điện thoại không được để trống',
                        reason: 'INVALID_PHONE',
                        employeeInfo
                    });
                    return;
                }

                // Validate phone number format
                const phoneRegex = /^(\+84|84|0)[3-9]\d{8}$/;
                if (!phoneRegex.test(phoneValue)) {
                    errors.push({
                        row: rowNumber,
                        phone: phoneValue,
                        error: 'Số điện thoại không đúng định dạng',
                        reason: 'INVALID_PHONE',
                        employeeInfo
                    });
                    return;
                }

                // Normalize phone number
                const normalizedPhone = phoneValue
                    .replace(/^(\+84|84)/, '0')
                    .replace(/^0/, '+84');

                // Check for duplicates in current file
                if (phoneNumbers.includes(normalizedPhone)) {
                    errors.push({
                        row: rowNumber,
                        phone: phoneValue,
                        error: 'Số điện thoại bị trùng lặp trong file',
                        reason: 'DUPLICATE',
                        employeeInfo
                    });
                    return;
                }

                // Lưu thông tin nhân viên theo phone để dùng sau
                phoneToEmployeeMap.set(normalizedPhone, employeeInfo);
                phoneNumbers.push(normalizedPhone);
            });

            // Kiểm tra user đã đăng ký trong hệ thống
            const registeredUsers = await this.userModel.find({
                $or: [
                    { username: { $in: phoneNumbers } },
                    { phone: { $in: phoneNumbers } }
                ]
            }).select('_id username phone').lean();

            // Tạo map để dễ dàng tìm user theo phone
            const phoneToUserMap = new Map();
            registeredUsers.forEach(user => {
                if (user.username) phoneToUserMap.set(user.username, user);
                if (user.phone) phoneToUserMap.set(user.phone, user);
                // Thêm các variant của phone number
                if (user.username && user.username.startsWith('+84')) {
                    phoneToUserMap.set(user.username.replace('+84', '0'), user);
                    phoneToUserMap.set(user.username.replace('+84', '84'), user);
                }
                if (user.phone && user.phone.startsWith('+84')) {
                    phoneToUserMap.set(user.phone.replace('+84', '0'), user);
                    phoneToUserMap.set(user.phone.replace('+84', '84'), user);
                }
            });

            const validUserIds: string[] = [];
            const validPhones: string[] = [];
            const addedToAnKhang: string[] = [];

            phoneNumbers.forEach((phone) => {
                const originalPhone = phone.replace('+84', '0');
                const phone84 = phone.replace('+84', '84');

                // Tìm user theo các variant của phone
                const user = phoneToUserMap.get(phone) ||
                           phoneToUserMap.get(originalPhone) ||
                           phoneToUserMap.get(phone84);

                if (user) {
                    validUserIds.push(user._id.toString());
                    validPhones.push(phone);
                    addedToAnKhang.push(user._id.toString());
                } else {
                    // Lấy thông tin nhân viên từ map
                    const employeeInfo = phoneToEmployeeMap.get(phone);
                    errors.push({
                        row: phoneNumbers.indexOf(phone) + 2, // +2 because we skip header and array is 0-indexed
                        phone: phone,
                        error: 'Số điện thoại chưa đăng ký tài khoản Medpro',
                        reason: 'NOT_REGISTERED',
                        employeeInfo
                    });
                }
            });

            // Nếu có user hợp lệ, thêm vào danh sách An Khang và update isCs = true
            if (validUserIds.length > 0) {
                try {
                    // 1. Update isCs = true cho tất cả users
                    await this.userModel.updateMany(
                        { _id: { $in: validUserIds } },
                        { $set: { isCS: true } }
                    );

                    // 2. Lấy danh sách An Khang hiện tại
                    const currentAnKhangString = await this.globalSettingService.findByKeyAndRepoName('AN_KHANG_CSKH_USER');

                    let currentAnKhangList: string[] = [];
                    if (currentAnKhangString) {
                        currentAnKhangList = currentAnKhangString
                            .split(',')
                            .map(id => id.trim())
                            .filter(id => id.length > 0);
                    }

                    // 3. Thêm user IDs mới vào danh sách (loại bỏ duplicate)
                    const uniqueUserIds = [...new Set([...currentAnKhangList, ...validUserIds])];
                    const newAnKhangString = uniqueUserIds.join(',');

                    // 4. Cập nhật global setting
                    await this.globalSettingService.updateByKeyAndRepoName(
                        { key: 'AN_KHANG_CSKH_USER' },
                        newAnKhangString
                    );

                } catch (updateError) {
                    console.error('Error updating users and An Khang list:', updateError);
                }
            }

            // 1. Gom tất cả branchId/branchName unique
            const branchMap = new Map<string, string>(); // branchId -> branchName
            for (const phone of validPhones) {
                const emp = phoneToEmployeeMap.get(phone);
                if (emp?.maSieuThi) branchMap.set(emp.maSieuThi, emp.tenSieuThi);
            }
            const branchIds = Array.from(branchMap.keys());

            // 2. Query 1 lần lấy hết branch đã có
            const existedBranches = await this.anKhangBranchModel.find({ branchId: { $in: branchIds } }).lean();
            const existedBranchMap = new Map<string, any>(); // branchId -> branchDoc
            existedBranches.forEach(b => existedBranchMap.set(b.branchId, b));

            // 3. Tạo bulk các branch chưa có
            const newBranches = branchIds.filter(bid => !existedBranchMap.has(bid)).map(bid => ({ branchId: bid, branchName: branchMap.get(bid) }));
            let insertedBranches: any[] = [];
            if (newBranches.length > 0) {
                insertedBranches = await this.anKhangBranchModel.insertMany(newBranches);
                insertedBranches.forEach(b => existedBranchMap.set(b.branchId, b));
            }

            // 4. Map branchId -> branchDoc._id
            // existedBranchMap đã có đủ

            // 5. Gom bulk an-khang-users cần insert
            const userBranchPairs: { userId: string, username: string, branchId: string, branchObjId: any }[] = [];
            for (let i = 0; i < validUserIds.length; i++) {
                const userId = validUserIds[i];
                const phone = validPhones[i];
                const emp = phoneToEmployeeMap.get(phone);
                const branchId = emp?.maSieuThi;
                if (!branchId) continue;
                const branchDoc = existedBranchMap.get(branchId);
                if (!branchDoc) continue;
                const user = registeredUsers.find(u => u._id.toString() === userId);
                if (!user) continue;
                userBranchPairs.push({ userId, username: user.username, branchId, branchObjId: branchDoc._id });
            }

            // 6. Query 1 lần lấy hết an-khang-user đã có
            const existedAnKhangUsers = await this.anKhangUserModel.find({
                $or: userBranchPairs.map(ub => ({ user: ub.userId, branch: ub.branchObjId }))
            }).lean();
            const existedUserBranchSet = new Set(existedAnKhangUsers.map(u => `${u.user}_${u.branch}`));

            // 7. Bulk insert những user-branch chưa có
            const toInsert = userBranchPairs.filter(ub => !existedUserBranchSet.has(`${ub.userId}_${ub.branchObjId}`))
                .map(ub => ({
                    username: ub.username,
                    user: ub.userId,
                    branch: ub.branchObjId,
                    branchId: ub.branchId,
                    code: phoneToEmployeeMap.get(validPhones[validUserIds.indexOf(ub.userId)])?.maNhanVien || '',
                }));
            if (toInsert.length > 0) {
                await this.anKhangUserModel.insertMany(toInsert);
            }

            return {
                success: true,
                message: `Import hoàn thành: ${validUserIds.length} thành công, ${errors.length} thất bại`,
                data: {
                    totalProcessed: phoneNumbers.length,
                    successCount: validUserIds.length,
                    errorCount: errors.length,
                    addedToAnKhang,
                    addedUserIds: addedToAnKhang, // User IDs được thêm vào
                    validPhones, // Số điện thoại hợp lệ (để reference)
                    errors
                }
            };

        } catch (error) {
            console.error('Error importing employee Excel from buffer:', error);
            throw new HttpException(
                error?.message || 'Lỗi khi import file Excel',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Create Excel buffer with error records (không lưu file)
     * @param errors - List of error records
     * @returns Excel buffer
     */
    private async createErrorExcelBuffer(errors: ErrorRecord[]): Promise<Buffer> {
        try {
            const workbook = new Excel.Workbook();
            const worksheet = workbook.addWorksheet('Lỗi Import');
            console.log(errors[0])
            // Set headers theo thứ tự yêu cầu
            worksheet.columns = [
                { header: 'STT', key: 'stt', width: 8 },
                { header: 'MÃ SIÊU THỊ', key: 'maSieuThi', width: 15 },
                { header: 'TÊN SIÊU THỊ', key: 'tenSieuThi', width: 25 },
                { header: 'MÃ NHÂN VIÊN', key: 'maNhanVien', width: 15 },
                { header: 'TÊN NHÂN VIÊN', key: 'tenNhanVien', width: 25 },
                { header: 'CHỨC VỤ', key: 'chucVu', width: 15 },
                { header: 'SĐT', key: 'phone', width: 15 },
                { header: 'MÃ LỖI', key: 'reason', width: 15 },
                { header: 'LỖI', key: 'error', width: 50 }
            ];

            // Add data
            errors.forEach((error) => {
                const rowData = {
                    stt: error.employeeInfo?.stt || '',
                    maSieuThi: error.employeeInfo?.maSieuThi || '',
                    tenSieuThi: error.employeeInfo?.tenSieuThi || '',
                    maNhanVien: error.employeeInfo?.maNhanVien || '',
                    tenNhanVien: error.employeeInfo?.tenNhanVien || '',
                    chucVu: error.employeeInfo?.chucVu || '',
                    phone: (error.phone || '').replace(/^\+84/, '0'),
                    reason: this.getReasonText(error.reason),
                    error: error.error
                };

                const addedRow = worksheet.addRow(rowData);

                // Bôi đỏ cột MÃ LỖI và LỖI
                const reasonCell = addedRow.getCell(8); // Cột MÃ LỖI
                const errorCell = addedRow.getCell(9);  // Cột LỖI

                reasonCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFFF0000' } // Màu đỏ
                };
                reasonCell.font = { color: { argb: 'FFFFFFFF' } }; // Chữ trắng

                errorCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFFF0000' } // Màu đỏ
                };
                errorCell.font = { color: { argb: 'FFFFFFFF' } }; // Chữ trắng
            });

            // Style headers
            const headerRow = worksheet.getRow(1);
            headerRow.font = { bold: true };
            headerRow.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FF00C853' }
            };

            // Căn giữa header
            headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

            // Thêm border cho tất cả cells
            worksheet.eachRow((row) => {
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            });

            // Return buffer instead of saving file
            return await workbook.xlsx.writeBuffer() as Buffer;
        } catch (error) {
            console.error('Error creating error Excel buffer:', error);
            throw new HttpException('Lỗi tạo file Excel lỗi', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get reason text in Vietnamese
     * @param reason - Error reason code
     * @returns Vietnamese text
     */
    private getReasonText(reason: string): string {
        const reasonMap = {
            'NOT_REGISTERED': 'Chưa đăng ký',
            'INVALID_PHONE': 'Số điện thoại không hợp lệ',
            'DUPLICATE': 'Trùng lặp',
            'OTHER': 'Lỗi khác'
        };
        return reasonMap[reason] || 'Không xác định';
    }

    /**
     * Lấy danh sách AN_KHANG_CSKH_USER từ global setting
     * @returns Danh sách user IDs và thông tin chi tiết
     */
    async getAnKhangUsersList(): Promise<any> {
        try {
            // Lấy danh sách user IDs từ global setting
            const currentListString = await this.globalSettingService.findByKeyAndRepoName('AN_KHANG_CSKH_USER');

            if (!currentListString) {
                return {
                    success: false,
                    message: 'Không tìm thấy danh sách AN_KHANG_CSKH_USER',
                    data: [],
                };
            }

            // Parse chuỗi user IDs cách nhau bởi dấu phẩy
            let userIds: string[] = [];
            try {
                userIds = currentListString
                    .split(',')
                    .map(id => id.trim())
                    .filter(id => id.length > 0);
            } catch (parseError) {
                return {
                    success: false,
                    message: 'Không tìm thấy danh sách AN_KHANG_CSKH_USER',
                    data: [],
                };
            }

            // Lấy thông tin chi tiết của các users
            const [userCs, userPermissions] = await Promise.all([
                this.userModel
                    .find({_id: { $in: userIds } })
                    .select(['username', 'fullname', 'permissions', 'isCS', 'isCare247', 'email', 'medproId'])
                    .exec(),
                this.userPermissionModel
                    .find()
                    .populate('permissions')
                    .select(['permissions', 'user'])
                    .exec(),
            ]);

            return this.mappingPermission(userCs, userPermissions);

        } catch (error) {
            console.error('Error getting AN_KHANG_CSKH_USER list:', error);
            throw new HttpException(
                'Lỗi khi lấy danh sách AN_KHANG_CSKH_USER',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Thêm user vào danh sách AN_KHANG_CSKH_USER
     * @param userId - User ID cần thêm
     * @returns Kết quả thêm user
     */
    async addUserToAnKhangList(userId: string): Promise<any> {
        try {
            // Kiểm tra user có tồn tại và có isCs = true không
            const user = await this.userModel.findById(userId).select('_id username fullName isCS').lean();
            if (!user) {
                throw new HttpException('Không tìm thấy user với ID này', HttpStatus.NOT_FOUND);
            }

            // Kiểm tra user có phải là CS không
            if (!user.isCS) {
                throw new HttpException('User này không phải CSKH !', HttpStatus.BAD_REQUEST);
            }

            // Lấy danh sách hiện tại
            const currentListString = await this.globalSettingService.findByKeyAndRepoName('AN_KHANG_CSKH_USER');

            let userIds: string[] = [];
            if (currentListString) {
                userIds = currentListString
                    .split(',')
                    .map(id => id.trim())
                    .filter(id => id.length > 0);
            }

            // Kiểm tra user đã có trong danh sách chưa
            if (userIds.includes(userId)) {
                throw new HttpException('User đã có trong danh sách AN_KHANG_CSKH_USER', HttpStatus.BAD_REQUEST);
            }

            // Thêm user vào danh sách
            userIds.push(userId);
            const newListString = userIds.join(',');

            // Cập nhật global setting
            await this.globalSettingService.updateByKeyAndRepoName(
                { key: 'AN_KHANG_CSKH_USER' },
                newListString
            );

            return {
                success: true,
                message: 'Thêm user vào danh sách AN_KHANG_CSKH_USER thành công'
            };

        } catch (error) {
            console.error('Error adding user to AN_KHANG_CSKH_USER list:', error);
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                'Lỗi khi thêm user vào danh sách AN_KHANG_CSKH_USER',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Xóa user khỏi danh sách AN_KHANG_CSKH_USER
     * @param userId - User ID cần xóa
     * @returns Kết quả xóa user
     */
    async removeUserFromAnKhangList(userId: string): Promise<any> {
        try {
            // Lấy danh sách hiện tại
            const currentListString = await this.globalSettingService.findByKeyAndRepoName('AN_KHANG_CSKH_USER');

            if (!currentListString) {
                throw new HttpException('Danh sách AN_KHANG_CSKH_USER trống', HttpStatus.NOT_FOUND);
            }

            let userIds: string[] = currentListString
                .split(',')
                .map(id => id.trim())
                .filter(id => id.length > 0);

            // Kiểm tra user có trong danh sách không
            if (!userIds.includes(userId)) {
                throw new HttpException('User không có trong danh sách AN_KHANG_CSKH_USER', HttpStatus.NOT_FOUND);
            }

            // Xóa user khỏi danh sách
            userIds = userIds.filter(id => id !== userId);
            const newListString = userIds.join(',');

            // Cập nhật global setting
            await this.globalSettingService.updateByKeyAndRepoName(
                { key: 'AN_KHANG_CSKH_USER' },
                newListString
            );

            return {
                success: true,
                message: 'Xóa user khỏi danh sách AN_KHANG_CSKH_USER thành công'
            };

        } catch (error) {
            console.error('Error removing user from AN_KHANG_CSKH_USER list:', error);
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                'Lỗi khi xóa user khỏi danh sách AN_KHANG_CSKH_USER',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
