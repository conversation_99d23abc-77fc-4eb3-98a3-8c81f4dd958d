import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { ClientUtilService } from '../config/client-util.service';
import { ConfigSMSMedproService } from '../config/config.sms.medpro.service';
import { ResetOtpLogDto } from './dto/reset-otp-log.dto';
import { GlobalSettingService } from '../global-setting/global-setting.service';
import { BOOKING_CARE_247_CONSTRAINT, BOOKING_CARE_247_SUCCESS, RESET_OTP_LOGS_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import { IRestOtpLog } from './interfaces/reset-otp-log.interface';
import { InjectModel } from '@nestjs/mongoose';
import { REPAYMENT_LOG_COLLECTION_NAME } from '../message-event/schemas/constants';
import { IRepaymentLog } from '../message-event/interfaces/repayment-log.interface';
import { USER_COLLECTION_NAME } from '../user/schemas/constants';
import { IUser } from '../user/interfaces/user.interface';
import {
    BOOKING_CARE_247,
    BOOKING_COLLECTION_NAME,
    BOOKING_LOCKED_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    VIEW_BOOKING_LOG_COLLECTION_NAME,
} from '../booking-gateway/schemas/constants';
import * as uuid from 'uuid';
import { IBooking } from '../booking-gateway/intefaces/booking.inteface';
import { CalledInfoDto } from './dto/called-info.dto';
import { IBookingLock } from '../booking-gateway/intefaces/booking-locked.inteface';
import { IViewBookingLog } from '../booking-gateway/intefaces/view-booking-log.inteface';
import { HOSPITAL_COLLECTION_NAME, PUSH_NOTIF_CSKH, PUSH_NOTIF_CSKH_CONSTRAINT } from '../hospital-mongo/schemas/constants';
import { IHospital } from '../hospital-mongo/interfaces/hospital.interface';
import { IPayment } from '../booking-gateway/intefaces/payment.inteface';
import { IPatient } from '../patient-mongo/intefaces/patient.inteface';
import { PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { UserService } from '../user/user.service';
import { IBookingCare247Success } from './interfaces/booking-care-247-success.interface';
import { IBookingCare247 } from '../booking-gateway/intefaces/booking-care-247.interface';
import { BookingCare247QueryDto } from './dto/booking-care247-query.dto';
import { groupBy, identity, keyBy, map, pickBy } from 'lodash';
import * as moment from 'moment';
import * as Excel from 'exceljs';
import { BookingCare247CskhRevenueQueryDto } from './dto/booking-care247-cskh-revenue-query.dto';
import { IBookingCare247Constraint } from './interfaces/booking-care247-constraint.interface';
import { MedproCareCancelDto } from './dto/medpro-care-cancel.dto';
import { UrlConfigService } from '../config/config.url.service';
import { BookingGatewayService } from '../booking-gateway/booking-gateway.service';
import _ = require('lodash');
import { IPushNotifCskh } from './interfaces/push-notif-cskh.interface';
import { CskhPushNotif } from './dto/cskh-push-notif.dto';
import { IPushNotifCskhConstraint } from './interfaces/push-notif-cskh-constraint.interface';

@Injectable()
export class CskhService {
    private logger = new Logger(CskhService.name);

    constructor(
        @InjectModel(RESET_OTP_LOGS_COLLECTION_NAME) private resetOtpLogModel: Model<IRestOtpLog>,
        @InjectModel(REPAYMENT_LOG_COLLECTION_NAME) private repaymentLogModel: Model<IRepaymentLog>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(VIEW_BOOKING_LOG_COLLECTION_NAME) private viewBookingLogModel: Model<IViewBookingLog>,
        @InjectModel(BOOKING_LOCKED_COLLECTION_NAME) private bookingLockedModel: Model<IBookingLock>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PUSH_NOTIF_CSKH) private pushNotifCskhModel: Model<IPushNotifCskh>,
        @InjectModel(PUSH_NOTIF_CSKH_CONSTRAINT) private pushNotifCskhConstrainModel: Model<IPushNotifCskhConstraint>,
        @InjectModel(BOOKING_CARE_247_SUCCESS) private care247SuccessModel: Model<IBookingCare247Success>,
        @InjectModel(BOOKING_CARE_247) private readonly bookingCare247Model: Model<IBookingCare247>,
        @InjectModel(BOOKING_CARE_247_CONSTRAINT) private readonly bookingCare247ConstraintModel: Model<IBookingCare247Constraint>,
        private readonly clientUtil: ClientUtilService,
        private readonly configSMSMedproService: ConfigSMSMedproService,
        private globalSettingService: GlobalSettingService,
        private userService: UserService,
        private readonly urlConfigService: UrlConfigService,
        private readonly http: HttpService,
        private readonly bookingGateWayService: BookingGatewayService,
    ) {}

    handleDateForFilter(fromDate: string, toDate: string) {
        const dateStart = fromDate ? moment(fromDate) : moment();
        const dateEnd = toDate ? moment(toDate) : moment();
        // Handle UTC time
        const timeStart = dateStart.set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        }).subtract(7, 'hours').toDate()
        const timeEnd = dateEnd.set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        }).subtract(7, 'hours').toDate();
        return { timeStart, timeEnd };
    }
    
    getPartnerName(partnerId: string, partnerName: string) {
        let name: string
        switch (partnerId) {
            case 'umc':
                name = `${partnerName} - Cơ sở chính`
                break;
            case 'umc2':
                name = `${partnerName} - Cơ sở 2`
                break;
            default:
                name = partnerName
                break;
        }
        return name;
    }

    handleStatusDescription(status: number) {
        switch (status) {
          case -2:
            return 'Đã hủy'
          case 0:
            return 'Chưa thanh toán'
          case 6:
            return 'Thanh toán hộ'
          case 1:
            return 'Đã thanh toán'
          case 2:
            return 'Đã khám'
          default:
            return ''
        }
    }

    async resetOtp(userId: string, body: any) {
        this.resetOtpLogModel.create({ userId, phone: body.phone });
        return this.clientUtil.post(`${this.configSMSMedproService.getMessageHubUrl()}/otp/reset-otp-log`, body);
    }

    async addPhoneBlacklistSms(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');

        const phoneArray = phones.split(',').filter(p => !!p);
        const phoneSet = new Set(phoneArray);
        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);
        for (let p of phoneSplits) {
            phoneSet.add(p);
        }

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'SMS_BLACKLIST_PHONE' }, Array.from(phoneSet).join(','));

        return { isOk: true };
    }

    async removePhoneBlacklistSms(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');

        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);
        const phoneArray = phones.split(',').filter(p => !phoneSplits.includes(p));

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'SMS_BLACKLIST_PHONE' }, phoneArray.join(','));

        return { isOk: true };
    }

    async getPhoneBlacklistSms() {
        const phones = await this.globalSettingService.findByKeyAndRepoName('SMS_BLACKLIST_PHONE');
        return { phones };
    }

    async addBlockedPhone(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('BLOCKED_USER_LIST');

        const phoneArray = phones.split(',').filter(p => !!p);
        const phoneSet = new Set(phoneArray);
        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);
        for (let p of phoneSplits) {
            phoneSet.add(p);
        }

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'BLOCKED_USER_LIST' }, Array.from(phoneSet).join(','));

        return { isOk: true };
    }

    async removeBlockedPhone(body: ResetOtpLogDto) {
        const { phone } = body;

        const phones = await this.globalSettingService.findByKeyAndRepoName('BLOCKED_USER_LIST');

        const phoneSplits = phone
            .split(',')
            .map(e => e.trim())
            .filter(e => !!e);

        const phoneArray = phones.split(',').filter(p => !phoneSplits.includes(p));

        await this.globalSettingService.updateByKeyAndRepoName({ key: 'BLOCKED_USER_LIST' }, phoneArray.join(','));

        return { isOk: true };
    }

    async getBlockedPhone() {
        const phones = await this.globalSettingService.findByKeyAndRepoName('BLOCKED_USER_LIST');
        return { phones };
    }

    async getRepaymentLog(query: any) {
        const { pageSize = 10, pageIndex = 0 } = query;

        let condition = {
            callStatus: { $ne: 1 },
        };

        const [rows, totalRows] = await Promise.all([
            this.repaymentLogModel
                .find(condition)
                .skip(pageSize * pageIndex)
                .limit(Number(pageSize))
                .exec(),
            this.repaymentLogModel.countDocuments(condition),
        ]);

        const data = await Promise.all(
            rows.map(async row => {
                const booking = await this.bookingModel
                    .findOne(
                        { bookingCode: row.bookingCode },
                        {
                            userId: true,
                            date: true,
                            createdAt: true,
                            transactionId: true,
                            partnerId: true,
                        },
                    )
                    .exec();
                const user = await this.userModel.findOne(
                    { _id: booking?.userId },
                    {
                        fullname: true,
                        username: true,
                        isCS: true,
                        email: true,
                        createdAt: true,
                    },
                );
                return {
                    ...row.toObject(),
                    user: user?.toObject(),
                    booking: booking?.toObject(),
                };
            }),
        );

        return {
            rows: data,
            totalRows,
        };
    }

    async updateRepaymentLog(data: any) {
        const { _id, ...rest } = data;
        return this.repaymentLogModel.findByIdAndUpdate(_id, { ...rest }, { new: true }).exec();
    }

    async addCalledInfo(body: CalledInfoDto, user: any) {
        const booking = await this.bookingModel.findById({ _id: body.bookingId }, { confirmCalledInfo: true }).exec();
        booking.set({
            confirmCalledInfo: {
                ...booking.confirmCalledInfo,
                callList: [
                    ...(booking.confirmCalledInfo?.callList || []),
                    {
                        callSuccess: body.callSuccess,
                        note: body.note,
                        userId: user._id,
                        createTime: new Date(),
                    },
                ],
                updateTime: new Date(),
                callSuccess: body.callSuccess,
            },
        });

        await booking.save();
        return booking.confirmCalledInfo;
    }

    async getBookingLocked(query: any) {
        const { pageSize = 10, pageIndex = 0, ...restQuery } = query;

        const condition = {
            ...restQuery,
        };

        const [rows, totalRows] = await Promise.all([
            this.bookingLockedModel
                .find(condition)
                .skip(pageSize * pageIndex)
                .limit(Number(pageSize))
                .exec(),
            this.bookingLockedModel.countDocuments(condition),
        ]);

        return {
            rows,
            totalRows,
        };
    }

    async unlockBooking(body: any) {
        const bookingLock = await this.bookingLockedModel.findOneAndDelete({ _id: body.id }).exec();

        if (bookingLock?.transactionId) {
            await this.viewBookingLogModel.deleteMany({ transactionId: bookingLock.transactionId }).exec();
        }

        return {
            isOk: true,
        };
    }

    async getBookingMedproCare(userId: string, body: BookingCare247QueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { transactionId, cskhUserIdGuide, partnerId, status, fromDate, toDate, pageSize, pageIndex } = body;

            const [care247EmployeeList, medproCarePartnerList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
            ]);
            
            const listIdCs = care247EmployeeList.split(',')

            let listPartnerIds = medproCarePartnerList.split(',')
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm']
            
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate)

            const query: any = {
                partnerId,
                status: !!status && status !== -3 ? status : { $in: [1, -2] },
                date: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
                bookingStatus: status === -3 ? -2 : status === 1 ? { $in: [1, 2] } : null,
                cskhUserIdGuide: cskhUserIdGuide === '-1' ? { $eq: null } : cskhUserIdGuide
            }
            
            const condition = transactionId ? { transactionId } : pickBy(query, identity);

            const [rows, totalRows, care247Cs, hospital] = await Promise.all([
                this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments(condition),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
                this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec()
            ]);

            const data = rows.map((r:any) => {
                const item = r.toObject();
                return { ...item, partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) } }
            })

            const partners = hospital.map((h:any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) }
            })

            const listCsGuide = [
                {
                    id: '',
                    fullname: 'Tất cả'
                },
                {
                    id: '-1',
                    fullname: 'Chưa điều phối'
                },
                ...care247Cs
            ]

            return {
                rows: data,
                totalRows,
                hospital: partners,
                listCsGuide,
                role: 'admin'
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi lấy danh sách phiếu Care247.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getBookingMedproCareCskhRevenue(userId: string, body: BookingCare247CskhRevenueQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { cskhUserId, partnerId, month, pageSize, pageIndex } = body;

            const [care247UserRoleList, cskhEmployeeList, medproCarePartnerList, csCommission] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CSKH_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CSKH')
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',')
            const listUserRole = new Set(care247UserRoleListArr);
            
            const listIdCs = cskhEmployeeList.split(',')
            
            let listPartnerIds = medproCarePartnerList.split(',')
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm']

            const currentMonth = moment().format('YYYY-MM')
            const daysInMonth = month ? moment(month, "YYYY-MM").daysInMonth() : moment(currentMonth, "YYYY-MM").daysInMonth()
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}` 
            
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate)

            let query: any = {
                partnerId,
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] }
            }
            let cskhUserCondition: any
            let userRole: string
            if (listUserRole.has(userId)) {
                query = { ...query, cskhUserId }
                cskhUserCondition = { $in: listIdCs }
                userRole = 'admin'
            } else {
                query = { ...query, cskhUserId: userId }
                cskhUserCondition = userId
                userRole = 'admin'
            }
            
            const condition = pickBy(
                query,
                identity,
            );

            if (!condition?.cskhUserId) {
                throw new HttpException('Không tìm thấy thông tin nhân viên CSKH cần thống kê.', HttpStatus.BAD_REQUEST);                
            }

            const [rows, totalRows, cskhEmployee, hospital] = await Promise.all([
                this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'cskh', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments(condition),
                this.userModel.find({ _id: cskhUserCondition, isCS: true }, { fullname: true, username: true }).exec(),
                this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec()
            ]);

            const data = rows.map((r:any) => {
                const item = r.toObject();
                return { ...item, csCommission: Number(csCommission),
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) }
                }
            })

            const partners = hospital.map((h:any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) }
            })

            return {
                rows: data,
                totalRows,
                hospital: partners,
                listCs: cskhEmployee,
                revenue: totalRows * Number(csCommission),
                role: userRole
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi thống kê.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getBookingMedproCareCare247Revenue(userId: string, body: BookingCare247CskhRevenueQueryDto) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { cskhUserIdGuide, partnerId, month, pageSize, pageIndex } = body;

            const [care247UserRoleList, care247EmployeeList, medproCarePartnerList, care247Commission] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CARE247')
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',')
            const listUserRole = new Set(care247UserRoleListArr);
            
            const listIdCs = care247EmployeeList.split(',')
            
            let listPartnerIds = medproCarePartnerList.split(',')
            listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm']

            const currentMonth = moment().format('YYYY-MM')
            const daysInMonth = month ? moment(month, "YYYY-MM").daysInMonth() : moment(currentMonth, "YYYY-MM").daysInMonth()
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}` 
            
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate)

            let query: any = {
                partnerId,
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] }
            }
            let care247UserCondition: any
            let userRole: string
            if (listUserRole.has(userId)) {
                query = { ...query, cskhUserIdGuide }
                care247UserCondition = { $in: listIdCs }
                userRole = 'admin'
            } else {
                query = { ...query, cskhUserIdGuide: userId }
                care247UserCondition = userId
                userRole = 'admin'
            }
            
            const condition = pickBy(
                query,
                identity,
            );

            if (!condition?.cskhUserIdGuide) {
                throw new HttpException('Không tìm thấy thông tin nhân viên Care247 cần thống kê.', HttpStatus.BAD_REQUEST);                
            }

            const [rows, totalRows, care247Employee, hospital] = await Promise.all([
                this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'booking', select: 'status' })
                    .populate({ path: 'partner', select: 'name' })
                    .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                    .populate({ path: 'user', select: { fullname: true, username: true } })
                    .populate({ path: 'subject', select: { name: true } })
                    .populate({ path: 'service', select: { name: true } })
                    .populate({ path: 'room', select: { name: true } })
                    .populate({ path: 'doctor', select: { name: true } })
                    .sort({ date: 1 })
                    .skip(pageSize * pageIndex)
                    .limit(Number(pageSize))
                    .exec(),
                this.bookingCare247Model.countDocuments(condition),
                this.userModel.find({ _id: care247UserCondition, isCS: true }, { fullname: true, username: true }).exec(),
                this.hospitalModel.find({ partnerId: { $in: listPartnerIds } }, { name: true, partnerId: true }).exec()
            ]);

            const data = rows.map((r:any) => {
                const item = r.toObject();
                return { ...item, care247Commission: Number(care247Commission),
                    partner: { ...item.partner, name: this.getPartnerName(item.partnerId, item.partner.name) }
                }
            })

            const partners = hospital.map((h:any) => {
                const item = h.toObject();
                return { ...item, name: this.getPartnerName(item.partnerId, item.name) }
            })

            return {
                rows: data,
                totalRows,
                hospital: partners,
                listCs: care247Employee,
                revenue: totalRows * Number(care247Commission),
                role: userRole
            };
        } catch (error) {
            console.log('error', error);
            const message = error?.message || 'Lỗi thống kê.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async cancelBookingMedproCare(cskhUserId: string, body: MedproCareCancelDto) {
        try {
            const isCS = await this.userService.isCs(cskhUserId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { id, cancelReason } = body;
            const bookingCare247 = await this.bookingCare247Model.findOne({ _id: id }).exec();
            if (!bookingCare247) {
                throw new HttpException(`Không tìm thấy thông tin Care247.`, HttpStatus.BAD_REQUEST);
            }

            const userCs = await this.userModel.findById({ _id: cskhUserId });
            let userCsObj = userCs.toObject();

            let cancelInfo = {
                fullname: userCsObj.fullname,
                mobile: userCsObj.username,
                cancelReason
            };

            const newData = await this.bookingCare247Model.findOneAndUpdate({ _id: id }, { status: -2, cancelInfo }, { new: true }).exec();
            //ban lark group khi cskh huy DV Care247
            try {
                const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/notification/cancel-care247`;
                await this.http.post(url, { bookingId: newData.bookingId }).toPromise()
            } catch (error) {
                this.logger.error(`care247-after-success ${id}`);
            }

            return newData;
        } catch (error) {
            const message = error?.message || 'Hủy dịch vụ Medpro Care gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    // async insertBookingCare247Success() {
    //     const condition = {
    //         $or: [{ medproCare: { $exists: true, $ne: null } }, { care247: { $exists: true, $ne: null } }],
    //         status: 1,
    //         'medproCare.status': { $ne: 0 },
    //     };

    //     const data = await this.bookingModel.find(condition).exec();

    //     const care247Success = data.map(d => {
    //         const bookingCode = d?.bookingCodeV1 || d.bookingCode;
    //         return {
    //             userId: d.userId,
    //             booking: d._id,
    //             transactionId: d?.transactionId,
    //             bookingCode: bookingCode,
    //             patient: d?.patient,
    //             patientVersion: d?.patientVersion,
    //             subject: d?.subject,
    //             service: d?.service,
    //             doctor: d?.doctor,
    //             room: d?.room,
    //             date: d?.date,
    //             partner: d?.partner,
    //         };
    //     });

    //     await this.care247SuccessModel.insertMany(care247Success);
    // }

    async seedDataBookingCare247() {
        try {
            const condition = {
                medproCare: { $exists: true, $ne: null },
                // status: { $in: [1, -2, 2] },
                status: 2,
                paymentStatus: 2
            };
            const data = await this.bookingModel.find(condition).exec();
            const care247Info = await this.globalSettingService.findByKeyAndRepoName('CARE_247_INFO')
            const care247InfoJson = JSON.parse(care247Info);
            
            for await (const booking of data) {
                const bookingObj = booking.toObject();
                try {
                    const bookingCare247Constraint = new this.bookingCare247ConstraintModel({
                        transactionId: bookingObj.transactionId,
                    });
                    await bookingCare247Constraint.save();

                    const { medproCare } = bookingObj;
                    const bookingCode = bookingObj?.bookingCodeV1 || bookingObj?.bookingCode;
                    const newBookingCare247 = new this.bookingCare247Model({
                        bookingId: bookingObj._id,
                        booking: bookingObj._id,
                        bookingCode: bookingCode,
                        bookingStatus: bookingObj.status,
                        patient: bookingObj.patient,
                        patientId: bookingObj.patientId,
                        patientVerSion: bookingObj.patientVerSion,
                        patientVersionId: bookingObj.patientVersionId,
                        userId: bookingObj.userId,
                        user: bookingObj.userId,
                        appId: bookingObj.appId,
                        partnerId: bookingObj.partnerId,
                        partner: bookingObj.partner,
                        subject: bookingObj.subject,
                        service: bookingObj.service,
                        doctor: bookingObj.doctor,
                        room: bookingObj.room,
                        section: bookingObj.section,
                        date: bookingObj.date,
                        cskhUserId: bookingObj.cskhUserId,
                        cskh: bookingObj.cskhUserId,
                        platform: bookingObj.platform,
                        name: medproCare.name,
                        id: uuid.v4().replace(/-/g, ''),
                        addonServices: medproCare.addonServices,
                        note: medproCare.note,
                        medproCareNote: medproCare.medproCareNote,
                        status: 1,
                        transactionId: bookingObj.transactionId,
                        type: 'original',
                        provider: care247InfoJson
                    });
                    await newBookingCare247.save();
                } catch (error) {
                    console.log('error seed data', error);
                }
            }
            return {
                isOk: true
            }
        } catch (error) {
            console.log('error', error);
        }
    }

    async cskhRevenueReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month } = formData;

            const [care247UserRoleList, cskhEmployeeList, csCommission] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CSKH_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CSKH')
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',')
            const listUserRole = new Set(care247UserRoleListArr);
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }
            const listIdCs = cskhEmployeeList.split(',')

            const currentMonth = moment().format('YYYY-MM')
            const daysInMonth = month ? moment(month, "YYYY-MM").daysInMonth() : moment(currentMonth, "YYYY-MM").daysInMonth()
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}` 
            
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate)

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] }
            }

            const [dataCare247, cskhEmployees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
            ]);
            
            const convertData = map(
                groupBy(dataCare247, 'cskhUserId'),
                (items, cskhUserId) => ({
                    cskhUserId,
                    bookings: items.length,
                    commission: items.length * Number(csCommission),
                })
            );
            const convertDataByUser = keyBy(convertData, 'cskhUserId');
            const result = cskhEmployees.map((employee) => ({
                ...employee.toObject(),
                bookings: convertDataByUser[employee._id]?.bookings || 0,
                commission: convertDataByUser[employee._id]?.commission || 0,
                month
            }));

            return result
        } catch (error) {
            const message = error?.message || 'Báo cáo doanh thu nhân viên CSKH gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async care247RevenueReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month } = formData;

            const [care247UserRoleList, care247EmployeeList, care247Commission] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('COMMISSION_CARE247')
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',')
            const listUserRole = new Set(care247UserRoleListArr);
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }
            const listIdCs = care247EmployeeList.split(',')

            const currentMonth = moment().format('YYYY-MM')
            const daysInMonth = month ? moment(month, "YYYY-MM").daysInMonth() : moment(currentMonth, "YYYY-MM").daysInMonth()
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}` 
            
            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate)

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] }
            }

            const [dataCare247, care247Employees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
            ]);
            
            const convertData = map(
                groupBy(dataCare247, 'cskhUserIdGuide'),
                (items, cskhUserIdGuide) => ({
                    cskhUserIdGuide,
                    bookings: items.length,
                    commission: items.length * Number(care247Commission),
                })
            );
            const convertDataByUser = keyBy(convertData, 'cskhUserIdGuide');
            const result = care247Employees.map((employee) => ({
                ...employee.toObject(),
                bookings: convertDataByUser[employee._id]?.bookings || 0,
                commission: convertDataByUser[employee._id]?.commission || 0,
                month
            }));

            return result
        } catch (error) {
            const message = error?.message || 'Báo cáo doanh thu nhân viên Care247 gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getStatisticsEmployeesByMotnh(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
            const { month } = formData;

            const [care247UserRoleList, care247EmployeeList, salaryList, targetAndRange] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
                this.globalSettingService.findByKeyAndRepoName('EMPLOYEE_CARE247_SALARY_LIST'),
                this.globalSettingService.findByKeyAndRepoName('TARGET_AND_RANGE_CONFIG'),
            ]);

            let targetAndRangeObj = targetAndRange ? JSON.parse(targetAndRange) : {};
            const care247UserRoleListArr = care247UserRoleList.split(',');
            const salaryListArr = new Set(salaryList.split(','));
            const listUserRole = new Set(care247UserRoleListArr);

            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }

            const listIdCs = care247EmployeeList.split(',');

            const currentMonth = moment().format('YYYY-MM');
            const daysInMonth = month ? moment(month, 'YYYY-MM').daysInMonth() : moment(currentMonth, 'YYYY-MM').daysInMonth();
            const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
            const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            const [dataCare247, care247Employees] = await Promise.all([
                this.bookingCare247Model.find(condition).exec(),
                this.userModel.find({ _id: { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
            ]);

            const convertData = map(groupBy(dataCare247, 'cskhUserIdGuide'), (items, cskhUserIdGuide) => ({
                cskhUserIdGuide,
                bookings: items.length,
            }));
            const convertDataByUser = keyBy(convertData, 'cskhUserIdGuide');

            const result = care247Employees.map(employee => ({
                ...employee.toObject(),
                bookings: convertDataByUser[employee._id]?.bookings || 0,
                target: targetAndRangeObj?.target || [0, 25],
                range: targetAndRangeObj?.range || [0, 100],
                month,
            }));
            result.sort((a, b) => b.bookings - a.bookings);

            const employeeCare247 = [];
            const employeeCskh = [];

            for (const employee of result) {
                if (salaryListArr.has(`${employee._id}`)) {
                    employeeCare247.unshift(employee);
                } else {
                    employeeCskh.unshift(employee);
                }
            }

            return {
                data: {
                    employeeCare247,
                    employeeCskh,
                },
            };
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async getAnnualBookingReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }
    
            const { year, partnerId } = formData;
    
            const [care247UserRoleList, care247EmployeeList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            ]);
    
            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }
    
            // Khởi tạo mảng kết quả để lưu tổng số booking theo tháng và từng bệnh viện
            const result: Array<{ month: string; total: number; partner?: string }> = [];
    
            // Lặp qua từng tháng trong năm
            for (let month = 1; month <= 12; month++) {
                const monthString = month.toString().padStart(2, '0');
                const fromDate = `${year}-${monthString}-01`;
                const daysInMonth = moment(fromDate, 'YYYY-MM-DD').daysInMonth();
                const toDate = `${year}-${monthString}-${daysInMonth}`;
    
                const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);
    
                const condition: any = pickBy({
                    status: 1,
                    date: { $gte: timeStart, $lte: timeEnd },
                    bookingStatus: { $in: [1, 2] },
                    partnerId
                },identity);

                // Lấy dữ liệu booking trong tháng
                const dataCare247 = await this.bookingCare247Model
                    .find(condition)
                    .populate({ path: 'partner', select: 'name' })
                    .exec();
    
                // Tổng số booking trong tháng
                const totalBookings = dataCare247.length;
    
                // Nhóm booking theo partnerId (bệnh viện)
                const hospitalTotals = dataCare247.reduce((acc: any[], booking: any) => {
                    const partnerId = booking.partnerId;
                    const existingHospital = acc.find(item => item.partner === partnerId);
    
                    if (existingHospital) {
                        existingHospital.total += 1;
                    } else {
                        acc.push({
                            total: 1,
                            partner: partnerId,
                        });
                    }
    
                    return acc;
                }, [] as { total: number; partner: string }[]);
    
                // Nếu không có booking nào trong tháng, thêm kết quả tháng với total = 0
                if (totalBookings === 0) {
                    result.push({
                        month: `${month}`,
                        total: 0,
                    });
                } else {
                    // Thêm kết quả cho mỗi bệnh viện trong tháng
                    hospitalTotals.forEach(hospital => {
                        result.push({
                            month: `${month}`,
                            total: hospital.total,
                            partner: hospital.partner,
                        });
                    });
                }
            }
    
            return result;
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }
    

    async getMonthBookingReport(userId: string, formData: any) {
        try {
            const isCS = await this.userService.isCs(userId);
            if (!isCS) {
                throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
            }

            const { month } = formData;

            const [care247UserRoleList, care247EmployeeList] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
                this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            ]);

            const care247UserRoleListArr = care247UserRoleList.split(',');
            const listUserRole = new Set(care247UserRoleListArr);
            if (!listUserRole.has(userId)) {
                throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
            }

            // Tính ngày bắt đầu và ngày kết thúc của tháng
            const currentMonth = moment().format('YYYY-MM');
            const selectedMonth = month || currentMonth;
            const daysInMonth = moment(selectedMonth, 'YYYY-MM').daysInMonth();
            const fromDate = `${selectedMonth}-01`;
            const toDate = `${selectedMonth}-${daysInMonth}`;

            const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

            const condition: any = {
                status: 1,
                date: { $gte: timeStart, $lte: timeEnd },
                bookingStatus: { $in: [1, 2] },
            };

            // Lấy dữ liệu booking trong khoảng thời gian của tháng
            const dataCare247 = await this.bookingCare247Model
                .find(condition)
                .populate({ path: 'partner', select: 'name' })
                .exec();

            // Khởi tạo mảng kết quả để lưu tổng số booking theo từng ngày và từng bệnh viện
            const dailyTotals: Array<{ day: string; total: number; hospitals: Record<string, { total: number }> }> = [];

            // Duyệt qua từng ngày trong tháng để đếm số booking cho từng ngày
            for (let day = 1; day <= daysInMonth; day++) {
                const dayString = day.toString().padStart(2, '0');
                const currentDate = `${selectedMonth}-${dayString}`;
                const startOfDay = moment(currentDate)
                    .startOf('day')
                    .toDate();
                const endOfDay = moment(currentDate)
                    .endOf('day')
                    .toDate();

                // Lọc các booking trong ngày hiện tại
                const dailyBookings = dataCare247.filter(booking => booking.date >= startOfDay && booking.date <= endOfDay);

                // Tổng số booking trong ngày
                const dailyBookingCount = dailyBookings.length;

                // Nhóm booking theo partnerId (bệnh viện)
                const hospitalTotals = dailyBookings.reduce((acc: any, booking: any) => {
                    const partnerId = booking.partnerId;
                    if (!acc[partnerId]) {
                        acc[partnerId] = { total: 0 };
                    }
                    acc[partnerId].total += 1;
                    acc[partnerId].name = booking?.partner?.name;

                    return acc;
                }, {} as Record<string, { total: number }>);

                // Thêm dữ liệu của ngày vào mảng kết quả
                dailyTotals.push({
                    day: `${day}`,
                    total: dailyBookingCount,
                    hospitals: hospitalTotals,
                });
            }

            return dailyTotals;
        } catch (error) {
            const message = error?.message || 'Thống kê gặp lỗi. Vui lòng thử lại sau.';
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    async bookingMedproCareExportExcel(userId: string, formData: any): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        if (!isCS) {
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng xin cấp thêm quyền.', HttpStatus.FORBIDDEN);
        }
        const { transactionId, cskhUserIdGuide, partnerId, status, fromDate, toDate } = formData;

        const [care247UserRoleList, medproCarePartnerList] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
            this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST'),
        ]);
        const care247UserRoleListArr = care247UserRoleList.split(',');
        const listUserRole = new Set(care247UserRoleListArr);
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }
        let listPartnerIds = medproCarePartnerList.split(',')
        listPartnerIds = [...listPartnerIds, 'umc', 'choray', 'dalieuhcm']
        
        const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate)

        const query: any = {
            partnerId,
            status: !!status && status !== -3 ? status : { $in: [1, -2] },
            date: fromDate && toDate && { $gte: timeStart, $lte: timeEnd },
            bookingStatus: status === -3 ? -2 : status === 1 ? { $in: [1, 2] } : null,
            cskhUserIdGuide: cskhUserIdGuide === '-1' ? { $eq: null } : cskhUserIdGuide
        }
        
        const condition = transactionId ? { transactionId } : pickBy(query, identity);

        const dataCare247 = await this.bookingCare247Model
                        .find(condition)
                        .populate({ path: 'booking', select: 'status' })
                        .populate({ path: 'partner', select: 'name' })
                        .populate({ path: 'patient', select: { name: true, surname: true, mobile: true, code: true } })
                        .populate({ path: 'user', select: { fullname: true, username: true } })
                        .populate({ path: 'cskh', select: { fullname: true, username: true } })
                        .populate({ path: 'subject', select: { name: true } })
                        .populate({ path: 'service', select: { name: true } })
                        .populate({ path: 'room', select: { name: true } })
                        .populate({ path: 'doctor', select: { name: true } })
                        .sort({ date: 1 })
                        .exec();

        //Create data excel
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet('Danh sách DV Care247');
        worksheet.columns = [
            { header: 'STT', key: 'stt', width: 25 },
            { header: 'Bệnh nhân', key: 'patient', width: 40 },
            { header: 'Tài khoản', key: 'user', width: 25 },
            { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
            { header: 'Trạng thái', key: 'bookingStatus', width: 25 },
            { header: 'Cơ sở y tế', key: 'partner', width: 35 },
            { header: 'partnerId', key: 'partnerId', width: 20 },
            { header: 'Dịch vụ', key: 'service', width: 45 },
            { header: 'Chuyên khoa', key: 'subject', width: 45 },
            { header: 'Phòng khám', key: 'room', width: 45 },
            { header: 'Ngày khám', key: 'date', width: 20 },
            { header: 'Platform', key: 'platform', width: 20 },
            { header: 'DV Care247', key: 'status', width: 25 },
            { header: 'Gói DV', key: 'care247Service', width: 35 },
            { header: 'Nhân viên CS', key: 'cskh', width: 35 },
            { header: 'Nhân viên Care247', key: 'instructor', width: 35 },
            { header: 'Mã giao dịch', key: 'transactionId', width: 25 },
        ];

        const dataMap = dataCare247.map((item:any, index:number) => ({
            stt: index + 1,
            patient: `${item?.patient?.surname} ${item?.patient?.name} - ${item?.patient?.mobile}`,
            user: `${item?.user?.fullname} ${item?.user?.username}`,
            bookingCode: item.bookingCode,
            bookingStatus: this.handleStatusDescription(item.bookingStatus),
            partner: this.getPartnerName(item.partnerId, item.partner.name),
            partnerId: item.partnerId,
            service: item?.service?.name,
            subject: item?.subject?.name,
            room: item?.room?.name,
            date: moment(item.date).format('HH:mm DD/MM/YYYY'),
            serviceName: item.addonServices[0].name,
            platform: item?.platform,
            status: this.handleStatusDescription(item.status),
            care247Service: item.addonServices[0].name,
            cskh: `${item?.cskh?.fullname} ${item?.cskh?.username}`,
            instructor: `${item?.instructor?.fullname} ${item?.instructor?.username}`,
            transactionId: item.transactionId
        }));
        
        dataMap.forEach(row => {
            worksheet.addRow(row);
        });

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer; 
    }

    async cskhRevenueExportExcel(userId: string, formData: any): Promise<any> {
        const { month, cskhUserId } = formData;
        const currentMonth = moment().format('YYYY-MM');
        const daysInMonth = month ? moment(month, "YYYY-MM").daysInMonth() : moment(currentMonth, "YYYY-MM").daysInMonth();
        const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
        const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;
        const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

        const [care247UserRoleList, cskhEmployeeList, csCommission] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
            this.globalSettingService.findByKeyAndRepoName('CSKH_EMPLOYEE_LIST'),
            this.globalSettingService.findByKeyAndRepoName('COMMISSION_CSKH')
        ]);
        const care247UserRoleListArr = care247UserRoleList.split(',');
        const listUserRole = new Set(care247UserRoleListArr);
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }
        const listIdCs = cskhEmployeeList.split(',');
        const [dataCare247, cskhEmployees] = await Promise.all([
            this.bookingCare247Model
                .find({
                    status: 1,
                    date: { $gte: timeStart, $lte: timeEnd },
                    bookingStatus: { $in: [1, 2] }
                })
                .populate({ path: 'partner', select: 'name' })
                .populate({ path: 'cskh', select: { fullname: true, username: true } })
                .sort({ date: 1 })
                .exec(),
            this.userModel.find({ _id: cskhUserId ? cskhUserId : { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
        ]);

        //Create data excel
        const workbook = new Excel.Workbook();
        for await (const user of cskhEmployees) {
            const userObj = user.toObject()

            const worksheet = workbook.addWorksheet(userObj.fullname);
            worksheet.columns = [
                { header: 'STT', key: 'stt', width: 25 },
                { header: 'Nhân viên CSKH', key: 'name', width: 40 },
                { header: 'SĐT', key: 'phone', width: 25 },
                { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
                { header: 'Trạng thái', key: 'status', width: 25 },
                { header: 'Cơ sở y tế', key: 'partner', width: 35 },
                { header: 'partnerId', key: 'partnerId', width: 20 },
                { header: 'Dịch vụ Care247', key: 'serviceName', width: 45 },
                { header: 'Thời gian khám', key: 'date', width: 20 },
                { header: 'Hoa hồng', key: 'commission', width: 25 },
            ];

            const dataMap = dataCare247.filter(e => e.cskhUserId === `${userObj._id}`).map((item:any, index:number) => ({
                stt: index + 1,
                name: item.cskh.fullname,
                phone: item.cskh.username,
                bookingCode: item.bookingCode,
                status: 'Đã thanh toán',
                partner: this.getPartnerName(item.partnerId, item.partner.name),
                partnerId: item.partnerId,
                serviceName: item.addonServices[0].name,
                date: moment(item.date).format('YYYY-MM-DD'),
                commission: this.bookingGateWayService.getPriceText(+csCommission, '')
            }));
            
            dataMap.forEach(row => {
                worksheet.addRow(row);
            });
        }

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer; 
    }

    async care247RevenueExportExcel(userId: string, formData: any): Promise<any> {
        const { month, cskhUserIdGuide } = formData;
        const currentMonth = moment().format('YYYY-MM');
        const daysInMonth = month ? moment(month, "YYYY-MM").daysInMonth() : moment(currentMonth, "YYYY-MM").daysInMonth();
        const fromDate = month ? `${month}-01` : `${currentMonth}-01`;
        const toDate = month ? `${month}-${daysInMonth}` : `${currentMonth}-${daysInMonth}`;
        const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

        const [care247UserRoleList, care247EmployeeList, care247Commission] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CARE247_ROLE'),
            this.globalSettingService.findByKeyAndRepoName('CARE247_EMPLOYEE_LIST'),
            this.globalSettingService.findByKeyAndRepoName('COMMISSION_CARE247')
        ]);
        const care247UserRoleListArr = care247UserRoleList.split(',');
        const listUserRole = new Set(care247UserRoleListArr);
        if (!listUserRole.has(userId)) {
            throw new HttpException('Bạn chưa được cấp quyền thực hiện thao tác này.', HttpStatus.FORBIDDEN);
        }
        const listIdCs = care247EmployeeList.split(',');
        const [dataCare247, care247Employees] = await Promise.all([
            this.bookingCare247Model
                .find({
                    status: 1,
                    date: { $gte: timeStart, $lte: timeEnd },
                    bookingStatus: { $in: [1, 2] }
                })
                .populate({ path: 'partner', select: 'name' })
                .populate({ path: 'cskh', select: { fullname: true, username: true } })
                .sort({ date: 1 })
                .exec(),
            this.userModel.find({ _id: cskhUserIdGuide ? cskhUserIdGuide : { $in: listIdCs }, isCS: true }, { fullname: true, username: true }).exec(),
        ]);

        //Create data excel
        const workbook = new Excel.Workbook();
        for await (const user of care247Employees) {
            const userObj = user.toObject()

            const worksheet = workbook.addWorksheet(userObj.fullname);
            worksheet.columns = [
                { header: 'STT', key: 'stt', width: 25 },
                { header: 'Nhân viên Care247', key: 'name', width: 40 },
                { header: 'SĐT', key: 'phone', width: 25 },
                { header: 'Mã phiếu', key: 'bookingCode', width: 25 },
                { header: 'Trạng thái', key: 'status', width: 25 },
                { header: 'Cơ sở y tế', key: 'partner', width: 35 },
                { header: 'partnerId', key: 'partnerId', width: 20 },
                { header: 'Dịch vụ Care247', key: 'serviceName', width: 45 },
                { header: 'Thời gian khám', key: 'date', width: 20 },
                { header: 'Hoa hồng', key: 'commission', width: 25 },
            ];

            const dataMap = dataCare247.filter(e => e.cskhUserIdGuide === `${userObj._id}`).map((item:any, index:number) => ({
                stt: index + 1,
                name: item.instructor.fullname,
                phone: item.instructor.username,
                bookingCode: item.bookingCode,
                status: 'Đã thanh toán',
                partner: this.getPartnerName(item.partnerId, item.partner.name),
                partnerId: item.partnerId,
                serviceName: item.addonServices[0].name,
                date: moment(item.date).format('YYYY-MM-DD'),
                commission: this.bookingGateWayService.getPriceText(+care247Commission, '')
            }));
            
            dataMap.forEach(row => {
                worksheet.addRow(row);
            });
        }

        const buffer = await workbook.xlsx.writeBuffer();
        return buffer; 
    }

    async pushNotifCskh(userMongoId: string, formData: CskhPushNotif) {
        const { onesignalId } = formData;
    
        try {
            const notifCskhConstraint = new this.pushNotifCskhConstrainModel({
                onesignalId,
                userId:userMongoId
            });
    
            await notifCskhConstraint.save();
    
            const newOnesignalId = new this.pushNotifCskhModel({
                onesignalId,
                userId: userMongoId,
            });
    
            await newOnesignalId.save();
            return { isOk: true, message: 'Lưu onesignalId thành công' };
    
        } catch (err) {
            console.log('err', err)
            if (err.code === 11000) {
                return { isOk: false, message: 'OnesignalId đã tồn tại cho user này' };
            }
            return { isOk: false, message: 'Có lỗi xãy ra vui lòng thử lại sau!', error: err };
        }
    }
}
