import { ApiProperty } from '@nestjs/swagger';

export class UploadHealthDataDto {
    @ApiProperty({
        type: 'string',
        format: 'binary',
        description: 'Excel file containing health data'
    })
    file: any;
}

export class UploadHealthDataResponseDto {
    @ApiProperty({ description: 'Upload status' })
    success: boolean;

    @ApiProperty({ description: 'Response message' })
    message: string;

    @ApiProperty({ description: 'File information', required: false })
    fileInfo?: {
        filename: string;
        size: number;
        mimetype: string;
        uploadedAt: Date;
    };

    @ApiProperty({ description: 'Processing result', required: false })
    data?: any;
}