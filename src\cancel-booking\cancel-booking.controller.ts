import {
    Body,
    Controller,
    Get,
    Headers,
    HttpCode,
    HttpException,
    HttpStatus,
    Param,
    Post,
    Req,
    Res,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Api<PERSON><PERSON><PERSON>A<PERSON>, <PERSON>piHeader, ApiOperation } from '@nestjs/swagger';
import { AppCskhInterceptor } from 'src/middleware/app-cskh.interceptor';
import { CancelBookingService } from './cancel-booking.service';
import { BookingQueryDto } from './dto/bookings-query-dto';
import { CancelBookingDto } from './dto/cancel-booking.dto';
import { ICancellationReasonConfig } from './interfaces/cancel-booking.interfaces';
import { CskhGuard } from 'src/common/guards/cskh.guard';

@Controller('booking')
export class CancelBookingController {
    constructor(private readonly cancelBookingService: CancelBookingService) {}

    @Post('cancel/:bookingId')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Cancel Reservation',
        description: 'Cancel Reservation',
    })
    @HttpCode(HttpStatus.OK)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async cancelReservation(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Param('bookingId') bookingId: string,
        @Body() dto: CancelBookingDto,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.cancelBookingService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return await this.cancelBookingService.cancelBooking(bookingId, dto, partnerid, appid, userMongoId, req.ipInfo, cskhInfo, platform);
    }

    @Post('refund-request/:bookingId')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Cancel Reservation',
        description: 'Cancel Reservation',
    })
    @HttpCode(HttpStatus.OK)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async cancelReservationCskh(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Param('bookingId') bookingId: string,
        @Body() dto: CancelBookingDto,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.cancelBookingService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return await this.cancelBookingService.cancelReservationCskh(bookingId, dto, partnerid, appid, userMongoId, req.ipInfo, cskhInfo, platform);
    }

    @Get('cancellation-reasons')
    @HttpCode(HttpStatus.OK)
    async listCancelledBookings(): Promise<ICancellationReasonConfig[]> {
        return await this.cancelBookingService.getCancellationReasons();
    }

    @Get('refund-reasons')
    @HttpCode(HttpStatus.OK)
    async getCancellationReasonsCskh(): Promise<ICancellationReasonConfig[]> {
        return await this.cancelBookingService.getCancellationReasonsCskh();
    }

    @Get('account-type')
    @HttpCode(HttpStatus.OK)
    async listAccountType(): Promise<any> {
        return await this.cancelBookingService.getAccountType();
    }

    @Get('required-bank-account')
    @HttpCode(HttpStatus.OK)
    async getRequiredBankAccount(): Promise<any> {
        return await this.cancelBookingService.getRequiredBankAccount();
    }

    @Post('cancellations')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Lấy danh sách booking đã hủy với thông tin lý do và bank account',
        description: 'Lấy danh sách booking đã hủy với thông tin lý do và bank account từ unified collection',
    })
    async getCancellations(@Body() body: BookingQueryDto): Promise<any> {
        return this.cancelBookingService.getCancellations(body);
    }

    @Post('cancellations/export-excel')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'),CskhGuard)
    @ApiOperation({
        summary: 'Xuất Excel danh sách booking đã hủy',
        description: 'Xuất file Excel danh sách booking đã hủy với thông tin bank account',
    })
    async exportCancellationsExcel(@Body() body: BookingQueryDto, @Res() res: any): Promise<any> {
        const buffer = await this.cancelBookingService.exportCancellationsExcel(body);
        const fileName = `danh-sach-huy-booking-${new Date().getTime()}.xlsx`;

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
        res.setHeader('Content-Length', buffer.length);
        res.send(buffer);
    }
}
