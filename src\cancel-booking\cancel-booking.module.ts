import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingGatewayModule } from 'src/booking-gateway/booking-gateway.module';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { PaymentSchema } from 'src/booking-gateway/schemas/payment.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { PatientMongoModule } from 'src/patient-mongo/patient-mongo.module';
import { CancelBookingController } from './cancel-booking.controller';
import { CancelBookingService } from './cancel-booking.service';
import { BookingCancellationSchema } from './schemas/booking-cancellation.schema';
import { BOOKING_CANCELLATION_COLLECTION_NAME } from './schemas/constants';
import { PaymentMethodModule } from 'src/payment-method/payment-method.module';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: BOOKING_CANCELLATION_COLLECTION_NAME, schema: BookingCancellationSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
        ]),
        PatientMongoModule,
        BookingGatewayModule,
        PaymentMethodModule,
    ],
    controllers: [CancelBookingController],
    providers: [CancelBookingService, GlobalSettingService],
})
export class CancelBookingModule {}
