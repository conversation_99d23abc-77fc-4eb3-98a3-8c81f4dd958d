import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { AUDIT_LOG_CSKH } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const AuditLogCskhSchema = new Schema({
    url:  { type: String },
    method:  { type: String},
    headers:  { type: Schema.Types.Mixed },
    params:  { type: Schema.Types.Mixed },
    body:  { type: Schema.Types.Mixed },
    query: { type: Schema.Types.Mixed },
    user:  { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    nameRepo: { type: String },
    category: { type: String },
    categoryCode: { type: String },
    action: { type: String },
    actionCode: { type: String },
}, {
    collection: AUDIT_LOG_CSKH,
    timestamps: true,
    versionKey: false,
}).plugin(jsonMongo);
