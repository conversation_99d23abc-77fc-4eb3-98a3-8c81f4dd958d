import { Schema } from 'mongoose';
import { CONSULTATION_REGISTRATION_COLLECTION_NAME } from './constants';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';

export const ConsultationRegistrationSchema = new Schema({
    fullName: { type: String, required: true },
    phone: { type: String, required: true },
    birthDate: { type: String, required: true },
    service_id: { type: String, required: true },
    service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
    note: { type: String }
}, {
    collection: CONSULTATION_REGISTRATION_COLLECTION_NAME,
});
