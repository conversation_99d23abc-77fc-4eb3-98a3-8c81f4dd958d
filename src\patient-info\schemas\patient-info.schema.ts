import * as mongoose from 'mongoose';
import { PATIENT_INFO_COLLECTION_NAME } from './constants';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const PatientInfoSchema = new Schema(
    {
        // Thông tin cơ bản
        patientName: {
            type: String,
            required: true,
            trim: true,
        },
        birthDate: {
            type: Date,
            required: true,
        },
        gender: {
            type: Number,
            enum: [0, 1], // 0: Nữ, 1: Nam
            required: true,
        },
        parentName: {
            type: String,
            trim: true,
            default: null,
        },
        
        // Địa chỉ
        address: {
            type: String,
            required: true,
            trim: true,
        },
        ward_id: {
            type: String,
            required: true,
            trim: true,
        },
        ward: { type: Schema.Types.ObjectId, ref: WARD_COLLECTION_NAME },
        district_id: {
            type: String,
            required: true,
            trim: true,
        },
        district: { type: Schema.Types.ObjectId, ref: DISTRICT_COLLECTION_NAME },
        city_id: {
            type: String,
            required: true,
            trim: true,
        },
        city: { type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
        phone: {
            type: String,
            required: true,
            trim: true,
        },
        
        // Lý do khám
        reasonForVisit: {
            type: String,
            trim: true,
        },
        
        // Checkbox options
        isXRay: {
            type: Boolean,
            default: false,
        },
        isHospitalized: {
            type: Boolean,
            default: false,
        },
        
        // Dấu hiệu sinh tồn
        vitalSigns: {
            // Ngày đo
            birthSign: {
                type: Date,
            },
            weight: {
                type: String,
                trim: true,
            },
            height: {
                type: String,
                trim: true,
            },
            // Nhiệt độ
            temperature: {
                type: String,
                trim: true,
            },
            // Mạch
            pulse: {
                type: String,
                trim: true,
            },
            // SpO2
            spO2: {
                type: String,
                trim: true,
            },

            bloodPressure: {
                type: String,
                trim: true,
            },
    },
    
    // QR Code information
    qrCodeData: {
        type: String,
        trim: true,
        default: null,
    },
    qrCodeImage: {
        type: String,
        default: null,
    },
    

    
    // Queue information from API
        queueId: {
            type: String,
            trim: true,
            default: null,
        },
        dateId: {
            type: String,
            trim: true,
            default: null,
        },
        sequenceNumber: {
            type: Number,
            default: null,
        },
        queueStatus: {
            type: String,
            trim: true,
            default: null,
        },
        timeSlotId: {
            type: String,
            trim: true,
            default: null,
        },
        timeShiftId: {
            type: String,
            trim: true,
            default: null,
        },
        queueDate: {
            type: Number,
            default: null,
        },
        blockDate: {
            type: Number,
            default: null,
        },
        dateInString: {
            type: String,
            trim: true,
            default: null,
        },
        blockDateInString: {
            type: String,
            trim: true,
            default: null,
        },
    },
    {
        collection: PATIENT_INFO_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
