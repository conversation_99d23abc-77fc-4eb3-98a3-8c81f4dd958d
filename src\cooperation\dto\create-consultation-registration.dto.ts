import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsDateString, IsMobilePhone } from 'class-validator';

export class CreateAppointmentsRequestDto {
    @IsString()
    @IsNotEmpty()
    fullName: string;

    @IsString()
    @IsNotEmpty()
    phone: string;

    @IsString()
    @IsNotEmpty()
    birthDate: string;

    @IsString()
    @IsNotEmpty()
    service_id: string;

    @IsString()
    @IsOptional()
    note?: string;
    
    @ApiProperty({ description: 'Google reCAPTCHA response token', required: false })
    @IsNotEmpty()
    @IsString({ message: 'captchaResponse phải là chuỗi' })
    captchaResponse: string;
}
