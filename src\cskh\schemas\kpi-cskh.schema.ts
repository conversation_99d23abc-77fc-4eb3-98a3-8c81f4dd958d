import * as mongoose from 'mongoose';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME, SECTION_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { KPI_CSKH } from './constants';
import { SUBJECT_COLLECTION_NAME } from '../../subject-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from '../../room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from '../../doctor-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from '../../service-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';
import { PATIENT_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const KpiCskhSchema = new Schema(
    {
        bookingId: { type: String, unique: true, required: true },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        createdDate: { type: Date },
        partnerId: { type: String },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        appId: { type: String },
        userId: { type: String },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        cskhUserId: { type: String },
        cskh: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        date: { type: Date },
        status: { type: Number },
        paymentStatus: { type: Number },
        cancelledBy: { type: String },
        treeId: { type: String },
        platform: { type: String },
        subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
        room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
        section: { type: Schema.Types.ObjectId, ref: SECTION_COLLECTION_NAME },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
        countDoanhSo:{ type: Number },
        mode: { type: String, default: 'booking' },
        payment: { type: Schema.Types.ObjectId, ref: PAYMENT_COLLECTION_NAME },
    },
    {
        collection: KPI_CSKH,
        timestamps: true,
    },
);