import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { DoctorDynamicLinkQuery } from './dto/doctor-dynamic-link.query';
import { FirebaseDynamicLinks } from 'firebase-dynamic-links';
import { InjectModel } from '@nestjs/mongoose';
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { Model } from 'mongoose';
import { IDoctor } from '../doctor-mongo/interfaces/doctor.interface';
import { FirebaseConfigService } from '../config/config.firebase.sevice';
import { DOCTOR_DYNAMIC_LINK_COLLECTION_NAME } from './schemas/constants';
import { IDoctorDynamicLink } from './interfaces/doctor-dynamic-link.interface';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME } from '../service-mongo/schemas/constants';
import { IDoctorDescription } from '../doctor-description/interface/doctor-description.inteface';
import { ServiceMongoService } from '../service-mongo/service-mongo.service';
import { DoctorBookingDynamicLinkQuery } from './dto/doctor-booking-dynamic-link.query';
import * as querystring from 'querystring';
import { MedproZoneDto } from './dto/medpro-zone.dto';
import { GlobalSettingService } from '../global-setting/global-setting.service';

@Injectable()
export class DynamicLinkService {
    firebaseDynamicLinks: FirebaseDynamicLinks;

    constructor(
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
        @InjectModel(DOCTOR_DESCRIPTION_COLLECTION_NAME) private doctorDescriptionModel: Model<IDoctorDescription>,
        @InjectModel(DOCTOR_DYNAMIC_LINK_COLLECTION_NAME) private doctorDynamicLinkModel: Model<IDoctorDynamicLink>,
        private firebaseConfigService: FirebaseConfigService,
        private serviceMongoService: ServiceMongoService,
        private globalSettingService: GlobalSettingService,
    ) {
        this.firebaseDynamicLinks = new FirebaseDynamicLinks(this.firebaseConfigService.getWebApiKey());
    }

    async getDoctorDynamicLink(query: DoctorDynamicLinkQuery) {
        const doctorDescription = await this.doctorDescriptionModel.findOne({ $or: [{ slug: query.slug }, { doctorId: query.slug }] }).exec();

        if (!doctorDescription) {
            throw new HttpException(`Không tìm thấy bác sĩ ${query.slug}`, HttpStatus.NOT_FOUND);
        }

        let [findLink, doctor, searchDetail] = await Promise.all([
            this.doctorDynamicLinkModel.findOne({ doctorId: doctorDescription.doctorId, slug: query.slug }).exec(),
            this.doctorModel.findOne({ id: doctorDescription.doctorId }).exec(),
            this.serviceMongoService.getSearchDetail({ id: doctorDescription.doctorId, category: 'doctor' }),
        ]);

        const doctorObj = doctor.toObject();

        let linkResult: any;

        if (findLink) {
            linkResult = { shortLink: findLink.shortLink, previewLink: findLink.previewLink };
        } else {
            const { shortLink, previewLink } = await this.firebaseDynamicLinks.createLink({
                dynamicLinkInfo: {
                    domainUriPrefix: 'https://app-link.medpro.vn',
                    link: `https://info.medpro.vn/bac-si/${doctorDescription.slug}`,
                    androidInfo: {
                        androidPackageName: 'vn.com.medpro',
                    },
                    iosInfo: {
                        iosBundleId: 'vn.com.medpro',
                        iosAppStoreId: '1481561748',
                    },
                },
                suffix: { option: 'SHORT' },
            });

            this.doctorDynamicLinkModel
                .create({
                    doctorId: doctorObj.id,
                    slug: query.slug,
                    shortLink,
                    previewLink,
                })
                .catch(err => {
                    console.error('err save doctorDynamicLink', err);
                });

            linkResult = { shortLink, previewLink };
        }

        return {
            ...linkResult,
            doctor: {
                ...searchDetail,
                doctorDescription: doctorDescription.toObject(),
            },
        };
    }

    async getTinhNangDynamicLink(query: any) {
        const queryString  = querystring.stringify(query)
        const link = `https://info.medpro.vn/tinh-nang?${queryString}`;

        const { shortLink, previewLink } = await this.firebaseDynamicLinks.createLink({
            dynamicLinkInfo: {
                domainUriPrefix: 'https://app-link.medpro.vn',
                link,
                androidInfo: {
                    androidPackageName: 'vn.com.medpro',
                },
                iosInfo: {
                    iosBundleId: 'vn.com.medpro',
                    iosAppStoreId: '1481561748',
                },
            },
            suffix: { option: 'SHORT' },
        });

        return { shortLink, previewLink };
    }

    async getBookingDetailDynamicLink(query: any) {
        const tabSetting = await this.globalSettingService.findByKeyAndRepoName('DEFAULT_TAB_BOOKING_CARE247')
        const link = `https://info.medpro.vn/phieu-kham-benh/chi-tiet/${query.transactionId}${tabSetting ? `?tab=${tabSetting}` : ''}`;

        const { shortLink, previewLink } = await this.firebaseDynamicLinks.createLink({
            dynamicLinkInfo: {
                domainUriPrefix: 'https://app-link.medpro.vn',
                link,
                androidInfo: {
                    androidPackageName: 'vn.com.medpro',
                },
                iosInfo: {
                    iosBundleId: 'vn.com.medpro',
                    iosAppStoreId: '1481561748',
                }
            },
            suffix: { option: 'SHORT' },
        });

        return { shortLink, previewLink };
    }

    async getDoctorBookingDynamicLink(query: DoctorBookingDynamicLinkQuery) {
        const link = `https://info.medpro.vn/bac-si/${query.slug}?action=${query.action}`;

        const { shortLink, previewLink } = await this.firebaseDynamicLinks.createLink({
            dynamicLinkInfo: {
                domainUriPrefix: 'https://app-link.medpro.vn',
                link,
                androidInfo: {
                    androidPackageName: 'vn.com.medpro',
                },
                iosInfo: {
                    iosBundleId: 'vn.com.medpro',
                    iosAppStoreId: '1481561748',
                },
            },
            suffix: { option: 'SHORT' },
        });

        return { shortLink, previewLink };
    }
    
    async getMedproZone(data: MedproZoneDto) {
        const { zone } = data
        const link = `https://info.medpro.vn/getapp?zone=${zone}`;

        const { shortLink, previewLink } = await this.firebaseDynamicLinks.createLink({
            dynamicLinkInfo: {
                domainUriPrefix: 'https://app-link.medpro.vn',
                link,
                androidInfo: {
                    androidPackageName: 'vn.com.medpro',
                },
                iosInfo: {
                    iosBundleId: 'vn.com.medpro',
                    iosAppStoreId: '1481561748',
                },
            },
            suffix: { option: 'SHORT' },
        });

        return { shortLink, previewLink };
    }

    
}
