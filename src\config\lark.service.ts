import { HttpService, Injectable } from '@nestjs/common';
import { larkMsgTrackingPatient, messageAddPatientToUserTemplate } from '../common/template/lark.template';
import { ConfigLarkService } from './config.lark.service';


@Injectable()
export class LarkService {
    constructor(private larkConfig: ConfigLarkService, private httpService: HttpService) {}

    sendMessageTrackingPatient(data: { action: string; cskhUser: any; patientUser: any, patient: any }) {
        let url = this.larkConfig.trackingPatientActionInfo;

        const msgContent = larkMsgTrackingPatient(data);

        this.httpService
            .post(url, {
                msg_type: 'interactive',
                card: msgContent,
            })
            .toPromise();
    }

    sendMessageAddPatientToUser(actionName: string, userAction: any, patientUser: any, patient: any, hospitalName: string, oldPatientName: string) {
        const url = this.larkConfig.trackingPatientActionInfo;
        const msgContent = messageAddPatientToUserTemplate(actionName, userAction, patientUser, patient, hospitalName, oldPatientName);

        this.httpService
            .post(url, {
                msg_type: 'interactive',
                card: msgContent,
            })
            .toPromise();
    }
}
