import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class BookingCare247CskhRevenueQueryDto {
    @ApiProperty({
        description: 'cskhUserId',
        required: false,
        type: String,
    })
    cskhUserId?: string;

    @ApiProperty({
        description: 'cskhUserIdGuide',
        required: false,
        type: String,
    })
    cskhUserIdGuide?: string;

    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM String',
        required: false,
        type: String,
    })
    month?: string;
    
    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @Transform(value => Number(value))
    pageIndex?: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
