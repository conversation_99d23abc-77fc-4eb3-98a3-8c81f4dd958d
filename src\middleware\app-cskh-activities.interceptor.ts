import { CallHandler, ExecutionContext, forwardRef, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CskhService } from '../cskh/cskh.service';
import { last, size } from 'lodash';
import * as jwt from 'jsonwebtoken';
import { JwtModuleOptions } from '@nestjs/jwt';
import { JwtUserConfigService } from '../config/config.user.jwt.service';
import { Reflector } from '@nestjs/core';
import { ActionEnum, CategoryEnum } from '../cskh/decorator/action-data.enum';

@Injectable()
export class AppCskhActivitiesInterceptor implements NestInterceptor {
    constructor(private reflector: Reflector, private readonly cskhService: CskhService, private jwtUserConfigService: JwtUserConfigService) {}

    async intercept(context: ExecutionContext, next: CallHandler<any>): Promise<Observable<any>> {
        try {
            const actionData = this.reflector.get<Record<string, any>>('actionData', context.getHandler());
            const request = context.switchToHttp().getRequest();
            return next.handle().pipe(
                tap(async data => {
                    try {
                        const { headers, body, params, query, url, method } = request;
                        const { category, action } = actionData;
                        const categoryCode = Object.entries(CategoryEnum).find(([_, v]) => v === category)?.[0];
                        const actionCode = Object.entries(ActionEnum).find(([_, v]) => v === action)?.[0];

                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
                        const { authorization } = request.headers;
                        let user: string;
                        if (size(authorization) > 0) {
                            const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                            const { userMongoId } = jwtVerify;
                            if (userMongoId) {
                                user = userMongoId;
                            }
                        }
                        const trackingData = { headers, body, params, query, url, method, user, category, categoryCode, action, actionCode };
                        await this.cskhService.createAuditLogCskh(trackingData);
                    } catch (error) {}
                }),
            );
        } catch (error) {
            throw error;
        }
    }
}
