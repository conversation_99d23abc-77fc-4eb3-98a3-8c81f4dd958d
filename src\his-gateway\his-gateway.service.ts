import { Injectable, HttpException, HttpStatus, HttpService, Logger, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BOOKING_SLOT_COLLECTION_NAME, BOOKING_HIS_COLLECTION_NAME, CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME, BOOKING_NUMBER_COLLECTION_NAME } from './schemas/constants';
import { IBookingSlot } from './intefaces/booking-slot.inteface';
import { IBookingHIS } from './intefaces/booking-his.inteface';
import { Model } from 'mongoose';
import { BookingSlotFormDTO } from 'src/booking-gateway/dto/booking-slot-form.dto';
import { first, get } from 'lodash';
import { BookingStatus } from './dto/bookingStatus.dto';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { UrlConfigService } from 'src/config/config.url.service';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import * as moment from 'moment';
import * as uuid from 'uuid';
import { BookingTreeTypeDTO } from './dto/booking-tree-type.dto';
import { BookingTreeDynamicDTO } from './dto/booking-tree-dynamic.dto';
import { BookingTreeCurrentNodeDTO } from './dto/booking-tree-current-node.dto';
import { UtilService } from 'src/config/util.service';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { IConstraintsSequenceNumber } from './intefaces/constraints-sequence-number.interface';
import { BOOKING_CARE_247, BOOKING_COLLECTION_NAME, EXPIRED_BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { IExpiredBooking } from 'src/booking-gateway/intefaces/expired-booking.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { IBookingNumber } from './intefaces/booking-numbers.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT, THIRD_PARTY_ERROR_LOG } from 'src/audit-log/constant';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import {SyncPatientEvent} from "../booking-gateway/dto/sync-patient.event";
import * as querystring from 'query-string';
import { REPO_NAME_BETA } from 'src/common/constants';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IBookingCare247 } from '../booking-gateway/intefaces/booking-care-247.interface';
import { IBooking } from '../booking-gateway/intefaces/booking.inteface';
import { BOOKING_CARE_247_CONSTRAINT_USER } from '../cskh/schemas/constants';
import { IBookingCare247ConstraintUser } from '../cskh/interfaces/booking-care247-constraint-user.interface';

@Injectable()
export class HisGatewayService {
    private logger = new Logger(HisGatewayService.name);
    private listAppId: Set<{}>;
    private readonly SWITCH_CTCH_V2 = 'SWITCH_CTCH_V2';
    private readonly repoName: string;
    constructor(
        @InjectModel(EXPIRED_BOOKING_COLLECTION_NAME) private expiredBookingModel: Model<IExpiredBooking>,
        @InjectModel(BOOKING_SLOT_COLLECTION_NAME) private bookingSlotModel: Model<IBookingSlot>,
        @InjectModel(BOOKING_HIS_COLLECTION_NAME) private bookingHisModel: Model<IBookingHIS>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME) private constraintsSequenceNumberModel: Model<IConstraintsSequenceNumber>,
        @InjectModel(BOOKING_NUMBER_COLLECTION_NAME) private bookingNumberModel: Model<IBookingNumber>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private readonly hospitalModel: Model<IHospital>,
        @InjectModel(BOOKING_CARE_247) private readonly bookingCare247Model: Model<IBookingCare247>,
        @InjectModel(BOOKING_CARE_247_CONSTRAINT_USER) private readonly bookingCare247ConstraintUserModel: Model<IBookingCare247ConstraintUser>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectSentry() private readonly clientSentry: SentryService,
        private readonly httpService: HttpService,
        private readonly urlConfigService: UrlConfigService,
        private readonly utilService: UtilService,
        private eventEmitter: EventEmitter2,
        private globalSettingService: GlobalSettingService,
        private readonly repoConfigService: ConfigRepoService,
    ) {
        this.listAppId = this.utilService.listAppId();
        this.repoName = this.repoConfigService.getRepoName();
    }

    async getBookingTreeRestApi(partnerId: string): Promise<any> {
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        const { bookingTreeRestApi = '' } = partnerConfig;

        if (REPO_NAME_BETA.includes(this.repoName)) {
            return this.urlConfigService.getBookingTreeUrl();
        }
        
        if (bookingTreeRestApi) {
            return bookingTreeRestApi;
        } else {
            return this.urlConfigService.getBookingTreeUrl();
        }
    }

    async updateAvailableSlot2(bookingSlotId: string): Promise<any> {
        // console.log(bookingSlotId);
        const findSlot = await this.bookingSlotModel.findOne({ bookingSlotId }).exec();
        if (findSlot) {
            return this.bookingSlotModel.findByIdAndUpdate({ _id: findSlot._id }, {
                availableSlot: findSlot.availableSlot + 1,
            }, { new: true }).exec();
        }
        return null;
    }

    async updateAvailableSlotWithCounter(bookingSlotId: string, count: number): Promise<any> {
        const findSlot = await this.bookingSlotModel.findOne({ bookingSlotId }).exec();
        if (findSlot) {
            return this.bookingSlotModel.findByIdAndUpdate({ _id: findSlot._id }, {
                availableSlot: findSlot.availableSlot + count,
            }, { new: true }).exec();
        }
        return null;
    }

    async updateAvailableSlot(bookingSlotId: string): Promise<any> {
        return this.bookingSlotModel.findOne({
            bookingSlotId,
        }, (err, doc) => {
            if (err) {
                this.clientSentry.instance().captureException(err);
            }
            doc.availableSlot = doc.availableSlot + 1;
            doc.save();
        }).exec();
        // return this.bookingSlotModel.findById({ _id: bookingSlotInfo._id }).exec();
    }

    async descrementAvailableSlot(bookingSlotId: string): Promise<any> {
        return this.bookingSlotModel.findOne({
            bookingSlotId,
        }, (err, doc) => {
            if (err) {
                this.clientSentry.instance().captureException(err);
            }
            let descre = doc.availableSlot - 1;
            if (descre < 0) {
                descre = 0;
            }
            doc.availableSlot = descre;
            doc.save();
        }).exec();
    }

    async insertManyConstraintsNumber(data: any): Promise<any> {
        // console.log('tổng số lương: ', data.length);
        for await (const item of data) {
            const newConstraints = new this.constraintsSequenceNumberModel(
                {
                    constraints: item.constraints,
                    bookingCode: item.bookingCode,
                });
            await newConstraints.save().catch(error => {
                console.log(JSON.stringify(item));
            });
        }
    }

    async deleteConstraintSequenceNumber(bookingCode: any): Promise<any> {
        try {
            return this.constraintsSequenceNumberModel.findOneAndDelete({ bookingCode }).exec();
        } catch (err) {
            Logger.log(`Error when exec deleteConstraintSequenceNumber() with bookingCode: ${bookingCode}\nError: ${err.message}`);
            throw err;
        }
    }

    async reserveBooking(bookingSlotId: string): Promise<IBookingSlot> {
        const booking = new this.bookingSlotModel();
        return (await booking.save()).toJSON();
    }

    async getBookingTree(partnerid: string): Promise<any> {
        let data: any = {};
        const params = {
            partnerid,
        };
        const partnerId = partnerid.toLowerCase();
        const baseUrl = await this.getBookingTreeRestApi(partnerid);
        try {
            const bookingTree = (await this.callThirdBookingTree(baseUrl, partnerId).toPromise()).data;
            data = bookingTree;
            return bookingTree;
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdBookingTree',
                summary: 'Lấy thông tin Booking Tree',
                nameParent: 'getBookingTree',
                params: { ...params, baseUrl },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || `Chưa cấu hình thông tin lịch khám cho luồng khám này.`,
            });
            throw new HttpException(error, HttpStatus.NOT_FOUND);
        }
    }

    async getFeature(hospital: any): Promise<any> {
        const feature = hospital.features;
        const res = [];
        feature.map((item: any) => {
            if (item.children.length > 0) {
                res.push(...item.children);
            } else {
                res.push(item);
            }
        });
        return res;
    }

    async validationForFeature(partnerId: string, featureType: string): Promise<any> {
        try {
            const checkModeBeta = await this.globalSettingService.findByKeyAndRepoName('BETA_IGNORE_MESSAGE');
            const setPartnerBeta = new Set(checkModeBeta ? checkModeBeta.split(',') : []);
            if (setPartnerBeta.has(partnerId)) {
                return;
            }

            const hospital = await this.hospitalModel.findOne({ partnerId }, { features: true }).exec();
            if (!hospital) {
                throw new HttpException(`Hospital with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
            }

            const features = await this.getFeature(hospital);
            const feature = features.find(item => item.type === featureType);
            const isValidFeature = feature?.disabled;
            if (isValidFeature) {
                throw new HttpException(`${feature.message}`, HttpStatus.SERVICE_UNAVAILABLE);
            }
        } catch (error) {
            this.logger.error(
                `Error when exec validationForFeature() with partnerId: ${partnerId} and featureType: ${featureType}\nError: ${error.message}`,
            );
            throw error;
        }
    }

    async getBookingTreeDynamic(partnerid: string, formData: BookingTreeDynamicDTO, appid: string): Promise<any> {
        // let data: any = {};
        const params = {
            ...formData,
            partnerid,
        };
        const partnerId = partnerid.toLowerCase();
        const baseUrl = 'https://portal-testing.medpro.com.vn';
        try {

            // const startTime = moment();
            // console.log('startTime: ', startTime.toISOString());
            const featureType = `booking.${formData.treeId}`.toLowerCase();

            // console.log('before validationForFeature: ', moment().toISOString());
            await this.validationForFeature(partnerid, featureType);
            // console.log('after validationForFeature: ', moment().toISOString());
            // console.log('duration validationForFeature: ', moment.duration(moment().diff(startTime)).milliseconds());
            // console.log('------------')

            // const startTimePortal = moment();
            // console.log('before call portal: ', startTimePortal.toISOString());
            try {
                const bookingTree = (await this.callThirdBookingTreeDynamic(baseUrl, partnerId, formData).toPromise()).data;
                if(appid === 'cskh' && treeId === 'TELEMEDNOW') {
                    return bookingTree;
                }else{
                    return bookingTree;
                }
            } catch (error) {
                this.eventEmitter.emit(THIRD_PARTY_ERROR_LOG, {
                    baseUrl,
                    ...params,
                    errorBody: this.utilService.errorHandler(error),
                    subjectMail: 'GET_BOOKING_TREE_DYNAMIC_ERROR - Lấy lịch khám bị lỗi. Vui lòng kiểm tra lại gấp.'
                })
                throw error;
            }

            // console.log('after call portal: ', moment().toISOString());
            // console.log('duration call portal: ', moment.duration(moment().diff(startTimePortal)).milliseconds());

            // console.log('++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n')

            // data = bookingTree;

        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdBookingTreeDynamic',
                summary: 'Lấy thông tin Booking Tree Dynamic',
                nameParent: 'getBookingTreeDynamic',
                params: { ...params, baseUrl },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || `Chưa cấu hình thông tin lịch khám cho luồng khám này.`,
            });
            throw new HttpException(error?.message || '[BookingTreeDynamic] Hệ thống chưa xử lý được thao tác này. Vui lòng liên hệ tổng đài 19002115 để được hỗ trợ.', HttpStatus.NOT_FOUND);
        }
    }

    async getBookingTreeCurrentNode(partnerid: string, fromData: BookingTreeCurrentNodeDTO): Promise<any> {
        let data: any = {};
        const params = {
            ...fromData,
            partnerid,
        };
        const partnerId = partnerid.toLowerCase();
        const baseUrl = await this.getBookingTreeRestApi(partnerid);
        try {
            const bookingTree = (await this.callThirdBookingTreeCurrentNode(baseUrl, partnerId, fromData).toPromise()).data;
            data = bookingTree;
            return bookingTree;
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdBookingTreeCurrentNode',
                summary: 'Lấy thông tin Booking Tree Current Node',
                nameParent: 'getBookingTreeCurrentNode',
                params: { ...params, baseUrl },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || `Chưa cấu hình thông tin lịch khám cho luồng khám này.`,
            });
            throw new HttpException(error, HttpStatus.NOT_FOUND);
        }
    }

    async getBookingTreeV2(partnerid: string, data: BookingTreeTypeDTO): Promise<any> {
        const params = {
            ...data,
            partnerid,
        };
        try {
            const partnerId = partnerid.toLowerCase();
            const baseUrl = await this.getBookingTreeRestApi(partnerid);
            const bookingTree = (await this.callThirdBookingTreeV2(baseUrl, partnerId, data.type).toPromise()).data;
            return bookingTree;
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callThirdBookingTreeV2',
                summary: 'Lấy thông tin Booking Tree Version 2',
                nameParent: 'getBookingTreeV2',
                params,
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || `Chưa cấu hình thông tin lịch khám cho luồng khám này.`,
            });
            throw new HttpException(error, HttpStatus.NOT_FOUND);
        }
    }

    callThirdBookingTreeV2(baseUrl: string, partnerId: string, type: string): Observable<AxiosResponse<any>> {
        if (!!type) {
            const urlv2 = `${baseUrl}/schedule/v2/booking/schedule/${partnerId}/${type}`;
            return this.httpService.get(urlv2);
        } else {
            return this.httpService.get(`${baseUrl}/schedule/v1/booking/schedule/${partnerId}`);
        }
    }

    callThirdBookingTree(baseUrl: string, partnerId: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(`${baseUrl}/schedule/v1/booking/schedule1/${partnerId}`);
    }

    callThirdBookingTreeDynamic(baseUrl: string, partnerId: string, formData: BookingTreeDynamicDTO): Observable<AxiosResponse<any>> {
        const prefix = formData.idBooking ? 'dynamic-schedule-update' : 'dynamic-schedule';
        let url = `${baseUrl}/schedule/v2/booking/${prefix}/${partnerId}`;
        if (formData.treeId) {
            url = `${url}/${formData.treeId}`;
        }
        if (formData.date) {
            url = `${url}?date=${formData.date}`;
        }

        const urlQuery = querystring.stringifyUrl({
            url,
            query: {
                ...(formData?.date && { date: formData.date }),
                ...(formData?.serviceId && { serviceId: formData.serviceId }),
                ...(formData?.subjectId && { subjectId: formData.subjectId }),
                ...(formData?.doctorId && { doctorId: formData.doctorId }),
                ...(formData?.roomId && { roomId: formData.roomId }),
            },
        });

        return this.httpService.get(urlQuery);
    }

    callThirdBookingTreeCurrentNode(baseUrl: string, partnerId: string, formData: BookingTreeCurrentNodeDTO): Observable<AxiosResponse<any>> {
        const prefix = formData.idBooking ? 'dynamic-schedule-update' : 'dynamic-schedule';
        let url = `${baseUrl}/schedule/v2/booking/${prefix}/getCurrentNode`;
        if (formData.date) {
            url = `${url}?date=${formData.date}`;
        }
        const finalParams = {
            partnerId, ...formData,
        };

        return this.httpService.post(url, finalParams);
    }

    async testTimeId(): Promise<any> {
        try {
            const timeIdData: any = (await this.layBoiSo1111().toPromise()).data;
            const { timeId, outOfNumber = false } = timeIdData;
            if (outOfNumber) {
                throw new HttpException('Giờ khám đã kín, vui lòng chọn giờ khác', HttpStatus.BAD_REQUEST);
            }
            if (timeId) {
                return timeId;
            }
        } catch (error) {
            const { response } = error;
            if (response && response === 'Giờ khám đã kín, vui lòng chọn giờ khác') {
                throw new HttpException('Giờ khám đã kín, vui lòng chọn giờ khác', HttpStatus.BAD_REQUEST);
            }
            this.clientSentry.instance().captureException(error);
        }
    }

    layBoiSo1111(): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const params = {
            partnerId: 'leloi',
            subjectId: 'leloi_3',
            timeId: 'DYNAMIC_SLOT_044_1_10:00',
            date: '06-10-2020',
            bookingCode: 'test',
            serviceId: 'leloi_849321',
        };
        return this.httpService.post(`${baseUrl}/schedule/v2/booking/booking-number/getAndNoBlock`, params);
    }

    async testBoiSo(): Promise<any> {
        try {
            const dataBoiSo = (await this.testBpS().toPromise()).data;
            return dataBoiSo;
        } catch (error) {
            console.log(error);
        }
    }

    testBpS(): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const params = {
            partnerId: 'leloi',
            timeId: 'leloi_4_leloi_4_1_08:00',
            date: '31-08-2020', // DD-MM-YYYY
        };
        return this.httpService.post(`${baseUrl}/schedule/v2/booking/booking-number/available`, params);
    }

    returnParams(bookingSlotFormDTO: any): any {
        const dateString = moment(bookingSlotFormDTO.startTime).add(7, 'hours').format('DD-MM-YYYY');
        const timeIdArr = bookingSlotFormDTO.bookingSlotId.split('_').slice(1, -1);
        const joinTimeId = timeIdArr.join('_');
        const getRoomId = get(bookingSlotFormDTO, 'roomId', null);
        const getSubjectId = get(bookingSlotFormDTO, 'subjectId', null);
        const getDoctorId = get(bookingSlotFormDTO, 'doctorId', null);
        const getServiceId = get(bookingSlotFormDTO, 'serviceId', null);
        const getExtra = get(bookingSlotFormDTO, 'extraBookingInfo', {});
        return {
            partnerId: bookingSlotFormDTO.partnerId,
            timeId: joinTimeId,
            date: dateString, // DD-MM-YYYY
            bookingCode: bookingSlotFormDTO.bookingCode,
            /* bổ sung thêm */
            roomId: getRoomId,
            subjectId: getSubjectId,
            doctorId: getDoctorId,
            serviceId: getServiceId,
            /* bo sung them patientId */
            patientId: bookingSlotFormDTO.patientId,
            ...getExtra,
        };
    }

    layBoiSo(baseUrl: string, bookingSlotFormDTO: any): Observable<AxiosResponse<any>> {
        const params = this.returnParams(bookingSlotFormDTO);
        return this.httpService.post(`${baseUrl}/schedule/v2/booking/booking-number/getAndBlock`, params);
    }

    getExactTimeId(baseUrl: string, bookingSlotFormDTO: any): Observable<AxiosResponse<any>> {
        const params = this.returnParams(bookingSlotFormDTO);
        // this.logger.log(`parms gửi sang: ${JSON.stringify(params, null, 2)}`, bookingSlotFormDTO.partnerId);
        return this.httpService.post(`${baseUrl}/schedule/v2/booking/booking-number/getAndNoBlock`, params);
    }

    getPriceText(price: number): string | number {
        let priceText = '';
        if (price > 0) {
            priceText = `${price}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        if (price === 0) {
            return price;
        }
        return priceText;
    }

    async checkCTCH(partnerId: string): Promise<boolean> {
        const switchCTCHHCM = await this.globalSettingService.findByKeyAndRepoName(this.SWITCH_CTCH_V2);
        return !(partnerId === 'ctchhcm' && switchCTCHHCM === 'ON');
    }

    async insertOrUpdateBookingSlotVaccine(appId: string, partnerId: string, bookingSlotFormDTO: any): Promise<any> {
        const findBookingSlot = await this.findOneBookingSlot(bookingSlotFormDTO.partnerId, bookingSlotFormDTO.bookingSlotId);
        if (!findBookingSlot) {
            return await this.insertBookingSlot(bookingSlotFormDTO);
        }
        return findBookingSlot;
    }

    async getDataMedproCare(partnerId: string, platform: string, version: string, isPrimary?: boolean, isCs?: boolean) {
        const care247Info = await this.globalSettingService.findByKeyAndRepoName('CARE_247_INFO')
        const discount = await this.globalSettingService.findByKeyAndRepoName('CARE247_DISCOUNT_REBOOKING');
        const care247InfoJson = JSON.parse(care247Info);
        const discountObj = discount ? JSON.parse(discount) : {};

        let nameConfig = 'Dịch Vụ Đặt Thêm';
        let subnameConfig = '(Không bắt buộc, tùy theo nhu cầu khách hàng)'
        if (['android', 'ios'].includes(platform)) {
            subnameConfig = '(𝘒𝘩ô𝘯𝘨 𝘣ắ𝘵 𝘣𝘶ộ𝘤, 𝘵ù𝘺 𝘵𝘩𝘦𝘰 𝘯𝘩𝘶 𝘤ầ𝘶 𝘬𝘩á𝘤𝘩 𝘩à𝘯𝘨) \n Đưa Đón - Trợ Lý Giúp Việc - Phiên Dịch'
        }

        let htmlTitle = `<div><h3 style="font-size:16px;line-height:18px;margin-bottom:6px;font-weight:500;color:#24313d">Dịch vụ trợ lý cá nhân (hướng dẫn quy trình, lấy thuốc, phiên dịch,...)</h3></div>`;

        if (platform === 'pc') {
            nameConfig = 'Dịch Vụ Đặt Thêm (Không bắt buộc, tùy theo nhu cầu khách hàng)'
            subnameConfig = 'Đưa Đón - Trợ Lý Giúp Việc - Phiên Dịch'
        }

        let descriptionVi = `<p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;margin-bottom:0;'><b>Mô tả dịch vụ:</b> Trợ lý cá nhân, giúp việc trước/trong/sau khám & cận lâm sàng, hỗ trợ người bệnh gặp khó khăn khi đi khám mà không có người thân trợ giúp, phiên dịch, hỗ trợ các thủ tục hành chính/bảo hiểm dịch vụ, xếp hàng nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm)</p>
                        <p style='text-align:left;margin-bottom:0;'><b>Thời gian phục vụ:</b> 3 giờ</p>
                        <p style='text-align:left;margin-bottom:0;'><b>Hotline đặt nhanh:</b> 1900 2267</p>
                        </div>
                        <p style='color: red; text-align:left;'>Lưu ý:</p>
                        <p style='text-align:left;'>Dịch vụ được cung cấp và phục vụ bởi Care247, KHÔNG phải của CSYT cung cấp, KHÔNG cam kết thời gian khám, không bao gồm chi phí khám</p>
                        </p>`
        let descriptionEn = `<p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;margin-bottom:0;'><b>Mô tả dịch vụ:</b> Trợ lý cá nhân, giúp việc trước/trong/sau khám & cận lâm sàng, hỗ trợ người bệnh gặp khó khăn khi đi khám mà không có người thân trợ giúp, phiên dịch, hỗ trợ các thủ tục hành chính/bảo hiểm dịch vụ, xếp hàng nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm)</p>
                        <p style='text-align:left;margin-bottom:0;'><b>Thời gian phục vụ:</b> 3 giờ</p>
                        <p style='text-align:left;margin-bottom:0;'><b>Hotline đặt nhanh:</b> 1900 2267</p>
                        </div>
                        <p style='color: red; text-align:left;'>Lưu ý:</p>
                        <p style='text-align:left;'>Dịch vụ được cung cấp và phục vụ bởi Care247, KHÔNG phải của CSYT cung cấp, KHÔNG cam kết thời gian khám, không bao gồm chi phí khám</p>
                        </p>`
        let descriptionKm = `<p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;margin-bottom:0;'><b>Mô tả dịch vụ:</b> Trợ lý cá nhân, giúp việc trước/trong/sau khám & cận lâm sàng, hỗ trợ người bệnh gặp khó khăn khi đi khám mà không có người thân trợ giúp, phiên dịch, hỗ trợ các thủ tục hành chính/bảo hiểm dịch vụ, xếp hàng nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm)</p>
                        <p style='text-align:left;margin-bottom:0;'><b>Thời gian phục vụ:</b> 3 giờ</p>
                        <p style='text-align:left;margin-bottom:0;'><b>Hotline đặt nhanh:</b> 1900 2267</p>
                        </div>
                        <p style='color: red; text-align:left;'>Lưu ý:</p>
                        <p style='text-align:left;'>Dịch vụ được cung cấp và phục vụ bởi Care247, KHÔNG phải của CSYT cung cấp, KHÔNG cam kết thời gian khám, không bao gồm chi phí khám</p>
                        </p>`
        
        if (partnerId === 'umc') {
            descriptionVi = `<p>
                        <p style='text-align:left;'>✅ <b>Dịch vụ bao gồm:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Giúp việc trước/trong/sau khi khám tại CSYT cho người bệnh gặp khó khăn mà không có người thân trợ giúp: mang vác hành lý, giúp đỡ di chuyển giữa các khoa/phòng, xếp hàng, liên lạc thông báo cho người thân, chăm sóc cơ bản...</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ giao tiếp - trao đổi thông tin bệnh lý với nhân viên y tế</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ các thủ tục hành chính khám bệnh, cận lâm sàng theo quy trình của CSYT</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ các thủ tục bảo hiểm dịch vụ (nếu có yêu cầu)</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ nhận & gửi thuốc tận nhà khi có yêu cầu (áp dụng nội thành TpHCM)</p>
                        </div>
                        <p style='text-align:left;'>✅ <b>Thời gian giúp việc:</b> 3 giờ</p>
                        <p style='text-align:left;'>❌ <b>Dịch vụ không bao gồm:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Chi phí khám chữa bệnh, cận lâm sàng, thuốc, vật tư y tế và phí vận chuyển thuốc.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Chi phí cá nhân: ăn uống, gửi/đậu xe,...</p>
                        </div>
                        <p style='text-align:left;'>🛑 <b>Lưu ý:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Dịch vụ được cung cấp độc lập và chịu trách nhiệm phục vụ bởi Công ty TNHH Care247.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Care247 không cam kết thứ tự ưu tiên và thời gian khám.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Thời gian giúp việc có thể linh hoạt từ 3 - 5 giờ phụ thuộc vào quy trình khám bệnh của từng CSYT.</p>
                        </div>
                        <p style='text-align:left;'>📞 <b>HOTLINE đặt nhanh:</b> 1900-2267</p>
                        <p style='color: red; text-align:left;'>🔆 Ưu đãi giảm giá cho mọi khách hàng</p>
                        </p>`
            descriptionEn = `<p>
                        <p style='text-align:left;'>✅ <b>Dịch vụ bao gồm:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Giúp việc trước/trong/sau khi khám tại CSYT cho người bệnh gặp khó khăn mà không có người thân trợ giúp: mang vác hành lý, giúp đỡ di chuyển giữa các khoa/phòng, xếp hàng, liên lạc thông báo cho người thân, chăm sóc cơ bản...</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Phiên dịch - giao tiếp - trao đổi thông tin bệnh lý với nhân viên y tế</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ các thủ tục hành chính khám bệnh, cận lâm sàng theo quy trình của CSYT</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ các thủ tục bảo hiểm dịch vụ (nếu có yêu cầu)</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ nhận & gửi thuốc tận nhà khi có yêu cầu (áp dụng nội thành TpHCM)</p>
                        </div>
                        <p style='text-align:left;'>✅ <b>Thời gian giúp việc:</b> 3 giờ</p>
                        <p style='text-align:left;'>❌ <b>Dịch vụ không bao gồm:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Chi phí khám chữa bệnh, cận lâm sàng, thuốc, vật tư y tế và phí vận chuyển thuốc.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Chi phí cá nhân: ăn uống, gửi/đậu xe,...</p>
                        </div>
                        <p style='text-align:left;'>🛑 <b>Lưu ý:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Dịch vụ được cung cấp độc lập và chịu trách nhiệm phục vụ bởi Công ty TNHH Care247.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Care247 không cam kết thứ tự ưu tiên và thời gian khám.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Thời gian giúp việc có thể linh hoạt từ 3 - 5 giờ phụ thuộc vào quy trình khám bệnh của từng CSYT.</p>
                        </div>
                        <p style='text-align:left;'>📞 <b>HOTLINE đặt nhanh:</b> 1900-2267</p>
                        <p style='color: red; text-align:left;'>🔆 Ưu đãi giảm giá cho mọi khách hàng</p>
                        </p>`
            descriptionKm = `<p>
                        <p style='text-align:left;'>✅ <b>Dịch vụ bao gồm:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Giúp việc trước/trong/sau khi khám tại CSYT cho người bệnh gặp khó khăn mà không có người thân trợ giúp: mang vác hành lý, giúp đỡ di chuyển giữa các khoa/phòng, xếp hàng, liên lạc thông báo cho người thân, chăm sóc cơ bản...</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Phiên dịch - giao tiếp - trao đổi thông tin bệnh lý với nhân viên y tế</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ các thủ tục hành chính khám bệnh, cận lâm sàng theo quy trình của CSYT</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ các thủ tục bảo hiểm dịch vụ (nếu có yêu cầu)</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Hỗ trợ nhận & gửi thuốc tận nhà khi có yêu cầu (áp dụng nội thành TpHCM)</p>
                        </div>
                        <p style='text-align:left;'>✅ <b>Thời gian giúp việc:</b> 3 giờ</p>
                        <p style='text-align:left;'>❌ <b>Dịch vụ không bao gồm:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Chi phí khám chữa bệnh, cận lâm sàng, thuốc, vật tư y tế và phí vận chuyển thuốc.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Chi phí cá nhân: ăn uống, gửi/đậu xe,...</p>
                        </div>
                        <p style='text-align:left;'>🛑 <b>Lưu ý:</b></p>
                        <div style='list-style-type:none;margin-bottom:15px;'>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Dịch vụ được cung cấp độc lập và chịu trách nhiệm phục vụ bởi Công ty TNHH Care247.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Care247 không cam kết thứ tự ưu tiên và thời gian khám.</p>
                        <p style='text-align:left;padding-left:20px;margin-bottom:0;'>➡️ Thời gian giúp việc có thể linh hoạt từ 3 - 5 giờ phụ thuộc vào quy trình khám bệnh của từng CSYT.</p>
                        </div>
                        <p style='text-align:left;'>📞 <b>HOTLINE đặt nhanh:</b> 1900-2267</p>
                        <p style='color: red; text-align:left;'>🔆 Ưu đãi giảm giá cho mọi khách hàng</p>
                        </p>`
        }

        //json
        let medproCare: any = {
            "addonServices": [
                {
                    "duration": "3 giờ",
                    "originalPrice": null,
                    "price": 399000,
                    "name": "Trợ lý cá nhân, giúp việc",
                    "subname": "",
                    "description": `${descriptionVi}`,
                    "description_following": "",
                    "currency": "đ",
                    "locale": "vi",
                    "id": "95066bbb72da4a8eaf6cc08df97c92a8"
                },
                {
                    "duration": "3 giờ",
                    "originalPrice": null,
                    "price": 699000,
                    "name": "Trợ lý cá nhân tiếng Anh / Hoa",
                    "subname": "",
                    "description": `${descriptionEn}`,
                    "description_following": "",
                    "currency": "đ",
                    "locale": "en",
                    "id": "665c4f03849c4379802bd4da346bf83d"
                },
                {
                    "duration": "3 giờ",
                    "originalPrice": null,
                    "price": 799000,
                    "name": "Trợ lý cá nhân tiếng Khmer",
                    "subname": "",
                    "description": `${descriptionKm}`,
                    "description_following": "",
                    "currency": "đ",
                    "locale": "km",
                    "id": "9ad7034872c3485b8aad87d8a408f3f5"
                }
            ],
            "name": `${nameConfig}`,
            "subname": `${subnameConfig}`,
            "htmlTitle": `${htmlTitle}`,
            "id": "9ed444d567f4492fb0e68655b5934b3b",
            "medproCareNote": "",
            "note": "Trong thời gian quy định, nếu quý khách hủy\nphiếu khám sẽ được hoàn lại tiền khám và\ncác dịch vụ đặt thêm (không bao gồm phí\ntiện ích)."
        }

        const care247VersionApp = await this.globalSettingService.findByKeyAndRepoName('CARE247_CONTENT_VERSION_APP')
        let checkVersion = false;
        if (version && `${version}`.trim() != '') {
            const splitVersion = `${version}`.trim().split('.');
            if (splitVersion.length > 0) {
                const joinVersionNumber = Number(splitVersion.join('')) || 0;
                if (joinVersionNumber < +care247VersionApp) {
                    checkVersion = true
                }
            }
        }
        if (['android', 'ios'].includes(platform) && checkVersion) {
            medproCare = {
                "addonServices": [
                    {
                        "duration": "3 giờ",
                        "originalPrice": 399000,
                        "price": 299000,
                        "name": "Trợ lý cá nhân, giúp việc",
                        "subname": "",
                        "description": `<p>
                            <ul style='list-style-type: disc'>
                            <li><b>Đối tượng hỗ trợ:</b> Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                            <li><b>Nội dung dịch vụ:</b> Trợ lý cá nhân trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                            <li><b>Thời gian phục vụ:</b> 3 giờ</li>
                            <li><b>Hotline đặt nhanh:</b> 1900 - 2267</li>
                            </ul>
                            <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                            <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                            <p style='color: red'>** Ưu đãi giảm ${this.getPriceText(discountObj.vi)}đ cho các lần đặt tiếp theo</p>
                            </p>`,
                        "description_following": "",
                        "currency": "đ",
                        "locale": "vi",
                        "id": "95066bbb72da4a8eaf6cc08df97c92a8"
                    },
                    {
                        "duration": "3 giờ",
                        "originalPrice": null,
                        "price": 699000,
                        "name": "Trợ lý cá nhân tiếng Anh / Hoa",
                        "subname": "",
                        "description": `<p>
                            <ul style='list-style-type: disc'>
                            <li><b>Đối tượng hỗ trợ:</b> Người nước ngoài/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                            <li><b>Nội dung dịch vụ:</b> Phiên dịch, trợ lý cá nhân trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                            <li><b>Thời gian phục vụ:</b> 3 giờ</li>
                            <li><b>Hotline đặt nhanh:</b> 1900 - 2267</li>
                            </ul>
                            <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                            <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                            <p style='color: red'>** Ưu đãi giảm ${this.getPriceText(discountObj.en)}đ cho các lần đặt tiếp theo</p>
                            </p>`,
                        "description_following": "Hỗ trợ Tiếng Anh / Hoa mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                        "currency": "đ",
                        "locale": "en",
                        "id": "665c4f03849c4379802bd4da346bf83d"
                    },
                    {
                        "duration": "3 giờ",
                        "originalPrice": null,
                        "price": 799000,
                        "name": "Trợ lý cá nhân tiếng Khmer",
                        "subname": "",
                        "description": `<p>
                            <ul style='list-style-type: disc'>
                            <li><b>Đối tượng hỗ trợ:</b> Người nước ngoài/ Người sử dụng bảo hiểm sức khỏe dịch vụ/ Người đi lại khó khăn/ Người có nhu cầu</li>
                            <li><b>Nội dung dịch vụ:</b> Phiên dịch, trợ lý cá nhân trước/trong/sau khám bệnh hoặc du lịch y tế, xử lý các thủ tục bảo hiểm dịch vụ, hỗ trợ nhận và gửi thuốc về nhà khi có nhu cầu (tiền thuốc và phí gửi chưa bao gồm), hỗ trợ người đi lại khó khăn</li>
                            <li><b>Thời gian phục vụ:</b> 3 giờ</li>
                            <li><b>Hotline đặt nhanh:</b> 1900 - 2267</li>
                            </ul>
                            <p>Bằng việc lựa chọn gói dịch vụ này, quý khách đồng ý chia sẻ họ tên và số điện thoại cho Care247 để thực hiện liên lạc</p>
                            <b>Sau khi đặt khám thành công, bộ phận dịch vụ sẽ liên hệ với bạn<WARNING_MESSAGE>.</b>
                            <p style='color: red'>** Ưu đãi giảm ${this.getPriceText(discountObj.km)}đ cho các lần đặt tiếp theo</p>
                            </p>`,
                        "description_following": "Hỗ trợ Khmer mức giao tiếp cơ bản theo các dịch vụ dưới đây",
                        "currency": "đ",
                        "locale": "km",
                        "id": "9ad7034872c3485b8aad87d8a408f3f5"
                    }
                ],
                "name": "Dịch vụ đặt thêm (Không bắt buộc - Không phải CSYT cung cấp)",
                "subname": "Vận chuyển, phiên dịch, trợ lý cá nhân (Personal Assistant),...",
                "id": "9ed444d567f4492fb0e68655b5934b3b",
                "medproCareNote": "",
                "note": "Trong thời gian quy định, nếu quý khách hủy\nphiếu khám sẽ được hoàn lại tiền khám và\ncác dịch vụ đặt thêm (không bao gồm phí\ntiện ích)."
            }
        }
        

        //Kiểm tra thời gian hiện tại
        const now = moment().utc();

        // Tạo mốc thời gian cho 22h (10:00 PM) hôm nay
        const startNight = moment().hour(22).minute(0).second(0);

        // Tạo mốc thời gian cho 6h (6:00 AM) ngày mai
        const endNight = moment().hour(6).minute(0).second(0).add(1, 'day');

        const isTimeAfter22h = now.isBetween(startNight, endNight)

        if (isTimeAfter22h) {
            medproCare = { ...medproCare, 
                addonServices: medproCare?.addonServices?.map(s => { 
                    return { ...s, description: s.description.replace('<WARNING_MESSAGE>', ' trong khoảng thời gian từ 6h - 22h') }
                })
            }
        } else {
            medproCare = { ...medproCare, 
                addonServices: medproCare?.addonServices?.map(s => { 
                    return { ...s, description: s.description.replace('<WARNING_MESSAGE>', '') }
                })
            }
        }

        if ((partnerId === 'choray' || partnerId === 'umc') && !isPrimary && !isCs) {
            medproCare = { ...medproCare, addonServices: medproCare?.addonServices?.slice(1) }
        }

        if (partnerId === 'umc' || partnerId === 'umc2') {
            medproCare = { ...medproCare, addonServices: medproCare?.addonServices?.map(item => {
                if (item.locale === 'vi') {
                    return { ...item, 
                        originalPrice: null,
                        price: 399000, 
                    }
                } else {
                    return { ...item }
                }
            }) }
        }

        const medproCarePartner2Bill = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_2_BILL')
        const listPartner2Bill = medproCarePartner2Bill.split(',');

        if (listPartner2Bill.includes(partnerId)) {
            medproCare = { ...medproCare, partner2bill: true }
        }

        const medproCareData = {...medproCare, provider: care247InfoJson}
        return medproCareData;
    }

    async insertOrUpdateBookingSlot(appId: string, partnerId: string, bookingSlotFormDTO: any, serviceTrackingLog?: any): Promise<any> {
        /* dành cho bv cũ hoặc mới */
        const newSet = this.utilService.oldHospitalSync();
        const getTreeId = bookingSlotFormDTO?.extraBookingInfo?.treeId;
        const treeIdGlobal = await this.globalSettingService.findByKeyAndRepoName('TREE_ID_PAYMENT_V2');
        const treeIdGlobalSet = new Set(treeIdGlobal.split(','));
        const checkCTCH = await this.checkCTCH(partnerId);
        if (this.listAppId.has(appId) && !!partnerId && newSet.has(partnerId) && !treeIdGlobalSet.has(getTreeId) && checkCTCH) {
            let getBookingSlot = await this.findOneBookingSlot(bookingSlotFormDTO.partnerId, bookingSlotFormDTO.bookingSlotId);
            const { bookingCode, extraBookingInfo, ...rest } = bookingSlotFormDTO;
            if (!getBookingSlot) {
                getBookingSlot = await this.insertBookingSlot(rest);
            }
            // getBookingSlot = await this.findOneBookingSlot(bookingSlotFormDTO.partnerId, bookingSlotFormDTO.bookingSlotId);
            const bookingHis = new this.bookingHisModel();
            bookingHis.bookingId = `${getBookingSlot.bookingSlotId}_${uuid.v4().replace(/-/g, '')}`;
            bookingHis.bookingSlotId = getBookingSlot.bookingSlotId;
            bookingHis.date = moment(getBookingSlot.startTime).toDate();
            bookingHis.serviceId = getBookingSlot.serviceId;
            bookingHis.patientId = bookingSlotFormDTO.patientId;
            bookingHis.patient = bookingSlotFormDTO.patient;
            bookingHis.subjectId = bookingSlotFormDTO.subjectId;
            bookingHis.roomId = bookingSlotFormDTO.roomId;
            bookingHis.doctorId = bookingSlotFormDTO.doctorId;
            bookingHis.status = BookingStatus.RESERVE;
            return bookingHis.toJSON();
        } else {
            /* tiến hành kiểm tra them timeId */
            const dynamicTimeId = bookingSlotFormDTO.bookingSlotId;
            const baseUrl = await this.getBookingTreeRestApi(bookingSlotFormDTO.partnerId);

            try {
                const StartExactTimeId = moment().toDate();
                const timeIdData: any = (await this.getExactTimeId(baseUrl, bookingSlotFormDTO).toPromise()).data;
                const EndExactTimeId = moment().toDate();
                this.logger.log(`Lấy getExactTimeId: ${moment(EndExactTimeId).diff(StartExactTimeId, 'milliseconds')} ms`)
                const { timeId, outOfNumber = false } = timeIdData;
                if (outOfNumber) {
                    throw new HttpException('Giờ khám đã kín, vui lòng chọn giờ khác', HttpStatus.BAD_REQUEST);
                }
                if (timeId) {
                    const formatNgay = moment(bookingSlotFormDTO.startTime).add(7, 'hours').format('YYMMDD');
                    bookingSlotFormDTO.bookingSlotId = `${formatNgay}_${timeId}_${bookingSlotFormDTO.partnerId}`;
                }
            } catch (error) {

                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'getExactTimeId',
                    summary: 'Lay thong tin getExactTimeId',
                    nameParent: 'insertOrUpdateBookingSlot',
                    params: { baseUrl, bookingSlotFormDTO },
                    errorBody: this.utilService.errorHandler(error),
                    message: error?.message || `Khong kiem tra duoc thong tin getExactTimeId.`,
                    ...serviceTrackingLog,
                });

                const { response } = error;
                if (response && response === 'Giờ khám đã kín, vui lòng chọn giờ khác') {
                    throw new HttpException('Giờ khám đã kín, vui lòng chọn giờ khác', HttpStatus.BAD_REQUEST);
                }
            }

            let getBookingSlot = await this.findOneBookingSlot(bookingSlotFormDTO.partnerId, bookingSlotFormDTO.bookingSlotId);
            const { bookingCode, ...rest } = bookingSlotFormDTO;
            if (!getBookingSlot) {
                getBookingSlot = await this.insertBookingSlot(rest);
            }

            // const getBookingSlot = await this.findOneBookingSlot(bookingSlotFormDTO.partnerId, bookingSlotFormDTO.bookingSlotId);
            /* Kiểm tra xem có expired booking hay không */
            const bookingHis = new this.bookingHisModel();
            const getExpiredBookings = await this.expiredBookingModel
                .find({ bookingSlotId: getBookingSlot.bookingSlotId })
                .sort({ createdAt: 'asc' })
                .limit(1)
                .exec();

            if (getExpiredBookings.length > 0) {
                const firstExpiredBooking: IExpiredBooking = first(getExpiredBookings);
                const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: bookingSlotFormDTO.partnerId }, { getSequenceNumberAfterSuccessPayment: true }).exec();

                // Lấy số sau thanh toán thì không xử lý expired booking
                if (!bookingSlotFormDTO.extraBookingInfo?.noPayment && partnerConfig.getSequenceNumberAfterSuccessPayment) {
                    bookingHis.bookingId = `${firstExpiredBooking.bookingId}_${moment().valueOf()}`;
                    bookingHis.bookingSlotId = getBookingSlot.bookingSlotId;
                    bookingHis.date = getBookingSlot.startTime;
                    bookingHis.serviceId = getBookingSlot.serviceId;
                    bookingHis.patientId = bookingSlotFormDTO.patientId;
                    bookingHis.patient = bookingSlotFormDTO.patient;
                    bookingHis.subjectId = bookingSlotFormDTO.subjectId;
                    bookingHis.roomId = bookingSlotFormDTO.roomId;
                    bookingHis.doctorId = bookingSlotFormDTO.doctorId;
                    bookingHis.status = BookingStatus.RESERVE;

                    return {
                        ...bookingHis.toJSON(),
                        bookingSlotForm: {
                            bookingSlotIdCheckExpiredBooking: getBookingSlot.bookingSlotId,
                            ...bookingSlotFormDTO,
                            bookingSlotId: dynamicTimeId, // gắn lại dynamic time để lock số trường hợp không còn expiredBooking
                        }
                    };
                }

                /* tiến hành xử lý */
                await this.descrementAvailableSlot(getBookingSlot.bookingSlotId);
                /* tiến hành remove đi */
                await this.expiredBookingModel.findByIdAndRemove({ _id: firstExpiredBooking._id }).exec();
                bookingHis.bookingId = `${firstExpiredBooking.bookingId}_${moment().valueOf()}`;
                bookingHis.bookingSlotId = firstExpiredBooking.bookingSlotId;
                bookingHis.date = firstExpiredBooking.date;
                bookingHis.serviceId = firstExpiredBooking.serviceId;
                bookingHis.patientId = bookingSlotFormDTO.patientId;
                bookingHis.patient = bookingSlotFormDTO.patient;
                bookingHis.subjectId = firstExpiredBooking.subjectId;
                bookingHis.roomId = firstExpiredBooking.roomId;
                bookingHis.doctorId = firstExpiredBooking.doctorId;
                bookingHis.status = BookingStatus.RESERVE;
                const getSequenceNumberExpired = get(firstExpiredBooking, 'sequenceNumber', null);
                /* kiểm tra xem có sequenceNumber thì gắn vào */
                if (getSequenceNumberExpired) {
                    bookingHis.sequenceNumber = getSequenceNumberExpired;
                }
                return bookingHis;
            }

            const curSlot = getBookingSlot.maxSlot - getBookingSlot.availableSlot;
            const minutes = moment(getBookingSlot.endTime).diff(moment(getBookingSlot.startTime), 'minutes'); // bao nhiêu phút
            const perSlotMinute = Math.floor(minutes / getBookingSlot.maxSlot); // 1 slot trong mấy phút
            bookingHis.bookingId = `${uuid.v4().replace(/-/g, '')}_${getBookingSlot.bookingSlotId}_${curSlot}`;
            bookingHis.bookingSlotId = getBookingSlot.bookingSlotId;
            bookingHis.date = getBookingSlot.startTime;
            bookingHis.serviceId = getBookingSlot.serviceId;
            bookingHis.patientId = bookingSlotFormDTO.patientId;
            bookingHis.patient = bookingSlotFormDTO.patient;
            bookingHis.subjectId = bookingSlotFormDTO.subjectId;
            bookingHis.roomId = bookingSlotFormDTO.roomId;
            bookingHis.doctorId = bookingSlotFormDTO.doctorId;

            if (getBookingSlot.availableSlot > 0) {
                bookingHis.status = BookingStatus.RESERVE;
                bookingSlotFormDTO.bookingSlotId = dynamicTimeId;
                const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: bookingSlotFormDTO.partnerId }, { getSequenceNumberAfterSuccessPayment: true }).exec();
                if (!bookingSlotFormDTO.extraBookingInfo?.noPayment && partnerConfig.getSequenceNumberAfterSuccessPayment) {
                    return {
                        ...bookingHis.toJSON(),
                        bookingSlotForm: {
                            ...bookingSlotFormDTO,
                        }
                    };
                }

                const availableSlot = getBookingSlot.availableSlot - 1; // cập nhật lại bookingSlot
                const minutesAdd = curSlot * perSlotMinute;
                bookingHis.date = moment(getBookingSlot.startTime).add(minutesAdd, 'minutes').toDate();
                /* tiến hành tạo bội số */
                let throwMessageError = '';

                try {
                    const loopArray = Array.from(Array(20));
                    let isOk = false;
                    let isOutofNumber = false;
                    const baseUrl2 = await this.getBookingTreeRestApi(bookingSlotFormDTO.partnerId);
                    for await (const detail of loopArray) {

                        const StartGetAndBlock = moment().toDate();
                        const dataBoiSo = (await this.layBoiSo(baseUrl2, bookingSlotFormDTO).toPromise()).data;
                        const EndGetAndBlock = moment().toDate();
                        this.logger.log(`Get AND Block: ${moment(EndGetAndBlock).diff(StartGetAndBlock, 'milliseconds')} ms`)
                        const { outOfNumber, syncErrorMessage = '' } = dataBoiSo;
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'BookingHIS',
                            summary: 'BookingHIS',
                            nameParent: 'insertOrUpdateBookingSlot',
                            params: bookingSlotFormDTO,
                            response: dataBoiSo,
                            errorBody: dataBoiSo,
                            message: '',
                            ...serviceTrackingLog,
                        });
                        if (outOfNumber) {
                            isOutofNumber = true;
                            throwMessageError = 'Giờ khám đã kín, vui lòng chọn giờ khác';
                            break;
                        }

                        if (syncErrorMessage) {
                            isOutofNumber = true;
                            throwMessageError = `${syncErrorMessage}`;
                            /* throw error duplicate sequence number */
                            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                name: 'layBoiSo',
                                summary: 'syncErrorMessage',
                                nameParent: 'insertOrUpdateBookingSlot',
                                params: { baseUrl2, bookingSlotFormDTO },
                                response: null,
                                errorBody: { message: `${syncErrorMessage}` },
                                message: `${syncErrorMessage}`,
                                ...serviceTrackingLog,
                            });
                            break;
                        }

                        const sequenceNumber = dataBoiSo.number;
                        bookingHis.sequenceNumber = sequenceNumber;
                        bookingHis.date = moment(dataBoiSo.date).toDate();
                        /* lấy thông tin bookingData */
                        const getBookingData = get(dataBoiSo, 'bookingData', {});
                        let constraintsBoiSo = sequenceNumber;
                        if (Object.keys(getBookingData).length > 0) {
                            const getSyncStatus = get(getBookingData, 'syncStatus', null);
                            const getBookingInternalId = get(getBookingData, 'bookingInternalId', '');
                            const getRoomId = get(getBookingData, 'roomId', null);
                            const getSubjectId = get(getBookingData, 'subjectId', null);
                            const getDoctorId = get(getBookingData, 'doctorId', null);
                            const getServiceId = get(getBookingData, 'serviceId', null);
                            const getSectionId = get(getBookingData, 'sectionId', null);
                            if (getRoomId) {
                                bookingHis.roomId = getRoomId;
                                constraintsBoiSo = `${constraintsBoiSo}_${getRoomId}`;
                            }
                            if (getSubjectId) {
                                bookingHis.subjectId = getSubjectId;
                                constraintsBoiSo = `${constraintsBoiSo}_${getSubjectId}`;
                            }
                            if (getDoctorId) {
                                bookingHis.doctorId = getDoctorId;
                                constraintsBoiSo = `${constraintsBoiSo}_${getDoctorId}`;
                            }
                            if (getServiceId) {
                                bookingHis.serviceId = getServiceId;
                                constraintsBoiSo = `${constraintsBoiSo}_${getServiceId}`;
                            }
                            if (getSyncStatus) {
                                bookingHis.syncStatus = getSyncStatus;
                            }

                            if (getBookingInternalId) {
                                bookingHis.bookingInternalId = getBookingInternalId;
                            }

                            if (getSectionId) {
                                bookingHis.sectionId = getSectionId;
                            }

                            constraintsBoiSo = `${constraintsBoiSo}_${bookingSlotFormDTO.partnerId}_${moment(bookingSlotFormDTO.startTime).add(7, 'hours').format('YYMMDD')}`;
                            try {
                                /* Tiến hành lưu constraints */
                                const newConstraints = new this.constraintsSequenceNumberModel({
                                    constraints: constraintsBoiSo,
                                    bookingCode: bookingSlotFormDTO.bookingCode,
                                });
                                await newConstraints.save();
                                isOk = true;
                                // break;
                            } catch (error) {
                                /* throw error duplicate sequence number */
                                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                    name: 'layBoiSo',
                                    summary: 'Trùng số',
                                    nameParent: 'insertOrUpdateBookingSlot',
                                    params: { baseUrl2, bookingSlotFormDTO },
                                    response: { ...dataBoiSo, constraints: constraintsBoiSo, bookingCode: bookingSlotFormDTO.bookingCode },
                                    errorBody: this.utilService.errorHandler(error),
                                    message: error?.message || `Trùng số.`,
                                    ...serviceTrackingLog,
                                });
                            }
                            if (isOk) {
                                break;
                            }
                        }
                    }
                    if (isOutofNumber) {
                        throw new HttpException(throwMessageError, HttpStatus.BAD_REQUEST);
                    }
                } catch (error) {
                    /* nếu ko timeId thì lấy cũ. có thì lấy cái mới. */
                    // this.clientSentry.instance().captureException(error);
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'layBoiSo',
                        summary: 'Lay thong tin layBoiSo',
                        nameParent: 'insertOrUpdateBookingSlot',
                        params: {},
                        errorBody: this.utilService.errorHandler(error),
                        message: error?.message || `Khong kiem tra duoc thong tin layBoiSo.`,
                        ...serviceTrackingLog,
                    });

                    if (error.response?.data?.errorDetail === "Partner hasn't got booking number config") {
                        await this.descrementAvailableSlot(getBookingSlot.bookingSlotId);
                    }

                    if (throwMessageError) {
                        throw new HttpException(throwMessageError, HttpStatus.BAD_REQUEST);
                    }
                }

                return bookingHis.toJSON();
            } else {
                bookingHis.status = BookingStatus.FULL;
            }

            // this.eventEmitter.emit(LOG_SERVICE_EVENT, {
            //     name: 'BookingHIS',
            //     summary: 'BookingHIS',
            //     nameParent: 'insertOrUpdateBookingSlot',
            //     params: bookingHis.toObject(),
            //     response: null,
            //     errorBody: bookingHis.toObject(),
            //     message: '',
            // });

            return bookingHis.toJSON();

        }
    }

    async insertOrUpdateBookingSlot2(appId: string, partnerId: string, bookingSlotFormDTO: any): Promise<any> {
        const bookingHis = new this.bookingHisModel();
        bookingHis.bookingId = `NO_BOOKING_TIME_${uuid.v4().replace(/-/g, '')}`;
        // bookingHis.bookingSlotId = `NO_BOOKING_TIME_${uuid.v4().replace(/-/g, '')}`;
        // bookingHis.date = moment(getBookingSlot.startTime).toDate();
        bookingHis.serviceId = bookingSlotFormDTO.serviceId;
        bookingHis.patientId = bookingSlotFormDTO.patientId;
        bookingHis.patient = bookingSlotFormDTO.patient;
        bookingHis.subjectId = bookingSlotFormDTO.subjectId;
        bookingHis.roomId = bookingSlotFormDTO.roomId;
        bookingHis.doctorId = bookingSlotFormDTO.doctorId;
        bookingHis.status = BookingStatus.RESERVE;
        return bookingHis.toJSON();
    }

    async findOneBookingSlot(partnerId: string, bookingSlotId: string): Promise<IBookingSlot> {
        const bookingSlots = await this.bookingSlotModel
            .find({ bookingSlotId })
            .limit(1)
            .exec();
        return first(bookingSlots);
    }

    async findOneBookingHis(partnerId: string, bookingId: string): Promise<IBookingHIS> {
        const bookingsHis = await this.bookingHisModel
            .find({ bookingId, partnerId })
            .limit(1)
            .exec();
        return first(bookingsHis);
    }

    async insertBookingSlot(bookingSlotFormDTO: BookingSlotFormDTO): Promise<any> {
        const bookingSlot = new this.bookingSlotModel(bookingSlotFormDTO);
        try {
            return (await bookingSlot.save()).toJSON();
        } catch (err) {
            // console.log('err insertBookingSlot', err);
            if (err.code === 11000) {
                const findBookingSlot = await this.bookingSlotModel.findOne({ bookingSlotId: bookingSlotFormDTO.bookingSlotId }).exec();

                if (findBookingSlot) {
                    return findBookingSlot.toJSON();
                } else {
                    return bookingSlotFormDTO;
                    // throw new HttpException({
                    //     message: 'Không tìm thấy duplicate bookingSlot!',
                    // }, HttpStatus.BAD_REQUEST);
                }
            } else {
                throw err;
            }

        }
    }

    async insertBookingHis(partnerid: string, bookingHis: IBookingHIS): Promise<IBookingHIS> {
        bookingHis.partnerId = partnerid;
        return (await bookingHis.save()).toJSON();
    }

    async updateOneBookingSlot(bookingSlotId: string, availableSlot: number): Promise<any> {
        try {
            return (await this.bookingSlotModel
                .findOne({ bookingSlotId }, { availableSlot }, { new: true })
                .exec()).toJSON();
        } catch (error) {
            // console.log('error updateOneBookingSlot', error);
            this.clientSentry.instance().captureException(error);
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        }

    }

    async updateStatusBookingHis(id: string, numberStatus: number): Promise<any> {
        try {
            return (await this.bookingHisModel
                .findByIdAndUpdate({ _id: id }, { status: numberStatus }, { new: true })
                .exec()).toJSON();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            throw new HttpException({
                message: 'Có lỗi xảy ra.',
                status: HttpStatus.BAD_REQUEST,
            }, HttpStatus.BAD_REQUEST);
        }
    }

    async getBookingInfo(bookingId: string): Promise<any> {
        const bookingsHis = await this.bookingHisModel
            .find({ bookingId })
            .limit(1)
            .exec();
        return first(bookingsHis);
    }

    async cancelReservation(bookingId: string): Promise<any> {
        const bookingInfo = await this.getBookingInfo(bookingId);
        if (!bookingInfo) {
            throw new HttpException({
                message: 'Không tìm thấy thông tin booking',
                status: HttpStatus.BAD_REQUEST,
            }, HttpStatus.BAD_REQUEST);
        }
        if (bookingInfo.status === BookingStatus.CONFIRMED) {
            throw new HttpException({
                message: `Booking này ${bookingId} đã confirmned, không hủy được.`,
                status: HttpStatus.BAD_REQUEST,
            }, HttpStatus.BAD_REQUEST);
        }
        // cập nhật trạng thái hủy của booking
        const bookingUpdateInfo = await this.updateStatusBookingHis(bookingInfo.id, BookingStatus.CANCEL_RESERVATION);
        return bookingUpdateInfo;
    }

    async updateBookingNumber(bookingCode: string): Promise<any> {
        try {
            return this.bookingNumberModel.findOneAndUpdate({ bookingCode }, { bookingCode: null, used: false }, { new: true }).exec();
        } catch (err) {
            Logger.error(`Error when exec updateBookingNumber() with bookingCode: ${bookingCode}\nError message: ${err.message}`);
        }
    }

    syncPatient(params: SyncPatientEvent) {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/syncPatient`;
        return this.httpService.post(url, params).toPromise();
    }

    async callConfirmUpdateBooking(bookingId: string, partnerId: string) {
        const baseUrl = await this.getBookingTreeRestApi(partnerId);
        const url = `${baseUrl}/schedule/v2/booking/confirm-update-booking/${bookingId}`;
        try {
            const { data } = await this.httpService.get(url).toPromise();
            // console.log(`response confirm-update-booking ${bookingId}: `, data);
            return data;
        } catch (err) {
            console.error(`error confirm-update-booking ${bookingId}: `, err);
        }

    }

    public async getMedproCarePost(formData: any, appId: string, partnerId: string, userId: string, platform?: string, version?: string, locale?: string,
        isCs?: boolean
    ): Promise<any> {
        const { bookingDate } = formData;
        const timeConfig = await this.globalSettingService.findByKeyAndRepoName('TIME_BEFORE_BOOKING_CARE247');
        const timeMessageConfig = await this.globalSettingService.findByKeyAndRepoName('TIME_BEFORE_BOOKING_CARE247_MESSAGE');
        const timeConfigObj = timeConfig ? JSON.parse(timeConfig) : {};
        const times = timeConfigObj[partnerId]
        const diffInHours = moment(bookingDate).utc().diff(moment().utc(), 'minutes');
        if (diffInHours < times) {
            throw new HttpException(timeMessageConfig.replace(`[count]`, times) || 'Dịch vụ Care247 chỉ được đặt sau 2 tiếng.', HttpStatus.BAD_REQUEST)
        }
        return this.getMedproCareFromPortal(appId, partnerId, userId, platform, version, locale, isCs, bookingDate)
    }

    public async getMedproCareFromPortal(appId: string, partnerId: string, userId: string, platform?: string, version?: string, locale?: string,
        isCs?: boolean, date?: Date, isPrimary?: boolean
    ): Promise<any> {
        if (locale === 'km') {
            throw new HttpException('Hệ thống chưa hỗ trợ ngôn ngữ này.', HttpStatus.BAD_REQUEST)
        }
        const medproCarePartnerList = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_LIST')
        let listPartner = medproCarePartnerList.split(',')
        if (REPO_NAME_BETA.includes(this.repoName)) {
            listPartner = [...listPartner, 'umc', 'dalieuhcm']
        }
        let medproCarePartnerListArr = new Set(listPartner);
        const onOffChoRay = await this.globalSettingService.findByKeyAndRepoName('ON_OFF_CHORAY_CARE247')
        if (onOffChoRay === 'ON') {
            medproCarePartnerListArr = new Set(listPartner).add('choray');
        }
        if (appId === 'medpro' && medproCarePartnerListArr.has(partnerId)) {
            // ? TEST ONLY const fakeBaseUrl  = `https://portal-testing.medpro.com.vn`
            // let baseUrl = await this.getBookingTreeRestApi(partnerId);
            // if (REPO_NAME_BETA.includes(this.repoName)) {
            //     baseUrl = this.urlConfigService.getBookingTreeUrl();
            // }
            // const url = `${baseUrl}/schedule/v2/booking/service-plus/${partnerId}`
            try {
                const care247VersionApp = await this.globalSettingService.findByKeyAndRepoName('CARE247_CONTENT_VERSION_APP')
                const medproCarePartner2Bill = await this.globalSettingService.findByKeyAndRepoName('MEDPRO_CARE_PARTNER_2_BILL')
                const listPartner2Bill = medproCarePartner2Bill.split(',');

                let checkVersion = false;
                if (version && `${version}`.trim() != '') {
                    const splitVersion = `${version}`.trim().split('.');
                    if (splitVersion.length > 0) {
                        const joinVersionNumber = Number(splitVersion.join('')) || 0;
                        if (joinVersionNumber < +care247VersionApp) {
                            checkVersion = true
                        }
                    }
                }
                if (['android', 'ios'].includes(platform) && checkVersion && listPartner2Bill.includes(partnerId)) {
                    throw new HttpException('Partner chưa cấu hình thông tin medpro care', HttpStatus.BAD_REQUEST)
                }
                const data = await this.getDataMedproCare(partnerId, platform, version, isPrimary, isCs);
                return this.checkDiscountCare247UserBookingAgain(data, userId, date);
            } catch (error) {
                console.log('error', error);
                
                throw new HttpException('Partner chưa cấu hình thông tin medpro care', HttpStatus.BAD_REQUEST)
            }
        }
        throw new HttpException('Partner chưa cấu hình thông tin medpro care', HttpStatus.BAD_REQUEST)
    }

    public async getMedproCareFromMessageEvent(appId: string, partnerIdHeaders: string, partnerId: string, platform: string,
        version: string, transactionId?: string): Promise<any> {
        try {
            const getBooking = await this.bookingModel.findOne({ transactionId }).exec();
            const isPrimary = true;
            const data = await this.getDataMedproCare(partnerId, platform, version, isPrimary); 
            if (getBooking && getBooking.userId) {
                return this.checkDiscountCare247UserBookingAgain(data, getBooking.userId, getBooking.date);
            }
            return data;
        } catch (error) {
            console.log('error', error);
        }
    }

    public async checkDiscountCare247UserBookingAgain(data: any, userId: string, date?: Date): Promise<any> {
        try {
            const bookingCare247ConstraintUser = await this.bookingCare247ConstraintUserModel
                .findOne({ userId })
                .populate({ path: 'care247', select: { date: true, status: true } })
                .populate({ path: 'latestPartner', select: 'name' })
                .exec();
            if (bookingCare247ConstraintUser) { // user đã từng đặt care247
                const constraintUserObj = bookingCare247ConstraintUser.toObject();
                const today = moment();
                const checkDiscount = constraintUserObj.care247.some(c => moment(c.date).isBefore(today) && c.status === 1);
                if (checkDiscount) {
                    const discount = await this.globalSettingService.findByKeyAndRepoName('CARE247_DISCOUNT_REBOOKING');
                    const discountObj = discount ? JSON.parse(discount) : {};
                    const addonServices = data.addonServices.map(s => {
                        if (s.locale === 'vi' && discountObj.vi > 0) {
                            return { ...s, 
                                originalPrice: s.price < s.originalPrice ? s.originalPrice : s.price,
                                price: s.price < s.originalPrice ? s.price : s.price - discountObj.vi, 
                                discountPrice: s.price < s.originalPrice ? null : discountObj.vi,
                                rebooking: true
                            }
                        } else if (s.locale === 'en' && discountObj.en > 0) {
                            return { ...s, 
                                originalPrice: s.price < s.originalPrice ? s.originalPrice : s.price,
                                price: s.price < s.originalPrice ? s.price : s.price - discountObj.en, 
                                discountPrice: s.price < s.originalPrice ? null : discountObj.en,
                                rebooking: true
                            }
                        } else if (s.locale === 'km' && discountObj.km > 0) {
                            return { ...s, 
                                originalPrice: s.price < s.originalPrice ? s.originalPrice : s.price,
                                price: s.price < s.originalPrice ? s.price : s.price - discountObj.km, 
                                discountPrice: s.price < s.originalPrice ? null : discountObj.km,
                                rebooking: true
                            }
                        }
                        return { ...s }
                    })
                    return { ...data, addonServices };
                }
            } else { // user chưa từng đặt care247
                const checkDiscount = this.isWeekdayGoldenHourCare247(date) //check khung giờ vàng
                if (checkDiscount) {
                    const discount = await this.globalSettingService.findByKeyAndRepoName('CARE247_DISCOUNT_GOLDEN_HOUR');
                    const discountObj = discount ? JSON.parse(discount) : {};
                    const addonServices = data.addonServices.map(s => {
                        if (s.locale === 'vi' && discountObj.vi > 0) {
                            return { ...s, 
                                originalPrice: s.price < s.originalPrice ? s.originalPrice : s.price,
                                price: s.price < s.originalPrice ? s.price : s.price - discountObj.vi, 
                                discountPrice: s.price < s.originalPrice ? null : discountObj.vi,
                                goldenHour: true
                            }
                        } else if (s.locale === 'en' && discountObj.en > 0) {
                            return { ...s, 
                                originalPrice: s.price < s.originalPrice ? s.originalPrice : s.price,
                                price: s.price < s.originalPrice ? s.price : s.price - discountObj.en, 
                                discountPrice: s.price < s.originalPrice ? null : discountObj.en,
                                goldenHour: true
                            }
                        } else if (s.locale === 'km' && discountObj.km > 0) {
                            return { ...s, 
                                originalPrice: s.price < s.originalPrice ? s.originalPrice : s.price,
                                price: s.price < s.originalPrice ? s.price : s.price - discountObj.km, 
                                discountPrice: s.price < s.originalPrice ? null : discountObj.km,
                                goldenHour: true
                            }
                        }
                        return { ...s }
                    })
                    return { ...data, addonServices };
                }
            }
            return data
        } catch (error) {
            console.log('error', error);
        }
    }

    public async isWeekdayGoldenHourCare247(bookingDate?: Date) {
        const configHour = await this.globalSettingService.findByKeyAndRepoName('CARE247_GOLDEN_HOUR');
        const rangeHour = configHour.split(',');

        const date = moment(bookingDate).utc();
        const formattedDate = date.format('YYYY-MM-DD');

        const dayOfWeek = date.day(); // 0 (Sunday) to 6 (Saturday)
      
        // Check for Monday to Saturday (dayOfWeek 1 to 6)
        if (dayOfWeek === 0) { // Sunday
          return false;
        }
        
        const startTime = `${formattedDate}T${rangeHour[0]}:00Z`
        const endTime = `${formattedDate}T${rangeHour[1]}:00Z`
        
        return date.isBetween(startTime, endTime, null, '[]')
    }
}
