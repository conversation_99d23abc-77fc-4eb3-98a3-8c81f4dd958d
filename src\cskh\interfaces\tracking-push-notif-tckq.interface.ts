import { Document } from 'mongoose';

export interface ITrackingPushNotifTCKQ extends Document {
    eventId: string;
    processId: string;
    syncStatus: string;
    appId: string;
    topic: string;
    topicBooking: string;
    partnerId: string;
    partnerName: string;
    type: number;
    title: string;
    transactionId: string;
    userId: string;
    notifAppId: string;
    notifApiKey: string;
    clientIds: string[];
    eventData: any;
    transporter: string;
    mailTemplate: string;
    repoName: string;
    clientViewId: string;
    viewResponse: any;
    booking: string;
    user: string;
    status: string;
    sent: number;
    ctr: string;
    oneSignalNotifInfo: any
    examResults: any
    viewNotif: number
}
