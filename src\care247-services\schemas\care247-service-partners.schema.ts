import * as mongoose from 'mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { Care247ServicesSchema } from './care247-services.schema';
import { CARE247_SERVICES_PARTNERS } from './constants';

const Schema = mongoose.Schema;

export const Care247ServicesPartnerSchema = new Schema(
    {
        hospital: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME, required: true },
        status: { type: Boolean, required: true, default: false },
        customPrice: { type: Boolean, required: false },
        addonServices: [Care247ServicesSchema],
        timeBeforeBookingCare247: { type: Number, required: false },
    },
    {
        collection: CARE247_SERVICES_PARTNERS,
        timestamps: true,
    },
);
