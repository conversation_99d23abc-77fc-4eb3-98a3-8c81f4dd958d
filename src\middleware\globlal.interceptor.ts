import { LOG_REQUEST_EVENT } from './../audit-log/constant';
import { ReUpdateEnum } from './enum/required-update.enum';
import { get, isArray } from 'lodash';
import { CallHandler, ExecutionContext, Injectable, NestInterceptor, HttpException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { VersionAppService } from 'src/version-app/version-app.service';
import { UrlConfigService } from 'src/config/config.url.service';

@Injectable()
export class GlobalInterceptor implements NestInterceptor {
    constructor(private eventEmitter: EventEmitter2, private versionService: VersionAppService, private configUrlService: UrlConfigService) {}

    async intercept(context: ExecutionContext, next: CallHandler<any>): Promise<Observable<any>> {
        try {
            const request = context.switchToHttp().getRequest();
            this.eventEmitter.emit(LOG_REQUEST_EVENT, request);
            const appId = get(request.headers, 'appid', null);
            let partnerId = get(request.headers, 'partnerid', null);

            // case partnerid null
            if (appId && !!partnerId === false) {
                // tslint:disable-next-line:no-string-literal
                request.headers['partnerid'] = appId;
                partnerId = appId;
            }
            // case xử lý check version mobile
            if (this.configUrlService.getCheckVersionMobile()) {
                const version = get(request.headers, 'version', null);
                const platform = get(request.headers, 'platform', null);
                const requiredUpdated = await this.versionService.checkVersionAppMobile(appId, version, partnerId, platform);
                return next.handle().pipe(
                    map(data => {
                        // nếu response là array
                        if (isArray(data)) {
                            return data;
                        } else {
                            // nếu force thì trả thêm key requiredUpdated
                            return requiredUpdated === ReUpdateEnum.FORCE
                                ? Object.assign({ requiredUpdated }, JSON.parse(JSON.stringify(data)))
                                : data;
                        }
                    }),
                );
            }
            // không xử lý gì cả
            return next.handle();
        } catch (error) {
            throw error;
        }
    }
}
