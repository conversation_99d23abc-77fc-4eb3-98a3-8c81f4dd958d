import * as mongoose from 'mongoose';
import { BOOKING_CANCELLATION_COLLECTION_NAME } from './constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const BookingCancellationSchema = new Schema(
    {
        bookingId: { type: String, required: true },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME, required: true },
        partnerId: { type: String, required: true },
        appId: { type: String, required: true },
        userId: { type: String, required: true },

        // lý do huỷ
        reasonIds: [{ type: String, required: true }],
        reasonsData: [{ type: Schema.Types.Mixed }],
        otherContent: { type: String }, 

        // Thông tin tài khoản ngân hàng (nếu có)
        bankCode: { type: String },
        bankName: { type: String },
        bankNumber: { type: String },
        accountName: { type: String },
        accountType: { type: String, default: 'account'},
        hasBankAccount: { type: Boolean, default: false }, // For easy filtering

        cskhUserId: { type: String },
        cskh: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },

        // Platform and IP info
        platform: { type: String },
        subTotal: { type: Number, default: 0 },
        transactionId: { type: String, required: true, unique: true },
        gatewayId: { type: String},
        paymentMethodDetail: { type: String},
        paymentMethod: { type: String},
        message: { type: String },
        status: { type: Number },
        cancelledBy: { type: String },
        userActionId: { type: String },
        userAction: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        canceledDate: { type: String },

        bookingCode: { type: String },
        bookingDate: { type: Date },
        bookingCreateAt: { type: Date },

        cancelledAt: { type: Date, default: Date.now },
    },
    {
        collection: BOOKING_CANCELLATION_COLLECTION_NAME,
        timestamps: true,
    },
);