import { Injectable, HttpService } from '@nestjs/common';
import * as md5 from 'md5';
import * as moment from 'moment';
import * as crypto from 'crypto';
import { MomoVPConfigService } from 'src/config/config.momo.vp.service copy';
import { UrlConfigService } from 'src/config/config.url.service';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class MomoService {

    constructor(
        private readonly momoVPConfigService: MomoVPConfigService,
        private readonly urlConfigService: UrlConfigService,
        private readonly httpService: HttpService,
    ) { }

    async createFeeVienPhiOrderPlatformWeb(data): Promise<any> {

        const transactionCodeTT = data.transaction_code_tt;
        const amount = 10000;
        const {
            momo_vp_access_key,
            momo_vp_secret_key,
            momo_vp_return_url,
            momo_vp_notify_url,
            momo_vp_partner_code,
        } = this.momoVPConfigService.getMoMo_VP_Config();

        const baseUrl = this.urlConfigService.getBaseUrl();
        const info = data.info ? data.info : `info-${md5(moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS'))}`;
        const params = {
            partnerCode: momo_vp_partner_code,
            accessKey: momo_vp_access_key,
            requestId: transactionCodeTT,
            amount,
            orderId: transactionCodeTT,
            orderInfo: info,
            returnUrl: `${baseUrl}${momo_vp_return_url}`,
            notifyUrl: `${baseUrl}${momo_vp_notify_url}`,
            extraData: data.extra ? data.extra : 'extra data',
            requestType: 'captureMoMoWallet',
            signature: '',
        };
        const rawSignature = `partnerCode=${params.partnerCode}&accessKey=${params.accessKey}&requestId=${params.requestId
            }&amount=${params.amount}&orderId=${params.orderId}&orderInfo=${params.orderInfo}&returnUrl=${params.returnUrl}&notifyUrl=${params.notifyUrl}
        &extraData=${params.extraData}`;

        const signature = crypto.createHmac('sha256', momo_vp_secret_key)
            .update(rawSignature)
            .digest('hex');
        params.signature = signature;
        const resData = (await this.payment(params).toPromise()).data;
        return resData;

    }

    momoDeepLink(data) {
        const {
            momo_vp_app_scheme,
            momo_vp_partner_code,
        } = this.momoVPConfigService.getMoMo_VP_Config();
        return {
            action: 'gettoken',
            partner: 'merchant',
            appScheme: momo_vp_app_scheme,
            amount: data.amount ? data.amount : 10000,
            merchantcode: momo_vp_partner_code,
            merchantname: 'MedPro',
            merchantnamelabel: 'Nhà cung cấp',
            language: 'vi',
            fee: 0,
            username: '',
            orderLabel: 'Mã đơn hàng',
            orderId: data.transaction_code_tt,
            extra: data.extra ? data.extra : null,
        };
    }

    payment(params: object): Observable<AxiosResponse<any>> {
        const url = this.urlConfigService.getMomoUrl();
        return this.httpService.post(url, params);
    }

}
