import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsJWT, IsEnum, IsDateString, ValidateIf, IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';

export class ApplySignInProviderDTO {
    @ApiProperty({
        description: 'Secret key để add patient to users',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON><PERSON> lòng bổ sung thông tin',
    })
    @IsJWT({
        message: 'Vui lòng gửi lên đúng dịnh dạng JWT',
    })
    readonly secretKey: string;

    @ApiProperty({
        description: 'type',
        required: true,
        type: String,
        enum: ['password', 'google', 'facebook', 'zalo', 'firebase'],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON><PERSON> lòng bổ sung thông tin',
    })
    @IsEnum(
        {
            password: 'password',
            google: 'google',
            facebook: 'facebook',
            zalo: 'zalo',
            firebase: 'firebase',
        },
        {
            message: '<PERSON><PERSON> lòng gửi lên đúng thông tin [password, google, facebook, zalo, firebase] ',
        },
    )
    readonly type: string;

    @ApiProperty({
        description: 'password',
        required: false,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'Mật khẩu',
    // })
    readonly password?: string;

    @ApiProperty({
        description: 'Token ID lấy từ firebase',
        required: false,
        type: String,
    })
    readonly token?: string;

    @ApiProperty({
        description: 'fullname',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    readonly fullname?: string;

    @ApiProperty({
        description: 'referralCode',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    readonly referralCode?: string;

    @ApiProperty({
        description: 'Năm sinh',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    readonly birthyear?: number;

    @ApiProperty({
        description: 'Ngày tháng năm sinh',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, { message: 'Ngày sinh theo ISOString' })
    @ValidateIf(o => o.birthdate)
    readonly birthdate?: string;

    @ApiProperty({
        description: 'Giới tính',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    readonly sex?: number;

    @ApiProperty({
        description: 'Email',
        required: false,
        type: String,
    })
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @ValidateIf(o => o.email)
    @Transform(value => `${value}`.trim())
    readonly email?: string;
}
