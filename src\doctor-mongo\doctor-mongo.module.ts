import { Modu<PERSON> } from '@nestjs/common';
import { Doctor<PERSON><PERSON><PERSON>Controller } from './doctor-mongo.controller';
import { DoctorMongoService } from './doctor-mongo.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DOCTOR_COLLECTION_NAME, UMC_DOCTOR_COLLECTION_NAME } from './schemas/constants';
import { DoctorSchema } from './schemas/doctor.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { UmcDoctorSchema } from './schemas/umc-doctor.schema';
import { ServiceMongoModule } from '../service-mongo/service-mongo.module';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: DOCTOR_COLLECTION_NAME, schema: DoctorSche<PERSON> },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: UMC_DOCTOR_COLLECTION_NAME, schema: UmcDoctorSchema },
        ]),
        ServiceMongoModule,
    ],
    controllers: [DoctorMongoController],
    providers: [DoctorMongoService],
})
export class DoctorMongoModule {}
