import * as mongoose from 'mongoose';
import { COOPERATION_PACKAGES } from './constants';
import * as jsonMongo from '@meanie/mongoose-to-json';

const Schema = mongoose.Schema;

const FeatureSchema = new Schema({
    name: { type: String, required: true }, // Tên tính năng
    isAvailable: { type: Boolean, default: true }, // Có sẵn hay không
    details: { type: String, default: '' }, // Chi tiết về tính năng (nếu có)
});

export const CooperationPackagesSchema = new Schema(
    {
        name: { type: String, required: true }, // Tên gói (VD: "Nâng Cao", "Cao Cấ<PERSON>")
        description: { type: String, required: true }, // Nội dung gói
        price: { type: Number, required: true }, // Gi<PERSON> tiền (VNĐ)
        durationInMonths: { type: Number, required: true, default: 12 }, // Thời hạn (tháng)
        features: { type: [FeatureSchema], default: [] }, // Danh sách tính năng
        isPopular: { type: Boolean, default: false }, // Đ<PERSON>h dấu gói phổ biến
    },
    {
        collection: COOPERATION_PACKAGES,
        timestamps: true,
        versionKey: false,
    },
).plugin(jsonMongo);
