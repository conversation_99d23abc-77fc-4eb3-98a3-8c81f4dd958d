import { Injectable } from '@nestjs/common';
import * as Sen<PERSON> from '@sentry/nestjs';

@Injectable()
export class RequestSentryService {
    constructor() {}

    // Hàm gửi lỗi với context và level
    captureError(error: any, request?: any, priority: string = 'low', body?: any) {
        Sentry.withScope(scope => {
            if (request) {
                scope.setContext('request', {
                    method: request.method,
                    url: request.url,
                    headers: request?.headers,
                    body: request.body,
                });
            }
            if (!request?.user?.isCs) {
                Sentry.setUser({
                    id: request?.user?._id,
                    userMongoId: request?.user?.userMongoId,
                });
            } else {
                Sentry.setUser({
                    id: request?.user?._id,
                    cskhUserId: request?.user?.userMongoId,
                });
            }
            if (body) {
                scope.setContext('request', body);
            }
            scope.setTag('priority', priority);
            Sentry.captureException(error);
        });
    }
}
