import { HttpException, HttpService, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { DANH_MUC_BANG_GIA_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import { IDanhMucBangGia } from './interfaces/danh-muc-bang-gia.interface';

@Injectable()
export class DanhMucBangGiaService {
    constructor(
        @InjectModel(DANH_MUC_BANG_GIA_COLLECTION_NAME) private danhMucBangGiaModel: Model<IDanhMucBangGia>,
        private httpService: HttpService,
    ) {}

    async getByPartner(query: any) {
        const { partnerId, pageSize = 100, pageIndex = 0, key_search } = query;
        let condition: any = { partnerId };
        let sortOrder: any = {};

        if (key_search) {
            condition = {
                ...condition,
                $text: {
                    $search: new RegExp('^' + key_search, 'i'),
                },
            };

            sortOrder = {
                score: {
                    $meta: 'textScore',
                },
            };
        }

        const [rows, totalRows] = await Promise.all([
            this.danhMucBangGiaModel
                .find(condition)
                .sort(sortOrder)
                .skip(pageSize * pageIndex)
                .limit(Number(pageSize))
                .lean(),
            this.danhMucBangGiaModel.countDocuments(condition).exec(),
        ]);

        return { rows, totalRows };
    }

    async seedUmcData() {
        const existed = await this.danhMucBangGiaModel.exists({ partnerId: 'umc' });
        if (existed) {
            throw new HttpException('Vui lòng empty collection trước', 409);
        }

        const pageSize = 200;
        let pageIndex = 0;
        while (true) {
            const { data = [] } = await this.httpService
                .get('https://demobvdaihocapi.umc.edu.vn/danh-muc-and-bang-gias', {
                    params: {
                        _limit: pageSize,
                        _start: pageIndex * pageSize,
                    },
                })
                .toPromise();

            const mapping = data.map(d => {
                let price = Number(d.Price ? d.Price.replace(/[\s,.]/g, '') : null);

                if (isNaN(price)) {
                    price = null;
                    // console.log('d.Price', d.Price);
                }
                return {
                    name: d.Name,
                    price,
                    description: d.Description,
                    group: d.Group,
                    partnerId: 'umc',
                };
            });

            await this.danhMucBangGiaModel.insertMany(mapping);

            if (data.length < pageSize) {
                break;
            }

            pageIndex++;
        }

        const count = await this.danhMucBangGiaModel.count({ partnerId: 'umc' });
        return { count };
    }
}
