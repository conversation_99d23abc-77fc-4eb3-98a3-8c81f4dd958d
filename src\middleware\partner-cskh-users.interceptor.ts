import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, forwardRef, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { last, size } from 'lodash';
import * as jwt from 'jsonwebtoken';
import { JwtModuleOptions } from '@nestjs/jwt';
import { JwtUserConfigService } from '../config/config.user.jwt.service';
import { GlobalSettingService } from '../global-setting/global-setting.service';
import { UtilService } from '../config/util.service';

@Injectable()
export class PartnerCSKHUsersInterceptor implements NestInterceptor {
    constructor(
        private jwtUserConfigService: JwtUserConfigService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly utilService: UtilService,
    ) {}

    async intercept(context: ExecutionContext, next: <PERSON><PERSON>and<PERSON><any>): Promise<Observable<any>> {
        try {
            const request = context.switchToHttp().getRequest();
            return next.handle().pipe(
                map(async data => {
                    try {
                        if (Array.isArray(data)) {
                            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
                            const { authorization } = request.headers;
                            if (size(authorization) > 0) {
                                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                                const { userMongoId } = jwtVerify;
                                if (userMongoId) {
                                    const partnerCskhUsers = await this.globalSettingService.findByKeyAndRepoName('PARTNER_CSKH_USERS');
                                    let partnerCskhUsersObj = partnerCskhUsers ? JSON.parse(partnerCskhUsers) : {};
                                    const partnerId = this.utilService.findKeyById(partnerCskhUsersObj, userMongoId);
                                    if (!partnerId) {
                                        return data;
                                    }
                                    return data.filter(item => item.partnerId === partnerId);
                                }
                            }
                        }
                        return data;
                    } catch (error) {}
                }),
            );
        } catch (error) {
            throw error;
        }
    }
}
