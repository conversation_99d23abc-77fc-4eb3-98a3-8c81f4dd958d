import { HttpModule, Module } from '@nestjs/common';
import { RecruitmentController } from './recruitment.controller';
import { RecruitmentService } from './recruitment.service';
import { recruitmentSchema } from './schemas/recruitment.schema';
import { APPLY_JOB_COLLECTION_NAME, RECRUITMENT_COLLECTION_NAME } from './schemas/constants';
import { MongooseModule } from '@nestjs/mongoose';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { ApplyJobSchema } from './schemas/apply-jobs.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: RECRUITMENT_COLLECTION_NAME, schema: recruitmentSchema },
            {
                name: GLOBAL_SETTING_COLLECTION_NAME,
                schema: GlobalSettingSchema,
            },
            { name: CITY_COLLECTION_NAME, schema: CitySchema },
            { name: APPLY_JOB_COLLECTION_NAME, schema: ApplyJobSchema },
        ]),
    ],
    controllers: [RecruitmentController],
    providers: [RecruitmentService, GlobalSettingService],
    exports: [RecruitmentService],
})
export class RecruitmentModule {}
