import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsPositive, Min } from 'class-validator';

export class GetAvailableDoctorDto {
    @ApiProperty({ description: 'pageSize', type: Number, required: false, default: 10 })
    @Transform(value => Number(value))
    @Min(1, { message: 'pageSize minimum value is 1' })
    readonly pageSize: number;

    @ApiProperty({ description: 'pageIndex', type: Number, required: true })
    @Transform(value => Number(value))
    @IsPositive({ message: 'pageIndex must be positive value' })
    @IsNotEmpty({ message: 'pageIndex is required' })
    readonly pageIndex: number;
}
