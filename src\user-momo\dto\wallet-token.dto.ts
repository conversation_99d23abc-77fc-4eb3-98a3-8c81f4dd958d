import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class EncodeWalletIdDTO {
    @ApiProperty({
        description: 'Wallet ID cần encode thành token',
        example: 'wallet_123456789'
    })
    @IsNotEmpty({ message: 'Wallet ID không được để trống' })
    @IsString({ message: 'Wallet ID phải là chuỗi' })
    walletId: string;
    
    @ApiProperty({
        description: 'Secret key để encode wallet ID',
        example: 'secret_key_123456789'
    })
    @IsNotEmpty({ message: 'Secret key không được để trống' })
    @IsString({ message: 'Secret key phải là chuỗi' })
    secretKey: string;
}

export class DecodeWalletTokenDTO {
    @ApiProperty({
        description: 'Token cần decode để lấy wallet ID',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    })
    @IsNotEmpty({ message: 'Token không được để trống' })
    @IsString({ message: 'Token phải là chuỗi' })
    token: string;
}
