import { Document } from "mongoose";

// DTO cho BannerServices
export interface IBannerServices extends Document {
    repo: string;
    imageUrl: string;
    cta: Record<string, any>; // hoặc định nghĩa chi tiết hơn nếu bạn biết cấu trúc
    status?: boolean;
    platform?: string;
    fromDate?: Date | null;
    toDate?: Date | null;
    appid?: string;
    order?: number;
    alt?: string | null;
    display: boolean;
}
