export class CancelBookingResponseDto {
  success: boolean;
  message: string;
  cancelReasonId?: string;
  bankAccountId?: string;
  timestamp: Date;
  hasBankAccount: boolean;

  constructor(success: boolean, message: string, cancelReasonId?: string, bankAccountId?: string) {
    this.success = success;
    this.message = message;
    this.cancelReasonId = cancelReasonId;
    this.bankAccountId = bankAccountId;
    this.timestamp = new Date();
    this.hasBankAccount = !!bankAccountId;
  }
}
