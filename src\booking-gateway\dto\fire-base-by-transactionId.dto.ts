import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class FireBaseByTransactionIdDTO {

    @ApiProperty({
        description: 'Transaction',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    transactionId: string;

    @ApiProperty({
        description: 'status',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    status:number

    @ApiProperty({
        description: 'description',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    description?:string
}
