import { HttpModule, Module } from '@nestjs/common';
import { HomePageController } from './home-page.controller';
import { HomePageServiceProvider } from './persistence/home-page.service';
import { MongooseModule } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import {
    DOCTOR_DESCRIPTION_COLLECTION_NAME,
    SEARCH_KEYWORD_COLLECTION_NAME,
    SERVICE_COLLECTION_NAME,
    SERVICE_DESCRIPTION_COLLECTION_NAME,
} from 'src/service-mongo/schemas/constants';
import { ServiceDescriptionSchema } from 'src/service-mongo/schemas/service-description.schema';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from 'src/hospital-description/schema/constants';
import { HospitalDescriptionSchema } from 'src/hospital-description/schema/hospital-description.schema';
import { SearchKeywordsSchema } from 'src/service-mongo/schemas/search-keywords.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { DoctorDescriptionSchema } from 'src/doctor-description/schema/doctor-description.schema';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { FeeRefundConfigsSchema } from 'src/cash-back/schemas/fee-refund-configs.schema';
import { HOME_PAGE } from './schemas/constants';
import { HomePageSchema } from './schemas/home-page.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            {
                name: HOSPITAL_COLLECTION_NAME,
                schema: HospitalSchema,
            },
            {
                name: SERVICE_DESCRIPTION_COLLECTION_NAME,
                schema: ServiceDescriptionSchema,
            },
            {   name: SERVICE_COLLECTION_NAME, 
                schema: ServiceSchema 
            },
            {   name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, 
                schema: HospitalDescriptionSchema 
            },
            {   
                name: GLOBAL_SETTING_COLLECTION_NAME, 
                schema: GlobalSettingSchema 
            },
            { 
                name: DOCTOR_DESCRIPTION_COLLECTION_NAME, 
                schema: DoctorDescriptionSchema 
            },
            {
                name: FEE_REFUND_CONFIGS_COLLECTION_NAME,
                schema: FeeRefundConfigsSchema
            },
            { name: HOME_PAGE, schema: HomePageSchema },
        ]),
        MongooseModule.forFeature(
            [
                {
                    name: SEARCH_KEYWORD_COLLECTION_NAME,
                    schema: SearchKeywordsSchema,
                },
            ],
            'services', // ? Collection SearchKeyWords nằm trong database services
        ),
    ],
    controllers: [HomePageController],
    providers: [   
        HomePageServiceProvider,
        GlobalSettingService
    ],
})
export class HomePageModule {}
