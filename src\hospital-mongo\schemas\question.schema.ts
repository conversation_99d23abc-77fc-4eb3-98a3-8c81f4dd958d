import * as mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { QUESTION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const QuestionSchema = new Schema(
  {
    locale: {
      type: String,
    },
    question: {
      type: String,
      required: true,
    },
    answer: {
      type: String,
      required: true,
    },
    status: {
      type: Number,
      required: true,
    },
    sortOrder: {
      type: Number,
      required: true,
    },
    partnerId: {
      type: String,
      required: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    collection: QUESTION_COLLECTION_NAME,
    timestamps: false,
    versionKey: false,
  },
);
