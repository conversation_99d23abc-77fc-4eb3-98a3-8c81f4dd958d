import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsArray, IsPhoneNumber, IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';

export class CooperationDto {
    @ApiProperty({
        description: 'Họ và tên',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Họ và tên không được để trống' })
    readonly fullname: string;

    @ApiProperty({
        description: 'Email',
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Email không được để trống' })
    @IsEmail()
    readonly email: string;

    @ApiProperty({
        description: 'Điện thoại',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
    readonly phone: string;

    @ApiProperty({
        description: '<PERSON><PERSON> chú',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: '<PERSON><PERSON> chú không được để trống' })
    readonly note: string;

    @ApiProperty({
        description: 'Loại hình cơ sở y tế',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Loại hình cơ sở y tế không được để trống' })
    readonly typeCooperation: string;

    @ApiProperty({
        description: 'Id gói hợp tác',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Id gói hợp tác không được để trống' })
    readonly packageId: string;

    @ApiProperty({
        description: 'captchaResponse',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'captchaResponse không được để trống' })
    captchaResponse?: string;
}
