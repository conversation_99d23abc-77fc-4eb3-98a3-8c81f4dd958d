import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SERVICE_TRACKING_LOG_NAME } from '../constant';
import * as uuid from 'uuid';

const Schema = mongoose.Schema;

const id = () => {
    const uuidv4 = uuid.v4();
    const idv4 = uuidv4.replace(/-/g, '');
    return idv4;
};

export const ServiceTrackingLogSchema = new Schema(
    {
        uuid: { type: String, default: id },
        name: { type: String },
        summary: { type: String },
        nameParent: { type: String },
        params: { type: Schema.Types.Mixed },
        response: { type: Schema.Types.Mixed },
        errorBody: { type: Schema.Types.Mixed },
        message: { type: Schema.Types.Mixed },
        nameRepo: { type: String },
        userId: { type: String },
        flow: { type: String },
    },
    {
        collection: SERVICE_TRACKING_LOG_NAME,
        timestamps: true,
        versionKey: false,
    },
).plugin(jsonMongo);
