import { Module, Global } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { RequestSentryService } from './request-sentry-service';

@Global()
@Module({
    imports: [SentryModule.forRoot()],
    providers: [
        {
            provide: 'SentryToken',
            useClass: SentryGlobalFilter,
        },
        RequestSentryService
    ],
    exports: ['SentryToken', SentryModule], // Export both
})
export class SentryToken {} // Rename to avoid confusion
