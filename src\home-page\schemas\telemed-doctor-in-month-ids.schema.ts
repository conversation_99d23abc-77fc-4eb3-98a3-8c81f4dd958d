import * as mongoose from 'mongoose';
import { TELEMED_DOCTOR_IN_MONTH_IDS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const TelemedDoctorInMonthIdSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        doctorId: {
            type: String,
            default: '',
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        status: {
            type: Boolean,
            default: false,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: TELEMED_DOCTOR_IN_MONTH_IDS_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
