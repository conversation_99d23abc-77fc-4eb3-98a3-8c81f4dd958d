import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { RatingHospitalController } from './rating-hospital.controller';
import { RatingHospitalService } from './rating-hospital.service';
import { RATING_HOSPITAL_COLLECTION_NAME } from './schema/constants';
import { RatingHospitalSchema } from './schema/schema-rating-hospital';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: RATING_HOSPITAL_COLLECTION_NAME, schema: RatingHospitalSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: USER_COLLECTION_NAME, schema: UserSchema},
            
        ]),
    ],
    controllers: [RatingHospitalController],
    providers: [RatingHospitalService],
    exports: [RatingHospitalService],
})
export class RatingHospitalModule {}
