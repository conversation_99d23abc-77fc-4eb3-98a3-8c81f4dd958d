import { Controller, Post, Body, Query, Param, Get, Headers, HttpCode, HttpException, HttpStatus, Put, UseGuards, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiHeader, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { HospitalMongoService } from './hospital-mongo.service';
import { CreateHospitalDTO } from './dto/create-hospital.dto';
import { OutCreateHospitalDTO } from './dto/out-create-hospital.dto';
import { plainToClass } from 'class-transformer';
import { ValidateObjectId } from 'src/shared/pipes/validate-object-id.pipe';
import { UpdateNotifPartnerDTO } from './dto/update-notif-partner.dto';
import { UpdateMessagePartnerDTO } from './dto/update-message-partner.dto';
import { UpdateBannerDTO } from './dto/update-banner.dto';
import { UpdateStaticResourceDTO } from './dto/update-static-resource.dto';
import { UrlConfigService } from 'src/config/config.url.service';
import { CreateClinicDto } from './dto/create-clinic.dto';
import { ClinicGuard } from 'src/common/guards/clinic.guard';
import { AuthGuard } from '@nestjs/passport';
import { GetContentPageDto } from './dto/get-content-page.dto';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { HeadersDto } from '../common/base/headers.dto';
import { DanhSachV5QueryDto } from './dto/danh-sach-v5-query.dto';
import { UtilService } from '../config/util.service';
import { SeoPageQuery } from './dto/seo-page.query';

@Controller('mongo/hospital')
@ApiTags('Bệnh viện - Quản lý Bệnh viện trên MongoDB')
export class HospitalMongoController {
    private appBoId: string;
    private appBoKey: string;
    constructor(private readonly hospitalMongoService: HospitalMongoService,
                private readonly urlConfigService: UrlConfigService,
                private readonly utilService: UtilService,
                private cacheService: CacheManagerService) {
        this.appBoId = urlConfigService.getAPIBoId();
        this.appBoKey = urlConfigService.getAPIBoKey();
    }

    @Get('booking-guide')
    @ApiHeader({
        name: 'appid',
    })
    async getBookingGuide(@Headers('appid') appid: string, @Headers('locale') locale: string): Promise<any> {
        return this.hospitalMongoService.getBookingGuide(appid, locale);
    }

    @Get('content-page')
    @ApiHeader({
        name: 'partnerid',
    })
    @ApiQuery({
        name: 'key',
    })
    async getContentPageByKeyAndPartner(@Headers('partnerid') partnerid: string,
                                        @Headers('locale') locale: string,
                                        @Query('key') key: string): Promise<GetContentPageDto> {
        const content = await this.hospitalMongoService.getContentPageByKeyAndPartner(partnerid, key, locale);

        return {
            key,
            content,
        }
    }

    @Post('create')
    async create(@Body() createHospitalDTO: CreateHospitalDTO): Promise<OutCreateHospitalDTO> {
        return plainToClass(OutCreateHospitalDTO, await this.hospitalMongoService.create(createHospitalDTO));
    }

    @Post('create-clinic')
    @UseGuards(ClinicGuard)
    async createClinic(@Body() formData: CreateClinicDto): Promise<OutCreateHospitalDTO> {
        return plainToClass(OutCreateHospitalDTO, await this.hospitalMongoService.create(formData));
    }

    @Get('list')
    async list(): Promise<any> {
        return await this.hospitalMongoService.list();
    }

    @Get('danh-sach')
    async danhSach(
        @Headers('appid') appId: string,
        @Query('version') version: number,
        @Query('type') type: number,
        @Req() req,
        @Headers('platform') platform: string,
    ): Promise<any> {
        const cskhInfo = await this.hospitalMongoService.verifyUserTokenModeCskh(req.headers.authorization);
        return this.hospitalMongoService.handleGetHospitalCacheOrSetNewIfNotExistAndGetData({
            appId,
            type,
            version,
            cskhInfo,
            platform,
        })
    }

    @Get('danh-sach-v2')
    async danhSachV2(@Headers('appid') appId: string, @Query('version') version: number, @Query('type') type: number, @Headers('platform') platform: string): Promise<any> {
        return await this.hospitalMongoService.danhSachV2ByVersion(appId, version, type, platform);
    }
    @Get('danh-sach-v3')
    async danhSachV3(
        @Headers('appid') appId: string,
        @Query('version') version: number,
        @Query('type') type: number,
        @Headers('platform') platform: string,
        @Query('homeBannerArray') homeBannerArray: string,
        ): Promise<any> {
        return await this.hospitalMongoService.danhSachV3ByVersion(appId, version, type, platform, homeBannerArray);
    }

    @Get('danh-sach-v4')
    async danhSachV4(
        @Headers('appid') appId: string,
        @Query('version') version: number,
        @Query('type') type: number,
        @Query('methodId') methodId: string,
        @Headers('platform') platform: string,
    ): Promise<any> {
        return await this.hospitalMongoService.danhSachV4(appId, type, methodId, version, platform);
    }

    @Get('danh-sach-v5')
    async danhSachV5(
        @Query() query: DanhSachV5QueryDto,
        @Headers() headers: HeadersDto,
    ): Promise<any> {
        return this.hospitalMongoService.danhSachV5(query, headers);
    }


    @Get('view/:id')
    async findOne(@Param('id', new ValidateObjectId()) id: string): Promise<any> {
        return this.hospitalMongoService.findOne(id);
    }

    @Get('get-partner-info')
    async getPartnerInfo(@Headers('partnerid') partnerid: string,
                         @Query('slug') slug: string): Promise<any> {
        const params = {
            partnerId: partnerid,
            slug
        }

        const key = `hospital:partner-info:${this.utilService.sortedStringifyUrl(params)}`
        const value = await this.cacheService.get(key)

        if (value) {
            return value;
        }

        const result = await this.hospitalMongoService.getPartnerInfo(partnerid, 1, slug);
        this.cacheService.set(key, result);
        return result
    }

    @Post('update-notif-partner')
    async updateNotifPartner(@Body() updateNotifPartnerDTO: UpdateNotifPartnerDTO): Promise<any> {
        return this.hospitalMongoService.updateNotifPartner(updateNotifPartnerDTO);
    }

    @Post('reset-notif-partner')
    async resetNotifPartner(): Promise<any> {
        return this.hospitalMongoService.resetNotifPartner();
    }

    @Post('seed-push-notif')
    async seedPushNotif(): Promise<any> {
        return this.hospitalMongoService.seedPushNotif();
    }

    @Post('update-features')
    async updateFeatures(@Query('partnerid') partnerId: string): Promise<any> {
        return this.hospitalMongoService.updateFeatures(partnerId);
    }

    @Post('update-message-partner')
    async updateMessage(@Body() data: UpdateMessagePartnerDTO): Promise<any> {
        return this.hospitalMongoService.updateMessage(data);
    }

    @Post('update-banner-partner')
    async updateBanner(@Body() data: UpdateBannerDTO): Promise<any> {
        return this.hospitalMongoService.updateBanner(data);
    }

    // @Post('update')
    // async update(@Query() updateHospitalDTO: UpdateHospitalDTO): Promise<OutUpdateHospitalDTO> {
    //     return plainToClass(OutUpdateHospitalDTO, await this.hospitalMongoService.updateOne(updateHospitalDTO));
    // }
    @Post('update-static-resource')
    @HttpCode(200)
    async updateStaticResource(
        @Body() staticResouceDTO: UpdateStaticResourceDTO,
        @Headers('appboid') appBoId?: string,
        @Headers('appbokey') appBoKey?: string,
    ): Promise<any> {
        const validate = !appBoId ? true : this.appBoId === appBoId && this.appBoKey === appBoKey;
        if (!validate) {
            throw new HttpException(`Xác thực không thành không. Vui lòng kiểm tra lại!`, HttpStatus.UNAUTHORIZED);
        }
        return this.hospitalMongoService.updateStaticResource(staticResouceDTO);
    }

    @Get('find-all-by-appId')
    @ApiOperation({ summary: 'Lấy danh sách các bệnh viện theo appId' })
    @HttpCode(200)
    async findAllHospitalByAppId(@Headers('appid') appId: string): Promise<any> {
        return this.hospitalMongoService.findAllHospitalByAppId(appId);
    }

    @Put('feed-data-hospital')
    @ApiOperation({ summary: 'Cập nhật data cho trường hợp deliveryStatus' })
    @HttpCode(200)
    async feedData(): Promise<any> {
        return this.hospitalMongoService.feedDataHospital();
    }

    @Put('seed-circle-logo')
    @ApiOperation({ summary: 'Cập nhật data cho circleLogo' })
    @HttpCode(204)
    async seedCircleLogo(): Promise<any> {
        return this.hospitalMongoService.seedCircleLogo();
    }

    @Put('seed-hospital-type')
    @ApiOperation({ summary: 'Cập nhật data cho hospitalType' })
    @HttpCode(204)
    async seedHospitalType(): Promise<any> {
        return this.hospitalMongoService.seedHospitalType();
    }


    @Get('hospital-have-his')
    @ApiOperation({
        summary: 'Lấy danh sách bệnh viện có kết nối HIS',
        description: 'Lấy danh sách bệnh viện có kết nối HIS',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async listHospitalHaveHisConnector(
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Query('type') type: number,
    ): Promise<any> {
        await this.hospitalMongoService.verifyCskhToken(cskhToken);
        return this.hospitalMongoService.getHospitalHaveHisConnector(appId, type);
    }

    @Get('clinic/report')
    @ApiOperation({
        summary: 'Lấy số liệu báo cáo cho Clinic',
        description: 'Lấy số liệu báo cáo cho Clinic',
    })
    clinicGAReport(): Promise<unknown> {
        return this.hospitalMongoService.clinicGAReport();
    }

    @Get('extra-info')
    getHospitalExtarInfo(@Headers('partnerid') partnerId: string, @Headers('locale') locale: string) {
        return this.hospitalMongoService.getHospitalExtraInfo(partnerId, locale);
    }

    @Get('danh-sach-v6')
    async danhSachV6(
        @Query('type') type: number
    ): Promise<any> {
        return this.hospitalMongoService.danhSachV6(type);
    }

    @Get('reset-cache-danh-sach-v6')
    async resetCacheDanhSachV6(): Promise<any> {
        return this.hospitalMongoService.resetCacheDanhSachV6();
    }

    @Get('hospital-status')
    async getHospitalStatus(@Query() query: any): Promise<any> {
        return this.hospitalMongoService.getHospitalStatus(query);
    }

    @Get('list-hospital-status')
    async getListHospitalStatus(@Query() query: any): Promise<any> {
        return this.hospitalMongoService.getListHospitalStatus(query);
    }

    @Get('seo-page')
    async getSeoPage(@Query() query: SeoPageQuery): Promise<any> {
        return this.hospitalMongoService.getSeoPage(query);
    }
}
