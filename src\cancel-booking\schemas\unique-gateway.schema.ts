import * as mongoose from 'mongoose';
import { UNIQUE_GATEWAY_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const UniqueGatewaySchema = new Schema(
    {
        gatewayId: { 
            type: String, 
            required: true, 
            unique: true,
            trim: true
        },
        name: { 
            type: String,
            default: ''
        },
        description: { 
            type: String,
            default: ''
        },
    },
    {
        collection: UNIQUE_GATEWAY_COLLECTION_NAME,
        timestamps: true,
    },
);

// Index for better performance
UniqueGatewaySchema.index({ gatewayId: 1 }, { unique: true });
