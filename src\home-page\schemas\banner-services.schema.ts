import * as mongoose from 'mongoose';
import { BANNER_SERVICES_COLLECTION_NAME } from './constants';
import { alt } from 'joi';

const Schema = mongoose.Schema;
export const BannerServicesSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        imageUrl: { type: String, required: true },
        cta: { type: Object, required: true },
        status: {
            type: Boolean,
            default: false,
        },
        platform: {
            type: String,
            default: 'desktop',
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        alt: {
            type: String,
            default: null,
        },
        display: {
            type: Boolean,
            default: true,
        },
    },
    {
        collection: BANNER_SERVICES_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
