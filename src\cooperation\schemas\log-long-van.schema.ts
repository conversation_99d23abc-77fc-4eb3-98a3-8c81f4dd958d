
import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { LOGS_LONG_VAN } from './constants';

const Schema = mongoose.Schema;

export const LogLongVanSchema = new Schema({
    url:  { type: String },
    method:  { type: String},
    body:  { type: Schema.Types.Mixed },
    nameRepo: { type: String },
    response: { type: Schema.Types.Mixed },
    status: { type: Boolean },
}, {
    collection: LOGS_LONG_VAN,
    timestamps: true,
    versionKey: false,
}).plugin(jsonMongo);
