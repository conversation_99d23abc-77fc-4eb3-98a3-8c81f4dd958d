import * as mongoose from 'mongoose';
import { BANNER_STICKY_BIG_COLLECTION_NAME } from './constant';

const Schema = mongoose.Schema;

export const BannersStickyBigSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        status: {
            type: Boolean,
            default: false,
        },
        imageDesktopUrl: { type: String, required: true },
        imageMobileUrl: { type: String, required: true },
        cta: { type: Object, required: true },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        alt: {
            type: String,
            default: null,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: BANNER_STICKY_BIG_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
