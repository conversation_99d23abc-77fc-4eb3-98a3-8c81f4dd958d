import * as querystring from 'query-string';
import { Injectable, HttpException, HttpStatus, Logger, HttpService } from '@nestjs/common';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import * as jwt from 'jsonwebtoken';
import * as uuid from 'uuid';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { JwtModuleOptions, JwtService } from '@nestjs/jwt';
import { UserService } from 'src/user/user.service';
import { IUser } from 'src/user/interfaces/user.interface';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UtilService } from 'src/config/util.service';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { InjectModel } from '@nestjs/mongoose';
import { CONSTRAINTS_USER_COLLECTION_NAME, USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { Model } from 'mongoose';
import { IUserConstraints } from 'src/user/interfaces/user-constraints.interface';
import { CreateUserZaloDto } from './dto/create-user-zalo.dto';
import { get } from 'lodash';
import { PatientMongoService } from '../patient-mongo/patient-mongo.service';
import * as md5 from 'md5';
import { ApiTags } from '@nestjs/swagger';
import { ZaloPayConfigService } from 'src/config/config.zalopay.service';
import { createHash } from 'crypto';
import { GetTokenByAuthorizedCodeDto, GetTokenByAuthorizedCodeResponseDto } from './dto/get-token-by-authorized-code.dto';
import { AxiosRequestConfig } from 'axios';
import { GetUserResourceByAccessTokenDto, GetUserResourceByAccessTokenResponse } from './dto/get-user-resource-by-access-token.dto';
import { LoginOrRegisterForUserFromZaloPayResponseDto } from './dto/login-or-register-new-zalo-pay-user.dto';
import { LoginOrRegisterForUserZaloPayByCodeAndCodeVerifierDto } from './dto/login-or-register-for-user-zalo-pay-by-code-and-code-verifier.dto';
@ApiTags("User Zalo")
@Injectable()
export class UserZaloService {
    private readonly logger: Logger = new Logger(UserZaloService.name);

    constructor(
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly userService: UserService,
        private readonly jwtService: JwtService,
        @InjectSentry() private readonly clientSentry: SentryService,
        private readonly eventEmitter: EventEmitter2,
        private readonly utilService: UtilService,
        private readonly patientMongoService: PatientMongoService,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(CONSTRAINTS_USER_COLLECTION_NAME) private readonly constraintUserModel: Model<IUserConstraints>,
        private emitService: EventEmitter2,
        private readonly zaloPayConfig: ZaloPayConfigService,
        private readonly httpService: HttpService,
    ) {}

    private get getAppId() {
        return this.zaloPayConfig.getZaloAppId() || '637628968723875573';
    }

    private get getState() {
        return this.zaloPayConfig.getZaloState() || '2JtnSJT1PoxEzp40avsUK14MuT5uMGQr7iAGvgpYU4IqJSCqCz0d70xgG67MUyz9exThNyZZ0zUD4CT1kaFMyGBfrau2eP6lqh3j';
    }

    private get getZaloPaySecretKey() {
        return this.zaloPayConfig.getZaloAppSecret() || 'SV2DWpDRDpcJcMtGoDFI';
    }

    private get getUserResourceByAccessTokenFromZaloPayUrl() {
        return `https://graph.zalo.me/v2.0/me`;
    }

    private get getRedirectUrl() {
        return 'https://zp-testing.medpro.com.vn';
    }

    private get getAccessTokenAndRefreshTokenFromZaloPayUrl() {
        return `https://oauth.zaloapp.com/v4/access_token`;
    }

    private generateCodeVerifier() {
        const chars = 'ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
        let codeVerifier = '';
        for (let i = 0; i < 43; i++) {
            codeVerifier += chars[Math.floor(Math.random() * chars.length)];
        }
        return codeVerifier;
    }

    private async generateCodeChallengeByCryptoJs(codeVerifier: string) {
        const hashed = createHash('sha256').update(codeVerifier).digest().toString('base64');
        const base64UrlEncoded = hashed
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=+$/, '');
        return base64UrlEncoded;
    }

    private generateAuthorizedCodeUrl({
        appId,
        redirectUrl,
        codeChallenge,
        state,
    }: {
        appId: string;
        redirectUrl: string;
        codeChallenge: string;
        state: string;
    }) {
        return `https://oauth.zaloapp.com/v4/permission?app_id=${appId}&redirect_uri=${redirectUrl}&code_challenge=${codeChallenge}&state=${state}`;
    }

    public async getParamsOAuthV1Code(): Promise<{
        urlOAuthZaloPay: string;
        codeVerifier: string;
    }> {
        const codeVerifier = this.generateCodeVerifier();
        const codeChallenge = await this.generateCodeChallengeByCryptoJs(codeVerifier);
        
        return {
            urlOAuthZaloPay: this.generateAuthorizedCodeUrl({
                appId: this.getAppId,
                redirectUrl: this.getRedirectUrl,
                codeChallenge: codeChallenge,
                state: this.getState,
            }),
            codeVerifier: codeVerifier,
        };
    }    


    public async getUserTokenFromZaloPay(payload: GetTokenByAuthorizedCodeDto) {
        const url = this.getAccessTokenAndRefreshTokenFromZaloPayUrl;
        const bodyWithUrlEncoded = querystring.stringify({
            code: payload.code,
            app_id: this.getAppId,
            grant_type: 'authorization_code',
            code_verifier: payload.code_verifier,
        });
        const config: AxiosRequestConfig = {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                secret_key: this.getZaloPaySecretKey,
            },
        };

        let response: {
            data: GetTokenByAuthorizedCodeResponseDto | null;
            success: boolean;
            message: string;
        };

        try {
            const accessTokenResponse = await this.httpService
                .post<GetTokenByAuthorizedCodeResponseDto>(url, bodyWithUrlEncoded, config)
                .toPromise();

            response = {
                data: accessTokenResponse.data,
                success: true,
                message: 'Get token successfully',
            };
        } catch (error) {
            console.error(`getUserTokenFromZaloPay error: ${error}`);
            response = {
                data: null,
                success: false,
                message: 'Get token failed',
            };
        }

        return response;
    }

    public async getUserInfoByAccessTokenFromZaloPay(payload: GetUserResourceByAccessTokenDto) {
        const config: AxiosRequestConfig = {
            headers: {
                access_token: payload.access_token,
            },
        };

        const queryParamsGetZaloInfo = {
            fields: 'id,name,picture,name,is_sensitive',
        };

        const urlGetZaloInfo = querystring.stringifyUrl({
            url: this.getUserResourceByAccessTokenFromZaloPayUrl,
            query: queryParamsGetZaloInfo,
        });

        let response: {
            data: GetUserResourceByAccessTokenResponse | null;
            success: boolean;
            message: string;
        };

        try {
            const userResource = await this.httpService.get(urlGetZaloInfo, config).toPromise();
            response = {
                data: userResource.data,
                success: true,
                message: 'Get user info successfully',
            };
        } catch (error) {
            console.error(`getUserInfoByAccessTokenFromZaloPay error: ${error}`);
            response = {
                data: null,
                success: false,
                message: 'Get user info failed',
            };
        }

        return response;
    }

    public async handleLoginOrRegisterForUserZaloPayByCodeAndCodeVerifier(payload: LoginOrRegisterForUserZaloPayByCodeAndCodeVerifierDto) {
        const getUserTokenFromZaloPayResponse = await this.getUserTokenFromZaloPay(payload);
        let response: {
            success: boolean;
            message: string;
        } & Partial<LoginOrRegisterForUserFromZaloPayResponseDto>;
        
        const access_token = getUserTokenFromZaloPayResponse?.data.access_token || '';

        if (access_token) {
            const { data: dataUserInfo, success, message } = await this.getUserInfoByAccessTokenFromZaloPay({ access_token });
            
            const shouldSyncUser = success && !!dataUserInfo;
            if (shouldSyncUser) {
                const userZalo = await this.createUserZalo({
                    zaloId: dataUserInfo?.id || '',
                });
                response = {
                    ...userZalo,
                    success: true,
                    message: 'Get token successfully',
                };
            } else {
                response = {
                    success: false,
                    message: 'Get user info failed',
                };
            }
        }
        return response;
    }
    // ? ============================== DELETE ME LATER ==============================

    async createUserZalo(params: CreateUserZaloDto): Promise<any> {
        const zaloId = params.zaloId;
        /* kiểm tra xem có thông tin này chưa  */
        let payload:any = { username: '', sub: 0, userMongoId: null };

        try {
            let checkZaloId: IUser = await this.userService.checkZaloIdExist(zaloId);
            
            if (!checkZaloId) {
                /* tạo thông tin user */
                checkZaloId = await this.insertUser({...params,userInfo:payload});
                if (checkZaloId) {
                    /* gán thông tin */
                    try {
                        await this.userService.getUserIdAndCreateSessionV1('medpro', 'noConfig', checkZaloId);
                    } catch (error) {
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'getUserIdAndCreateSessionV1',
                            summary: 'getUserIdAndCreateSessionV1',
                            nameParent: 'createUserZalo',
                            params: params,
                            errorBody: this.utilService.errorHandler(error),
                            message: error?.message || 'Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1',
                        });
                        this.clientSentry.instance().captureException(error);
                    }
                    payload = {
                        ...payload,
                        username: zaloId,
                        userMongoId: checkZaloId._id,
                    };
                } else {
                    throw new Error('Hệ thống chưa xử lý được thao tác này. Vui lòng liên hệ tổng đài đặt khám nhanh 19002115 để được hỗ trợ.');
                }
            } else {
                payload = {
                    ...payload,
                    username: zaloId,
                    userMongoId: checkZaloId._id,
                };
                // console.log('>>>>',payload);
                
            }

            const resultData = {
                userId: checkZaloId._id,
                userName: zaloId,
                fullName: checkZaloId.fullname,
                number: '',
                email: zaloId,
                historyBookingCount: 0,
                patientCount: 0,
            };

            const token = this.jwtService.sign(payload);
            return { ...resultData, token };
        } catch (error) {
            throw new HttpException(
                error?.message || 'Thông tin nhập vào không chính xác. Vui lòng liên hệ tổng đài đặt khám nhanh 19002115 để được hỗ trợ.',
                HttpStatus.FORBIDDEN,
            );
        }
    }

    async insertUser(params: CreateUserZaloDto): Promise<any> {
        const userName = params.zaloId;
        const userSalt = this.generateSalt();
        
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        const checkUserInfo = get(params, 'userInfo', null);
        if (checkUserInfo) {
            /* Tạo mới thông tin user trên mongo */
            const userMongo = new this.userModel({
                username: userName,
                password: userHashPwd,
                salt: userSalt,
                fullname: get(params, 'userInfo.name', ''),
                zaloId: userName,
                medproId: `mp${userName}`,
            });
            const result = await userMongo.save();
            if (params.userInfo) {
                try {
                    const { userInfo } = params;
                    const patient = await this.patientMongoService.insertPatientZalo(userInfo);
                    let newPatients: any[] = [];
                    if (patient) {
                        const patientObj = patient.toObject();
                        newPatients = [...(result?.patients || []), patientObj._id];
                    }

                    await this.userModel.findByIdAndUpdate(result._id, { patients: newPatients }).exec();
                } catch (error) {
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'createPatientFromAccount',
                        summary: 'Tạo patient cho user',
                        nameParent: 'insertPatient',
                        params: { ...params.userInfo, mobile: get(params, 'userInfo.phone'), user: result._id },
                        error: error?.message,
                        message: error?.message,
                    });
                }
            }

            return result;
        } else {
            return null;
        }
    }

    async createUserWithToken(secretKey: string, appId: string): Promise<any> {
        const zalo_id = secretKey; // await this.getMomoToken(secretKey);
        /* kiểm tra xem có thông tin này chưa  */
        let payload = { username: '', sub: 0, userMongoId: null };

        if (appId === 'zalopay') {
            let zaloId = '';
            try {
                const payload = this.jwtService.verify(secretKey);
                zaloId = payload.username;
            } catch (err) {
                throw new HttpException('Không có quyền truy cập!', 401);
            }

            return this.createUserZalo({ zaloId });
        }

        try {
            const checkZaloId: IUser = await this.userService.checkZaloIdExist(zalo_id);
            if (!checkZaloId) {
                /* tạo thông tin user */
                const user: IUser = await this.userService.createUserZalo(zalo_id);
                /* gán thông tin */
                try {
                    await this.userService.getUserIdAndCreateSessionV1('medpro', 'noConfig', user);
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'getUserIdAndCreateSessionV1',
                        summary: 'getUserIdAndCreateSessionV1',
                        nameParent: 'createUserWithToken',
                        params: { secretKey },
                        errorBody: this.utilService.errorHandler(error),
                        message: error?.message || 'Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1',
                    });
                    this.clientSentry.instance().captureException(error);
                }
                payload = {
                    ...payload,
                    username: zalo_id,
                    userMongoId: user._id,
                };
            } else {
                payload = {
                    ...payload,
                    username: zalo_id,
                    userMongoId: checkZaloId._id,
                };
            }
            const resultData = {
                userId: 0,
                userName: zalo_id,
                fullName: '',
                number: '',
                email: zalo_id,
                historyBookingCount: 0,
                patientCount: 0,
            };
            const token = this.jwtService.sign(payload);
            return { ...resultData, token };
        } catch (error) {
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    generateHashPwd(str = '', salt = '') {
        return md5(`${str}${salt}`);
    }

    generateSalt(vLength = 16) {
        const alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }
}
