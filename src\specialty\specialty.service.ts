import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { ISpecialty } from './interface/specialty.interface';
import { InjectModel } from '@nestjs/mongoose';
import { SCHEMA_SPECIALTY_COLLECTION_NAME } from './schema/constants';
import { CreateSpecialtyDto } from './dto/create-specialty.dto';
import { ISearchKeywords } from 'src/service-mongo/interfaces/search-keywords.interface';
import { flattenDeep } from 'lodash';
import { SEARCH_KEYWORD_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';

@Injectable()
export class SpecialtyService {
    private readonly logger = new Logger(SpecialtyService.name);
    constructor(
        @InjectModel(SCHEMA_SPECIALTY_COLLECTION_NAME) private specialtyModel: Model<ISpecialty>,
        @InjectModel(SEARCH_KEYWORD_COLLECTION_NAME)
        private readonly searchKeywordsModel: Model<ISearchKeywords>,
    ) {}

    async getAllSpecialty(): Promise<any> {
        try {
            const specialties = await this.specialtyModel.find({ status: true }).lean();
            return specialties;
        } catch (error) {
            return { success: false, message: error.message };
        }
    }

    async createSpecialty(createSpecialtyDto: CreateSpecialtyDto): Promise<any> {
        try {
            // Kiểm tra trùng tên
            const existingSpecialty = await this.specialtyModel.findOne({
                name: { $regex: new RegExp(`^${createSpecialtyDto.name}$`, 'i') },
            });

            if (existingSpecialty) {
                throw new HttpException('Chuyên khoa này đã tồn tại', HttpStatus.BAD_REQUEST);
            }

            const newSpecialty = new this.specialtyModel(createSpecialtyDto);
            await newSpecialty.save();

            return newSpecialty;
        } catch (error) {
            this.logger.error(`Error creating specialty: ${error.message}`);
            return { success: false, message: error.message };
        }
    }

    private async getSubjects() {
        const keywords = await this.searchKeywordsModel.find({ enable: true, type: 'departments' }).exec();

        // return keywords.map(keyword => keyword.value).map(value => JSON.parse(value)).flat()
        const subjects = keywords.map(keyword => keyword.value).map(value => JSON.parse(value));
        return flattenDeep(subjects);
    }

    async syncSpecialtyPortal(): Promise<any> {
        try {
            const allSubject = await this.getSubjects();

            const existingSpecialties = await this.specialtyModel.find({}).lean();

            const specialtiesToInsert = allSubject
                .filter(subject => 
                    !existingSpecialties.some(existing => 
                        existing.name.toLowerCase() === subject?.name.toLowerCase()
                    )
                )
                .map(subject => ({
                    name: subject?.name,
                    icon: subject?.icon || '',
                    status: true,
                }));

            if (specialtiesToInsert.length > 0) {
                await this.specialtyModel.insertMany(specialtiesToInsert);
            }

            return { success: true, message: 'Đồng bộ thành công' };
        } catch (error) {
            this.logger.error(`Error synchronizing specialties: ${error.message}`);
            return { success: false, message: error.message };
        }
    }
}
