import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';

@Injectable()
export class ZaloPayConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            // ZALO_URL_ACCESS_TOKEN: {
            //     validate: Joi.string(),
            //     required: true,
            //     default: 'https://oauth.zaloapp.com/v3/access_token',
            // },
            // ZALO_URL_GET_INFO: {
            //     validate: Joi.string(),
            //     required: true,
            //     default: 'https://graph.zalo.me/v2.0/me',
            // },
            // ZALO_APP_ID: {
            //     validate: Joi.string(),
            //     required: true,
            //     default: '1837150420993020578',
            // },
            // ZALO_APP_SECRET: {
            //     validate: Joi.string(),
            //     required: true,
            //     default: 'QR8EOmdOZVDLRnNILm11',
            // },
        };
    }

    getZaloMerchantUrl() {
        return this.get<string>('ZALO_MERCHANT_URL');
    }

    getZaloSecretKey() {
        return this.get<string>('ZALO_SECRET_KEY');
    }

    public getZaloAppId () {
        return this.get<string>('ZALO_APP_ID');
    }

    public getZaloWebInAppId () {
        return this.get<string>("ZALO_V4_APP_ID")
    }

    public getZaloWebInAppSecret () {
        return this.get<string>("ZALO_V4_APP_SECRET")
    }

    public getZaloState() {
        return this.get<string>("ZALO_V4_STATE")
    }

    public getZaloAppSecret () {
        return this.get<string>("ZALO_APP_SECRET")
    }
}
