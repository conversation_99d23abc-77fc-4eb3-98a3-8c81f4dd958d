import { Document } from 'mongoose';

export interface IBookingCare247 extends Document {
    id: string;
    bookingId: string;
    bookingCode: string;
    booking: string;
    bookingStatus: string;
    userId: string;
    user: string;
    patient: string;
    patientId: string;
    patientVersion: string;
    patientVersionId: string;
    partnerId: string;
    partner: string;
    appId?: string;
    platform: string;
    name: string;
    addonServices: MedproCareAddonService[];
    note: string;
    instructor?: any;
    cskhUserIdGuide?: string;
    csAdmin?: any;
    medproCareNote: string;
    status: number;
    provider?: any;
    time: string;
    userInfo: any;
    cancelInfo: any;
    transactionId?: string;
    type: string;
    parentId?: string;
    date: Date;
    subject?: string;
    room?: string;
    doctor?: string;
    service?: string;
    section?: string;
    cskhUserId?: string;
    cskh?: string;
    bookingsRelation?: any;
    payment: string;
    refundCare247: boolean;
    refundCare247Date: Date;
    refundCare247User: string;
    refundCare247Note: string;
    cskhConfirmedCare247?: string;
    viewedPayment?:boolean;
    isCSKHConfirmCare247?:boolean;
}

export interface MedproCareAddonService {
    name: string;
    id: string;
    description: string;
    currency: string;
    duration: string;
    price: number;
    originalPrice: number;
    extendData?: string;
    title?: string;
    subTitle?: string;
}
