
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class ViewBookingCSDTO {
    @ApiProperty({
        description: '_id của booking',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    _id?: string;

    @ApiProperty({
        description: 'Id của booking v2',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    id?: string;

    @ApiProperty({
        description: 'booking code',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    bookingCode?: string;

    @ApiProperty({
        description: 'TransactionId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    transactionId?: string;

    @ApiProperty({
        description: 'smsCode',
        required: false,
        type: String,
    })
    smsCode?: string;

    @ApiProperty({
        description: 'bookingId',
        required: false,
        type: String,
    })
    bookingId?: string;

    paymentList?: boolean

    phone?: string;
}
