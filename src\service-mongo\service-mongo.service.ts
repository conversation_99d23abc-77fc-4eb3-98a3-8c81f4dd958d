import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import {
    CONFIG_COLLECTION_NAME,
    DOCTOR_DESCRIPTION_COLLECTION_NAME,
    DOCTOR_TELEMED_DESCRIPTION_COLLECTION_NAME,
    SCHEDULE_COLLECTION_NAME,
    SEARCH_KEYWORD_COLLECTION_NAME,
    SERVICE_COLLECTION_NAME,
    SERVICE_DESCRIPTION_COLLECTION_NAME,
    SHIFT_COLLECTION_NAME,
} from './schemas/constants';
import { IService } from './interfaces/service.interface';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { first, has, orderBy, size, slice, get, pickBy, identity } from 'lodash';
import { CreateServiceDTO } from './dto/create-service.dto';
import { UpdateServiceDTO } from './dto/update-service.dto';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import * as moment from 'moment';
import { ValidReExamDateDto } from './dto/valid-re-exam-date.dto';
import { IShift } from './interfaces/shifts.interface';
import { IConfig } from './interfaces/config.interface';
import { ISchedule } from './interfaces/schedule.interface';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { IGlobalSetting } from 'src/global-setting/interfaces/global-setting.interface';
import { SearchServiceDto } from './dto/search-service.dto';
import { UrlConfigService } from '../config/config.url.service';
import { ISearchKeywords } from './interfaces/search-keywords.interface';
import { HeadersDto } from '../common/base/headers.dto';
import { IServiceDescription } from './interfaces/service-description.interface';
import { UtilService } from '../config/util.service';
import { IDoctorDescription } from '../doctor-description/interface/doctor-description.inteface';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../hospital-description/schema/constants';
import { IHospitalDescription } from '../hospital-description/interface/hospital-description.inteface';
import { IDoctorTelemedDescription } from './interfaces/doctor-sort.interface';
import { JwtService } from '@nestjs/jwt';
import { LOG_SERVICE_EVENT } from '../audit-log/constant';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IUmcDoctor } from '../doctor-mongo/interfaces/umc-doctor.interface';
import { UMC_DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { FeatureService } from '../feature/feature.service';
import { GlobalSettingService } from '../global-setting/global-setting.service';
import { IAppId } from 'src/app-id/interfaces/app-id.inteface';
import { APP_ID_COLLECTION_NAME } from 'src/app-id/schemas/constants';
import { CacheManagerService } from 'src/cache-manager/cache-manager.service';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { IFeeRefundConfigs } from 'src/cash-back/interfaces/fee-refund-configs.interface';
import { HomePageServiceInject } from 'src/home-page/persistence/home-page.service';
import { HomePageService } from 'src/home-page/home-page.service';
import { REPO_NAME_BETA } from 'src/common/constants';
// import { platform } from 'os';

@Injectable()
export class ServiceMongoService {
    private readonly logger: Logger = new Logger(ServiceMongoService.name);
    private readonly VALID_DATE_RANGE: string = 'VALID_DATE_RANGE';
    private readonly BOOKING_DATE_INVALID_MESSAGE: string = 'BOOKING_DATE_INVALID_MESSAGE';
    private readonly INVALID_DATE_MESSAGE: string = 'INVALID_DATE_MESSAGE';

    constructor(
        @HomePageServiceInject() private readonly homePageService: HomePageService,
        @InjectModel(FEE_REFUND_CONFIGS_COLLECTION_NAME) private readonly feeRefundConfigs: Model<IFeeRefundConfigs>,
        @InjectModel(SERVICE_COLLECTION_NAME) private serviceModel: Model<IService>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(SHIFT_COLLECTION_NAME) private readonly shiftModel: Model<IShift>,
        @InjectModel(CONFIG_COLLECTION_NAME) private readonly configModel: Model<IConfig>,
        @InjectModel(SCHEDULE_COLLECTION_NAME) private readonly scheduleModel: Model<ISchedule>,
        @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private readonly globalSettingModel: Model<IGlobalSetting>,
        @InjectModel(SEARCH_KEYWORD_COLLECTION_NAME) private readonly searchKeywordsModel: Model<ISearchKeywords>,
        @InjectModel(SERVICE_DESCRIPTION_COLLECTION_NAME) private readonly serviceDescriptionModel: Model<IServiceDescription>,
        @InjectModel(DOCTOR_TELEMED_DESCRIPTION_COLLECTION_NAME) private readonly doctorSortModel: Model<IDoctorTelemedDescription>,
        @InjectModel(DOCTOR_DESCRIPTION_COLLECTION_NAME) private readonly doctorDescriptionModel: Model<IDoctorDescription>,
        @InjectModel(HOSPITAL_DESCRIPTION_COLLECTION_NAME) private readonly hospitalDescriptionModel: Model<IHospitalDescription>,
        @InjectModel(UMC_DOCTOR_COLLECTION_NAME) private readonly umcDoctorModel: Model<IUmcDoctor>,
        @InjectModel(APP_ID_COLLECTION_NAME) private appIdModel: Model<IAppId>,

        // @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private readonly globalSettingModel: Model<IGlobalSetting>,
        private readonly urlConfigService: UrlConfigService,
        private readonly http: HttpService,
        private readonly utilService: UtilService,
        private readonly jwtService: JwtService,
        private readonly emitService: EventEmitter2,
        private readonly featureService: FeatureService,
        private readonly globalSettingService: GlobalSettingService,
        private cacheService: CacheManagerService,
    ) {}

    async create(createServiceDTO: CreateServiceDTO): Promise<IService> {
        try {
            const service = new this.serviceModel(createServiceDTO);
            const hospital = await this.hospitalModel.findOne({ partnerId: createServiceDTO.partnerId }).exec();
            service.hospitalId = hospital._id;
            const insertService = await service.save();
            hospital.services.push(insertService._id);
            await hospital.save();
            return insertService.toJSON();
        } catch (error) {
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    async findOne(id: string): Promise<any> {
        try {
            const info = await this.serviceModel
                .findOne({ _id: id })
                .populate('hospitalId')
                .exec();
            if (!info) {
                throw new HttpException('Không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
            }
            return info;
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        }
    }

    async deleteById(id: string): Promise<any> {
        try {
            const result = await this.serviceModel.findByIdAndRemove(id, { select: '_id' }).exec();
            if (!result) {
                throw new HttpException('Thao tác xóa không thành công. Vui lòng kiểm tra lại.', HttpStatus.BAD_REQUEST);
            }
            return result;
        } catch (error) {
            throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async updateOne(updateServiceDTO: UpdateServiceDTO): Promise<any> {
        try {
            const { id, ...updateInfo } = updateServiceDTO;
            return this.serviceModel.findByIdAndUpdate(updateServiceDTO.id, { ...updateInfo }, { new: true }).exec();
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        }
    }

    async find(hospitalId: string): Promise<any> {
        const hospitals = await this.hospitalModel
            .find({ _id: hospitalId })
            .limit(1)
            .exec();
        if (hospitals.length === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.NOT_FOUND);
        }
        const hospital = first(hospitals);
        const services = await this.serviceModel.find({ hospitalId: hospital.id }).exec();
        return {
            hospital,
            services,
        };
    }

    async getExceptionMessage(): Promise<any> {
        try {
            const [bookingDateInvalid, invalidDate] = await Promise.all([
                this.globalSettingModel.findOne({ key: this.BOOKING_DATE_INVALID_MESSAGE }).exec(),
                this.globalSettingModel.findOne({ key: this.INVALID_DATE_MESSAGE }).exec(),
            ]);
            const bookingDateInvalidMessage =
                bookingDateInvalid?.value || 'Ngày tái khám không hợp lệ. Vui lòng kiểm tra lại hoặc chọn hình thức bảo hiểm là chuyển tuyến';
            const invalidDateMessage = invalidDate?.value || 'Bạn đã quá thời hạn hẹn tái khám. Vui lòng chọn hình thức bảo hiểm là chuyển tuyến.';
            return { bookingDateInvalidMessage, invalidDateMessage };
        } catch (error) {
            throw error;
        }
    }

    async validDayOff(partnerId: string, formData: ValidReExamDateDto): Promise<any> {
        // pre validation
        const bookingDate = moment.utc(formData.bookingDate).set({ hours: 23, minutes: 59, seconds: 59 });
        const reExamDate = moment.utc(formData.reExamDate).set({ hours: 23, minutes: 59, seconds: 59 });

        // exception message
        const { bookingDateInvalidMessage, invalidDateMessage } = await this.getExceptionMessage();
        if (bookingDate.isBefore(reExamDate)) {
            throw new HttpException(bookingDateInvalidMessage, HttpStatus.BAD_REQUEST);
        }
        const [extraDay, validDateRange] = await Promise.all([this.calculateExtraDay(formData, partnerId), this.getValidDateRange()]);
        // get range after add extraDay
        const acceptRange = reExamDate.clone().set('dates', reExamDate.toDate().getDate() + validDateRange + extraDay);
        this.logger.debug(`Log Request validDayOff: ${JSON.stringify({ ...formData }, null, 2)}`);
        this.logger.debug(`Log Handle validDayOff: ${JSON.stringify({ bookingDate, reExamDate, acceptRange, extraDay }, null, 2)}`);
        // re exam date between bookingDate and acceptRange
        if (bookingDate.isBefore(reExamDate) || bookingDate.isAfter(acceptRange)) {
            throw new HttpException(invalidDateMessage, HttpStatus.BAD_REQUEST);
        }
        return { isOk: true };
    }

    async calculateExtraDay(formData: ValidReExamDateDto, partnerId: string): Promise<number> {
        // get config day off
        const [hospitalDayOff, holidayDayOff, serviceDayOff, validDateRange] = await Promise.all([
            this.getHospitalDayOff(partnerId),
            this.getHolidayDayOff(partnerId),
            this.getServiceDaysOff(partnerId, formData.serviceId),
            this.getValidDateRange(),
        ]);
        // collect data from database
        const extraDayOfHospital = await this.getExtraDayOfHospitalDayOff(formData, hospitalDayOff);
        const extraHoliday = this.getExtraDay(formData, holidayDayOff, validDateRange);
        const extraDayService = this.getExtraDay(formData, serviceDayOff, validDateRange);
        return Number(extraDayOfHospital) + Number(extraHoliday) + Number(extraDayService);
    }

    async getValidDateRange(): Promise<number> {
        try {
            const validDateRange = await this.globalSettingModel.findOne({ key: this.VALID_DATE_RANGE }).exec();
            return Number(validDateRange?.value) || 9;
        } catch (error) {
            throw error;
        }
    }

    getExtraDay(formData: ValidReExamDateDto, daysOff: string, validDateRange: number): number {
        const current = new Date();
        let extraDay: number = 0;
        // examDate before
        const reExamDate = moment.utc(formData.reExamDate).set({ hours: 23, minutes: 59, seconds: 59 });
        // accept range valid date for re exam
        const acceptRange = reExamDate.clone().set('date', reExamDate.toDate().getDate() + validDateRange);
        daysOff.split(',').map(element => {
            const data = element.split('/');
            const day = data[0];
            // because month of date type start from 0
            const month = data[1] === '*' ? current.getMonth() + 1 : data[1];
            // set config from portal
            const daysOffDate = moment().set({ date: +day, month: +month - 1, year: current.getFullYear() });
            if (daysOffDate.isAfter(reExamDate) && daysOffDate.isBefore(acceptRange)) {
                extraDay++;
            }
        });
        return extraDay;
    }

    // case hospital day off
    async getHospitalDayOff(partnerId: string): Promise<any> {
        try {
            const dayOff = await this.scheduleModel.findOne({ partnerId, deleted: { $ne: true } }).exec();
            if (dayOff?.detailShiftId && dayOff.version === 'v2') {
                const shiftDayOff = await this.shiftModel.findOne({ id: dayOff.detailShiftId }).exec();
                if (!shiftDayOff?.days) {
                    return '';
                }
                return shiftDayOff.days;
            } else {
                if (!dayOff?.days) {
                    return '';
                }
                return dayOff.days;
            }
        } catch (error) {
            throw error;
        }
    }

    // get days off of hospital day off converter day from 0-6
    getHospitalDayOffTransform(dayOff: string): number[] {
        const data: number[] = [];
        dayOff.split(',').map(element => {
            if (element === 'CN') {
                data.push(0);
            } else {
                data.push(Number(element) - 1);
            }
        });
        return data;
    }

    // case holiday
    async getHolidayDayOff(partnerId: string): Promise<any> {
        try {
            const holidayDayOff = await this.configModel.findOne({ partnerId }).exec();
            if (!holidayDayOff?.holidayList) {
                return '';
            }
            return holidayDayOff.holidayList;
        } catch (error) {
            throw error;
        }
    }

    // case service day off
    async getServiceDaysOff(partnerId: string, serviceId: string = ''): Promise<any> {
        if (!!serviceId === false) {
            return '';
        }
        try {
            const service = await this.serviceModel.findOne({ id: serviceId, partnerId }).exec();
            if (!service?.daysOff) {
                return '';
            }
            return service.daysOff;
        } catch (error) {
            throw error;
        }
    }

    // handle for hospital day off
    async getExtraDayOfHospitalDayOff(formData: ValidReExamDateDto, dayOff: string): Promise<any> {
        const reExamDate = moment.utc(formData.reExamDate);
        const bookingDate = moment.utc(formData.bookingDate);
        let extraDay: number = 0;
        const dayOfWeek = new Set(this.getHospitalDayOffTransform(dayOff));
        // if daysOff include reExamDate days
        while (reExamDate.isBefore(bookingDate)) {
            const condition = dayOfWeek.has(
                reExamDate
                    .clone()
                    .toDate()
                    .getDay(),
            );
            if (!condition) {
                extraDay++;
            }
            reExamDate.add(1, 'days');
        }
        return extraDay;
    }

    async search122(body: SearchServiceDto, headers: any = {}): Promise<any> {
        try {
            // console.log('body', JSON.stringify(body, null, 2));
            // console.log('headers', JSON.stringify(headers, null, 2));
            const getApi = this.urlConfigService.getAppMedproApi();
            const url = `${getApi}/mongo/service/search`;
            const { data } = await this.http
                .post(
                    url,
                    {
                        ...body,
                    },
                    {
                        headers: {
                            // ...headers,
                            platform: 'android',
                            appid: headers?.appid || 'medpro',
                            partnerid: 'medpro',
                            ...(headers?.authorization && { authorization: headers?.authorization }),
                            locale: 'vi',
                            versions: '1.3.2',
                            // 'content-type': 'application/json;charset=utf-8'
                        },
                    },
                )
                .toPromise();

            if (REPO_NAME_BETA.includes(this.cacheService.repoName)) {
                if (body.category === 'doctor' || body.category === 'package' ) {
                    return data.map(ite => {
                        const results = (ite?.results || []).map(item => {
                            if (['medlatecbp', 'binhthanhhcm'].includes(item.partner.partnerId)) {
                                let popup = null;
                                if(item.partner.partnerId === 'medlatecbp'){
                                    popup =  {
                                        "title": "Ưu đãi hoàn tiền",
                                        "content": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Melatec Bình Phước. khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>",
                                        "html": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Melatec Bình Phước. khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>"
                                    };
                                }

                                if(item.partner.partnerId === 'binhthanhhcm'){
                                    popup = {
                                        "title": "Ưu đãi hoàn tiền",
                                        "content": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Quận Bình Thạnh khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>",
                                        "html": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Quận Bình Thạnh khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>"
                                    };
                                }
                               
                                return {
                                    ...item,
                                    partner: {
                                        ...item.partner,
                                        isCashBack: true,
                                        popup
                                    }
                                }
                            }
                            return item;
                        });
                        return {
                            ...ite,
                            results
                        }
                    });

                }
            }    
            
            return data;
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async search(body: SearchServiceDto, headers: any = {}): Promise<any> {
        try {
            const getApi = this.urlConfigService.getAppMedproApi();
            const api = `${getApi}/mongo/service/search`;
            const { data } = await this.http
                .post(
                    api,
                    {
                        ...body,
                    },
                    {
                        headers: {
                            // ...headers,
                            platform: 'android',
                            appid: headers?.appid || 'medpro',
                            partnerid: 'medpro',
                            ...(headers?.authorization && { authorization: headers?.authorization }),
                            locale: 'vi',
                            versions: '1.3.2',
                            // 'content-type': 'application/json;charset=utf-8'
                        },
                    },
                )
                .toPromise();
            return data;
        } catch (e) {
            console.log('Error during search:', e.message);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async search_bk(body: SearchServiceDto, headers: any = {}): Promise<any> {
        try {
            let userId = undefined;
            const thisAuthToken = headers.authorization;
            if (thisAuthToken && thisAuthToken.startsWith('Bearer ')) {
                const payload: any = this.jwtService.decode(thisAuthToken.split(' ')[1]);
                userId = payload?.userMongoId || undefined;
            }

            let usercountry = this.utilService.getUserCountryByLocale(headers.locale);
            const { tags = '' } = body;

            const api = `${this.urlConfigService.getUrlApiSearchService}/services/services`;

            let params = {
                ...body,
                category: body.category || 'all',
                tags,
            };
            for (let p in params) {
                if (['', null, undefined].includes(params[p])) {
                    delete params[p];
                }
            }

            let thisHeaders: any = {
                language: headers.locale || '',
                usercountry,
            };
            if (userId) {
                thisHeaders.userId = userId;
            }
            const res = await this.http
                .get(api, {
                    headers: thisHeaders,
                    params,
                })
                .toPromise();

            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: this.search.name,
                summary: 'Search thông tin từ portal',
                nameParent: ServiceMongoService.name,
                params: { params, headers: thisHeaders },
            });

            let portalRes = res.data;
            const hospitalList = await this.hospitalDescriptionModel.find();
            const hospitalDescriptionObj = {};
            hospitalList.forEach(h => {
                if (!!h?.hospitalId) hospitalDescriptionObj[h.hospitalId] = h;
            });
            for (let l of portalRes) {
                if (l.category === 'tab_all') {
                    const partnerFeatureMap = await this.featureService.getPartnerFeatureMap(headers);
                    l.results.map(async (a: any) => {
                        if (a.category === 'doctor') {
                            let doctorDes = await this.getDoctorDescriptionMapByDoctorId([a.id]);
                            a.description = doctorDes[`${a.id}`] || {};
                            if (!get(a, 'description.slug', '')) {
                                a.description.slug = a.id;
                            }
                            if (!get(a, 'description.isDetailVisible', '')) {
                                a.description.isDetailVisible = false;
                            }
                            if (!get(a, 'description.rating', '')) {
                                a.description.rating = { count: 100, rate: 5, description: 'Rất tốt' };
                            }
                            if (!get(a, 'description.bookingTotal', '')) {
                                a.description.bookingTotal = 100;
                            }
                        }

                        if (a.category === 'package') {
                            const hospitalMap = await this.getHospitalInfoMap(Array.from(new Set(l.results.map(i => i.cta.partnerId))));
                            let doctorDes = await this.getServiceDescriptionMapByServiceId([a.id], {
                                slug: true,
                                isDetailVisible: true,
                                serviceId: true,
                            });
                            a.description = doctorDes[`${a.id}`] || {};
                            if (!get(a, 'description.slug', '')) {
                                a.description.slug = a.id;
                            }
                            if (!get(a, 'description.isDetailVisible', '')) {
                                a.description.isDetailVisible = false;
                            }
                            a.description.circleLogo = hospitalMap[a.cta.partnerId].circleLogo;
                            a.description.partnerSlug = hospitalMap[a.cta.partnerId].slug;
                        }

                        if (a.category === 'hospital') {
                            let hospitalDes = await this.getHospitalDescriptionMapByHospitalId([a.id]);
                            a.description = hospitalDes[`${a.id}`] || {};
                            a.features = partnerFeatureMap[`${a.id}`]?.features;
                        }

                        return a;
                    });
                }

                if (l.category === 'doctor') {
                    let doctorDes = await this.getDoctorDescriptionMapByDoctorId(l.results.map(r => r.id));
                    l.results.forEach(d => {
                        d.description = doctorDes[`${d.id}`] || {};
                        if (!get(d, 'description.slug', '')) {
                            d.description.slug = d.id;
                        }
                        if (!get(d, 'description.isDetailVisible', '')) {
                            d.description.isDetailVisible = false;
                        }
                        if (!get(d, 'description.rating', '')) {
                            d.description.rating = { count: 100, rate: 5, description: 'Rất tốt' };
                        }
                        if (!get(d, 'description.bookingTotal', '')) {
                            d.description.bookingTotal = 100;
                        }
                    });
                }

                if (l.category === 'package') {
                    const hospitalMap = await this.getHospitalInfoMap(Array.from(new Set(l.results.map(i => i.cta.partnerId))));
                    let packageDes = await this.getServiceDescriptionMapByServiceId(l.results.map(r => r.id));
                    l.results.forEach(p => {
                        p.description = packageDes[`${p.id}`] || {};
                        if (!get(p, 'description.slug', '')) {
                            p.description.slug = p.id;
                        }
                        if (!get(p, 'description.isDetailVisible', '')) {
                            p.description.isDetailVisible = false;
                        }
                        p.description.circleLogo = hospitalMap[p.cta.partnerId].circleLogo;
                        p.description.partnerSlug = hospitalMap[p.cta.partnerId].slug;
                        p.partner = hospitalDescriptionObj[hospitalMap[p.cta.partnerId].slug];
                    });
                }

                if (l.category === 'hospital') {
                    let [hospitalDes, featureMap] = await Promise.all([
                        this.getHospitalDescriptionMapByHospitalId(l.results.map(r => r.id)),
                        this.featureService.getPartnerFeatureMap(headers),
                    ]);
                    l.results.forEach(h => {
                        h.description = hospitalDes[`${h.id}`] || {};
                        h.features = featureMap[`${h.id}`]?.features;
                    });
                }
            }

            return portalRes;
        } catch (e) {
            console.log('error search: ', e.toJSON ? e.toJSON() : e);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getServiceDescriptionMapByServiceId(serviceIds: string[], projection?: any) {
        const serviceDescriptions = await this.serviceDescriptionModel.find({ serviceId: { $in: serviceIds } }, projection).exec();

        return serviceDescriptions.reduce((res, item) => {
            res[`${item.serviceId}`] = { ...item.toJSON() };
            return res;
        }, {});
    }

    async getHospitalInfoMap(partnerIds: string[]): Promise<any> {
        const hospitals = await this.hospitalModel
            .find(
                {
                    partnerId: {
                        $in: partnerIds,
                    },
                },
                {
                    partnerId: 1,
                    city_id: 1,
                    name: 1,
                    address: 1,
                    packageImgDefault: 1,
                    circleLogo: 1,
                    image: 1,
                    slug: 1,
                    newHospitalTypes: 1,
                    isCashBack: 1,
                },
            )
            .lean();
        const hospitalList = await this.hospitalDescriptionModel.find();
        const hospitalDescriptionObj = {};
        hospitalList.forEach(h => {
            if (!!h?.hospitalId) hospitalDescriptionObj[h.hospitalId] = h;
        });

        // ? Bổ sung popup hoàn tiền 
        const fee = await this.homePageService.getPartnerCashBackPopupList();

        const hospitalMapped = hospitals.map((item) => {
            return {
                [`${item.partnerId}`]: {
                    ...item,
                    circleLogo: item.circleLogo || item.image,
                    rating: hospitalDescriptionObj[item.slug]?.rating,
                    ...(fee.hasOwnProperty(item.partnerId) ? { popup: fee[item.partnerId] } : {}),
                }
            };
        });

        return hospitalMapped.reduce((res, item) => ({ ...res, ...item }), {});
    }
    async getAllPartnerAppId(headers: any, slug?: string): Promise<string | null> {
        try {
            const cacheKey = `get-list-id-config-partner-${headers?.appid}-${slug}_web_app`;
            const cachedData = await this.cacheService.get(cacheKey);

            if (cachedData) {
                return cachedData;
            }

            let partnerInApp: any = await this.appIdModel.findOne({ appId: headers.appid, country: 'VN' });
            partnerInApp = partnerInApp.toObject();
            if (partnerInApp && partnerInApp?.features) {
                const listIdsPartner = partnerInApp?.features
                    .filter(feature => slug === feature?.slug)
                    .flatMap(feature => feature.partners.map(partner => partner.partnerId));
                if (size(listIdsPartner) < 1) {
                    throw new HttpException('Kênh đặt khám chưa có cấu hình cơ sở y tế', HttpStatus.BAD_REQUEST);
                }
                const result = listIdsPartner.join(',');

                await this.cacheService.set(cacheKey, result, { ttl: 60 * 60 });
                return result;
            }

            throw new HttpException('Kênh đặt khám chưa có cấu hình cơ sở y tế', HttpStatus.BAD_REQUEST);
        } catch (err) {
            console.error('Error:', err);
            return null;
        }
    }

    async getAllPackage(body: SearchServiceDto, headers: any): Promise<any> {
        try {
            let userId = undefined;
            const thisAuthToken = headers.authorization;
            if (thisAuthToken && thisAuthToken.startsWith('Bearer ')) {
                const payload: any = this.jwtService.decode(thisAuthToken.split(' ')[1]);
                userId = payload?.userMongoId || undefined;
            }
            const getListCheckConfigPartner = await this.globalSettingService.findByKeyAndRepoName('GET_PARTNER_TO_CONFIG');
            let listConfigPartner;
            if (getListCheckConfigPartner) {
                listConfigPartner = getListCheckConfigPartner.split(',');
            }
            let idsPartner;
            if (listConfigPartner.includes(headers?.appid)) {
                idsPartner = await this.getAllPartnerAppId(headers, body?.slug);
                headers.partnerid = undefined;
            }
            // console.log('idsPartner', idsPartner)
            const api = `${this.urlConfigService.getUrlApiSearchService}/services/services`;
            const { tags = '' } = body;
            let params = pickBy(
                {
                    partnerId: headers.partnerid,
                    category: 'package',
                    search_key: body.search_key || '',
                    tags,
                    limit: body.limit || 10000,
                    offset: body.offset,
                    city_id: body.city_id,
                    hospitalIds: idsPartner,
                },
                identity,
            );
            // return params
            for (let p in params) {
                if (['', null, undefined].includes(params[p])) {
                    delete params[p];
                }
            }

            let thisHeaders: any = {};
            if (userId) {
                thisHeaders.userId = userId;
            }
            const { data } = await this.http
                .get(api, {
                    params,
                    headers: thisHeaders,
                })
                .toPromise();

            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: this.getAllPackage.name,
                summary: 'Search gói khám từ portal',
                nameParent: ServiceMongoService.name,
                params: { params, headers: thisHeaders },
            });

            const packagesResult = data.find(d => d.category === 'package');

            const [hospitalDescriptionsMapServiceId, hospitalMap] = await Promise.all([
                this.getServiceDescriptionMapByServiceId(packagesResult.results.map(item => item.id)),
                this.getHospitalInfoMap(Array.from(new Set(packagesResult.results.map(i => i.cta.partnerId)))),
            ]);

            // packages = packages.results?.filter(item => !!hospitalDescriptionsMapServiceId[`${item.id}`]);
            packagesResult.results.forEach(item => {
                item.serviceDescription = hospitalDescriptionsMapServiceId[`${item.id}`];
                delete item.data;
                if (!item.serviceDescription) {
                    item.serviceDescription = {
                        serviceId: item.id,
                        slug: item.id,
                        images: [],
                        _id: '',
                        description: '',
                        banner: '',
                        bgImage: '',
                        downloadAppImage: '',
                        package_cate: '',
                        seo_description: '',
                        seo_keyword: '',
                        shortDescription: '',
                    };
                } else {
                    if (!item.serviceDescription.serviceId) {
                        item.serviceDescription.serviceId = item.id;
                    }
                    if (!item.serviceDescription.slug) {
                        item.serviceDescription.slug = item.id;
                    }
                }

                item.partner = hospitalMap[`${item.cta?.partnerId}`];
                item.serviceDescription.circleLogo = item.partner?.circleLogo;
                delete item.data;
            });

            if (REPO_NAME_BETA.includes(this.cacheService.repoName)) {
                const results =  packagesResult.results.map(item => {
                    if (['medlatecbp', 'binhthanhhcm'].includes(item.partner.partnerId)) {
                        let popup = null;
                        if (item.partner.partnerId === 'medlatecbp') {
                            popup = {
                                "title": "Ưu đãi hoàn tiền",
                                "content": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Melatec Bình Phước. khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>",
                                "html": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Melatec Bình Phước. khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>"
                            };
                        }

                        if (item.partner.partnerId === 'binhthanhhcm') {
                            popup = {
                                "title": "Ưu đãi hoàn tiền",
                                "content": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Quận Bình Thạnh khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>",
                                "html": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Quận Bình Thạnh khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>"
                            };
                        }

                        return {
                            ...item,
                            partner: {
                                ...item.partner,
                                isCashBack: true,
                                popup
                            }
                        }
                    }
                    return item;
                });

                return {
                    ...packagesResult,
                    results
                }

            } 

            return packagesResult;
        } catch (e) {
            console.error('error getAllPackage: ', e.toJSON ? e.toJSON() : e);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getHospitalDescriptionMapByHospitalId(hospitalIds: string[], projection?: any) {
        const hospitals = await this.hospitalModel.find({ partnerId: { $in: hospitalIds } }).exec();

        const slugs = hospitals.map(h => h.slug);
        const hospitalDescriptions = await this.hospitalDescriptionModel.find({ hospitalId: { $in: slugs } }, projection).exec();

        return hospitalDescriptions.reduce((res, item) => {
            const { slug, partnerId, sponsored, listingPackagePaid, googleMap, circleLogo } = hospitals.find(h => h.slug === item.hospitalId);
            res[`${partnerId}`] = { ...item.toJSON(), slug, sponsored, listingPackagePaid, googleMap };

            return res;
        }, {});
    }

    async getDoctorDescriptionMapByDoctorId(doctorIds: string[], projection: any = { description: false, introduction: false }) {
        const hospitalDescriptions = await this.doctorDescriptionModel.find({ doctorId: { $in: doctorIds } }, projection).exec();

        return hospitalDescriptions.reduce((res, item) => {
            const { translate, ...rest } = item.toObject();
            res[`${item.doctorId}`] = rest;
            return res;
        }, {});
    }

    async getAllDoctor(body: SearchServiceDto, headers: any): Promise<any> {
        try {
            let userId = undefined;
            const thisAuthToken = headers.authorization;
            if (thisAuthToken && thisAuthToken.startsWith('Bearer ')) {
                const payload: any = this.jwtService.decode(thisAuthToken.split(' ')[1]);
                userId = payload?.userMongoId || undefined;
            }
            const [getListCheckConfigPartner, dataNotifBookingDoctorDigimed] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('GET_PARTNER_TO_CONFIG'),
                this.globalSettingService.findByKeyAndRepoName('NOTIF_BOOKING_DOCTOR_DIGIMED')
            ])
            
            let listConfigPartner;
            let notifBookingDoctorDigimed;
            if (getListCheckConfigPartner) {
                listConfigPartner = getListCheckConfigPartner.split(',');
            }
            if(dataNotifBookingDoctorDigimed){
                notifBookingDoctorDigimed = JSON.parse(dataNotifBookingDoctorDigimed)
            }
            let idsPartner;
            if (listConfigPartner.includes(headers?.appid)) {
                idsPartner = await this.getAllPartnerAppId(headers, body?.slug);
                headers.partnerid = undefined;
            }
            const api = `${this.urlConfigService.getUrlApiSearchService}/services/services`;

            let params = pickBy(
                {
                    partnerId: headers.partnerid,
                    category: 'doctor',
                    hospitalIds: idsPartner,
                    ...body,
                },
                identity,
            );
            for (let p in params) {
                if (['', null, undefined].includes(params[p])) {
                    delete params[p];
                }
            }

            let thisHeaders: any = {
                'Content-Encoding': 'gzip',
            };
            if (userId) {
                thisHeaders.userId = userId;
            }
            const { data, config } = await this.http
                .get(api, {
                    params,
                    headers: thisHeaders,
                })
                .toPromise();

            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: this.getAllDoctor.name,
                summary: 'Search bác sĩ từ portal',
                nameParent: ServiceMongoService.name,
                params: { params, headers: thisHeaders },
            });

            let doctors = data.find(r => r.category === 'doctor');

            const [doctorDescriptionMap, partnersMap] = await Promise.all([
                this.getDoctorDescriptionMapByDoctorId(doctors.results.map(r => r.id)),
                this.getHospitalInfoMap(Array.from(new Set(doctors.results.map(i => i.cta.partnerId)))),
            ]);

            doctors.results.forEach(item => {
                item.doctorDescription = doctorDescriptionMap[`${item.id}`];
                item.partner = partnersMap[`${item.cta.partnerId}`];
                if (!item.doctorDescription) {
                    item.doctorDescription = {
                        doctorId: item.id,
                        slug: item.id,
                        position: '',
                        id: '',
                        rating: {},
                        introduction: '',
                        description: '',
                        seo_keyword: '',
                        seo_description: '',
                    };
                } else {
                    if (!item.doctorDescription.doctorId) {
                        item.doctorDescription.doctorId = item.id;
                    }
                    if (!item.doctorDescription.slug) {
                        item.doctorDescription.slug = item.id;
                    }
                    if (notifBookingDoctorDigimed?.status && item?.partnerId === 'digimed'){
                        item.doctorDescription.notiBookingTelemed = notifBookingDoctorDigimed?.noti;
                    }
                }

                delete item.data;
            });

            return doctors;
        } catch (e) {
            console.error('error getAllDoctor: ', e.toJSON ? e.toJSON() : e);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async searchKeywords(headers: HeadersDto) {
        const { locale, appid, platform } = headers;
        const condition = this.utilService.getPlatformType(platform) === 'web' ? { type: 'package' } : {};

        try {
            const keywords = await this.searchKeywordsModel
                .find({ ...condition, enable: true }, 'type value')
                .lean()
                .exec();

            const groupedKeywords = keywords.reduce((acc, keyword) => {
                if (keyword.type === 'departments') {
                    JSON.parse(keyword.value).map(item => {
                        acc.departments = [...(acc.departments || []), item];
                    });
                } else {
                    acc[keyword.type] = [...(acc[keyword.type] || []), keyword.value];
                }
                return acc;
            }, {});

            return groupedKeywords;
        } catch (err) {
            // console.log(`${this.searchKeywords.name}: `, err.toJSON ? err.toJSON() : err);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getPackageDetail(params: any) {
        const { serviceId, treeId, slug } = params;

        let paramsApiSearch = { id: serviceId, treeId };
        let serviceDescription;
        if (slug) {
            serviceDescription = await this.serviceDescriptionModel.findOne({ slug }).exec();
            if (!serviceDescription) {
                throw new HttpException('Không tìm thấy gói khám', 404);
            }

            paramsApiSearch = {
                id: serviceDescription.serviceId,
                treeId,
            };
        } else {
            // console.log('{ serviceId, treeId }: ', { serviceId, treeId });
            serviceDescription = await this.serviceDescriptionModel.findOne({ serviceId, treeId }).exec();
        }

        try {
            const keywords = await this.searchKeywordsModel
                .find({ enable: true }, 'type value')
                .lean()
                .exec();

            const api = `${this.urlConfigService.getUrlApiSearchService}/services/getByCategoryAndId`;

            const { data: detailService } = await this.http
                .get(api, {
                    params: {
                        ...paramsApiSearch,
                        category: 'package',
                    },
                })
                .toPromise();

            const getHospital = await this.hospitalModel.findOne({ partnerId: detailService.cta.partnerId }).exec();

            return {
                ...detailService,
                serviceDescription,
                hospitalLogo: getHospital.circleLogo || getHospital.image,
            };
        } catch (e) {
            console.error('error getPackageDetail: ', e.toJSON ? e.toJSON() : e);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getDoctorDetail(params: any) {
        const { doctorId, treeId, slug } = params;

        let paramsApiSearch = { id: doctorId, treeId };

        paramsApiSearch = {
            id: doctorId || slug,
            treeId,
        };

        let filterSql: any = [{ doctorId: doctorId || slug }];
        if (slug) {
            filterSql.push({ slug });
        }
        const [dataDoctorDescription, dataNotifBookingDoctorDigimed] = await Promise.all([
            this.doctorDescriptionModel.findOne({ $or: filterSql }).exec(),
            this.globalSettingService.findByKeyAndRepoName('NOTIF_BOOKING_DOCTOR_DIGIMED')
        ])
        let notifBookingDoctorDigimed
        if(dataNotifBookingDoctorDigimed){
            notifBookingDoctorDigimed = JSON.parse(dataNotifBookingDoctorDigimed)
        }
        const doctorDescription = dataDoctorDescription.toObject()
        if (doctorDescription) {
            paramsApiSearch = {
                ...paramsApiSearch,
                id: doctorDescription.doctorId,
            };
        }

        try {
            const api = `${this.urlConfigService.getUrlApiSearchService}/services/getByCategoryAndId`;

            const { data: detailDoctor } = await this.http
                .get(api, {
                    params: {
                        ...paramsApiSearch,
                        category: 'doctor',
                    },
                })
                .toPromise();

            let umcDoctor;
            if (detailDoctor.cta?.partnerId === 'umc') {
                umcDoctor = await this.umcDoctorModel.findOne({ doctorId: paramsApiSearch.id }).exec();
            }

            const partner = await this.hospitalModel.findOne({ partnerId: detailDoctor.cta?.partnerId }, 'name slug image circleLogo address').exec();
            if (notifBookingDoctorDigimed?.status && detailDoctor.cta?.partnerId === "digimed"){
                doctorDescription.notiBookingTelemed = notifBookingDoctorDigimed?.noti;
            }
            return {
                ...detailDoctor,
                doctorDescription,
                umcDoctor: umcDoctor?.toObject(),
                partner: partner?.toObject(),
            };
        } catch (e) {
            console.error('error getDoctorDetail: ', e.toJSON ? e.toJSON() : e);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getDoctorTelemed(body: SearchServiceDto, headers: HeadersDto) {
        const treeId = body.treeIds || 'TELEMED';
        body.offset = body.offset + 1;
        const { offset = 1, limit = 10, includes, excludes } = body;
        const dataNotifBookingDoctorDigimed = await this.globalSettingService.findByKeyAndRepoName('NOTIF_BOOKING_DOCTOR_DIGIMED');
        let notifBookingDoctorDigimed: any = {};
        if (dataNotifBookingDoctorDigimed) {
            notifBookingDoctorDigimed = JSON.parse(dataNotifBookingDoctorDigimed);
        }
        if (body.search_key || body.subject_ids || body.role_ids || body.gender_ids || body.city_id) {
            const [searchResult, doctorSort] = await Promise.all([
                this.search({
                    ...body,
                    category: 'doctor',
                    treeIds: treeId,
                    ...(headers.partnerid && { partnerId: headers.partnerid }),
                }),
                this.getDoctorSortGroup(),
            ]);

            let [{ results, ...restCate }] = searchResult;

            results.forEach(r => {
                r['sortOrder'] = doctorSort[r.id]?.sortOrder || Number.MAX_SAFE_INTEGER;
            });

            // results = orderBy(results, ['sortOrder'], ['asc']);
            results = results.map(r => {
                if (r.description) {
                    if (treeId === 'TELEMED') {
                        r.description.disabled = false;
                    }
                    if (notifBookingDoctorDigimed?.status) {
                        r.description.notiBookingTelemed = notifBookingDoctorDigimed?.noti;
                    }
                }
                return r;
            });

            return [
                {
                    ...restCate,
                    results: {
                        page: offset,
                        limit: limit,
                        rows: results.filter(d => size(d) > 0),
                        total: restCate.total,
                    },
                },
            ];
        } else {
            const doctorSortSql = { treeId, deleted: { $ne: true } };
            let doctorSortTotal = await this.doctorSortModel.countDocuments(doctorSortSql);
            const searchResult = await this.search({
                ...body,
                category: 'doctor',
                treeIds: treeId,
                limit,
                offset,
                ...(headers.partnerid && { partnerId: headers.partnerid }),
                ...(excludes && { excludes }),
                ...(includes && { includes }),
            });

            let hospitalAdded = 0;
            let hospitalAddedData = [];
            if (treeId === 'TELEMED' && this.utilService.getPlatformType(headers.platform) === 'web') {
                const partnerDoctorTelemed = await this.globalSettingService.findByKeyAndRepoName('PARTNER_DOCTOR_TELEMED');
                let thisHospitals: Array<any> = await this.hospitalModel
                    .find(
                        {
                            partnerId: { $in: partnerDoctorTelemed.split(',') },
                        },
                        {
                            message: true,
                            deliveryStatus: true,
                            newHospitalTypes: true,
                            sponsored: true,
                            listingPackagePaid:true,
                            partnerId: true,
                            city_id: true,
                            name: true,
                            address: true,
                            circleLogo: true,
                            image: true,
                            deliveryMessage: true,
                            showPartnerInfo: true,
                            workFlow: true,
                            slug: true,
                        },
                    )
                    .exec();
                const hospitalList = await this.hospitalDescriptionModel.find();
                const hospitalDescriptionObj = {};
                hospitalList.forEach(h => {
                    if (!!h?.hospitalId) hospitalDescriptionObj[h.hospitalId] = h;
                });
                hospitalAddedData = thisHospitals.map(h => {
                    return { ...h.toObject(), category: 'hospital', rating: hospitalDescriptionObj[h.slug]?.rating };
                });
                hospitalAdded += hospitalAddedData.length;
            }

            const resultsSearch = first<any>(searchResult)?.results
            const doctorSort = resultsSearch ? resultsSearch?.map(r => {
                return { ...r, doctorId: r.id }
            }) : []

            // const doctorSort = await this.doctorSortModel
            //     .find(doctorSortSql)
            //     .skip((offset - 1) * limit > 0 ? (offset - 1) * limit - hospitalAdded : 0)
            //     .limit(offset == 1 ? limit - hospitalAdded : limit)
            //     .sort({ sortOrder: 1, _id: 1 })
            //     .exec();

            let doctors = await Promise.all(
                doctorSort?.map(async doctorSort => {
                    const api = `${this.urlConfigService.getUrlApiSearchService}/services/getByCategoryAndId`;

                    try {
                        const { data } = await this.http
                            .get(api, {
                                params: {
                                    id: doctorSort.doctorId,
                                    treeId: treeId,
                                    category: 'doctor',
                                },
                            })
                            .toPromise();

                        return {
                            ...data,
                        };
                    } catch (err) {
                        console.error(`err getByCategoryAndId`, this.utilService.errorToJson(err));
                        throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau!', HttpStatus.BAD_REQUEST);
                    }
                }),
            );

            doctors = doctors.filter(d => size(d) > 0);

            let doctorDes: any = await this.doctorDescriptionModel
                .find(
                    { doctorId: { $in: doctors.map(d => d.id) } },
                    {
                        translate: false,
                        description: false,
                        introduction: false,
                    },
                )
                .exec();

            doctorDes = doctorDes.reduce((d, d0) => {
                d[`${d0.doctorId}`] = d0;
                return d;
            }, {});
            doctors = doctors.map(d => {
                const desc = doctorDes[d?.id]?.toObject() || { slug: d?.id, isDetailVisible: false, disabled: false };

                if (notifBookingDoctorDigimed?.status) {
                    desc['notiBookingTelemed'] = notifBookingDoctorDigimed?.noti;
                }
                if (treeId === 'TELEMED') {
                    desc['disabled'] = false;
                }
                return { ...d, description: desc };
            });

            if (offset == 1) {
                doctors.unshift(...hospitalAddedData);
            }
            let [{ results, ...restCate }] = searchResult;

            return [
                {
                    ...restCate,
                    results: {
                        page: offset,
                        limit: limit,
                        rows: doctors.filter(d => size(d) > 0),
                        total: doctorSortTotal + hospitalAdded,
                    },
                },
            ];
        }
    }

    async getDoctorSortV2(body: SearchServiceDto, headers: HeadersDto) {
        const treeId = body.treeIds || 'TELEMED';
        const { offset = 0, limit = 10, includes, excludes } = body;
        const searchResult = await this.search({
            ...body,
            category: 'doctor',
            treeIds: treeId,
            limit: 10000,
            offset: 0,
            ...(headers.partnerid && { partnerId: headers.partnerid }),
            ...(excludes && { excludes }),
            ...(includes && { includes }),
        });

        let [{ results, ...restCate }] = searchResult;
        const thisDoctors = results.map(d => d.cta.doctorId);
        const doctorSortSql = { treeId, deleted: { $ne: true }, doctorId: { $in: thisDoctors } };
        const doctorSort = await this.doctorSortModel
            .find(doctorSortSql)
            .skip(0)
            .limit(10000)
            .sort({ sortOrder: 1, _id: 1 })
            .exec();
        let doctorSortTotal = await this.doctorSortModel.countDocuments(doctorSortSql).exec();

        let doctors: any = await Promise.all(
            doctorSort.map(async doctorSort => {
                const api = `${this.urlConfigService.getUrlApiSearchService}/services/getByCategoryAndId`;
                try {
                    const { data } = await this.http
                        .get(api, {
                            params: {
                                id: doctorSort.doctorId,
                                treeId: treeId,
                                category: 'doctor',
                            },
                        })
                        .toPromise();

                    return {
                        ...data,
                        sortOrder: doctorSort.sortOrder || Number.MAX_SAFE_INTEGER,
                    };
                } catch (err) {
                    console.error(`err getByCategoryAndId`, this.utilService.errorToJson(err));
                    throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau!', HttpStatus.BAD_REQUEST);
                }
            }),
        );

        doctors = doctors.filter(d => JSON.stringify(d) != '{}');
        doctors = orderBy(doctors, 'sortOrder', 'asc');
        if (treeId === 'TELEMED') {
            let thisHospitals: Array<any> = await this.hospitalModel
                .find(
                    {
                        partnerId: { $in: searchResult[0].hospitals.filter(d => d.id != 'digimed').map(d => d.id) },
                    },
                    {
                        message: true,
                        deliveryStatus: true,
                        newHospitalTypes: true,
                        sponsored: true,
                        listingPackagePaid:true,
                        partnerId: true,
                        city_id: true,
                        name: true,
                        address: true,
                        circleLogo: true,
                        image: true,
                        deliveryMessage: true,
                        showPartnerInfo: true,
                        workFlow: true,
                        slug: true,
                    },
                )
                .exec();

            const thisHospitalsData = thisHospitals.map(h => {
                return { ...h.toObject(), category: 'hospital' };
            });
            doctors.unshift(...thisHospitalsData);

            doctorSortTotal += thisHospitalsData.length;
        }

        return [
            {
                ...restCate,
                results: {
                    page: Number(offset),
                    limit: Number(limit),
                    rows: slice(doctors, Number(offset) * Number(limit), Number(offset) * Number(limit) + Number(limit)),
                    total: doctorSortTotal,
                },
            },
        ];
    }

    async getDoctorSortGroup() {
        const hospitalDescriptions = await this.doctorSortModel.find({}).exec();

        return hospitalDescriptions.reduce((res, item) => {
            res[`${item.doctorId}`] = item;
            return res;
        }, {});
    }

    async getSearchDetail(params: { id: string; category: string; treeId?: string }, queryHospital = true) {
        const api = `${this.urlConfigService.getUrlApiSearchService}/services/getByCategoryAndId`;

        const { data: detailService } = await this.http
            .get(api, {
                params,
            })
            .toPromise();

        let hospital: any;
        if (detailService?.cta?.partnerId && queryHospital) {
            hospital = await this.hospitalModel
                .findOne(
                    { partnerId: detailService.cta.partnerId },
                    {
                        name: true,
                        city_id: true,
                        circleLogo: true,
                        image: true,
                    },
                )
                .exec();
        }

        return {
            ...detailService,
            ...(hospital && { partner: hospital }),
        };
    }
}
  