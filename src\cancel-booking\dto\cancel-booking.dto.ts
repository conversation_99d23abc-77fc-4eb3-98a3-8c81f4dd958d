import { IsNotEmpty, IsString, IsArray, <PERSON>idate<PERSON>ested, IsOptional } from 'class-validator';

export class CancelBookingDto {
    @IsArray()
    @IsString({ each: true })
    @IsNotEmpty({ each: true })
    reasonIds: string[];

    @IsOptional()
    @IsString()
    otherContent?: string;

    @IsOptional()
    @IsString()
    bankCode?: string;

    @IsOptional()
    @IsString()
    bankName?: string;

    @IsOptional()
    @IsString()
    bankNumber?: string;

    @IsOptional()
    @IsString()
    accountName?: string;

    @IsOptional()
    @IsString()
    accountType?: string;
}
