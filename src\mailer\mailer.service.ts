import { Injectable, Inject, HttpService } from '@nestjs/common';
import { SENDGRID_CONNECTION } from 'src/config/sendgridConnection';
import { SendgridConfigService } from 'src/config/config.sendgrid.sevice';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import * as moment from 'moment';
import { get } from 'lodash';
import { UrlConfigService } from 'src/config/config.url.service';
import { InsuranceChoiceValue } from 'src/booking-gateway/dto/insurance-choice.dt';
import { IEvent } from 'src/event/intefaces/event.inteface';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { BookingStatus } from 'src/his-gateway/dto/bookingStatus.dto';
import { UtilService } from 'src/config/util.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';

@Injectable()
export class MailerService {
   constructor(
      @Inject(SENDGRID_CONNECTION) private readonly sendgrid,
      private readonly sendgridConfigService: SendgridConfigService,
      private readonly restApiMapping: RestfulAPIOldHospitalConfigService,
      private readonly urlConfigService: UrlConfigService,
      private readonly utilService: UtilService,
      private readonly globalSettingService: GlobalSettingService,
      private readonly httpService: HttpService,
      private readonly eventEmitter: EventEmitter2,
   ) { }

   /* dành cho phần quản ly event version 2 */
   async sendMailPaymentFeeByCard(data: IEvent, hospitalInfo: IHospital): Promise<any> {
      const { eventData } = data.toObject();
      const { paymentHospitalFeeData = {} } = eventData;
      let billId = '';
      if (Object.keys(paymentHospitalFeeData).length > 0) {
         const getBillId = get(paymentHospitalFeeData, 'billId');
         if (getBillId) {
            billId = getBillId;
         }
      }
      const { email } = eventData;
      const env = this.urlConfigService.getEnv();
      let baseUrl = this.urlConfigService.getBaseUrl();
      if (env === 'DEVELOPMENT') {
         baseUrl = 'https://medpro-api-v2-testing.medpro.com.vn';
      }

      const htmlString = `
   <!DOCTYPE html
   PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
   <meta name="viewport" content="width=device-width" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <style>
      * {
         margin: 0;
         font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
         box-sizing: border-box;
         font-size: 14px;
      }

      img {
         max-width: 100%;
      }

      body {
         -webkit-font-smoothing: antialiased;
         -webkit-text-size-adjust: none;
         width: 100% !important;
         height: 100%;
         line-height: 1.6em;
      }

      table td {
         vertical-align: top;
      }
      body {
         background-color: #f6f6f6;
      }

      .body-wrap {
         background-color: #f6f6f6;
         width: 100%;
      }

      .container {
         display: block !important;
         max-width: 600px !important;
         margin: 0 auto !important;
         /* makes it centered */
         clear: both !important;
      }

      .content {
         max-width: 600px;
         margin: 0 auto;
         display: block;
         padding: 20px;
      }

      .main {
         background-color: #fff;
         border: 1px solid #e9e9e9;
         border-radius: 3px;
      }

      .content-wrap {
         padding: 20px;
      }

      .content-block {
         padding: 0 0 20px;
      }

      .last {
         margin-bottom: 0;
      }

      .first {
         margin-top: 0;
      }

      .aligncenter {
         text-align: center;
      }

      .alignright {
         text-align: right;
      }

      .alignleft {
         text-align: left;
      }

      .clear {
         clear: both;
      }

      .invoice {
         width: 100%;
         margin: 0;
         text-align: left;
      }

      .invoice td {
         padding: 5px 0;
      }

      .invoice .invoice-items {
         width: 100%;
      }

      .invoice .invoice-items td {
         border-top: #eee 1px solid;
      }

      .invoice .invoice-items .total td {
         border-top: 2px solid #333;
         border-bottom: 2px solid #333;
         font-weight: 700;
      }
   </style>
</head>

<body itemscope itemtype="http://schema.org/EmailMessage">

   <table class="body-wrap">
      <tr>
         <td></td>
         <td class="container" width="600">
            <div class="content">
             Chờ email template cho phần thanh toán dùng thẻ khám bệnh.
            </div>
         </td>
         <td></td>
      </tr>
   </table>

</body>

</html>
      `;
      return this.sendgrid.send({
         to: email,
         from: {
            name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
            email: '<EMAIL>',
         },
         // bcc: '<EMAIL>',
         subject: `${hospitalInfo.name} - Thanh toán phiếu thành công. Mã phiếu ${billId}.`,
         html: htmlString, // `Đăng ký khám bệnh thành công!`,
      });
   }

   async sendMailCreateDeposit(data: IEvent, hospitalInfo: IHospital): Promise<any> {
      const { eventData } = data.toObject();
      const { email, transactionId } = eventData;
      const env = this.urlConfigService.getEnv();
      let baseUrl = this.urlConfigService.getBaseUrl();
      if (env === 'DEVELOPMENT') {
         baseUrl = 'https://medpro-api-v2-testing.medpro.com.vn';
      }

      const htmlString = `
   <!DOCTYPE html
   PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
   <meta name="viewport" content="width=device-width" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <style>
      * {
         margin: 0;
         font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
         box-sizing: border-box;
         font-size: 14px;
      }

      img {
         max-width: 100%;
      }

      body {
         -webkit-font-smoothing: antialiased;
         -webkit-text-size-adjust: none;
         width: 100% !important;
         height: 100%;
         line-height: 1.6em;
      }

      table td {
         vertical-align: top;
      }
      body {
         background-color: #f6f6f6;
      }

      .body-wrap {
         background-color: #f6f6f6;
         width: 100%;
      }

      .container {
         display: block !important;
         max-width: 600px !important;
         margin: 0 auto !important;
         /* makes it centered */
         clear: both !important;
      }

      .content {
         max-width: 600px;
         margin: 0 auto;
         display: block;
         padding: 20px;
      }

      .main {
         background-color: #fff;
         border: 1px solid #e9e9e9;
         border-radius: 3px;
      }

      .content-wrap {
         padding: 20px;
      }

      .content-block {
         padding: 0 0 20px;
      }

      .last {
         margin-bottom: 0;
      }

      .first {
         margin-top: 0;
      }

      .aligncenter {
         text-align: center;
      }

      .alignright {
         text-align: right;
      }

      .alignleft {
         text-align: left;
      }

      .clear {
         clear: both;
      }

      .invoice {
         width: 100%;
         margin: 0;
         text-align: left;
      }

      .invoice td {
         padding: 5px 0;
      }

      .invoice .invoice-items {
         width: 100%;
      }

      .invoice .invoice-items td {
         border-top: #eee 1px solid;
      }

      .invoice .invoice-items .total td {
         border-top: 2px solid #333;
         border-bottom: 2px solid #333;
         font-weight: 700;
      }
   </style>
</head>

<body itemscope itemtype="http://schema.org/EmailMessage">

   <table class="body-wrap">
      <tr>
         <td></td>
         <td class="container" width="600">
            <div class="content">
             Chờ email template cho phần nạp tiền vào thẻ khám bệnh.
            </div>
         </td>
         <td></td>
      </tr>
   </table>

</body>

</html>
      `;
      return this.sendgrid.send({
         to: email,
         from: {
            name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
            email: '<EMAIL>',
         },
         // bcc: '<EMAIL>',
         subject: `${hospitalInfo.name} - Nạp tiền vào thẻ khám bệnh thành công. Mã giao dịch ${transactionId}.`,
         html: htmlString, // `Đăng ký khám bệnh thành công!`,
      });
   }

   async sendMailToSupporter(countValue: number): Promise<any> {
      const msg = {
         to: this.sendgridConfigService.getListUserRevieveError(),
         from: '<EMAIL>',
         subject: 'Thông báo số lượng lỗi từ Hệ thống!',
         html: `<p>Hệ thống tracking được trong bảng <strong>system_exception</strong> ghi nhận <strong>${countValue}</strong> exception. Vui lòng check lại thông tin chi tiết </p>`,
      };
      return this.sendgrid.send(msg);
   }

   async sendMailReExam(data: any): Promise<any> {

      /* Lấy thông tin khoa, phòng, bác sĩ */
      const getSubject = get(data, 'subject.name');
      const getRoom = get(data, 'room.name');
      const getDoctor = get(data, 'doctor.name');
      const getService = get(data, 'service.name');

      let subjectHtml = '';
      if (getSubject) {
         subjectHtml = `
            <td style="display: block"><span
               style="display: inline-block;width: 120px">Khoa </span>
            <b>${getSubject} </b></td>`;
      }

      let roomHtml = '';
      if (getRoom) {
         roomHtml = `
            <td style="display: block"><span
               style="display: inline-block;width: 120px">Phòng </span>
            <b>${getRoom}</b> </td>`;
      }

      let doctorHtml = '';
      if (getDoctor) {
         doctorHtml = `
            <td style="display: block"><span
               style="display: inline-block;width: 120px">Bác sĩ </span>
            <b>${getDoctor}</b> </td>`;
      }
      const env = this.urlConfigService.getEnv();
      let baseUrl = this.urlConfigService.getBaseUrl();
      if (env === 'DEVELOPMENT') {
         baseUrl = 'https://medpro-api-v2-testing.medpro.com.vn';
      }
      const hospitalImage = `${baseUrl}/st/hospitals/${data.partnerId}-logo-email.png`;

      const htmlString = `
   <!DOCTYPE html
   PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
   <meta name="viewport" content="width=device-width" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <style>
      * {
         margin: 0;
         font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
         box-sizing: border-box;
         font-size: 14px;
      }

      img {
         max-width: 100%;
      }

      body {
         -webkit-font-smoothing: antialiased;
         -webkit-text-size-adjust: none;
         width: 100% !important;
         height: 100%;
         line-height: 1.6em;
      }

      table td {
         vertical-align: top;
      }
      body {
         background-color: #f6f6f6;
      }

      .body-wrap {
         background-color: #f6f6f6;
         width: 100%;
      }

      .container {
         display: block !important;
         max-width: 600px !important;
         margin: 0 auto !important;
         /* makes it centered */
         clear: both !important;
      }

      .content {
         max-width: 600px;
         margin: 0 auto;
         display: block;
         padding: 20px;
      }

      .main {
         background-color: #fff;
         border: 1px solid #e9e9e9;
         border-radius: 3px;
      }

      .content-wrap {
         padding: 20px;
      }

      .content-block {
         padding: 0 0 20px;
      }

      .last {
         margin-bottom: 0;
      }

      .first {
         margin-top: 0;
      }

      .aligncenter {
         text-align: center;
      }

      .alignright {
         text-align: right;
      }

      .alignleft {
         text-align: left;
      }

      .clear {
         clear: both;
      }

      .invoice {
         width: 100%;
         margin: 0;
         text-align: left;
      }

      .invoice td {
         padding: 5px 0;
      }

      .invoice .invoice-items {
         width: 100%;
      }

      .invoice .invoice-items td {
         border-top: #eee 1px solid;
      }

      .invoice .invoice-items .total td {
         border-top: 2px solid #333;
         border-bottom: 2px solid #333;
         font-weight: 700;
      }
   </style>
</head>

<body itemscope itemtype="http://schema.org/EmailMessage">

   <table class="body-wrap">
      <tr>
         <td></td>
         <td class="container" width="600">
            <div class="content">
               <table class="main" width="100%" cellpadding="0" cellspacing="0">
                  <tr>
                     <td class="content-wrap aligncenter">
                        <table width="100%" cellpadding="0" cellspacing="0">
                           <tr>
                              <td class="content-block">
                                 <h1 class="aligncenter"
                                    style="text-align: center;text-transform: uppercase;margin:0;font-size:14px;height: 55px;">
                                    <img src="${hospitalImage}" alt="${data.hospitalName}"
                                       style="width:250px" />
                                 </h1>

                              </td>
                           </tr>
                           <tr>
                              <td style="display:block">
                                 <h2 class="aligncenter"
                                    style="text-align: center;font-weight: 500;text-transform: uppercase;font-size: 18px;color: #0a9ae8;margin: 0">
                                    Lịch tái khám</h2>
                              </td>
                              <td style="display:block;margin-top:20px;text-align: left">
                                 Xin chào <b>${data.parentName}</b>,
                              </td>
                              <td style="display:block;margin-top:10px;text-align: left">
                                 Quý khách có lịch hẹn tái khám cho bé tại <b>${data.hospitalName}.</b>
                              </td>
                           </tr>
                           <tr>
                              <td class="content-block aligncenter">
                                 <table class="invoice">
                                    <tr>
                                       <td style="display: block"><span
                                             style="display: inline-block;width: 120px">Số hồ sơ
                                          </span><b>${data.patientCode}</b>
                                       </td>
                                       <td style="display: block"><span
                                             style="display: inline-block;width: 120px">Họ tên
                                          </span><b>${data.childName}</b>
                                       </td>
                                       ${subjectHtml}
                                       ${roomHtml}
                                       ${doctorHtml}
                                       <td style="display: block"><span
                                             style="display: inline-block;width: 120px">Thời gian</span>
                                          <b>${data.scheduleDate}</b> </td>
                                       <td style="display: block"><span
                                             style="display: inline-block;width: 120px">Nội dung </span>
                                          Tái khám theo lịch hẹn của bác sĩ </td>
                                       <td style="display:block">
                                          <table class="invoice-items" cellpadding="0" cellspacing="0">
                                             <tr>
                                                <td style="text-align: center;padding-top:25px"><a
                                                      rel="noopener noreferrer" target="_blank"
                                                      href="${data.quickBookingUrl}"
                                                      style="display: inline-block;font-weight: 400;text-align: center;white-space: nowrap;vertical-align: middle;padding: .75rem 70px;font-size: 1rem;line-height: 1.5;border-radius: .25rem;background: #0070b6;color: #fff;text-decoration: none;">Đặt
                                                      khám nhanh</a></td>
                                             </tr>

                                          </table>
                                       </td>
                                    </tr>
                                 </table>
                              </td>
                           </tr>

                           <tr>
                              <td
                                 style="font-style: italic;font-size: 12px;padding: 0 0 20px 0;text-align: center">
                                 Cảm ơn quý khách đã sử dụng đăng ký khám bệnh và thanh toán trực
                                 tuyến của
                                 chúng tôi. Mọi
                                 thắc mắc vui lòng liên hệ tổng đài <b>19002115</b> để được hỗ trợ.
                                 <br />Xin chân thành cảm ơn
                              </td>
                           </tr>
                        </table>
                     </td>
                  </tr>
               </table>

            </div>
         </td>
         <td></td>
      </tr>
   </table>

</body>

</html>
      `;
      return this.sendgrid.send({
         to: data.email,
         from: {
            name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
            email: '<EMAIL>',
         },
         // bcc: '<EMAIL>',
         subject: `${data.hospitalName} - Lịch tái khám của ${data.childName} ngày ${data.scheduleDate}`,
         html: htmlString, // `Đăng ký khám bệnh thành công!`,
      });
   }

   async sendMailPaymentFeeSuccess(hospital: any, data: any): Promise<any> {
      const tienThanhToan = `${data.amount}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
      let baseUrl = this.urlConfigService.getBaseUrl();
      const env = this.urlConfigService.getEnv();
      if (env === 'DEVELOPMENT') {
         baseUrl = 'https://medpro-api-v2-testing.medpro.com.vn';
      }
      // const imageUrl = `${baseUrl}/st/hospitals/choray-logo-email.png`;
      const imageUrl = `https://bo-api.medpro.com.vn:5000/static/images/${hospital?.partnerId}/web/thanh-toan-vien-phi.png`;
      const htmlString = `
      <!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta name="viewport" content="width=device-width" />
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<style>
		* {
			margin: 0;
			font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
			box-sizing: border-box;
			font-size: 14px;
		}
		img {
			max-width: 100%;
		}

		body {
			-webkit-font-smoothing: antialiased;
			-webkit-text-size-adjust: none;
			width: 100% !important;
			height: 100%;
			line-height: 1.6em;
		}

		table td {
			vertical-align: top;
		}

		body {
			background-color: #f6f6f6;
		}

		.body-wrap {
			background-color: #f6f6f6;
			width: 100%;
		}

		.container {
			display: block !important;
			max-width: 600px !important;
			margin: 0 auto !important;
			/* makes it centered */
			clear: both !important;
		}

		.content {
			max-width: 600px;
			margin: 0 auto;
			display: block;
			padding: 20px;
		}

		.main {
			background-color: #fff;
			border: 1px solid #e9e9e9;
			border-radius: 3px;
		}

		.content-wrap {
			padding: 20px;
		}

		.content-block {
			padding: 0 0 20px;
      }
		.last {
			margin-bottom: 0;
		}

		.first {
			margin-top: 0;
		}

		.aligncenter {
			text-align: center;
		}

		.alignright {
			text-align: right;
		}

		.alignleft {
			text-align: left;
		}

		.clear {
			clear: both;
      }

		.invoice {
			width: 100%;
			margin: 0;
			text-align: left;
		}

		.invoice td {
			padding: 5px 0;
		}

		.invoice .invoice-items {
			width: 100%;
		}

		.invoice .invoice-items td {
			border-top: #eee 1px solid;
		}

		.invoice .invoice-items .total td {
			border-top: 2px solid #333;
			border-bottom: 2px solid #333;
			font-weight: 700;
		}
	</style>
</head>

<body itemscope itemtype="http://schema.org/EmailMessage">

	<table class="body-wrap">
		<tr>
			<td></td>
			<td class="container" width="600">
				<div class="content">
					<table class="main" width="100%" cellpadding="0" cellspacing="0">
						<tr>
							<td class="content-wrap aligncenter">
								<table width="100%" cellpadding="0" cellspacing="0">
									<tr>
										<td class="content-block">
											<h1 class="aligncenter"
												style="text-align: center;text-transform: uppercase;margin:0;font-size:14px;height: 55px;">
												<img src="${imageUrl}" alt="${hospital.name}"
													style="width:250px" />
											</h1>
										</td>
									</tr>
									<tr>
										<td style="display:block">
											<h2 class="aligncenter"
												style="text-align: center;font-weight: 500;text-transform: uppercase;font-size: 18px;color: #0a9ae8;margin: 0">
												Thanh toán thành công</h2>
										</td>
										<td style="display:block;margin-top:20px;text-align: left">
											Xin chào <b>${data.fullname} (${data.bv_id})</b>,
										</td>
										<td style="display:block;margin-top:10px;text-align: left">
											Quý khách vừa thanh toán cho đơn hàng <b>${data.fee_code}</b> (#${data.transactionId})
										</td>
									</tr>
									<tr>
										<td class="content-block aligncenter">
											<table class="invoice">
												<tr>
													<td style="display: block"><span
															style="display: inline-block;width: 120px">Khoa </span>
														${data.subject_name} </td>
													<td style="display: block"><span
															style="display: inline-block;width: 120px">Nội dung </span>
														${data.content} </td>
													<td style="display:block">
														<table class="invoice-items" cellpadding="0" cellspacing="0">
															<tr>
																<td>Số tiền thanh toán</td>
																<td class="alignright"><b>${tienThanhToan}đ</b></td>
															</tr>

														</table>
													</td>
												</tr>
												<tr>
													<td>
														<table class="invoice-items" cellpadding="0" cellspacing="0">


															<tr class="total">
																<td class="alignright" width="80%">Tổng tiền thanh toán
																</td>
																<td class="alignright" style="padding-left:15px">
																	${tienThanhToan}đ</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</td>
									</tr>

									<tr>
										<td
											style="font-style: italic;font-size: 12px;padding: 0 0 20px 0;text-align: center">
											Cảm ơn quý khách đã sử dụng dịch vụ thanh toán trực tuyến của chúng tôi. Mọi
											thắc mắc vui lòng liên hệ tổng đài <b>19002115</b> để được hỗ trợ.
											<br />Xin chân thành cảm ơn
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>

				</div>
			</td>
			<td></td>
		</tr>
	</table>
</body>
</html>`;
      let display = '';
      if (env !== 'PRODUCTION') {
         display = `[${env}]`;
      }

      return this.sendgrid.send({
         to: data.emailPatient, // '<EMAIL>', // data.emailPatient
         from: {
            name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
            email: '<EMAIL>',
         },
         // bcc: '<EMAIL>',
         subject: `${display}${hospital.name} - Thanh toán Phiếu tạm ứng ${data.fee_code}`,
         html: htmlString, // `Đăng ký khám bệnh thành công!`,
      });
   }

   // getBookingText(booking: any): string {
   //    const { status, noPayment = false, partnerId = '' } = booking;
   //    switch (status) {
   //       case BookingStatus.RESERVE:
   //          return 'Chưa thanh toán';
   //       case BookingStatus.CONFIRMED: {
   //          if (noPayment) {
   //             if (partnerId && partnerId === 'pkdkanhao') {
   //                return 'Đặt khám thành công';
   //             } else {
   //                return 'Đã xác nhận';
   //             }
   //          } else {
   //             return 'Đã thanh toán';
   //          }
   //       }
   //       case BookingStatus.DA_KHAM:
   //          return 'Đã khám';
   //       case BookingStatus.EXPIRED_RESERVATION:
   //          return 'Hết hạn';
   //       case BookingStatus.CANCEL_RESERVATION:
   //          return 'Đã hủy';
   //       default:
   //          return 'Chờ xác nhận';
   //    }
   // }

   getInsuranceChoiceText(booking: any): string {
      const getInsuranceChoice = get(booking, 'insuranceChoice', null);
      if (getInsuranceChoice) {
         switch (getInsuranceChoice) {
            case InsuranceChoiceValue.CHUYEN_TUYEN:
               return 'Chuyển tuyến';
            case InsuranceChoiceValue.DUNG_TUYEN:
               return 'Đúng tuyến';
            case InsuranceChoiceValue.TAI_KHAM:
               return 'Tái khám';
            default:
               return '';
         }
      }
      return '';
   }

   async sendMailBookingSuccess(hospital: any, data: any, booking: any, checkIn: any, payment: any, isInvoiceUrl?: boolean): Promise<any> {
      // const bookingCode = data.bookingCode ? data.bookingCode : '';
      const bookingCode = booking?.bookingCodeV1 || booking.bookingCode;
      const bookingNumberValue = 0;
      let strBookingNunber = '';
      if (bookingNumberValue > 0) {
         strBookingNunber = `, số thứ tự khám là <b>${bookingNumberValue}</b>`;
      }
      const bookingTime = booking.date === null ? '' : (moment(booking.date).add(7, 'hours').format('HH:mm'));
      const bookingDate = booking.date === null ? 'Chờ xác nhận' : (moment(booking.date).add(7, 'hours').format('DD/MM/YYYY'));
      // return data.date;
      // return bookingTime;
      const serviceName = get(booking, 'service.name');
      const subjectName = get(booking, 'subject.name');
      // console.log('booking ne', booking);
      // console.log('chuyen koa ne', subjectName);
      const doctorName = get(booking, 'doctor.name');
      const roomName = get(booking, 'room.name');
      const partnerId = get(booking, 'partnerId');
      const fullname = `${get(booking, 'patient.surname')} ${get(booking, 'patient.name')}`;
      const gender = Number(get(booking, 'patient.sex', 0)) === 1 ? 'Nam' : 'Nữ';

      let birthdate = '';
      try {
         if (booking?.birthDayPT) {
            birthdate = get(booking, 'birthDayPT', get(booking, 'birthYearPT'));
         } else {
            const bt = get(booking, 'patient.birthdate', '');
            birthdate = !!bt ? bt : `${get(booking, 'patient.birthyear')}`;
         }
      } catch (error) {
         birthdate = `${get(booking, 'patient.birthyear')}`;
      }

      const insuranceCode = get(booking, 'insuranceCode') ? 'Có BHYT' : 'Không có BHYT';
      // console.log('mail: insuranceCode', insuranceCode);
      // console.log('mail: booking', booking);
      const getDescription = get(checkIn, 'description');
      const getSectionAddress = get(checkIn, 'sectionAddress', '');
      const getSequenceNumber = get(booking, 'sequenceNumber', 0);
      const getAwaitMessage = get(booking, 'awaitMessage', null);
      const priceText = get(booking, 'service.priceText', 0);
      const shortDescription = get(booking, 'service.shortDescription', '');
      const addonServices = get(booking, 'addonServices', null);
      const bookingStyleStatus = get(booking, 'bookingStyleStatus');
      const section = get(booking, 'section');
      // case thanh toan tai co so
      const checkPaymentAtHospital = get(payment, 'chargeFeeInfo.payMedproFeeAtHospital', false);
      const [totalMessageExtra, totalPaymentMessageGlobal] = await Promise.all([
         this.globalSettingService.findByKeyAndRepoName('TOTAL_PAYMENT_MESSAGE_EXTRA'),
         this.globalSettingService.findByKeyAndRepoName('TOTAL_PAYMENT_MESSAGE'),
      ]);
      const totalPaymentMessage = checkPaymentAtHospital
         ? totalPaymentMessageGlobal.replace(`{totalPaymentMessage}`, `${payment?.amount || 0}`).replace(/\B(?=(\d{3})+(?!\d))/g, '.')
         : null;
      const totalPaymentMessageExtra = checkPaymentAtHospital
         ? totalMessageExtra
         : null;

      let concatString = '';
      if (serviceName) {
         switch (partnerId) {
            case 'bvmathcm':
               concatString = `${concatString}<tr>
                        <td style="text-align: left;">Hình thức khám: <b>${serviceName}</b></td>
                  </tr>`;
               break;
            case 'dalieuhcm':
               concatString = `${concatString}`; break;
            default:
               concatString = `${concatString}<tr>
                        <td style="text-align: left;">Dịch vụ: <b>${serviceName}</b></td>
                  </tr>`;
               break;
         }
      }
      if (section) {
         switch (partnerId) {
            case 'bvmathcm':
               concatString = `${concatString}<tr>
                        <td style="text-align: left;">Khu khám: <b>${section?.name}</b></td>
                  </tr>`;
               break;
            default:
               concatString = `${concatString}`;
               break;
         }
      }
      if (doctorName) {
         concatString = `${concatString}<tr>
                                             <td style="text-align: left;">Bác sĩ: <b>${doctorName}</b></td>
                                       </tr>`;
      }
      const titleBookingTime =
         partnerId === 'trungvuong' ? 'Giờ tiếp nhận dự kiến' : 'Giờ khám dự kiến';
      let showSTTOrGioKham = bookingTime;
      let showDuoiSTT = titleBookingTime;
      const awaitMessage = getAwaitMessage;
      if (getSequenceNumber > 0) {
         if (partnerId === 'choray') {
            showDuoiSTT = 'Số thứ tự phòng khám';
         } else {
            showDuoiSTT = 'Số thứ tự khám';
         }
         showSTTOrGioKham = getSequenceNumber;
      } else {
         showDuoiSTT = '';
         showSTTOrGioKham = bookingTime;
      }
      let displayDateNE = '';
      if (booking.date !== null && getSequenceNumber > 0) {
         displayDateNE = `<tr>
         <td style="text-align: left;">${titleBookingTime}: <b>${bookingTime}</b></td>
      </tr>`;
      }
      const price = get(payment, 'subTotal');
      const formatPrice = `${price}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
      const url = this.restApiMapping.getRestfulAPIOldHospital('THUDUC').apiUrl;
      const patientCode = get(booking, 'patientCode');

      // let getBarcode = '';
      // if (patientCode) {
      //    getBarcode = `<img align="center" border="0" src="${url}/resource/barcode?bv_id=${patientCode}" alt="barcode" title="barcode">`;
      // }
      const descriptionInfo = !!getDescription ? `<tr>
                                                   <td style="text-align: left;"><i>${getDescription}</i></td>
                                                </tr>` : '';
      const getBookingNoteData = get(booking, 'bookingNote');
      const bookingNote = !!getBookingNoteData ? `<tr>
                                                      <td style="text-align: left;"><i>${getBookingNoteData}</i></td>
                                                  </tr>` : '';
      let thongTinPhongKhamChuyenKhoa = '';
      if (roomName) {
         thongTinPhongKhamChuyenKhoa = `${thongTinPhongKhamChuyenKhoa}<tr>
                                             <td style="text-align: center;"><i>Phòng: ${roomName}</i></td>
                                       </tr>`;
      }

      if (getSectionAddress) {
         thongTinPhongKhamChuyenKhoa = `${thongTinPhongKhamChuyenKhoa}<tr>
                                             <td style="text-align: center;"><i>${getSectionAddress}</i></td>
                                       </tr>`;
      }

      if (subjectName) {
         thongTinPhongKhamChuyenKhoa = `${thongTinPhongKhamChuyenKhoa}<tr>
                                             <td style="text-align: center;"><i>Chuyên khoa: ${subjectName}</i></td>
                                       </tr>`;
      }
      const insuranceChoiceText = this.getInsuranceChoiceText(booking);
      let showInsuranceChoiceType = '';
      if (insuranceChoiceText) {
         showInsuranceChoiceType = `
            <tr>
               <td style="text-align: left;">Loại bảo hiểm: <b>${insuranceChoiceText}</b></td>
            </tr>`;
      }

      let showWhezbookingDate = '';
      if (booking.date !== null && getSequenceNumber > 0) {
         showWhezbookingDate = `
         <p style="color: #48a7f2;font-size: 100px;margin: 0px;font-weight: bold;height:100px">
            <b>${showSTTOrGioKham}</b>
         </p>
         <p style="padding:0px;margin:0px; margin-top: 15px;">(${showDuoiSTT})</p>`;
      } else if (awaitMessage) {
         showWhezbookingDate = `
            <p style="color: #FF0000;font-size: 14px;margin: 15px;text-align: center">
               <b>${awaitMessage}</b>
            </p>`;
      } else {
         showWhezbookingDate = `
         <p style="color: #0352cc;font-size: 65px;margin: 0px;font-weight: 700;line-height:70px; text-align: center;">
            <b>${showSTTOrGioKham}</b>
         </p>`;
      }
      // if (totalPaymentMessage) {
      //    showWhezbookingDate = `${showWhezbookingDate}<p style="color: #ff4500;font-size: 14px;margin: 15px;text-align: center">
      //          <b>${totalPaymentMessage}</b>
      //       </p>`;
      // }
      let headerBarCode = '';
      if (booking.displayCodeBooking) {
         const options: string = `${booking.displayCodeBooking?.type}`.toLocaleLowerCase() || 'barcode';
         const codeUrl: string = await this.generateCode(data.userId, partnerId, booking.displayCodeBooking.value, options);
         headerBarCode = `
               ${
                  booking.displayCodeBooking?.visible === true ? `
                  <tr>
                  <td style="text-align: center;">
                  <b>${booking.displayCodeBooking.title}</b>
                  <br/>
                  <img align="center" border="0" src="${codeUrl}" alt="${options}" title="${options}" />
                  <br/>
                  ${booking.displayCodeBooking.value}
                  </td>
                  </tr>
                  ` : ''
               }
         `;
         if (options !== 'barcode') {
            headerBarCode = `
               ${
                  booking.displayCodeBooking?.visible === true ? `
                  <tr>
                  <td style="text-align: center;">
                  <b>${booking.displayCodeBooking.title}</b>
                  <br/>
                  <img align="center" border="0" style="height: 120px; width: 120px" src="${codeUrl}" alt="${options}" title="${options}" />
                  </td>
                  </tr>
                  ` : ''
               }
         `;
         }
      }

      let addonServicesHtml = '';
      if (addonServices && addonServices.length) {
         addonServices.forEach(service => {
            addonServicesHtml += `
            <tr>
               <td style="text-align: left;"> ${service.name}: <b>${service.priceText}</b></td>
            </tr>
            `;
         });
      }

      let bookingStatusDesc = this.utilService.getBookingText(booking);
      bookingStatusDesc = `<td style="display: inline-block; margin-top: 5px; padding: 5px 20px; border-radius: 20px; background-color: ${bookingStyleStatus?.background}; color: ${bookingStyleStatus?.color};">${bookingStatusDesc}</p></td>`

      let actionExam = `
         <tr>
            <td style="text-align: left;">Hình thức khám: <b>${insuranceCode}</b></td>
         </tr>
      `;
      if (partnerId === 'bvmathcm') {
         actionExam = '';
      }

      let showShortDescription = '';
      if (shortDescription) {
         showShortDescription = `<td style="text-align: center;">${shortDescription}</td>`;
      }

      // return getBarcode;
      const tableBooking = `
         <table align="center" style="margin: 0 auto;width: 350px;height: 746px;border:5px solid #e0e6e8;padding: 10px;">
            <tr>
               <td style="text-align: center;"><b>${hospital.name}</b></td>
            </tr>
            ${headerBarCode}
            ${showShortDescription}
            <tr>
               <td style="text-align: center;">
               ${thongTinPhongKhamChuyenKhoa}
                  ${showWhezbookingDate}
               </td>
            </tr>
            <tr>
              ${bookingStatusDesc}
            </tr>
            ${
         totalPaymentMessage
            ? `<tr>
                 <td style="text-align: center;padding:0px; margin:0px;"><p style="margin:0px; color: #ff4500;">${totalPaymentMessage || ''}</p></td>
                           </tr>`
            : ''
         }
                 ${totalPaymentMessageExtra
            ? `<tr>
                 <td style="text-align: center;padding:0px; margin:0px;"><p style="margin:0px; margin-bottom: 10px; color: #ff4500;">${totalPaymentMessageExtra || ''}</p></td>
                           </tr>`
            : ''
                 }
            <tr>
               <td style="text-align: left;">Mã phiếu: <b>${bookingCode}</b></td>
            </tr>
            <tr>
               <td style="text-align: left;">Ngày khám: <b>${bookingDate}</b></td>
            </tr>
            ${displayDateNE}
            ${concatString}
            <tr>
               <td style="text-align: left;">Họ tên: <b>${fullname}</b></td>
            </tr>
            <tr>
               <td style="text-align: left;">Giới tính: <b>${gender}</b></td>
            </tr>
            <tr>
               <td style="text-align: left;">Năm sinh: <b>${birthdate}</b></td>
            </tr>
            ${actionExam}
            ${showInsuranceChoiceType}
            <tr>
               <td style="text-align: left;">Tiền khám: <b>${priceText}</b></td>
            </tr>
            ${addonServicesHtml}
            ${descriptionInfo}
            <tr>
               <td>
                  <hr style="color: #e0e6e8;margin-bottom: 1px;margin-top: 1px;"/>
               </td>
            </tr>
            <tr>
               <td style="text-align: center;">
                  Số hồ sơ (Mã số bệnh nhân):<br/> <b>${patientCode}</b> <br/>
               </td>
            </tr>
            ${bookingNote}
         </table>`;
      let introduction = `<p>
      Cám ơn bạn đã sử dụng dịch vụ đăng ký khám bệnh trực tuyến của chúng tôi, đây là phiếu khám bệnh của bạn${strBookingNunber}</b><br/>
      Vui lòng mang theo phiếu khám bệnh này khi đến khám tại bệnh viện. Xin chân thành cám ơn!
     </p>`;
      if (isInvoiceUrl) {
         introduction = `<p>
         Cám ơn bạn đã sử dụng dịch vụ đăng ký khám bệnh trực tuyến của chúng tôi, <a href="${data.invoiceUrl}" rel="noopener noreferrer" target="_blank">Nhấn vào đây để xem thông tin hóa đơn của phiếu khám bệnh!</a>
        </p>`;
      }
      const htmlString = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
                <style type="text/css" id="media-query-bodytag">
                    @media (max-width: 520px) {
                    .block-grid {
                        min-width: 320px!important;
                        max-width: 100%!important;
                        width: 100%!important;
                        display: block!important;
                    }
                    .col {
                        min-width: 320px!important;
                        max-width: 100%!important;
                        width: 100%!important;
                        display: block!important;
                    }
                    .col > div {
                        margin: 0 auto;
                    }
                    img.fullwidth {
                        max-width: 100%!important;
                    }
                    img.fullwidthOnMobile {
                        max-width: 100%!important;
                    }
                    .no-stack .col {
                        min-width: 0!important;
                        display: table-cell!important;
                    }
                    .no-stack.two-up .col {
                        width: 50%!important;
                    }
                    .no-stack.mixed-two-up .col.num4 {
                        width: 33%!important;
                    }
                    .no-stack.mixed-two-up .col.num8 {
                        width: 66%!important;
                    }
                    .no-stack.three-up .col.num4 {
                        width: 33%!important;
                    }
                    .no-stack.four-up .col.num3 {
                        width: 25%!important;
                    }
                    .mobile_hide {
                        min-height: 0px!important;
                        max-height: 0px!important;
                        max-width: 0px!important;
                        display: none!important;
                        overflow: hidden!important;
                        font-size: 0px!important;
                    }
                    }
                </style>
            </head>
            <body>
            <!--[if IE]>
            <div class="ie-browser">
            <![endif]-->
            <!--[if mso]>
            <div class="mso-container">
               <![endif]-->
               ${introduction}
               <table class="nl-container" style="border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #FFFFFF;width: 100%" cellpadding="0" cellspacing="0">
                  <tbody>
                     <tr style="vertical-align: top">
                        <td style="word-break: break-word;border-collapse: collapse !important;vertical-align: top">
                           <!--[if (mso)|(IE)]>
                           <table width="100%" cellpadding="0" cellspacing="0" border="0">
                              <tr>
                                 <td align="center" style="background-color: #FFFFFF;">
                                    <![endif]-->
                                    <div style="background-color:transparent;">
                                       <div style="Margin: 0 auto;min-width: 320px;max-width: 500px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;" class="block-grid ">
                                          <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                                             <!--[if (mso)|(IE)]>
                                             <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                <tr>
                                                   <td style="background-color:transparent;" align="center">
                                                      <table cellpadding="0" cellspacing="0" border="0" style="width: 500px;">
                                                         <tr class="layout-full-width" style="background-color:transparent;">
                                                            <![endif]-->
                                                            <!--[if (mso)|(IE)]>
                                                            <td align="center" width="500" style=" width:500px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top">
                                                               <![endif]-->
                                                               <div class="col num12" style="min-width: 320px;max-width: 500px;display: table-cell;vertical-align: top;">
                                                                  <div style="background-color: transparent; width: 100% !important;">
                                                                     <!--[if (!mso)&(!IE)]><!-->
                                                                     <div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                                                                        <!--<![endif]-->
                                                                        <div align="center" class="img-container center fullwidthOnMobile fixedwidth " style="padding-right: 0px;  padding-left: 0px;">
                                                                           <!--[if mso]>
                                                                           <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                                              <tr style="line-height:0px;line-height:0px;">
                                                                                 <td style="padding-right: 0px; padding-left: 0px;" align="center">
                                                                                    <![endif]-->
                                                                                    <!--[if mso]>
                                                                                 </td>
                                                                              </tr>
                                                                           </table>
                                                                           <![endif]-->
                                                                        </div>
                                                                        <!--[if (!mso)&(!IE)]><!-->
                                                                     </div>
                                                                     <!--<![endif]-->
                                                                  </div>
                                                               </div>
                                                               <!--[if (mso)|(IE)]>
                                                            </td>
                                                         </tr>
                                                      </table>
                                                   </td>
                                                </tr>
                                             </table>
                                             <![endif]-->
                                          </div>
                                       </div>
                                    </div>
                                    <div style="background-color:transparent;">
                                       <div style="Margin: 0 auto;min-width: 320px;max-width: 500px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;" class="block-grid ">
                                          <div style="border-collapse: collapse;display: table;width: 100%;background-color:transparent;">
                                             <!--[if (mso)|(IE)]>
                                             <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                <tr>
                                                   <td style="background-color:transparent;" align="center">
                                                      <table cellpadding="0" cellspacing="0" border="0" style="width: 500px;">
                                                         <tr class="layout-full-width" style="background-color:transparent;">
                                                            <![endif]-->
                                                            <!--[if (mso)|(IE)]>
                                                            <td align="center" width="500" style=" width:500px; padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top">
                                                               <![endif]-->
                                                               <div class="col num12" style="min-width: 320px;max-width: 500px;display: table-cell;vertical-align: top;">
                                                                  <div style="background-color: transparent; width: 100% !important;">
                                                                     <!--[if (!mso)&(!IE)]><!-->
                                                                     <div style="border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
                                                                        <!--<![endif]-->
                                                                        <div class="" style="font-size: 16px;font-family:Arial, 'Helvetica Neue', Helvetica, sans-serif; text-align: center;">
                                                                           <div class="our-class">
                                                                              <!-- Block PKB -->
                                                                              ${tableBooking}
                                                                              <!-- END -->
                                                                           </div>
                                                                        </div>
                                                                        <!--[if (!mso)&(!IE)]><!-->
                                                                     </div>
                                                                     <!--<![endif]-->
                                                                  </div>
                                                               </div>
                                                               <!--[if (mso)|(IE)]>
                                                            </td>
                                                         </tr>
                                                      </table>
                                                   </td>
                                                </tr>
                                             </table>
                                             <![endif]-->
                                          </div>
                                       </div>
                                    </div>
                                    <!--[if (mso)|(IE)]>
                                 </td>
                              </tr>
                           </table>
                           <![endif]-->
                        </td>
                     </tr>
                  </tbody>
               </table>
               <!--[if (mso)|(IE)]>
            </div>
            <![endif]-->
            </body>
            </html>`;

      // return msg;
      const env = this.urlConfigService.getEnv();
      let display = '';
      if (env !== 'PRODUCTION') {
         display = `[${env}]`;
      }
      // let defaultSubject = `${display}${hospital.name} - Xác nhận Phiếu Khám Bệnh: ${bookingCode}`;
      // if (isInvoiceUrl) {
      //    defaultSubject = `${display}${hospital.name} - Thông tin hóa đơn phiếu khám: ${bookingCode}`;
      // }
      let defaultSubject = `${display}${hospital.name} - Xác nhận Phiếu Khám Bệnh`;
      if (isInvoiceUrl) {
         defaultSubject = `${display}${hospital.name} - Thông tin hóa đơn phiếu khám`;
      }
      return this.sendgrid.send({
         to: data.emailPatient, // '<EMAIL>', // data.emailPatient
         from: {
            name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
            email: '<EMAIL>',
         },
         // bcc: '<EMAIL>', // '<EMAIL>'
         subject: defaultSubject,
         html: htmlString, // `Đăng ký khám bệnh thành công!`,
      });
   }

   async generateCode(userId: string, partnerId: string, text: string, type: string): Promise<any> {
      const url = `${this.urlConfigService.getS3Url()}/generator/code?userId=${userId}&text=${text}&type=${type}`;
      try {
          const data = await(
              await this.httpService.get(url, { headers: { partnerid: partnerId, secretkey: this.urlConfigService.getS3SecretKey() } }).toPromise()
          ).data;
          return data.fileUrl;
      } catch (error) {
          this.eventEmitter.emit(LOG_SERVICE_EVENT, {
              name: 'generateCode',
              summary: 'S3 gen code',
              nameParent: 'sendMailBookingSuccess',
              params: { url },
              errorBody: this.utilService.errorHandler(error),
              response: {},
              message: error?.message || `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.`,
          });
          return '';
      }
   }
}
