import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BannerController } from './banner.controller';
import { BannerService } from './banner.service';
import { BannersHeaderSchema } from './schema/banner-header.schema';
import { BannersStickyBigSchema } from './schema/banner-sticky-big.schema';
import { BannersStickySmallSchema } from './schema/banner-sticky-small.schema';
import { BANNER_HEADER_COLLECTION_NAME, BANNER_STICKY_BIG_COLLECTION_NAME, BANNER_STICKY_SMALL_COLLECTION_NAME } from './schema/constant';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: BANNER_HEADER_COLLECTION_NAME, schema: BannersHeaderSchema },
            { name: BANNER_STICKY_BIG_COLLECTION_NAME, schema: BannersStickyBigSchema },
            {
                name: BANNER_STICKY_SMALL_COLLECTION_NAME,
                schema: BannersStickySmallSchema,
            },
        ]),
    ],
    controllers: [BannerController],
    providers: [BannerService],
})
export class BannerModule {}
