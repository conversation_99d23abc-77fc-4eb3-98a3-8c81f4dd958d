import * as mongoose from 'mongoose';
import { BOOKING_LOCKED_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const BookingLockSchema = new Schema(
    {
        transactionId: String,
        id: { type: String },
        booking: Schema.Types.Mixed,
        patient: Schema.Types.Mixed,
        partner: Schema.Types.Mixed,
        user: Schema.Types.Mixed,
    },
    {
        timestamps: true,
        collection: BOOKING_LOCKED_COLLECTION_NAME,
    },
);
