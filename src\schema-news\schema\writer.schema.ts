import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { WRITER_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const WriterSchema = new Schema(
    {
        image: { type: String },
        name: { type: String },
        role: { type: String },
        experience: { type: String },
        facebook: { type: String },
        linkedIn: { type: String },
    },
    {
        collection: WRITER_COLLECTION_NAME,
        timestamps: true
    },
).plugin(jsonMongo);
