import { CacheModule, Global, Module } from '@nestjs/common';
import { CacheManagerController } from './cache-manager.controller';
import { CacheManagerService } from './cache-manager.service';
import * as redisStore from 'cache-manager-redis-store';
import { ConfigModule } from 'src/config/config.module';
import { ConfigCacheManagerService } from 'src/config/config.cache-manager.service';
import { MEMORY, REDIS } from './cache-manager.constant';
import { Logger } from '@nestjs/common';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { MongooseModule } from '@nestjs/mongoose';

@Global()
@Module({
    imports: [
        CacheModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configCacheManagerService: ConfigCacheManagerService) => {
                const typeCache = configCacheManagerService.getCacheManagerType();
                const logger = new Logger(`CacheMannager`);
                const ttl = configCacheManagerService.getCacheTtlDefault();
                switch (typeCache) {
                    case REDIS:
                        const host = configCacheManagerService.getRedisHost();
                        const port = configCacheManagerService.getRedisPort();
                        logger.warn(`Redis listen on ${host}:${port}`);
                        return {
                            store: redisStore,
                            host,
                            port,
                            ttl,
                            auth_pass: configCacheManagerService.getRedisPassword(),
                        };
                    case MEMORY:
                        logger.warn(`Cache Memory`);
                        return {
                            ttl,
                        };
                }
            },
            inject: [ConfigCacheManagerService],
        }),
        MongooseModule.forFeature([
            {
                name: GLOBAL_SETTING_COLLECTION_NAME,
                schema: GlobalSettingSchema,
            },
        ]),
    ],
    controllers: [CacheManagerController],
    providers: [CacheManagerService, GlobalSettingService],
    exports: [CacheManagerService],
})
export class CacheManagerModule {}
