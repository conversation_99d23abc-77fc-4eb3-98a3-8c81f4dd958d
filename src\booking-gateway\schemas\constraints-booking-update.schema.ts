import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CONSTRAINTS_BOOKING_UDPATE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ConstraintsBookingUpdateSchema = new Schema(
    {
        bookingCode: { type: String, unique: true, required: true },
    },
    {
        collection: CONSTRAINTS_BOOKING_UDPATE_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
