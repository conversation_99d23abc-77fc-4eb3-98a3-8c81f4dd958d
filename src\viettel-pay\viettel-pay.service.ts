import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
// import { ConfigsService } from './config/configs.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import * as crypto from 'crypto';
import { AppIdService } from 'src/app-id/app-id.service';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { ViettelMoneyConfigService } from 'src/config/config.viettelmoney.service';
import { UtilService } from 'src/config/util.service';
import { UserViettelMoneyService } from 'src/user-viettel-money/user-viettel-money.service';
import { IUser } from 'src/user/interfaces/user.interface';
import { UserService } from 'src/user/user.service';
import { VietetlPayGetMerchantUrlDto } from './dto/viettelpay-get-merchant-url.dto';
import * as jwt from 'jsonwebtoken';
import { DecodeFromSessionIdDto, TokenDecodedPayload } from './dto/decode-from-seesion-id.dto';

@Injectable()
export class ViettelPayService {

    constructor(
        private readonly userViettelMoneyService: UserViettelMoneyService,
        private readonly utilService: UtilService,
        private readonly viettelPayConfig: ViettelMoneyConfigService,
        private readonly featureSevices: AppIdService,
        private readonly jwtService: JwtService,
        private readonly ViettelPayService: UserViettelMoneyService,
        private readonly userService: UserService,
        private readonly eventEmitter: EventEmitter2,
        @InjectSentry() private readonly clientSentry: SentryService,
    ) {
    }

    async createUserWithToken(secretKey: string, appId: string): Promise<any> {
        const viettel_money_id = secretKey; // await this.getMomoToken(secretKey);
        /* kiểm tra xem có thông tin này chưa  */
        let payload = { username: '', sub: 0, userMongoId: null };
        if (appId === 'viettelpay') {
            let viettelMoneyId = '';
            try {
                const {payload} = jwt.decode(secretKey, { complete: true });
                viettelMoneyId = payload.username;
            } catch (err) {
                throw new HttpException('Không có quyền truy cập!', 401);
            }
            return this.ViettelPayService.createUserVietelMoney({ viettelMoneyId, phone: '' });
        }
        throw new HttpException(
            'Thông tin không chính xác. Vui lòng liên hệ tổng đài đặt khám nhanh 19002115 để được hỗ trợ.',
            HttpStatus.FORBIDDEN,
        );
    }
    private async handleUserViettelMoney(phone: string, id: string): Promise<any> {
        const paramsHandleUser = {
            viettelMoneyId: id,
            phone
        };
        console.log('paramsHandleUser', paramsHandleUser)
        return this.userViettelMoneyService.createUserVietelMoney(paramsHandleUser)
    }


    private base64UrlEncodeFromSessionId(session_id: string): string {
        const decoded = Buffer.from(session_id, 'base64');
        const decodedToken = decoded.toString('utf8');
        const base64UrlEncoded = decodedToken.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
        return base64UrlEncoded
    }

    public privateKeyFromPublicKey (publicKey: string) {
        const publicKeyWithFormat = `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----\n`
        return Buffer.from(publicKeyWithFormat).toString("base64");
    }
    
    private get publicKeyFromENV () {
        const publicKey = this.viettelPayConfig.getViettelMoneyPublicKey();
        // console.log('publicKey: ', process.env.VIETTEL_MONEY_PUBLIC_KEY)
        return publicKey
    }

    private get base64FromENVKey () {
        // ? ********************************************************************************************************************
        // ? https://stackoverflow.com/questions/55459528/using-private-key-in-a-env-file - DOCUMENT CONVERT PUBLIC KEY TO STRING
        // ? ********************************************************************************************************************
        return this.publicKeyFromENV;
        return Buffer.from(this.publicKeyFromENV, 'base64').toString('ascii');
    }

    public async decodeJwtTokenFromSessionId(decodeFromSessionIdDto: DecodeFromSessionIdDto) {
        let response: {
            success: boolean;
            type: string;
            payload: TokenDecodedPayload;
            message?: string;
        };
        try {
            let shouldVerified;
            // console.log(`JWT: ${this.base64UrlEncodeFromSessionId(decodeFromSessionIdDto.session_id)}`)
            const payloadVerified = jwt.verify(this.base64UrlEncodeFromSessionId(decodeFromSessionIdDto.session_id), this.base64FromENVKey, {
                algorithms: ['RS256'],
            }) as TokenDecodedPayload;
            // console.log('payloadVerified:', JSON.stringify(payloadVerified, null, 2))
            // console.log('decodeFromSessionIdDto.session_id:', decodeFromSessionIdDto.session_id)

            if (payloadVerified.usr) {
                shouldVerified = decodeFromSessionIdDto.msisdn === payloadVerified.usr.toString().replace(/^84/, '0');
            }else{
                response = {
                    success: false,
                    type: 'jwt',
                    payload: null,
                    message: 'Không tìm thấy thông tin user Viettel Monney',
                };
            }
            if (shouldVerified) {
                response = {
                    success: true,
                    type: 'jwt',
                    payload: payloadVerified,
                    message: 'Decode success',
                };
                let userViettelMoney;
                try {
                    userViettelMoney = await this.handleUserViettelMoney(payloadVerified.usr, payloadVerified.usr);
                    // console.log('handle user response: ', userViettelMoney);
                } catch (err) {
                    console.log(err);
                    throw this.utilService.getException(err, { message: 'Error handleUserViettelMoney' });
                }

                const result = {
                    status: 200,
                    message: 'Success',
                    token: userViettelMoney.token,
                };
                return result;
            } else {
                response = {
                    success: false,
                    type: 'jwt',
                    payload: null,
                    message: 'Decode failed - msisdn not match with usr in token payload!',
                };
            }
        } catch (error) {
            console.log(JSON.stringify(error, null, 2))
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return response;
    }
}

