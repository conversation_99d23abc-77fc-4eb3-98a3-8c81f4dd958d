import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsBoolean, IsDateString, IsOptional } from 'class-validator';

export class VitalSignsDto {
    @ApiProperty({ description: '<PERSON><PERSON><PERSON> sinh dấu hiệu', example: '2025-07-18T03:15:40.453Z' })
    @IsOptional()
    birthSign: string;

    @ApiProperty({ description: 'Cân nặng (Kg)', example: '65' })
    @IsString({ message: 'Cân nặng phải là chuỗi' })
    @IsOptional()
    weight: string;

    @ApiProperty({ description: 'Chiều cao (cm)', example: '165' })
    @IsString({ message: 'Chiều cao phải là chuỗi' })
    @IsOptional()
    height: string;

    @ApiProperty({ description: 'Nhiệt độ (°C)', example: '37' })
    @IsString({ message: 'Nhiệt độ phải là chuỗi' })
    @IsOptional()
    temperature: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> (lần/phút)', example: '90' })
    @IsString({ message: '<PERSON>ạch phải là chuỗi' })
     @IsOptional()
    pulse: string;

    @ApiProperty({ description: 'SpO2 (%)', example: '36' })
    @IsString({ message: 'SpO2 phải là chuỗi' })
    @IsOptional()
    spO2: string;

    @ApiProperty({ description: 'Huyết áp (mmHg)', example: '80/80' })
    @IsString({ message: 'Huyết áp phải là chuỗi' })
    @IsOptional()
    bloodPressure: string;
}

export class CreatePatientInfoDto {
    @ApiProperty({ description: 'Họ và tên bệnh nhân', example: 'Nguyễn Văn A' })
    @IsString({ message: 'Tên bệnh nhân phải là chuỗi' })
    @IsNotEmpty({ message: 'Tên bệnh nhân không được để trống' })
    patientName: string;

    @ApiProperty({ description: 'Ngày sinh', example: '2025-04-07T03:15:07.888Z' })
    @IsDateString({}, { message: 'Ngày sinh phải là ngày hợp lệ' })
    birthDate: string;

    @ApiProperty({ description: 'Giới tính', enum: [0,1], example: 0 })
    @IsEnum([0,1], { message: 'Giới tính phải là 0 (Nữ) hoặc 1 (Nam)' })
    gender: 0 | 1;

    @ApiProperty({ description: 'Họ và tên Mẹ/Ba', example: 'TRUONG HA', required: false })
    @IsOptional()
    @IsString({ message: 'Tên Mẹ/Ba phải là chuỗi' })
    parentName?: string;

    @ApiProperty({ description: 'Số điện thoại', example: '**********' })
    @IsString({ message: 'Số điện thoại phải là chuỗi' })
    @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
    phone: string;

    @ApiProperty({ description: 'ID Thành phố', example: 'medpro_79' })
    @IsString({ message: 'ID Thành phố phải là chuỗi' })
    @IsNotEmpty({ message: 'ID Thành phố không được để trống' })
    city_id: string;

    @ApiProperty({ description: 'ID Quận/Huyện', example: 'medpro_787' })
    @IsString({ message: 'ID Quận/Huyện phải là chuỗi' })
    @IsNotEmpty({ message: 'ID Quận/Huyện không được để trống' })
    district_id: string;

    @ApiProperty({ description: 'ID Xã/Phường', example: 'medpro_27673' })
    @IsString({ message: 'ID Xã/Phường phải là chuỗi' })
    @IsNotEmpty({ message: 'ID Xã/Phường không được để trống' })
    ward_id: string;

    @ApiProperty({ description: 'Địa chỉ chi tiết', example: '778' })
    @IsString({ message: 'Địa chỉ phải là chuỗi' })
    @IsNotEmpty({ message: 'Địa chỉ không được để trống' })
    address: string;

    @ApiProperty({ description: 'Lý do đến khám', example: 'Đau bụng' })
    @IsString({ message: 'Lý do đến khám phải là chuỗi' })
    @IsNotEmpty({ message: 'Lý do đến khám không được để trống' })
    reasonForVisit: string;

    @ApiProperty({ description: 'Chụp X-Ray', example: true })
    @IsOptional()
    isXRay: boolean;

    @ApiProperty({ description: 'Nhập viện', example: true })
    @IsOptional()
    isHospitalized: boolean;

    @ApiProperty({ description: 'Dấu hiệu sinh tồn', type: VitalSignsDto })
    vitalSigns: VitalSignsDto;
}
