import * as mongoose from 'mongoose';
import * as uuid from 'uuid';
import { GUIDE_PARTNER_COLLECTION_NAME, TypeStep } from './constants';

const Schema = mongoose.Schema;

export const GuidePartnerSchema = new Schema(
    {
        id: {
            type: String,
            default: () => uuid.v4().replace(/-/g, ''),
            required: true,
        },
        name: {
            type: String,
            required: true,
        },
        stepName: {
            type: String,
            required: true,
        },
        content: {
            type: String,
            required: true,
        },
        sortOrder: {
            type: Number,
            required: true,
        },
        partnerId: {
            type: String,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
            required: true,
        },
        type: {
            type: String,
            default: TypeStep.FLOW,
        },
        imageUrl: {
            type: String,
            default: '',
        },
        locale: {
            type: String,
            default: 'vi',
        },
    },
    {
        collection: GUIDE_PARTNER_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
