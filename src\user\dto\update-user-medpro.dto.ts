import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsOptional, ValidateIf } from "class-validator";

export class UpdateUserMedproDto {
    @ApiProperty({ description: '<PERSON><PERSON> điện thoại', required: true, type: 'string' })
    @IsOptional()
    @Transform(value => `${value}`.trim())
    fullname: string;

    @IsOptional()
    @Transform(value => `${value}`.trim())
    username: string;

    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @ValidateIf(o => o.email)
    @Transform(value => `${value}`.trim())
    email: string;

    @IsOptional()
    isSaveEmail: boolean;
}