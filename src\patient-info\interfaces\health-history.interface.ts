import { Document } from 'mongoose';

export enum RecordType {
    INITIAL = 'INITIAL',
    CHECKUP = 'CHECKUP',
    EMERGENCY = 'EMERGENCY',
    FOLLOW_UP = 'FOLLOW_UP'
}

export interface IVitalSigns {
    weight?: number;
    height?: number;
    temperature?: number;
    pulse?: number;
    spO2?: number;
    bloodPressure?: string;
}

export interface IHealthHistory {
    patientInfoId: string;
    patientId?: string;
    recordDate: Date;
    vitalSigns?: IVitalSigns;
    symptoms?: string[];
    diagnosis?: string;
    notes?: string;
    recordType: RecordType;
    createdBy: string;
}

export interface IHealthHistoryDocument extends IHealthHistory, Document {}
