import { ConfigCacheManagerService } from './../config/config.cache-manager.service';
import { ConfigModule } from './../config/config.module';
import { CacheModule, Global, HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { AppIdController } from './app-id.controller';
import { AppIdService } from './app-id.service';
import { AppIdSchema } from './schemas/app-id.schema';
import { APP_ID_COLLECTION_NAME } from './schemas/constants';
import { CacheManagerModule } from '../cache-manager/cache-manager.module';
import { HospitalMongoModule } from '../hospital-mongo/hospital-mongo.module';
import { FeatureModule } from '../feature/feature.module';
import { ReportTransactionDailySchema } from '../report/schemas/report-daily.schema';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from '../report/schemas/constants';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../hospital-description/schema/constants';
import { HospitalDescriptionSchema } from '../hospital-description/schema/hospital-description.schema';
import { PARTNER_DOMAIN_COLLECTION } from '../partner-domain/schema/constant';
import { PartnerDomainScema } from '../partner-domain/schema/partner-domain.schema';
import { FeeRefundConfigsSchema } from 'src/cash-back/schemas/fee-refund-configs.schema';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { HomePageServiceProvider } from 'src/home-page/persistence/home-page.service';
import { ServiceDescriptionSchema } from 'src/service-mongo/schemas/service-description.schema';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME, SEARCH_KEYWORD_COLLECTION_NAME, SERVICE_COLLECTION_NAME, SERVICE_DESCRIPTION_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { DoctorDescriptionSchema } from 'src/doctor-description/schema/doctor-description.schema';
import { SearchKeywordsSchema } from 'src/service-mongo/schemas/search-keywords.schema';
import { HOME_PAGE } from '../home-page/schemas/constants';
import { HomePageSchema } from '../home-page/schemas/home-page.schema';

@Global()
@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: APP_ID_COLLECTION_NAME, schema: AppIdSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: FEATURE_COLLECTION_NAME, schema: FeatureSchema },
            { name: DAILY_TRANSACTION_REPORT_COLLECTION_NAME, schema: ReportTransactionDailySchema },
            { name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, schema: HospitalDescriptionSchema },
            { name: FEE_REFUND_CONFIGS_COLLECTION_NAME, schema: FeeRefundConfigsSchema },

            {
                name: SERVICE_DESCRIPTION_COLLECTION_NAME,
                schema: ServiceDescriptionSchema,
            },
            {   name: SERVICE_COLLECTION_NAME, 
                schema: ServiceSchema 
            },
            {   
                name: GLOBAL_SETTING_COLLECTION_NAME, 
                schema: GlobalSettingSchema 
            },
            { 
                name: DOCTOR_DESCRIPTION_COLLECTION_NAME, 
                schema: DoctorDescriptionSchema 
            },
            { name: HOME_PAGE, schema: HomePageSchema },
        ]),
        MongooseModule.forFeature([
            { name: PARTNER_DOMAIN_COLLECTION, schema: PartnerDomainScema },
        ], 'backoffice'),
        CacheModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigCacheManagerService],
            useFactory: (config: ConfigCacheManagerService) => config.createCacheOptions,
        }),
        HospitalMongoModule,
        CacheManagerModule,
        FeatureModule,
        MongooseModule.forFeature(
            [
                {
                    name: SEARCH_KEYWORD_COLLECTION_NAME,
                    schema: SearchKeywordsSchema,
                },
            ],
            'services', // ? Collection SearchKeyWords nằm trong database services
        ),
    ],
    controllers: [AppIdController],
    providers: [AppIdService, HomePageServiceProvider],
    exports: [AppIdService],
})
export class AppIdModule {}
