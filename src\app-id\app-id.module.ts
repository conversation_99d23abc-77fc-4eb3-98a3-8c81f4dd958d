import { ListingSchema } from './schemas/listing.schema';
import { ConfigCacheManagerService } from './../config/config.cache-manager.service';
import { ConfigModule } from './../config/config.module';
import { CacheModule, Global, HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { AppIdController } from './app-id.controller';
import { AppIdService } from './app-id.service';
import { AppIdSchema } from './schemas/app-id.schema';
import { APP_ID_COLLECTION_NAME, LISTING_COLLECTION_NAME } from './schemas/constants';
import { CacheManagerModule } from '../cache-manager/cache-manager.module';
import { HospitalMongoModule } from '../hospital-mongo/hospital-mongo.module';
import { FeatureModule } from '../feature/feature.module';
import { ReportTransactionDailySchema } from '../report/schemas/report-daily.schema';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from '../report/schemas/constants';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../hospital-description/schema/constants';
import { HospitalDescriptionSchema } from '../hospital-description/schema/hospital-description.schema';
import { PARTNER_DOMAIN_COLLECTION } from '../partner-domain/schema/constant';
import { PartnerDomainScema } from '../partner-domain/schema/partner-domain.schema';
import { FeeRefundConfigsSchema } from 'src/cash-back/schemas/fee-refund-configs.schema';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { HomePageServiceProvider } from 'src/home-page/persistence/home-page.service';
import { ServiceDescriptionSchema } from 'src/service-mongo/schemas/service-description.schema';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME, SEARCH_KEYWORD_COLLECTION_NAME, SERVICE_COLLECTION_NAME, SERVICE_DESCRIPTION_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { DoctorDescriptionSchema } from 'src/doctor-description/schema/doctor-description.schema';
import { SearchKeywordsSchema } from 'src/service-mongo/schemas/search-keywords.schema';
import { ADS_COLLECTION_NAME, BANNER_SERVICES_COLLECTION_NAME, BANNERS_HOME_COLLECTION_NAME, BANNERS_MULTI_COLLECTION_NAME, CATEGORY_SERVICE_IN_MONTH_COLLECTION_NAME, HOME_PAGE, HOSPITALS_HOME_IDS_COLLECTION_NAME, PARTNER_HOME_IDS_COLLECTION_NAME, SUBJECT_HOME_PAGE_IDS_COLLECTION_NAME, TELEMED_DOCTOR_IN_MONTH_IDS_COLLECTION_NAME, TESTIMONIALS_COLLECTION_NAME } from '../home-page/schemas/constants';
import { AdsSchema } from 'src/home-page/schemas/ads.schema';
import { BannerServicesSchema } from 'src/home-page/schemas/banner-services.schema';
import { BannersMultiSchema } from 'src/home-page/schemas/banners-multi.schema';
import { BannersHomeSchema } from 'src/home-page/schemas/home-banners.schema';
import { HospitalsHomeIdsSchema } from 'src/home-page/schemas/hospitals-homepage-ids.schema';
import { PartnerHomeIdsSchema } from 'src/home-page/schemas/partner-homepage-ids.schema';
import { CategoryServiceInMonthSchema } from 'src/home-page/schemas/service-in-month-category.schema';
import { SubjectsHomePageIdsSchema } from 'src/home-page/schemas/subjects-homepage-ids.schema';
import { TelemedDoctorInMonthIdSchema } from 'src/home-page/schemas/telemed-doctor-in-month-ids.schema';
import { TestimonialsSchema } from 'src/home-page/schemas/testimonials.schema';

@Global()
@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: APP_ID_COLLECTION_NAME, schema: AppIdSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: FEATURE_COLLECTION_NAME, schema: FeatureSchema },
            { name: DAILY_TRANSACTION_REPORT_COLLECTION_NAME, schema: ReportTransactionDailySchema },
            { name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, schema: HospitalDescriptionSchema },
            { name: FEE_REFUND_CONFIGS_COLLECTION_NAME, schema: FeeRefundConfigsSchema },

            {
                name: SERVICE_DESCRIPTION_COLLECTION_NAME,
                schema: ServiceDescriptionSchema,
            },
            {   name: SERVICE_COLLECTION_NAME, 
                schema: ServiceSchema 
            },
            {   
                name: GLOBAL_SETTING_COLLECTION_NAME, 
                schema: GlobalSettingSchema 
            },
            { 
                name: DOCTOR_DESCRIPTION_COLLECTION_NAME, 
                schema: DoctorDescriptionSchema 
            },
             {name: ADS_COLLECTION_NAME, schema: AdsSchema },
            { name: BANNER_SERVICES_COLLECTION_NAME, schema: BannerServicesSchema },
            { name: BANNERS_MULTI_COLLECTION_NAME, schema: BannersMultiSchema },
            { name: BANNERS_HOME_COLLECTION_NAME, schema: BannersHomeSchema },
            { name: TESTIMONIALS_COLLECTION_NAME, schema: TestimonialsSchema },
            { name: CATEGORY_SERVICE_IN_MONTH_COLLECTION_NAME, schema: CategoryServiceInMonthSchema },
            { name: TELEMED_DOCTOR_IN_MONTH_IDS_COLLECTION_NAME, schema: TelemedDoctorInMonthIdSchema },
            { name: SUBJECT_HOME_PAGE_IDS_COLLECTION_NAME, schema: SubjectsHomePageIdsSchema },
            { name: HOSPITALS_HOME_IDS_COLLECTION_NAME, schema: HospitalsHomeIdsSchema },
            { name: PARTNER_HOME_IDS_COLLECTION_NAME, schema: PartnerHomeIdsSchema },
            { name: LISTING_COLLECTION_NAME, schema: ListingSchema },
        ]),
        MongooseModule.forFeature([
            { name: PARTNER_DOMAIN_COLLECTION, schema: PartnerDomainScema },
        ], 'backoffice'),
        CacheModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigCacheManagerService],
            useFactory: (config: ConfigCacheManagerService) => config.createCacheOptions,
        }),
        HospitalMongoModule,
        CacheManagerModule,
        FeatureModule,
        MongooseModule.forFeature(
            [
                {
                    name: SEARCH_KEYWORD_COLLECTION_NAME,
                    schema: SearchKeywordsSchema,
                },
            ],
            'services', // ? Collection SearchKeyWords nằm trong database services
        ),
    ],
    controllers: [AppIdController],
    providers: [AppIdService, HomePageServiceProvider],
    exports: [AppIdService],
})
export class AppIdModule {}
