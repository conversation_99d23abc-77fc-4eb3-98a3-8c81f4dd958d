import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsNumber, IsNotEmpty } from 'class-validator';

export class CskhContactCustomersDto {
  @ApiProperty({
    description: 'Number of bookings',
    required: false,
    type: Number,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  bookingCount?: number;

  @ApiProperty({
    description: 'id contact',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  id?: String;

  @ApiProperty({
    description: 'bookingId',
    required: false,
    type: String,
    default: 0,
  })
  @IsNotEmpty()
  @IsString()
  bookingCare247: String;

  @ApiProperty({
    description: 'Indicates if only one person is involved',
    required: false,
    type: Boolean,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  onePerson?: boolean;

  @ApiProperty({
    description: 'Results of the call',
    required: false,
    type: String,
    default: '',
  })
  @IsOptional()
  @IsString()
  callResults?: string;

  @ApiProperty({
    description: 'Description or notes',
    required: false,
    type: String,
    default: '',
  })
  @IsOptional()
  @IsString()
  description?: string;
}
