import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PageOptionnsDto } from '../common/base/page-options.dto';
import { SchemaNewsService } from './schema-news.service';

@ApiTags('schema-news')
@Controller('schema-news')
@ApiBearerAuth()
export class SchemaNewsController {
    constructor(private readonly service: SchemaNewsService) {}

    @Get()
    getSchemaNewss(@Query() query: PageOptionnsDto) {
        return this.service.getSchemaNewss(query);
    }

    @Get('/:id')
    getSchemaNews(@Param('id') id: string) {
        return this.service.getSchemaNews(id);
    }
}
