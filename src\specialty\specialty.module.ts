import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SpecialtyController } from './specialty.controller';
import { SpecialtyService } from './specialty.service';
import { SCHEMA_SPECIALTY_COLLECTION_NAME } from './schema/constants';
import { SpecialtySchema } from './schema/schema.specialty';
import { SEARCH_KEYWORD_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { SearchKeywordsSchema } from 'src/service-mongo/schemas/search-keywords.schema';

@Module({
    imports: [
        MongooseModule.forFeature([{ name: SCHEMA_SPECIALTY_COLLECTION_NAME, schema: SpecialtySchema }]),
        MongooseModule.forFeature(
            [
                {
                    name: SEARCH_KEYWORD_COLLECTION_NAME,
                    schema: SearchKeywordsSchema,
                },
            ],
            'services', // ? Collection SearchKeyWords nằm trong database services
        ),
    ],

    controllers: [SpecialtyController],
    providers: [SpecialtyService],
    exports: [SpecialtyService],
})
export class SpecialtyModule {}
