import * as mongoose from 'mongoose';
import { SETTING_SALARY_CARE247 } from './constants';

const Schema = mongoose.Schema;

export const SettingSalaryCare247Schema = new Schema(
    {
        salary: { type: Number },
        kpiMonth: { type: Number },
        kpiOverBonus: { type: Number },
        kpiMonthBonus: { type: Number },
        total: { type: Number },
        type: { type: String },
    },
    {
        collection: SETTING_SALARY_CARE247,
        timestamps: true,
    },
);

