import { Controller, Get, Query, Headers, Body, Post } from '@nestjs/common';
import { ApiBody, ApiOperation } from '@nestjs/swagger';
import { CreateUserMomoDto } from './dto/create-user-momo.dto';
import { UserMomoService } from './user-momo.service';
import { DecodeWalletTokenDTO, EncodeWalletIdDTO } from './dto/wallet-token.dto';

@Controller('user-momo')
export class UserMomoController {

    constructor(
        private readonly userService: UserMomoService,
    ) { }

    @Get('generate-uuid-momo')
    @ApiOperation({ summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.', description: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.' })
    async generateUUID(): Promise<any> {
        return await this.userService.generateUUID();
    }

    @Get('get-momo-token')
    @ApiOperation({ summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.', description: 'L<PERSON>y thông tin token bên M<PERSON>O gửi qua để tạo tài khoản.' })
    async getMomoToken(@Query('token') token: string): Promise<any> {
        return await this.userService.getMomoToken(token);
    }

    @Get('create-user-with-token')
    @ApiOperation({ summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.', description: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.' })
    async createUserWithToken(@Headers('momoid') token: string): Promise<any> {
        return await this.userService.createUserWithToken(token);
    }

    @Post('create-user')
    @ApiOperation({ summary: 'MOMO tạo tài khoản.', description: 'MOMO tạo tài khoản.' })
    async createUserMomo(@Body() formData: CreateUserMomoDto): Promise<any> {
        return this.userService.createUserMomo(formData);
    }
    
    @Post('encode-wallet-id')
    @ApiOperation({
        summary: 'Encode wallet ID thành token',
        description: 'API để mã hóa wallet ID thành một chuỗi token JWT có thời hạn'
    })
    @ApiBody({ type: EncodeWalletIdDTO })
    async encodeWalletId(@Body() formData: EncodeWalletIdDTO): Promise<any> {
        return await this.userService.encodeWalletId(formData.walletId);
    }

    @Post('decode-wallet-token')
    @ApiOperation({
        summary: 'Decode token để lấy wallet ID',
        description: 'API để giải mã token JWT và lấy thông tin wallet ID'
    })
    @ApiBody({ type: DecodeWalletTokenDTO })
    async decodeWalletToken(@Body() formData: DecodeWalletTokenDTO): Promise<any> {
        return await this.userService.decodeWalletToken(formData.token);
    }
}
