import { Injectable, HttpStatus, HttpException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { EVENT_SHARE_COLLECTION_NAME, EVENT_SHARE_SUBMIT_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import * as uuid from 'uuid';
import { IEventShare } from './interfaces/event-share.interface';
import { IEventShareSubmit } from './interfaces/event-share-submit.interface';
import { CreateEventShareDTO } from './dto/create-event-share.dto';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { EventShareDTO } from './dto/event-share.dto';
import { first } from 'lodash';
import { RemoveEventShareDTO } from './dto/remove-event-share.dto';
import { EventShareSubmitDTO } from './dto/event-share-submit.dto';

@Injectable()
export class EventShareService {

    constructor(
        @InjectModel(EVENT_SHARE_COLLECTION_NAME) private eventShareModel: Model<IEventShare>,
        @InjectModel(EVENT_SHARE_SUBMIT_COLLECTION_NAME) private eventShareSubmitModel: Model<IEventShareSubmit>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectSentry() private readonly clientSentry: SentryService,
    ) { }

    async removeEventShare(appId: string, partnerId: string, formData: RemoveEventShareDTO): Promise<any> {
        return this.eventShareModel.findOneAndRemove({ id: formData.id }).exec();
    }

    async createEventShare(appId: string, partnerId: string, formData: CreateEventShareDTO): Promise<any> {
        /* tìm lại thông tin partner */
        const hospital = await this.hospitalModel.findOne({ partnerId: appId }).exec();
        if (!hospital) {
            throw new HttpException('Không tìm thấy thông tin bệnh viện.', HttpStatus.NOT_FOUND);
        }

        try {
            const IEventShareParam = {
                id: uuid.v4().replace(/-/g, ''),
                type: formData.type,
                url: formData.url,
                description: formData.description,
                appId,
                partnerId,
                partner: hospital._id,
            };
            const eventShare = new this.eventShareModel(IEventShareParam);

            return eventShare.save();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }

    }

    async getEventShareInfo(appId: string, partnerId: string, formData: EventShareDTO): Promise<any> {
        /* lấy thông tin eventShare mới nhất  */
        const eventShares: IEventShare[] = await this.eventShareModel.find({ appId }).sort({ createdAt: 'desc' }).limit(1).exec();
        if (eventShares.length === 0) {
            throw new HttpException('Chưa có event', HttpStatus.NOT_FOUND);
        }
        const eventShare: IEventShare = first(eventShares);
        /* Kiểm tra xem clientId này đã có trong bảng eventShareSubmit chưa */
        const checkEventShareSubmit = await this.eventShareSubmitModel.findOne({
            clientId: formData.clientId,
            appId,
            eventShareId: eventShare.id,
        }).exec();
        if (!checkEventShareSubmit) {
            return eventShare;
        } else {
            throw new HttpException('Bạn đã kích hoạt share rồi.', HttpStatus.CONFLICT);
        }
    }

    async eventShareSubmit(appId: string, partnerId: string, formData: EventShareSubmitDTO): Promise<any> {
        /* tìm lại thông tin partner */
        const hospital = await this.hospitalModel.findOne({ partnerId: appId }).exec();
        if (!hospital) {
            throw new HttpException('Không tìm thấy thông tin bệnh viện.', HttpStatus.NOT_FOUND);
        }

        /* tìm lại thông tin event share */
        const eventShare: IEventShare = await this.eventShareModel.findOne({
            id: formData.eventShareId,
        }).exec();

        if (!eventShare) {
            throw new HttpException('Không tìm thấy thông tin thông tin chia sẻ.', HttpStatus.NOT_FOUND);
        }

        try {
            const IEventShareSubmitParam = {
                id: uuid.v4().replace(/-/g, ''),
                clientId: formData.clientId,
                clientToken: formData.clientToken,
                platform: formData.platform,

                eventShareId: eventShare.id,
                eventShare: eventShare._id,
                eventShareData: eventShare.toObject(),

                appId,
                partnerId,
                partner: hospital._id,
            };
            const eventShareSubmit = new this.eventShareSubmitModel(IEventShareSubmitParam);

            return eventShareSubmit.save();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

}
