
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class ViewBookingMedproCareCSDTO {
    @ApiProperty({
        description: '_id của booking care247',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @Transform(value => `${value}`.trim())
    _id?: string;

}
