import * as mongoose from 'mongoose';
import { COOPERATION, COOPERATION_PACKAGES, COOPERATION_PACKAGES_PRICE } from './constants';
import { CooperationPackagesSchema } from './cooperation-packages.schema';
import { DEAL_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';

const Schema = mongoose.Schema;

export const CooperationsSchema = new Schema(
    {
        fullname: { type: String, required: true, trim: true },
        email: { type: String, required: true, trim: true },
        phone: { type: String, required: true, trim: true },
        note: { type: String, required: true, trim: true },
        packageId: { type: String },
        package: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES },
        packagePriceId: { type: String },
        packagePrice: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES_PRICE },
        typeCooperation: { type: String, required: true, trim: true },
        dealId: { type: Schema.Types.ObjectId, ref: DEAL_COLLECTION_NAME },
        type: { type: Number }, //1: csyt, 2: doctor, 3: phong mach
        name: { type: String } // Tên gói (VD: "Nâng Cao", "Cao Cấp")
    },
    {
        collection: COOPERATION,
        timestamps: true, // Tự động tạo `createdAt` và `updatedAt`
    },
);