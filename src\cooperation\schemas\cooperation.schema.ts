import * as mongoose from 'mongoose';
import { COOPERATION, COOPERATION_PACKAGES } from './constants';
import { CooperationPackagesSchema } from './cooperation-packages.schema';
import { DEAL_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';

const Schema = mongoose.Schema;

export const CooperationsSchema = new Schema(
    {
        fullname: { type: String, required: true, trim: true },
        email: { type: String, required: true, trim: true, unique: true },
        phone: { type: String, required: true, trim: true },
        note: { type: String, required: true, trim: true },
        packageId:{ type: String, required: true, trim: true },
        package: { type: Schema.Types.ObjectId, ref: COOPERATION_PACKAGES },
        typeCooperation: { type: String, required: true, trim: true },
        dealId: { type: Schema.Types.ObjectId, ref: DEAL_COLLECTION_NAME },
    },
    {
        collection: COOPERATION,
        timestamps: true, // Tự động tạo `createdAt` và `updatedAt`
    },
);
