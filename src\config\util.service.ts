import { HttpException, HttpService, HttpStatus, Injectable } from '@nestjs/common';
import { BookingStatus } from 'src/his-gateway/dto/bookingStatus.dto';
import { OrderItemStatus } from 'src/vaccine/dto/order-item-status.dto';
import * as QRCode from 'qrcode';
import * as JsBarcode from 'jsbarcode';
import { DOMImplementation, XMLSerializer } from 'xmldom';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { xorBy, keyBy, sortBy } from 'lodash';
import { notifyDatabaseDisconnectTemplate } from "../common/template/lark.template";
import { ConfigLarkService } from "./config.lark.service";
import * as uuid from 'uuid';

@Injectable()
export class UtilService {
    constructor(private readonly httpService: HttpService, private readonly larkConfig: ConfigLarkService) { }

    getException = (err, response) => {
        let error = err.message;
        if (err.isAxiosError) {
            error = err.response?.data || {};
        }

        if (err instanceof HttpException) {
            error = this.getHttpResponse(err);
        }

        return new HttpException(
            {
                error,
                ...response,
            },
            error.code || HttpStatus.BAD_REQUEST,
        );
    }

    getHttpResponse = (ex: HttpException): any => {
        if (typeof ex.getResponse() === 'string') {
            return { message: ex.getResponse() as string };
        } else {
            return ex.getResponse();
        }
    }

    orderItemStatus = (status: number): object => {
        switch (status) {
            case OrderItemStatus.DA_TIEM:
                return {
                    cls: 3,
                    status: 'Đã tiêm',
                    statusDisplay: 'Đã tiêm',
                };
            case OrderItemStatus.CHUA_TIEM:
                return {
                    cls: 2,
                    status: 'Chưa tiêm',
                    statusDisplay: 'Đổi lịch tiêm',
                };
            case OrderItemStatus.CHUA_DAT_LICH:
                return {
                    cls: 1,
                    status: 'Chưa đặt lịch tiêm',
                    statusDisplay: 'Đặt lịch tiêm',
                };
            default:
                return {};
        }
    }
    getRandomInt = (min: number, max: number): number => {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }
    randomText(vLength = 16) {
        const alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }

    randomNumber(vLength = 4) {
        const alphabet = '0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }

    oldHospitalSync(): Set<{}> {
        const newSetOldHospitals = new Set();
        newSetOldHospitals.add('nhidong1');
        // newSetOldHospitals.add('dalieuhcm');
        newSetOldHospitals.add('ctchhcm');
        newSetOldHospitals.add('thuduc');
        newSetOldHospitals.add('umc');
        return newSetOldHospitals;
    }

    oldHospitalSearch(): Set<{}> {
        const newSetOldHospitals = new Set();
        newSetOldHospitals.add('ctchhcm');
        newSetOldHospitals.add('umc');
        return newSetOldHospitals;
    }

    listAppId(): Set<{}> {
        const newSetListAppId = new Set();
        newSetListAppId.add('medpro');
        newSetListAppId.add('nhidong1');
        newSetListAppId.add('umc');
        newSetListAppId.add('momo');
        newSetListAppId.add('zalopay');
        newSetListAppId.add('viettelpay');
        newSetListAppId.add('cskh');
        newSetListAppId.add('1900');
        newSetListAppId.add('umcmono');
        return newSetListAppId;
    }

    patientIdV1Key(partnerId: string): string {
        let key = '';
        switch (partnerId) {
            case 'nhidong1':
                key = 'patientIdV1';
                break;
            case 'dalieuhcm':
                key = 'patientIdV1DaLieu';
                break;
            case 'umc':
                key = 'patientIdV1UMC';
                break;
            case 'ctchhcm':
                key = 'patientIdV1CTCH';
                break;
            case 'thuduc':
                key = 'patientIdV1ThuDuc';
                break;
            default:
                break;
        }
        return key;
    }

    transformPhone(phone: string): string {
        return `${phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
    }

    errorHandler(error: any): any {
        try {
            const { response } = error;
            if (response?.data || response?.config) {
                return {
                    message: response?.message,
                    data: response.data,
                    config: {
                        url: response?.url || '',
                        method: response?.method || '',
                        data: response?.data || '',
                        headers: response?.headers || '',
                    },
                };
            }
            return JSON.parse(JSON.stringify(error));
        } catch (error) {
            return {
                message: 'Không parse được Error',
            };
        }
    }

    errorToJson(error: any): any {
        try {
            const { response } = error;

            if (error.isAxiosError) {
                return {
                    ...error.toJSON(),
                    responseData: error.response?.data,
                };
            }

            return JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error)));
        } catch (error) {
            return {
                message: 'Không parse được Error',
            };
        }
    }

    textMessageBooking(type: string): string {
        if (type === 'booking') {
            return 'Đặt khám thành công';
        } else {
            return 'Đã thanh toán';
        }
    }

    getBookingText(booking: any): string {
        const { status, noPayment = false, partnerId = '', bookingGroupType = 'booking' } = booking;
        switch (status) {
            case BookingStatus.RESERVE:
            case BookingStatus.SHARETOPAY:
                return 'Chưa thanh toán';
            case BookingStatus.CONFIRMED: {
                if (noPayment) {
                    if (partnerId && partnerId === 'pkdkanhao') {
                        return 'Đặt khám thành công';
                    } else {
                        return 'Đặt khám thành công';
                    }
                } else {
                    if (partnerId && partnerId === 'choray') {
                        return this.textMessageBooking(bookingGroupType);
                    } else {
                        if (booking?.subTotal === 0) {
                            return 'Đặt khám thành công';
                        } else {
                            return 'Đã thanh toán';
                        }
                    }
                }
            }
            case BookingStatus.DA_KHAM:
                return 'Đã khám';
            case BookingStatus.EXPIRED_RESERVATION:
                return 'Hết hạn';
            case BookingStatus.CANCEL_RESERVATION:
                return 'Đã hủy';
            default:
                return 'Chờ xác nhận';
        }
    }

    compareVersion(versionA: string, versionB: string): number {
        const arrVersionA = versionA.split('.').map(Number);
        const arrVersionB = versionB.split('.').map(Number);
        for (let i = 0; i < arrVersionA.length; ++i) {
            if (arrVersionB.length === i) {
                return 1;
            }
            if (arrVersionA[i] === arrVersionB[i]) {
                continue;
            } else if (arrVersionA[i] > arrVersionB[i]) {
                return 1;
            } else {
                return -1;
            }
        }
        if (arrVersionA.length !== arrVersionB.length) {
            return -1;
        }
        return 0;
    }

    generateQRCode(text: string) {
        try {
            return QRCode.toDataURL(text);
        } catch (error) {
            throw error;
        }
    }

    generateUUID(): string {
        const idV4 = uuid.v4();

        return idV4.replace(/-/g, '');
    }

    splitFullName(fullName: string = ''): string[] {
        const [name, ...surnameTokens] = fullName
            .trim()
            .split(' ')
            .map(t => t.trim())
            .reverse();
        return [surnameTokens.reverse().join(' '), name];
    }

    generateBarCode(text: string) {
        const xmlSerializer = new XMLSerializer();
        const document = new DOMImplementation().createDocument('http://www.w3.org/1999/xhtml', 'html', null);
        const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

        JsBarcode(svgNode, text, {
            xmlDocument: document,
        });

        const svgText = xmlSerializer.serializeToString(svgNode);
        return svgText;
    }

    sortPartnersPriorityFolowGlobalConfig(parnerGlobalConfig: string[], partners: IHospital[]): IHospital[] {
        const endOfList = partners.filter(
            partner => parnerGlobalConfig.includes(partner.partnerId),
        );
        const priorityList = xorBy(partners, endOfList, 'partnerId');
        return [...priorityList, ...endOfList];
    }

    getGroupIdAfterCheck(query: any): number {
        const { cskhtoken, cskhInfo, booking, groupId } = query;
        if (groupId === 2) return groupId;
        if (cskhtoken || cskhInfo) {
            return 3;
        } else if (!cskhtoken && booking?.cskhUserId) {
            return 4;
        }
        return 1;
    }

    tranformArrToObj(arr: any[]): any {
        let obj: any = {};
        for (const item of arr) {
            obj = {
                ...obj,
                [`${item.key}`]: item.value,
            };
        }
        return obj;
    }

    slugVietnameseName(value: string, prefix: string = '-'): string {
        const specialCharacter = /[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi;
        value = value.replace(specialCharacter, '');
        // value = value.replace(/\s+|\s+$/g, '-');
        value = value.replace(/\s+|\s+$/g, prefix);
        value = value.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
        value = value.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
        value = value.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
        value = value.replace(/o|ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
        value = value.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
        value = value.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
        value = value.replace(/đ|d/g, 'd');
        value = value.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
        value = value.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư
        return value;
    }

    getCountryByLocale(locale = 'vi') {
        switch (locale) {
            case 'km':
                return 'KH'
            default:
                return 'VN'
        }
    }

    transform = (obj, predicate) => {
        return Object.keys(obj).reduce((memo, key) => {
            if (predicate(obj[key], key)) {
                memo[key] = obj[key]
            }
            return memo
        }, {})
    }

    omit = (obj, items) => this.transform(obj, (value, key) => !items.includes(key))

    pick = (obj, items) => this.transform(obj, (value, key) => items.includes(key))

    getPlatformType(platform: string) {
        if (!platform) {
            return platform;
        }

        return ['ios', 'android', 'mobile'].includes(platform) ? 'mobile' : 'web';
    }

    isFeatureEnableInplatform(feature: any, platform: string) {
        if (!feature) {
            return false;
        }

        return this.getPlatformType(platform) === 'web' ? !feature.disabled && feature.status : !feature.disabled && feature.mobileStatus;
    }

    getPhonePrefixByLocale(locale = 'vi'): any {
        switch (locale) {
            case 'km':
                return '+855';
            default:
                return '+84';
        }
    }

    formatPhoneByCountry(phone = '', country = 'VN') {
        switch (country.toUpperCase()) {
            case 'KH':
                return phone.replace(/^[+]855|^0/, '+855');
            default:
                return phone
                    .replace(/^[+]84|^0/, '+84')
                    .replace(/^9/, '+849')
                    .replace(/^3/, '+843');
        }
    }


    sortedStringifyUrl(obj: any = {}) {
        const sortedKeys = Object.keys(obj)
            .filter(k => obj[k] != undefined)
            .sort();

        const sortedParams = sortedKeys.map(key => `${key}=${obj[key]}`);
        return sortedParams.join('&');
    }

    sendDatabaseEventToLark(connection: any, eventName: string, content: string) {
        try {
            connection.on(eventName, async (ref) => {
                const larkEnv = this.larkConfig.larkEnv == 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
                const msgContent = notifyDatabaseDisconnectTemplate(larkEnv, content, JSON.stringify(ref));
    
                this.httpService.post(this.larkConfig.larkReceiveSystemInfoUrl, {
                    msg_type: 'interactive',
                    card: msgContent,
                }).toPromise().then(success => {
                    console.log('[SUCCESS] push message to lark');
                }).catch(e => {
                    console.error('[ERROR] push message to lark: ', e);
                });
            });
    
            return connection;
        } catch (error) {
            //TO DO HANDLE ERROR
        }
    }

    getUserCountryByLocale(locale) {
        switch (locale) {
            case 'km':
                return 'KH';
            default:
                return 'VN';
        }
    }

    groupByKey(data = [], iteratee: string) {
        return data.reduce((r, item) => {
            r[item[iteratee]] = item;

            return r;
        }, {});
    }

    splitName(fullName = '') {
        if (!fullName) {
          return {
            surname: '',
            name: '',
          };
        }
    
        const tokens = fullName
          .trim()
          .split(' ')
          .map((token) => token.trim())
          .filter((t) => t);
    
        const name = tokens.splice(-1);
    
        return {
          name: name.join(' '),
          surname: tokens.join(' '),
        };
    }

    getAddressHospital(address: string, addressCensored?: string, isContractSigned?: boolean) {
        if (isContractSigned) {
          return address; 
        }
        
        return addressCensored
    }

    sortlistingPackagePaidHospital(hospitals: any[]): any[] {
        const hospitalListing = [];
        const hospitalNoListing = [];

        for (const item of hospitals) {
            if (item.listingPackagePaid === true) {
                hospitalListing.push(item);
            } else {
                hospitalNoListing.push(item);
            }
        }

        hospitalListing.sort((a, b) => a.sortOrderListing - b.sortOrderListing);

        let result = [...hospitalListing, ...hospitalNoListing];
        
        result = this.moveElementToNewIndex(result.slice(), 'partnerId', 'drcheck', 0);
        
        return result;
    }

    sortPartnerListingByBoMkt(data: any[], listing: any[], key: string): any[] {
        const sortOrderLookup = keyBy(listing, key);
        const sortedData = sortBy(data, item => {
            const sortListItem = sortOrderLookup[item[key]];
            if (sortListItem) {
                return sortListItem.order;
            } else {
                return Infinity;
            }
        });
        return sortedData;
    }

    moveElementToNewIndex(arr: any[], propertyName: any, targetValue: any, targetIndex: number) {
        const index = arr.findIndex(item => item[propertyName] === targetValue);
        if (index > -1) {
          const element = arr.splice(index, 1)[0]; // Remove and get the element
          if (targetIndex >= 0 && targetIndex <= arr.length) {
            arr.splice(targetIndex, 0, element); // Insert at the target index
          } else if (targetIndex < 0) {
            arr.unshift(element); // Insert at the beginning if targetIndex is negative
          } else {
            arr.push(element); // Insert at the end if targetIndex is out of bounds (greater than arr.length)
          }
        }
        return arr;
    }

    findKeyById(dataObject, targetId) {
        for (const key in dataObject) {
            if (Object.prototype.hasOwnProperty.call(dataObject, key)) {
                const valueString = dataObject[key];
                if (valueString.split(',').map(s => s.trim()).includes(targetId)) {
                    return key;
                }
            }
        }
        return null;
    }
}

export enum ResponseCode {
    SUCCESS = 'SUCCESS',
    ERROR = 'ERROR',
}

export class ResponseMessage {
    en?: string;
    vi?: string;
    cam?: string;
}

export class ResponseModel<T> {
    data: T;
    http_code: HttpStatus;
    service?: string;
    error?: any;
    stack?: any;
    message?: ResponseMessage;
    code?: ResponseCode;
}
