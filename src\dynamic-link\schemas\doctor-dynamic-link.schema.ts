import * as mongoose from 'mongoose';
import { DOCTOR_DYNAMIC_LINK_COLLECTION_NAME } from './constants';
import { DOCTOR_COLLECTION_NAME } from '../../doctor-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const DoctorDynamicLinkSchema = new Schema(
    {
        doctorId: String,
        slug: String,
        shortLink: String,
        previewLink: String,
    },
    {
        collection: DOCTOR_DYNAMIC_LINK_COLLECTION_NAME,
        timestamps: true,
    },
);
