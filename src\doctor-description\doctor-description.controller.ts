import { Controller, Get, Param } from '@nestjs/common';
import { DoctorDescriptionService } from './doctor-description.service';

@Controller('doctor-description')
export class DoctorDescriptionController {
    constructor(private readonly service: DoctorDescriptionService) {}

    @Get('available-doctor')
    getAvailableDoctor() {
        return this.service.getAvailableDoctor();
    }

    @Get('/:id')
    readById(@Param('id') doctorId: string) {
        return this.service.get(doctorId);
    }
}
