import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { DoctorDescriptionService } from './doctor-description.service';

@Controller('doctor-description')
export class DoctorDescriptionController {
    constructor(private readonly service: DoctorDescriptionService) {}

    @Get('available-doctor')
    getAvailableDoctor() {
        return this.service.getAvailableDoctor();
    }

    @Get('/:id')
    readById(@Param('id') doctorId: string) {
        return this.service.get(doctorId);
    }

    @Post('seed-data')
    async seedDataDoctorDescription(@Body() formData: any) {
        return this.service.seedDataDoctorDescription(formData);
    }
}
