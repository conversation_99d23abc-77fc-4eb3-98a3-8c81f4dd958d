import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { APPLY_JOB_COLLECTION_NAME, RECRUITMENT_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ApplyJobSchema = new Schema(
  {
    fullname: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    platform: { type: String, required: true }, // bạn biết qua đâu?
    coverLetter: { type: String }, // thư ứng tuyển
    cvFilePath: { type: String, required: true }, // tên hoặc đường dẫn file CV
    recruitmentId: { type: Schema.Types.ObjectId, ref: RECRUITMENT_COLLECTION_NAME },
  },
  {
    collection: APPLY_JOB_COLLECTION_NAME,
    timestamps: true,
  },
).plugin(jsonMongo);
