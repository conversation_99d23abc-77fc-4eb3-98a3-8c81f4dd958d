import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { PATIENT_INSURANCE_CCCD } from './constants';

const Schema = mongoose.Schema;

export const PatientInsuranceCccdSchema = new Schema({
    cccd: { type: String, unique: true, required: true },
    fullname: { type: String },
    surname: { type: String },
    name: { type: String },
    birthdate: { type: String },
    sex: { type: Number },
    address: { type: String },
    ward_id: { type: String },
    district_id: { type: String },
    city_id: { type: String },
    maThe: { type: String },
    hanThe: { type: String },
    expiredDate: { type: String },
}, {
    collection: PATIENT_INSURANCE_CCCD,
    timestamps: true,
}).plugin(jsonMongo);
