import { AppCskhInterceptor } from './../middleware/app-cskh.interceptor';
import { GetAllPayment } from './dto/get-all-payment.dto';
import { Controller, Post, Body, Get, UseGuards, Headers, Query, UseInterceptors, Req } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { PaymentMethodService } from './payment-method.service';
import { PaymentMethodDTO } from './dto/payment-method.dto';
import { AuthGuard } from '@nestjs/passport';
import { UrlConfigService } from 'src/config/config.url.service';
import { BookingSlotFormDTO } from 'src/booking-gateway/dto/booking-slot-form.dto';
import { GetAllPaymentMultiBookingDto } from './dto/get-all-payment-multi-booking.dto';
import { JwtService } from '@nestjs/jwt';
import { GetAllPaymentMedproCare } from './dto/get-all-payment-medpro-care.dto';

@Controller('payment-method')
@ApiTags('Payment Method - Quản lý Phương thức thanh toán')
export class PaymentMethodController {
    constructor(
        private readonly paymentMethodService: PaymentMethodService,
        private readonly urlConfigService: UrlConfigService,
        private readonly jwtService: JwtService,
    ) { }

    @Post('list-and-fee')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    // @UseGuards(AuthGuard('user-jwt'))
    async getAllPaymentMethod(@Body() paymentMethodDTO: PaymentMethodDTO): Promise<any> {
        const atms = await this.paymentMethodService.getATMs(paymentMethodDTO);
        const visaMasters = await this.paymentMethodService.getVisaMasters(paymentMethodDTO);
        const baseUrl = await this.paymentMethodService.getBaseUrl();
        const {
            vp_rate_int_card,
            vp_add_int_card,
            vp_sevice_fee,
            vp_rate_atm,
            vp_add_atm,
            vp_rate_qr,
            vp_add_qr,
            vp_rate_offline,

        } = await this.paymentMethodService.getPaymentMethodRate();
        return [
            {
                id: 2,
                rate: vp_rate_int_card,
                const_rate: vp_add_int_card,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Thẻ quốc tế Visa, Master, JCB',
                data: visaMasters,
                type: 2,
                status: 1,
            },
            {
                id: 3,
                rate: vp_rate_atm,
                const_rate: vp_add_atm,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Thẻ ATM nội địa/Internet Banking',
                data: atms,
                type: 1,
                status: 1,
            },
            {
                id: 7,
                rate: 0,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Ví MoMo',
                data: [
                    {
                        id: 10000,
                        name: 'Thanh toán bằng Ví MoMo',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/momo.png`,
                    },
                ],
                type: 6,
                status: 1,
            },
            {
                id: 8,
                rate: vp_rate_qr,
                const_rate: vp_add_qr,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng QRCode/Mobile Banking',
                data: [
                    {
                        id: 11000,
                        name: 'Thanh toán bằng QRCode',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/qrcode.png`,
                    },
                ],
                type: 8,
                status: 0,
            },
            {
                id: 5,
                rate: vp_rate_offline,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán tại đại lý',
                data: [
                    {
                        id: 2001,
                        name: 'Đến đại lý thanh toán.',
                        rate: vp_rate_offline,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/home.png`,
                        min_fee: 5500,
                        max_fee: 550000,
                    },
                ],
                type: 4,
                status: 0,
            },
            {
                id: 6,
                rate: 0,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Hỗ trợ thanh toán',
                data: [
                    {
                        id: 9999,
                        name: 'Chuyển khoản đến MedPro.',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/tt-chuyen-khoan.png`,
                    },
                ],
                type: 5,
                status: 0,
            },
        ];
    }

    // @Get('getallpayment')
    // async getAllPayment(@Headers('partnerid') partnerid: string, @Query('price') price: number, @Query('groupId') groupId: number): Promise<any> {
    //     return this.paymentMethodService.getPaymentMethodByPartnerId(partnerid, price, groupId);
    // }

    // @Get('getallpayment')
    // async getAllPayment(
    //     @Headers('appid') appId: string,
    //     @Headers('partnerid') partnerid: string,
    //     @Headers('platform') platform: string,
    //     @Headers('version') version: string,
    //     @Query('price') price: number,
    //     @Query('groupId') groupId: number,
    //     @Query('treeId') treeId?: string,
    //     @Headers('cskhtoken') cskhtoken?: string,
    // ): Promise<any> {
    //     return this.paymentMethodService.getAllPaymentMethod(partnerid, appId, platform, version, price, treeId, groupId, cskhtoken);
    // }

    @Get('getallpayment')
    @UseInterceptors(AppCskhInterceptor)
    async getAllPayment(
        @Query() query: GetAllPayment,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerid: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') token: string,
        @Headers('cskhtoken') cskhtoken?: string,
    ): Promise<any> {
        let userMongoId
        if (token?.startsWith('Bearer ')) {
            try {
                const payload = this.jwtService.verify(token.split(' ')[1]);
                userMongoId = payload.userMongoId;
            } catch (err) {

            }
        }

        const cskhInfo = await this.paymentMethodService.verifyCskhToken(cskhtoken);
        if (cskhInfo?.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }

        return this.paymentMethodService.getAllPaymentMethod(query, partnerid, appId, platform, version, userMongoId, cskhtoken);
    }

    @Post('getallpayment')
    @UseInterceptors(AppCskhInterceptor)
    async getAllPaymentBooking(
        @Body() body: GetAllPaymentMedproCare,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerid: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') token: string,
        @Headers('cskhtoken') cskhtoken?: string,
    ): Promise<any> {
        let userMongoId
        if (token?.startsWith('Bearer ')) {
            try {
                const payload = this.jwtService.verify(token.split(' ')[1]);
                userMongoId = payload.userMongoId;
            } catch (err) {

            }
        }

        const cskhInfo = await this.paymentMethodService.verifyCskhToken(cskhtoken);
        if (cskhInfo?.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }

        const { medproCareServiceIds } = body

        if (medproCareServiceIds?.length) {
            return this.paymentMethodService.getAllPaymentMethodMedproCare(body, partnerid, appId, platform, version, userMongoId, cskhtoken);
        } else {
            return this.paymentMethodService.getAllPaymentMethodNew(body, partnerid, appId, platform, version, userMongoId, cskhtoken);
        }
    }

    @Post('getallpayment-deal')
    async getAllPaymentDeal(
        @Body() body: { packageId: string },
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerid: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') token: string,
    ): Promise<any> {
        return this.paymentMethodService.getAllPaymentDeal(body, platform);
    }

    @Get('check-payment-zero')
    async checkPaymentZero(@Headers('partnerid') partnerid: string, @Query('price') price: number, @Query('groupId') groupId: number): Promise<any> {
        return this.paymentMethodService.checkPaymentZeroDong(partnerid, price, groupId);
    }

    @Post('isRequirePayment')
    @UseInterceptors(AppCskhInterceptor)
    async checkPaymentZero2(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() bookingData: BookingSlotFormDTO,
        @Headers('cskhtoken') cskhtoken?: string,
        ): Promise<any> {
        let userMongoId

        const cskhInfo = await this.paymentMethodService.verifyCskhToken(cskhtoken);
        if (cskhInfo?.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }
        return this.paymentMethodService.isRequirePayment(partnerid, bookingData, appid, userMongoId);
    }

    @Post('umc/list-and-fee')
    @ApiBearerAuth()
    // @UseGuards(AuthGuard('jwt'))
    @UseGuards(AuthGuard('user-jwt'))
    async getUMCAllPaymentMethod(@Body() paymentMethodDTO: PaymentMethodDTO): Promise<any> {
        const atms = await this.paymentMethodService.getATMs(paymentMethodDTO);
        const visaMasters = await this.paymentMethodService.getVisaMasters(paymentMethodDTO);
        const baseUrl = await this.paymentMethodService.getBaseUrl();
        const {
            vp_rate_int_card,
            vp_add_int_card,
            vp_sevice_fee,
            vp_rate_atm,
            vp_add_atm,
            vp_rate_qr,
            vp_add_qr,
            vp_rate_offline,

        } = await this.paymentMethodService.getPaymentMethodRate();
        return [
            {
                id: 2,
                rate: vp_rate_int_card,
                const_rate: vp_add_int_card,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Thẻ quốc tế Visa, Master, JCB',
                data: visaMasters,
                type: 2,
                status: 1,
            },
            {
                id: 3,
                rate: vp_rate_atm,
                const_rate: vp_add_atm,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Thẻ ATM nội địa/Internet Banking',
                data: atms,
                type: 1,
                status: 1,
            },
            {
                id: 7,
                rate: 0,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Ví MoMo',
                data: [
                    {
                        id: 10000,
                        name: 'Thanh toán bằng Ví MoMo',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/momo.png`,
                    },
                ],
                type: 6,
                status: 1,
            },
            {
                id: 8,
                rate: vp_rate_qr,
                const_rate: vp_add_qr,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng QRCode/Mobile Banking',
                data: [
                    {
                        id: 11000,
                        name: 'Thanh toán bằng QRCode',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/qrcode.png`,
                    },
                ],
                type: 8,
                status: 0,
            },
            {
                id: 5,
                rate: vp_rate_offline,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán tại đại lý',
                data: [
                    {
                        id: 2001,
                        name: 'Đến đại lý thanh toán.',
                        rate: vp_rate_offline,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/home.png`,
                        min_fee: 5500,
                        max_fee: 550000,
                    },
                ],
                type: 4,
                status: 0,
            },
            {
                id: 6,
                rate: 0,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Hỗ trợ thanh toán',
                data: [
                    {
                        id: 9999,
                        name: 'Chuyển khoản đến MedPro.',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/tt-chuyen-khoan.png`,
                    },
                ],
                type: 5,
                status: 0,
            },
        ];
    }

    @Post('trungvuong/list-and-fee')
    @ApiBearerAuth()
    // @UseGuards(AuthGuard('jwt'))
    @UseGuards(AuthGuard('user-jwt'))
    async getTrungVuongAllPaymentMethod(@Body() paymentMethodDTO: PaymentMethodDTO): Promise<any> {
        const atms = await this.paymentMethodService.getATMs(paymentMethodDTO);
        const visaMasters = await this.paymentMethodService.getVisaMasters(paymentMethodDTO);
        const baseUrl = await this.paymentMethodService.getBaseUrl();
        const {
            vp_rate_int_card,
            vp_add_int_card,
            vp_sevice_fee,
            vp_rate_atm,
            vp_add_atm,
            vp_rate_qr,
            vp_add_qr,
            vp_rate_offline,

        } = await this.paymentMethodService.getPaymentMethodRate();
        return [
            {
                id: 2,
                rate: vp_rate_int_card,
                const_rate: vp_add_int_card,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Thẻ quốc tế Visa, Master, JCB',
                data: visaMasters,
                type: 2,
                status: 1,
            },
            {
                id: 3,
                rate: vp_rate_atm,
                const_rate: vp_add_atm,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Thẻ ATM nội địa/Internet Banking',
                data: atms,
                type: 1,
                status: 1,
            },
            {
                id: 7,
                rate: 0,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng Ví MoMo',
                data: [
                    {
                        id: 10000,
                        name: 'Thanh toán bằng Ví MoMo',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/momo.png`,
                    },
                ],
                type: 6,
                status: 1,
            },
            {
                id: 8,
                rate: vp_rate_qr,
                const_rate: vp_add_qr,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán bằng QRCode/Mobile Banking',
                data: [
                    {
                        id: 11000,
                        name: 'Thanh toán bằng QRCode',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/qrcode.png`,
                    },
                ],
                type: 8,
                status: 0,
            },
            {
                id: 5,
                rate: vp_rate_offline,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Thanh toán tại đại lý',
                data: [
                    {
                        id: 2001,
                        name: 'Đến đại lý thanh toán.',
                        rate: vp_rate_offline,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/home.png`,
                        min_fee: 5500,
                        max_fee: 550000,
                    },
                ],
                type: 4,
                status: 0,
            },
            {
                id: 6,
                rate: 0,
                const_rate: 0,
                medpro_fee: vp_sevice_fee,
                name: 'Hỗ trợ thanh toán',
                data: [
                    {
                        id: 9999,
                        name: 'Chuyển khoản đến MedPro.',
                        rate: 0,
                        const_rate: 0,
                        medpro_fee: vp_sevice_fee,
                        image: `${baseUrl}/st/bank/tt-chuyen-khoan.png`,
                    },
                ],
                type: 5,
                status: 0,
            },
        ];
    }

    @Get('list')
    @UseGuards(AuthGuard('jwt'))
    async getList(): Promise<any> {
        return await this.paymentMethodService.getList();
    }

    @Post('getallpayment-multi-booking')
    @UseInterceptors(AppCskhInterceptor)
    async getAllPaymentMethodMultiBooking(
        @Body() formData: GetAllPaymentMultiBookingDto,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerid: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') token: string,
        @Headers('cskhtoken') cskhtoken?: string,
    ): Promise<any> {
        let userMongoId
        if (token?.startsWith('Bearer ')) {
            try {
                const payload = this.jwtService.verify(token.split(' ')[1]);
                userMongoId = payload.userMongoId;
            } catch (err) {

            }
        }

        const cskhInfo = await this.paymentMethodService.verifyCskhToken(cskhtoken);
        if (cskhInfo?.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }
        return this.paymentMethodService.getAllPaymentMethodMultiBooking(formData, partnerid, appId, platform, version, userMongoId, cskhtoken);
    }
}
