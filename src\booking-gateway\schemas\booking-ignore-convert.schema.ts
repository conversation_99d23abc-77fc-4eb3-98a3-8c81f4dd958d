import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, BOOKING_IGNORE_CONVERT, CONVERT_USER_CSKH } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const BookingIgnoreConvertSchema = new Schema({
    convertUserCskh: { type: Schema.Types.ObjectId, ref: CONVERT_USER_CSKH },
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String, unique: true, required: true },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    bookingId: { type: String },
    bookingCode: { type: String },
    transactionId: { type: String },
    date: { type: Date },
}, {
    collection: BOOKING_IGNORE_CONVERT,
    timestamps: true,
}).plugin(jsonMongo);
