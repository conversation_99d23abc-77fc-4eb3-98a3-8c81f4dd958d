import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { last, size } from 'lodash';
import { JwtUserConfigService } from '../../config/config.user.jwt.service';
import { GlobalSettingService } from '../../global-setting/global-setting.service';
import { UtilService } from '../../config/util.service';

@Injectable()
export class MedproCSKHGuard implements CanActivate {
    constructor(
        private jwtUserConfigService: JwtUserConfigService, 
        private readonly globalSettingService: GlobalSettingService,
        private readonly utilService: UtilService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();

        const { authorization } = request.headers;

        if (authorization && size(authorization.split(' ')) > 1) {
            try {
                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const [partnerCskhUsers, cskhUsersAnKhang] = await Promise.all([
                        this.globalSettingService.findByKeyAndRepoName('PARTNER_CSKH_USERS'),
                        this.globalSettingService.findByKeyAndRepoName('AN_KHANG_CSKH_USER')
                    ])
                    const partnerCskhUsersObj = partnerCskhUsers ? JSON.parse(partnerCskhUsers) : {};
                    const partnerId = this.utilService.findKeyById(partnerCskhUsersObj, userMongoId);
                    let cskhUsersAnKhangList = cskhUsersAnKhang ? cskhUsersAnKhang.split(',') : [];
                    if (partnerId || cskhUsersAnKhangList.includes(userMongoId)) {
                        throw new HttpException('Bạn chưa được phân quyền sử dụng chức năng này. Vui lòng liên hệ bộ phận CSKH Medpro để được hỗ trợ.', HttpStatus.FORBIDDEN);
                    }
                }
                return true;
            } catch (error) {
                throw new HttpException('Bạn chưa được phân quyền sử dụng chức năng này. Vui lòng liên hệ bộ phận CSKH Medpro để được hỗ trợ.', HttpStatus.FORBIDDEN);
            }
        }

        return true;
    }
}
