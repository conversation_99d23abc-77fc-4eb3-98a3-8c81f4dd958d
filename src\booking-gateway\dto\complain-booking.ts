import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose } from 'class-transformer';
import { IsNotEmpty, IsDateString, ValidateIf } from 'class-validator';
import * as uuid from 'uuid';

export class ComplainBookingDTO {

    @ApiProperty({
        description: 'Transaction',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    transactionId: string;

    @ApiProperty({
        description: 'id',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung id booking',
    })
    id: string;

    @ApiProperty({
        description: 'Nội dung khiếu nại',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.message)
    complain: string;

}
