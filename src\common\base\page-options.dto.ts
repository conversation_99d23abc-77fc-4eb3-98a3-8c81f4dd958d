import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Min } from 'class-validator';

export class PageOptionnsDto {
    @ApiProperty({ description: 'Số trang', required: false, default: 0 })
    @Transform(value => Number(value))
    page: number = 0;

    @ApiProperty({ description: 'Số lượng', required: false, default: 10 })
    @Transform(value => Number(value))
    limit: number = 100;

    @ApiProperty({ description: 'Tìm kiếm', required: false , default: 'key1:<value1>,<value2>&key2:<value1>,<value2>' })
    q?: string;

    get skip(): number {
        return this.page * this.limit;
    }
}
