import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME, VISA_USER_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const VisaUserSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String, unique: true, required: true },
    email: { type: String },
    phone: { type: String },
    fullname: { type: String },
}, {
    collection: VISA_USER_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
