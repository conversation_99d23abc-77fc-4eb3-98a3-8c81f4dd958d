import { Transform } from 'class-transformer';
import {
  IsDateString,
  IsMobilePhone,
  IsMongoId,
  IsNotEmpty,
  MaxLength,
} from 'class-validator';

export class PatientBasicInfoDto {
  @IsNotEmpty({
    message: '<PERSON><PERSON> lòng bổ sung thông tin',
  })
  @MaxLength(50, {
    message: 'Vui lòng không vượt quá 50 ký tự.',
  })
  @Transform((value) => `${value}`.trim().toUpperCase())
  readonly name: string;

  @IsDateString(
    { strict: true },
    {
      message: 'Ng<PERSON>y sinh theo ISOString',
    },
  )
  readonly birthdate: string;

  @IsNotEmpty()
  sex: number;

  @IsNotEmpty()
  @IsMobilePhone('vi-VN')
  mobile: string;

  mobileLocaleIso?: string;

  @IsNotEmpty()
  city_id: string;

  bookingData?: any
}
