import { RecruitmentGuard } from './../common/guards/recruitment.guard';
import { Controller, Post, Get, Body, HttpException, HttpStatus, Delete, Param, Patch, Query, UseGuards } from '@nestjs/common';
import { RecruitmentService } from './recruitment.service';
import { RecruitmentDto } from './dto/recruitment.dto';
import { query } from 'express';
import { QueryRecruitmentDto } from './dto/query-recruitment.dto';
import { ApplyJobDto } from './dto/apply-job.dto';

@Controller('recruitment')
export class RecruitmentController {
    constructor(private readonly recruitmentService: RecruitmentService) {}

    @UseGuards(RecruitmentGuard)
    @Post()
    async createJob(@Body() body: RecruitmentDto): Promise<any> {
        return await this.recruitmentService.createRecruitment(body);
    }

    @UseGuards(RecruitmentGuard)
    @Patch(':id')
    async updateJob(@Param('id') id: string, @Body() body: RecruitmentDto): Promise<any> {
        return await this.recruitmentService.updateRecruitment(id, body);
    }

    @UseGuards(RecruitmentGuard)
    @Delete(':id')
    async deleteJob(@Param('id') id: string): Promise<any> {
        return await this.recruitmentService.deleteRecruitment(id);
    }

    @Get('list')
    async getAllJobs(@Query() query: QueryRecruitmentDto): Promise<any> {
        return await this.recruitmentService.getAllRecruitments(query);
    }

    @Get('detail/:id')
    async getJobById(@Param('id') id: string): Promise<any> {
        return await this.recruitmentService.getJobById(id);
    }

    @Get('list-nature')
    async getlistNature(): Promise<any> {
        return await this.recruitmentService.getlistNature();
    }

    @Get('list-working-form')
    async getlistWorkingForm(): Promise<any> {
        return await this.recruitmentService.getWorkingForm();
    }

    @Get('list-platform')
    async getlistPlatform(): Promise<any> {
        return await this.recruitmentService.getlistPlatform();
    }

    @Post('apply')
    async applyJobRecruitment(@Body()body:ApplyJobDto): Promise<any> {
        return await this.recruitmentService.saveApplication(body);
    }
}
