import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsOptional, IsArray, ArrayMaxSize, IsEmail } from 'class-validator';

export class ReserveDealDTO {

    @ApiProperty({
        description: 'fullname',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin fullname',
    })
    @ValidateIf(o => o.platform)
    fullname: string;

    @ApiProperty({
        description: 'fullname',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin fullname',
    })
    @ValidateIf(o => o.platform)
    phone: string;

    @ApiProperty({
        description: 'partnerName',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin partnerName',
    })
    @ValidateIf(o => o.platform)
    partnerName: string;
    
    @ApiProperty({
        description: 'Email',
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Email không được để trống' })
    @IsEmail()
    readonly email: string;

    @ApiProperty({
        description: 'Giấy phép kinh doanh',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin businessImage',
    })
    businessImage: string;

    @ApiProperty({
        description: 'Giấy phép y tế',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin medicalLicenseImage',
    })
    medicalLicenseImage: string;

    invoiceInformation?: any;

    @ApiProperty({
        description: 'Id gói hợp tác',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin gói hợp tác',
    })
    packagePriceId: string;

    typeCooperation?: string;

    platform?: string;

    @ApiProperty({
        description: 'Phương thức thanh toán',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    methodId: string;

    @ApiProperty({
        description: 'Thông tin payment type detail',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    paymentTypeDetail: string;

    @ApiProperty({
        description: 'Redirect URL',
        required: true,
        type: String,
    })
    redirectUrl: string;

    note?: string;
}