import { IsNumber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class ServiceLogRequest {
    @IsOptional()
    @ApiProperty()
    @IsNumber()
    @Type(() => Number)
    pageSize: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    @Type(() => Number)
    pageIndex: number;

    @ApiProperty()
    nameParent?: string;

    @ApiProperty()
    nameRepo?: string;

    @ApiProperty()
    userId?: string;
}
