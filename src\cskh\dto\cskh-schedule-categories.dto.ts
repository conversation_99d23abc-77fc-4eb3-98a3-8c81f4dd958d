import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CskhScheduleCategoriesDto {
    @ApiProperty({
        description: 'Tên loại lịch',
        example: 'BT',
    })
    @IsString()
    @IsNotEmpty()
    name: String;

    @ApiProperty({
        description: 'Tiêu đề',
        example: 'Lịch nghỉ lễ Tết',
    })
    @IsString()
    @IsNotEmpty()
    title: string;

    @ApiProperty({
        description: '<PERSON><PERSON>y bắt đầu',
        example: '12:30',
    })
    @IsNotEmpty()
    fromTime: string;

    @ApiProperty({
        description: 'Ng<PERSON>y kết thúc',
        example: '14:15',
    })
    @IsNotEmpty()
    toTime: string;

    @ApiProperty({
        description: '<PERSON><PERSON><PERSON> làm việc (ON = làm việc, OFF = nghỉ)',
        example: true,
    })
    @IsBoolean()
    @IsNotEmpty()
    workDay: boolean;
}
