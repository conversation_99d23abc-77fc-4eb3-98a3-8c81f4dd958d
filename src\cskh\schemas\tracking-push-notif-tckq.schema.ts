import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { EVENT_COLLECTION_NAME, TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA } from './constants';
import { TransporterEvent } from 'src/message-event/constant';
import { BOOKING_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const TrackingPushNotifTCKQSchema = new Schema({
    eventId: { type: Schema.Types.ObjectId, ref: EVENT_COLLECTION_NAME },
    processId: { type: String },
    syncStatus: { type: String, default: 'success' },
    appId: { type: String },
    topic: { type: String },
    topicBooking: { type: String },
    partnerId: { type: String },
    partnerName: { type: String },
    type: { type: Number, default: 0 },
    title: { type: String },
    transactionId: { type: String },
    userId: {type: String},
    notifAppId: {type: String},
    notifApiKey: {type: String},
    clientIds: { type: [String]},
    eventData: { type: Schema.Types.Mixed },
    transporter:  { type: String, default: TransporterEvent.PUSH },
    mailTemplate: { type: String },
    repoName:  { type: String },
    clientViewId: { type: String },
    viewResponse: { type: Schema.Types.Mixed },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    status: { type: String },
    sent: { type: Number },
    viewNotif: { type: Number },
    ctr: { type: String },
    oneSignalNotifInfo: { type: Schema.Types.Mixed },
    examResults: { type: Schema.Types.Mixed }
}, {
    collection: TRACKING_PUSH_NOTIF_TRA_CUU_KET_QUA,
    timestamps: true,
}).plugin(jsonMongo);
