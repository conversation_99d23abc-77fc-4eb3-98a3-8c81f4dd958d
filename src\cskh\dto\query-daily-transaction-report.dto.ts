import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class IDailyTransaction {
    @ApiProperty({ description: '', required: false })
    @Transform(value => `${value}`.trim())
    from?: string;

    @ApiProperty({ description: '', required: false })
    @Transform(value => `${value}`.trim())
    to?: string;

    @ApiProperty({ description: 'page', required: false })
    page: number;

    @ApiProperty({ description: 'limit', required: false })
    limit: number;
}
