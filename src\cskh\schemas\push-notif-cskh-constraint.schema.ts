import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PUSH_NOTIF_CSKH_CONSTRAINT } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const PushNotifCskhConstraintSchema = new Schema(
  {
    onesignalId: { type: String, required: true },
    userId: { type: String, required: true },
  },
  {
    collection: PUSH_NOTIF_CSKH_CONSTRAINT,
    timestamps: true,
  }
);
PushNotifCskhConstraintSchema.index({ onesignalId: 1, userId: 1 }, { unique: true });

PushNotifCskhConstraintSchema.plugin(jsonMongo);
