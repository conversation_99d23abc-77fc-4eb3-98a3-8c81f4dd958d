import { Controller, Get, Headers } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { BannerService } from './banner.service';

@Controller('banner')
@ApiTags('banner')
// @Resource(ResourceName.SyncBooking, { allowGet: false })
export class BannerController {
    constructor(private service: BannerService) {}
    @Get()
    async getBanners(@Headers('appid') appid: string) {
        return await this.service.getBanner(appid);
    }
}
