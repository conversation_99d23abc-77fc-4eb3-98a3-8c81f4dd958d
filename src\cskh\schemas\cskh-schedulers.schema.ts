import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { CSKH_SCHEDULE_CATEGORIES, CSKH_SCHEDULERS } from './constants';

const Schema = mongoose.Schema;

export const CskhSchedulersSchema = new Schema(
    {
        partnerId: { type: String, required: true },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        cskhUserId: { type: String, required: true },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        scheduleId: { type: String, required: true },
        schedule: { type: Schema.Types.ObjectId, ref: CSKH_SCHEDULE_CATEGORIES },
        fromDate: { type: Date, required: true },
        toDate: { type: Date, required: true },
        title: { type: String, required: false },
        locked: { type: Boolean, default: false },
    },
    {
        collection: CSKH_SCHEDULERS,
        timestamps: true,
    },
).plugin(jsonMongo);
