export interface HomePageBlockConfig {
    enableCache: boolean;
    cacheKey: string;
    ads: {
        isAds: boolean,
        adsContent: string,
        adsStyle: Record<string,string>
    },
    partner: {
        hide: boolean;
        ids: string[];
    };
    hospitals: {
        hide: boolean;
        ids: string[];
        ctas: Record<
            string, 
            {
                name: string,
                partnerId: string,
                treeId: string,
                subjectId: string | null,
                serviceId: string | null,
                doctorId: string | null,
                roomId: string | null
            }
        >;
    };
    telemed_doctor_in_month: {
        hide: boolean;
        ids: string[];
    };
    service_in_month: {
        hide: boolean;
        ids: {
            "suc-khoe": string[]
            "xet-nghiem": string[]
            "tiem-chung": string[]
        };
    };
    testimonials: {
        hide: boolean;
        data: {
            name: string;
            content: string;
        }[];
    };
    subjects: {
        hide: boolean;
        ids: string[];
    };
    banners: {
        hide: boolean;
        data: {
            desktop: {
                imageUrl: string;
                cta: object;
            }[];
            mobile: {
                imageUrl: string;
                cta: object;
            }[];
        };
    }
    bannersMulti: {
        hide: boolean;
        data: {
            desktop: {
                imageUrl: string;
                cta: object;
            }[];
            mobile: {
                imageUrl: string;
                cta: object;
            }[];
        };
    }
    bannerServices: {
        hide: boolean;
        data: {
            desktop: {
                imageUrl: string;
                cta: object;
            }[];
            mobile: {
                imageUrl: string;
                cta: object;
            }[];
        };
    }
}