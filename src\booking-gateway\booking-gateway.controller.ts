import { AppCskhInterceptor } from './../middleware/app-cskh.interceptor';
import {
    Controller,
    Post,
    Param,
    Query,
    HttpCode,
    HttpStatus,
    Get,
    HttpException,
    Headers,
    Req,
    UseGuards,
    Body,
    Request,
    Delete,
    Patch,
    UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiHeader, ApiBody } from '@nestjs/swagger';
import { BookingGatewayService } from './booking-gateway.service';
import { BookingSlotFormDTO } from './dto/booking-slot-form.dto';
import { OutBookingDTO } from './dto/out-booking.dto';
import { OutBadRequestBookingDTO } from './dto/out-bad-request-booking.dto';
import { ConfirmBookingDTO } from './dto/confirm-booking.dto';
import { AuthGuard } from '@nestjs/passport';
import { UpdateStatusDTO } from './dto/update-status.dto';
import * as moment from 'moment';
import { PaymentFeeFormDTO } from './dto/payment-fee-form.dto';
import { ListenBookingChangedDTO } from './dto/listen-booking-changed.dto';
import { ListenBookingSTTChangedDTO } from './dto/listen-booking-stt-changed.dto';
import { UpdateBookingTelemedStatusDTO } from './dto/update-booking-telemed-status.dto';
import { ListenReExamChangedDTO } from './dto/listen-reexam-changed.dto';
import { CreateDepositGatewayDTO } from './dto/create-deposit-gateway.dto';
import { SearchBookingCSDTO } from './dto/search-booking.dto';
import { ListenSyncPatientCodeFromHisDTO } from './dto/listen-sync-patient-code-from-his.dto';
import { ViewBookingCSDTO } from './dto/view-booking.dto';
import { ListenBookingDateChangedDTO } from './dto/listen-booking-date-changed.dto';
import { GetInfoByPhoneDTO } from './dto/get-info-by-phone.dto';
import { PortalCancelBookingDTO } from './dto/portal-cancel-booking.dto';
import { CrExamResultsDTO } from './dto/cr-exam-results.dto';
import { HealthHistoriesByPatientIdDTO } from './dto/health-histories-by-patient-id.dto';
import { ChangeBookingDTO } from './dto/change-booking.dto';
import { GetInfoByPhoneThirdPartnerDTO } from './dto/get-info-by-phone-third-partner.dto';
import { HealthHistoriesByExamIdDTO } from './dto/health-histories-by-exam-id.dto';
import { SendBookingMailSMSDTO } from './dto/send-booking-mail.dto';
import { SendBookingSMSDTO } from './dto/send-booking-sms.dto';
import { BookingShareToPayDTO } from './dto/booking-share-to-pay.dto';
import { ReserveByTranstionDTO } from './dto/reserve-by-transation.dto';
import { HealthHistoriesByExamGroupIdDTO } from './dto/health-histories-by-exam-group-id.dto copy';
import { BookingMultipleDTO } from './dto/multiple-bookings.dto';
import { ViewTransactionCSDTO } from './dto/view-transaction.dto';
import { CheckTransactionDTO } from './dto/check-transaction.dto';
import { IBooking } from './intefaces/booking.inteface';
import { ChangeBookingStatusDto } from './dto/change-booking-status.dto';
import { GetBookingResultDto } from './dto/get-booking-result.dto';
import { CskhOfflineRegisterDto } from './dto/cskh-offline-register.dto';
import { BookingTrackingPageOptionsDto } from './dto/booking-tracking-page-options.dto';
import { PermissionEnum } from 'src/user-permission/common/permission.enum';
import { PermissionGuard } from './../user-permission/guard/permission.guard';
import { Permission } from './../user-permission/decorator/permission.decorator';
import { RetrySyncBookingDTO } from './dto/retry-sync-booking.dto';
import { JwtService } from '@nestjs/jwt';
import { ViewBookingCSByTokenDTO } from './dto/view-booking-by-token.dto';
import { RetrySyncBookingDTOV2 } from './dto/retry-sync-booking-v2.dto';
import { AuthService } from "../auth/auth.service";
import { InjectModel } from "@nestjs/mongoose";
import { USER_DOCTOR_COLLECTION_NAME } from "../user/schemas/constants";
import { Model } from "mongoose";
import { IUserDoctor } from "../user/interfaces/user-doctor.inteface";
import { CskhGuard } from "../common/guards/cskh.guard";
import { GlobalSettingService } from "../global-setting/global-setting.service";
import { MedproCareInstructorDto } from './dto/medpro-care-instructor.dto';
import { MedproCareTrackingDTO } from './dto/medpro-care-tracking.dto';
import { ViettelPayInterceptor } from 'src/middleware/viettelpay.interceptor';
import { ComplainBookingDTO } from './dto/complain-booking';
import { MedproCareSlotFormDTO } from './dto/medpro-care-slot-form.dto';
import { MedproCareAddonFormDTO } from './dto/medpro-care-addon-form.dto';
import { ViewBookingMedproCareCSDTO } from './dto/view-booking-medpro-care.dto';
import { FireBaseByTransactionIdDTO } from './dto/fire-base-by-transactionId.dto';
import { AppCskhActivitiesInterceptor } from '../middleware/app-cskh-activities.interceptor';
import { ActionData } from '../cskh/decorator/cskh-action.decorator';
import { ActionEnum, CategoryEnum } from '../cskh/decorator/action-data.enum';
import { Care247Guard } from '../common/guards/care247.guard';
import { BookingssQueryDto } from './dto/bookings-query-dto';
import { ReserveDealDTO } from './dto/reserve-deal.dto';
import { PartnerCSKHUsersInterceptor } from '../middleware/partner-cskh-users.interceptor';
import { AnKhangCSKHUsersInterceptor } from '../middleware/ankhang-cskh-users.interceptor';
import { PartnerCSKHGuard } from '../common/guards/partner-cskh.guard';
import { AnKhangCSKHGuard } from '../common/guards/ankhang-cskh.guard';
import { MedproCSKHGuard } from '../common/guards/medpro-cskh.guard';

@Controller('booking-gateway')
@ApiTags('Booking Gateway- Quản lý Booking trên MongoDB')
export class BookingGatewayController {

    constructor(
        private readonly service: BookingGatewayService,
        private readonly authService: AuthService,
        private jwtService: JwtService,
        @InjectModel(USER_DOCTOR_COLLECTION_NAME) private userDoctorModel: Model<IUserDoctor>,
        private readonly globalSettingService: GlobalSettingService,
    ) { }

    @Post('one-booking')
    async syncOneBooking(
        @Query('id') id: number,
    ): Promise<any> {
        return this.service.syncOneBooking(id);
    }

    @Post('one-patient')
    async syncOnePatient(
        @Query('id') id: number,
    ): Promise<any> {
        return this.service.syncOnePatient(id);
    }

    @Post('test-check-booking-rules')
    async testCheckBookingRules(): Promise<any> {
        return this.service.checkBookingRules('dkdongnai', 'dkdongnai', {
            patientId: 'patientId001',
            hasInsuranceCode: true,
            insuranceChoice: 'DUNG_TUYEN',
            insuranceCode: 'TT0701',
            startTime: '2021-02-02T00:00:00.000Z',
            bookingUUID: 'vp0000111111',
        });
    }

    @Post('all-invisible-bookings')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allInvisibleBookingByNoGroupUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allInvisibleBookingByNoGroupUser(userMongoId, partnerid, appid);
    }

    @Get('all-invisible-bookings-by-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allInvisibleBookingByUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allInvisibleBookingByUser(userMongoId, partnerid, appid);
    }

    @Get('invisible')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async invisibleBooking(
        @Query('id') id: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.invisibleBooking(id, userMongoId);
    }

    @Post('hot-fix-data-numbers')
    async hotfixDataNumbers(): Promise<any> {
        return this.service.hotfixDataNumbers();
    }

    @Get('check-insurance-reserve')
    async checkInsuranceReserve(): Promise<any> {
        return this.service.checkInsuranceReserve('leloi', {
            patientId: '2e300d251b4546e8b13d26c80758e0dc',
            insuranceCode: 'CH4775104005748',
            endTime: '2020-12-22T00:30:00.000Z',
        });
    }

    @Get('check-rule')
    async checkRule(): Promise<any> {
        return this.service.checkRule();
    }

    @Get('check-retry')
    async checkRetry(): Promise<any> {
        return this.service.checkRetry();
    }

    @Get('test-update-status-v1')
    async testUpdateStatusV1(
        @Query('transactionId') transactionId: string,
        @Query('partnerid') partnerId: string,
    ): Promise<any> {
        return this.service.testUpdateStatusV1(partnerId, transactionId);
    }

    @Get('test-pop')
    async testPopulate(): Promise<any> {
        return this.service.testPopulate();
    }

    @Get('test-transform-room-section')
    async testTransformRoomAndSection(): Promise<any> {
        return this.service.transformRoomSectionV1NhiDong1('nhidong1', {
            id: 302,
            bv_id: 344,
            name: 'B15 - Khám Ngoại 2',
            price: 80000,
            nd1_service_id: 6,
            nd1_section_id: 3,
            nd1_subject_id: 16,
            description: 'Khu khám',
            bhyt: null,
            is_old: 0,
        },
            { id: 3, name: 'Khu AB' },
        );
    }

    @Get('test-mapping-payment')
    async testPaymentMapping(): Promise<any> {
        return this.service.findPaymentMethod('nhidong1', 'visa', 'VISA');
    }

    @Get('test-insert-multi')
    async testInsertMulti(): Promise<any> {
        return this.service.testInsertMulti();
    }

    @Get('test-qua-ngay-kham')
    async testQuaNgayKham(): Promise<any> {
        return this.service.testQuaNgayKham();
    }

    @Get('test-transaction-event')
    async testTransactionEvent(): Promise<any> {
        return this.service.testTransactionEvent();
    }

    @Get('test-update-fee-v2')
    async testUpdateFeeV2(): Promise<any> {
        return this.service.testUpdateFeeV2();
    }

    @Get('test-transaction')
    async testTransaction(): Promise<any> {
        return this.service.transaction('VPDev2007175E4OX4WBHSGP');
    }

    @Get('test-booking-patient')
    async testBookingPatient(): Promise<any> {
        return this.service.testBookingPatient();
    }

    @Get('test-moment')
    async testMoment(): Promise<any> {
        const time = '2020-07-21T18:29:00.000Z';
        return {
            current: moment().format('DD-MM-YYYY HH:mm:ss'),
            value: time,
            isUTC: moment(time).isUTC() ? 'UTC' : 'NON-UTC',
            momentFormat: moment(time).format('DD-MM-YYYY HH:mm:ss'),
            momentFormatLocal: moment(time).local().format('DD-MM-YYYY HH:mm:ss'),
            momentFormatUTC: moment(time).utc().format('DD-MM-YYYY HH:mm:ss'),
            // ket_qua:
        };
    }

    @Post('visible-all-bookings')
    async visibleAllBookings(): Promise<any> {
        return this.service.visibleAllBookings();
    }

    @Get('update-status-payment-fee-manual')
    async updateStatusPaymentFeeManual(@Query('code') code: string): Promise<any> {
        return this.service.updateStatusPaymentFeeManual(code);
    }

    @Get('re-payment')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async rePayment(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Query('id') id: string,
        @Query('redirectUrl') redirectUrl: string,
        @Query('methodId') methodId: string,
        @Query('paymentTypeDetail') paymentTypeDetail: string,
        @Query('groupId') groupId?: number,
        @Query('customerIpAddress') customerIpAddress?: string,
        @Query('browserScreenHeight') browserScreenHeight?: string,
        @Query('browserScreenWidth') browserScreenWidth?: string,
    ): Promise<any> {
        const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }

        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        let getGroupId = 1;
        if (typeof groupId !== typeof undefined && (groupId === 3 || groupId === 4)) {
            getGroupId = groupId;
        }
        const userId = objUser.userMongoId;
        return this.service.rePayment(partnerid, appId, userId, id, redirectUrl, methodId, paymentTypeDetail, getGroupId, cskhInfo,
            customerIpAddress, browserScreenHeight, browserScreenWidth);
    }

    @Get('re-payment-share-to-pay')
    @UseInterceptors(AppCskhInterceptor)
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async rePaymentCSKH(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        // @Headers('cskhtoken') cskhToken: string,
        // @Req() req,
        @Query('id') id: string,
        @Query('redirectUrl') redirectUrl: string,
        @Query('methodId') methodId: string,
        @Query('paymentTypeDetail') paymentTypeDetail: string,
        @Headers('platform') platform?: string,
        // @Query('groupId') groupId?: number,
    ): Promise<any> {
        // const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }

        // const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        // const objUser: any = {
        //     // ...user,
        // };

        // if (cskhInfo && cskhInfo.userIdPatient) {
        //     objUser.userMongoId = cskhInfo.userIdPatient;
        // }
        const getGroupId = 1;
        return this.service.rePaymentShareToPay(partnerid, appId, id, redirectUrl, methodId, paymentTypeDetail, getGroupId, platform);
    }

    @Post('re-payment-share-to-pay')
    @UseInterceptors(AppCskhInterceptor)
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async rePaymentCSKHByInfo(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        // @Headers('cskhtoken') cskhToken: string,
        // @Req() req,
        @Body() formData: any,
        // @Query('id') id: string,
        // @Query('redirectUrl') redirectUrl: string,
        // @Query('methodId') methodId: string,
        // @Query('paymentTypeDetail') paymentTypeDetail: string,
        @Headers('platform') platform?: string,
        // @Query('groupId') groupId?: number,
    ): Promise<any> {
        // const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }

        // const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        // const objUser: any = {
        //     // ...user,
        // };

        // if (cskhInfo && cskhInfo.userIdPatient) {
        //     objUser.userMongoId = cskhInfo.userIdPatient;
        // }
        const getGroupId = 1;
        return this.service.rePaymentShareToPayByInfo(partnerid, appId, formData, getGroupId, platform);
    }

    @Post('change-booking')
    @UseInterceptors(AppCskhInterceptor)
    async changeBooking(
        @Body() formData: ChangeBookingDTO,
    ): Promise<any> {
        return this.service.changeBooking(formData);
    }

    @Get('test-thoi-gian')
    async test(): Promise<any> {
        const currentDate = moment('2020-07-05').subtract(1, 'day'); // '2020-05-14
        const mark = currentDate.format('YYYY-MM-DD');
        const noBookingAfterTime = '16:00:00';
        const timeAfter = moment(mark, 'YYYY-MM-DD').format('YYYY-MM-DD');
        if (moment('2020-07-05 15:30:00', 'YYYY-MM-DD HH:mm:ss').isAfter(timeAfter + ' ' + noBookingAfterTime)) {
            throw new HttpException(
                'Bạn chỉ có thể HỦY phiếu khám bệnh trước thời điểm 16h00 của ngày liền trước ngày khám.',
                HttpStatus.BAD_REQUEST);
        }
        return true;
    }

    @Post('create-order-load-test')
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    async createOrderLoadTest(): Promise<any> {
        return this.service.createOrderLoadTest();
    }

    @Post('reserve')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveBooking(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('refcode') refcode: string,
        @Req() req,
        @Body() bookingData: BookingSlotFormDTO): Promise<any> {
        const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveBooking(req, partnerid, appId, userId, bookingData, null, cskhInfo);
    }
    
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @Post('add-firebase-by-transaction')
    async addPayment(
        @Body() body: FireBaseByTransactionIdDTO
    ) {
        return this.service.addPayment(body);
    }

    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @Post('update-firebase-by-transaction')
    async updatePaymentFirebaseByTransaction(
        @Body() body: FireBaseByTransactionIdDTO
    ) {
        return this.service.updateTransactionInFirebase(body);
    }

    @Post('reserve-by-transation')
    @UseInterceptors(AppCskhInterceptor)
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking  Noauth',
        description: 'Reserve Booking Noauth',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    async reserveByTransation(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Body() bookingData: any): Promise<any> {
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        return this.service.reserveFromTransaction(bookingData);
    }

    @Post('reserve-multiple')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMultipleBooking(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() bookingData: BookingMultipleDTO): Promise<any> {
        const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveMultipleBooking(partnerid, appId, userId, bookingData, null, cskhInfo);
    }

    @Post('payment-fee')
    @ApiOperation({
        description: 'Dùng cho thanh toán viện phí của bệnh viện chợ rẫy',
        summary: 'Dùng cho thanh toán viện phí của bệnh viện chợ rẫy',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async paymentFee(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string, @Req() req, @Body() paymentFeeForm: PaymentFeeFormDTO): Promise<any> {
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const userId = req.user.userMongoId;
        return this.service.paymentFee(partnerid, appId, userId, paymentFeeForm);
    }

    @Post('create-deposit')
    @ApiOperation({
        description: 'Dùng cho thanh toán tiền qua cổng. sau đó nạp tiền vào trong thẻ',
        summary: 'Dùng cho thanh toán tiền qua cổng. sau đó nạp tiền vào trong thẻ',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async createDeposit(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string, @Req() req,
        @Body() formData: CreateDepositGatewayDTO): Promise<any> {
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const userId = req.user.userMongoId;
        return this.service.createDeposit(partnerid, appId, userId, formData);
    }

    /* nhận thông tin thay đổi invoiceId, invoiceCode trong booking */
    @Post('listen-booking-changed')
    async listenBookingChanged(@Body() formData: ListenBookingChangedDTO): Promise<any> {
        return this.service.listenBookingChanged(formData);
    }

    /* nhận thông tin thay đổi Số thứ tự, giờ khám dự kiến trong booking */
    @Post('listen-booking-stt-changed')
    async listenBookingSTTChanged(@Body() formData: ListenBookingSTTChangedDTO): Promise<any> {
        return this.service.listenBookingSTTChanged(formData);
    }

    /* nhận thông tin thay đổi Số thứ tự, giờ khám dự kiến trong booking */
    // @Post('listen-booking-date-changed')
    // async listenBookingDateChanged(@Body() formData: ListenBookingDateChangedDTO): Promise<any> {
    //     return this.service.listenBookingDateChanged(formData);
    // }

    @Post('listen-patient-code-his')
    /* nhận thông tin khi đồng bộ msbn */
    async listenSyncPatientCodeFromHIS(
        @Body() formData: ListenSyncPatientCodeFromHisDTO,
    ): Promise<any> {
        return this.service.listenSyncPatientCodeFromHIS(formData);
    }

    @Post('listen-re-exam-changed')
    async listenReExamChanged(@Body() formData: ListenReExamChangedDTO): Promise<any> {
        return this.service.listenReExamChanged(formData);
    }

    /* Lịch sử thanh toán viện phí */
    @Post('payment-fee-tracking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async paymentFeeTracking(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string, @Req() req,
    ): Promise<any> {
        const userId = req.user.userMongoId;
        return this.service.paymentFeeTracking(partnerid, appId, userId);
    }

    @Get('check-update-nd1')
    async checkUpdateNhiDong1(@Query('id') id: string): Promise<any> {
        return this.service.checkUpdateNhiDong1(id);
    }

    /* cập nhật trạng thái */
    @Post('update-status')
    async updateStatus(@Body() updateStatus: UpdateStatusDTO): Promise<any> {
        return this.service.updatePaymentStatus(updateStatus.transactionId, updateStatus.status, updateStatus.message);
    }

    @Post('retry-sync-booking')
    async retrySyncBookingV1(@Body() formData: RetrySyncBookingDTO): Promise<any> {
        const { id, transactionId } = formData
        return this.service.retrySyncBookingV1(id, transactionId);
    }

    @Post('retry-sync-booking-v2')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async retrySyncBookingV1_V2(@Body() formData: RetrySyncBookingDTOV2, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.retrySyncBookingV1_v2(formData, userMongoId);
    }

    /* cập nhật thông tin newBill từ FeeHub */
    @Post('billPaid')
    async processBillPaid(@Body() newBill: any): Promise<any> {
        return this.service.processBillPaid(newBill);
    }

    @Post('testBillPaid')
    async processTestBillPaid(@Body() newBill: any): Promise<any> {
        return this.service.processTestBillPaid(newBill);
    }

    /* cập nhật trạng thái booking telemed status */
    @Post('update-booking-telemed-status')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateBookingTelemedStatus(@Body() updateStatus: UpdateBookingTelemedStatusDTO, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateBookingTelemedStatus(userMongoId, updateStatus);
    }

    /* Kiểm tra booking telemed */
    @Post('/telemed/meeting/rooms')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async telemedMeetingRooms(@Body() body: any): Promise<void> {
        return this.service.telemedMeetingRooms(body);
    }

    /* Kiểm tra booking telemed */
    @Post('telemed/meeting/rooms/history')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async telemedMeetingRoomHistory(@Body() body: any): Promise<void> {
        return this.service.telemedMeetingRoomHistory(body);
    }

    /* Lấy lại thông tin lúc thanh toán  */
    @Get('get-payment-info')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPaymentGatewayInfo(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Query('transactionId') transactionId: string,
        @Req() req,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.getPaymentGatewayInfo(userMongoId, appId, partnerid, transactionId, platform);
    }

    @Get('cskh/get-booking-list')
    async getBookingListCSKH(
        @Query('transactionId') transactionId: string,
        @Query('partnerId') partnerId: string,
        @Query('bookingCode') bookingCode: string,
    ): Promise<any> {
        return this.service.getBookingListCSKH(partnerId, bookingCode, transactionId);
    }

    @Post('check-transaction')
    async checkTransaction(
        @Body() formData: CheckTransactionDTO,
    ): Promise<any> {
        return this.service.transaction(formData.transactionId, 0);
    }

    @Get('manual-remind-booking')
    async manualRemindBooking(
        @Query('transactionId') transactionId: string,
        @Query('minutes') minutes: number,
    ): Promise<any> {
        return this.service.manualRemindBooking(transactionId, minutes);
    }

    @Post('remind-before-30-minutes')
    async remindBefore30Minutes(): Promise<any> {
        return this.service.remindBefore30Minutes();
    }

    @Post('remind-expired-booking')
    async remindExpiredBookings(
        @Query('partnerId') partnerId?: string,
        @Query('serviceId') serviceId?: string,
        @Query('fromDate') fromDate?: Date,
        @Query('toDate') toDate?: Date,
    ): Promise<any> {
        return this.service.remindExpiredBookings(partnerId, serviceId, fromDate, toDate);
    }

    @Post('booking-share-to-pay')
    async bookingShareToPay(
        @Body() formData: BookingShareToPayDTO,
    ): Promise<any> {
        return this.service.getBookingShareToPay(formData);
    }

    @Post('booking-share-to-pay-umc')
    async bookingShareToPayUmc(
        @Body() formData: BookingShareToPayDTO,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        return this.service.getBookingShareToPayUMC(formData);
    }

    /* Lấy lại thông tin  */
    @Get('get-booking-by-transaction-code')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getBookingWithTransaction(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Query('transactionId') transactionId: string,
        @Req() req,
        @Headers('platform') platform?: string,
        ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        // return this.service.getBookingInfoAfterTransaction(appId, partnerid, transactionId, platform);
        return this.service.getBookingInfoAfterTransaction(appId, partnerid, transactionId, platform, null, userMongoId);
    }

    /* Lấy lại thông tin  */
    @Get('cskh/bookings-transaction')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async cskhGetBookingWithTransaction(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('authorization') authorization: string,
        @Query('transactionId') transactionId: string,
        @Query('phone') phone: string,
        // @Req() req,
        @Headers('platform') platform?: string,
        ): Promise<any> {
        // const user = req.user;
        // const { userMongoId } = user;
        let userMongoId;
        try {
            const payload = this.jwtService.verify(authorization.substring(7));
            userMongoId = payload.userMongoId;
        } catch (err) {
        }
        return this.service.getBookingInfoAfterTransaction(appId, partnerid, transactionId, platform, phone, userMongoId);
    }

    @Post('sync-booking-umc')
    @UseGuards(AuthGuard('user-jwt'))
    async syncUmcBooking(
        @Req() req,
        @Body() formData :  { bookingCodeV1?: string }
    ): Promise<any> {

        const user = req.user;
        const { userMongoId } = user;

        return this.service.syncUmcBooking(formData, userMongoId);
    }

    // @Get('test-payment')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    // async testPayment(@Req() req, @Headers('partnerid') partnerid: string): Promise<any> {
    //     const userId = req.user.userMongoId;
    //     return this.service.testPayment(userId, partnerid);
    // }

    @Get('cancel-reservation')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @Permission(PermissionEnum.CANCEL_BOOKING)
    @UseGuards(AuthGuard('user-jwt'), PermissionGuard)
    @ApiOperation({
        summary: 'Cancel Reservation',
        description: 'Cancel Reservation',
    })
    @HttpCode(HttpStatus.OK)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async cancelReservation(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Query('id') id: string,
        @Req() req,
        @Headers('platform') platform?: string,
        ): Promise<any> {

        if (!!partnerid === false) {
            throw new HttpException('Vui lòng gửi partnerId lên.', HttpStatus.BAD_REQUEST);
        }

        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.cancelReservation2(partnerid, appid, userMongoId, id, req.ipInfo, cskhInfo, platform);
    }

    @Post('portal-cancel-booking')
    async portalCancelBooking(
        @Body() formData: PortalCancelBookingDTO,
        @Req() req,
    ): Promise<any> {
        return this.service.portalCancelBooking(formData, req.ipInfo);
    }

    @Post('exam-results')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Lấy thông tin bên chợ rẫy',
        description: 'Cancel Reservation',
    })
    async examResults(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: CrExamResultsDTO,
        @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.examResults(appid, partnerid, userMongoId, formData);
    }

    @Post('get-health-histories')
    @ApiBearerAuth()
    // @ApiOperation({ summary: 'Lấy thông tin user - user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async getHealthHistories(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.getHealthHistories(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('get-health-exam-histories')
    @ApiBearerAuth()
    // @ApiOperation({ summary: 'Lấy thông tin user - user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async getHealthExamHistories(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.getHealthExamHistories(appId, partnerId, req.user.userMongoId, formData);
    }

    @Get('historyExamByUser')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExamByUser(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Request() req): Promise<any> {
        return this.service.historyExamByUser(appId, partnerId, req.user.userMongoId);
    }

    @Post('historyExamByPatient')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExamByPatient(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.historyExamByPatient(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('historyExam')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExam(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByExamIdDTO,
        @Request() req): Promise<any> {
        return this.service.historyExam(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('historyExamGroup')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExamGroup(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.historyExamGroup(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('historyExamGroupDetail')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async historyExamGroupDetail(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByExamGroupIdDTO,
    ): Promise<any> {
        return this.service.historyExamGroupDetail(appId, partnerId, formData);
    }

    // @Get('test-exam-result')
    // async testExamREsultNeku(): Promise<any> {
    //     return this.service.reGetUserId('choray', '111111', '3035171');
    // }

    @Get('get-detail-health-history/:id')
    async getDetailHealthHistory(
        // @Headers('appid') appId: string,
        // @Headers('partnerid') partnerId: string,
        @Param('id') id: string): Promise<any> {
        return this.service.getDetailHealthHistory(id);
    }

    // @Get('cancel')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    // @ApiOperation({
    //     summary: 'Cancel Reservation',
    //     description: 'Cancel Reservation',
    // })
    // @HttpCode(HttpStatus.OK)
    // @ApiResponse({
    //     status: HttpStatus.OK,
    //     type: OutBookingDTO,
    //     description: 'Kết quả trả về reserve booking.',
    // })
    // @HttpCode(HttpStatus.BAD_REQUEST)
    // @ApiResponse({
    //     status: HttpStatus.BAD_REQUEST,
    //     type: OutBadRequestBookingDTO,
    //     description: 'Kết quả trả về khi lỗi 400 - Bad request',
    // })
    // async cancel(@Headers('partnerid') partnerid: string, @Query('bookingId') bookingId: string, @Req() req): Promise<any> {
    //     const user = req.user;
    //     const { userMongoId } = user;
    //     return this.service.cancelReservation(partnerid, userMongoId, bookingId, req.ipInfo);
    // }

    @Get('confirm')
    @ApiOperation({
        summary: 'Confirm Booking',
        description: 'Confirm Booking',
    })
    async confirm(@Headers('partnerid') partnerid: string, @Query() confirmBookingForm: ConfirmBookingDTO): Promise<any> {
        return this.service.confirmBooking(partnerid, confirmBookingForm);
    }

    @Get('booking-status')
    @ApiOperation({
        summary: 'Lấy thông tin trạng thái của booking',
        description: 'Lấy thông tin trạng thái của booking',
    })
    async getBookingStatus(@Headers('partnerid') partnerid: string, @Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getBookingStatus(partnerid, bookingId);
    }

    @Get('booking-info')
    @ApiOperation({
        summary: 'Lấy thông tin của booking',
        description: 'Lấy thông tin của booking',
    })
    async getBookingInfo(@Headers('partnerid') partnerid: string, @Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getBookingDetailInfo(partnerid, bookingId);
    }

    @Get('his-booking-status')
    @ApiOperation({
        summary: 'Lấy thông tin trạng thái của booking từ HIS',
        description: 'Lấy thông tin trạng thái của booking từ HIS',
    })
    async getBookingHisStatus(@Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getBookingHisStatus(bookingId);
    }

    @Get('his-booking-info')
    @ApiOperation({
        summary: 'Lấy thông tin của booking từ HIS',
        description: 'Lấy thông tin của booking từ HIS',
    })
    async getHisBookingDetailInfo(@Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getHisBookingDetailInfo(bookingId);
    }

    @Post('all-bookings')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @UseInterceptors(ViettelPayInterceptor)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allBookingByNoGroupUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {

        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allBookingByNoGroupUser(userMongoId, partnerid, appid);
    }

    @Get('all-bookings-by-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allBookingByUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allBookingByUser(userMongoId, partnerid, appid);
    }

    /* cskh */
    @Post('cskh/all-bookings')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async allBookingByCSKH(
        @Body() formData: SearchBookingCSDTO,
        @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.allBookingByCSKH(userMongoId, formData);
    }

    @Post('cskh/booking-info')
    async getBookingInfoCSKH(
        @Body() formData: ViewBookingCSDTO,
        @Req() req,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('authorization') authorization: string,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        let userMongoId; // userId của cskh
        try {
            const payload = this.jwtService.verify(authorization.substring(7));
            userMongoId = payload.userMongoId;
        } catch (err) {
        }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);
        return this.service.getBookingInfoCSKH(cskhInfo, userMongoId, formData, platform);
    }

    @Post("backoffice/cskh/booking-info")
    getBookingInfoCSKHFromBO(
        @Headers('secret') secret: string,
        @Body() formData: ViewBookingCSDTO,
    ) {
        return this.service.getBookingInfoCSKH(null, null, formData, null, null, secret);
    }

    @Post('cskh/booking-info/medpro-care')
    async getBookingInfoMedproCareCSKH(
        @Body() formData: ViewBookingMedproCareCSDTO,
        @Req() req,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('authorization') authorization: string,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        let userMongoId; // userId của cskh
        try {
            const payload = this.jwtService.verify(authorization.substring(7));
            userMongoId = payload.userMongoId;
        } catch (err) {
        }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);
        return this.service.getBookingInfoMedproCareCSKH(cskhInfo, userMongoId, formData, platform);
    }

    @Get('cskh/enable-check-phone-view-booking')
    isEnableCheckPhoneViewBooking(@Query() query: any, @Headers('authorization') authorization: string) {
        let userMongoId;
        if (authorization) {
            try {
                const payload = this.jwtService.verify(authorization.substring(7));
                userMongoId = payload.userMongoId;
            } catch (err) {
            }
        }

        return this.service.isEnableCheckPhoneViewBooking(query, userMongoId);
    }

    @Get('cskh/booking-info-by-token')
    async getBookingInfoCSKHByToken(
        @Query() formData: ViewBookingCSByTokenDTO,
        @Req() req,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const token = formData.token;
        let bookingId = ''

        try {
            const payload = this.jwtService.verify(token);
            bookingId = payload.bookingId;
        } catch (err) {
            throw new HttpException("Thông tin xem phiếu không hợp lệ!", 401);
        }

        return this.service.getBookingInfoCSKH(cskhInfo, null, { bookingId }, platform);
    }

    @Post('cskh/transaction-info')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getTransactionInfoCSKH(
        @Body() formData: ViewTransactionCSDTO,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);
        return this.service.getTransactionInfoCSKH(cskhInfo, userMongoId, formData);
    }

    @Post('cskh/get-info-by-phone')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getInfoByPhone(
        @Body() formData: GetInfoByPhoneDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getInfoByPhone(formData, userMongoId);
    }

    @Post('cskh/get-info-by-phone-v2')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getInfoByPhoneV2(
        @Body() formData: GetInfoByPhoneDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getInfoByPhoneV2(formData, userMongoId);
    }

    @Post('third-partner/get-info-by-phone')
    async thirdPartnerGetInfoByPhone(
        @Body() formData: GetInfoByPhoneThirdPartnerDTO,
    ): Promise<any> {
        return this.service.thirdPartnerGetInfoByPhone(formData);
    }

    @Post('send-mail-sms')
    async sendMailOrSMS(
        @Body() formData: SendBookingMailSMSDTO,
    ): Promise<any> {
        return this.service.sendMailOrSMS(formData);
    }

    @UseGuards(CskhGuard)
    @Post('send-sms')
    async sendSMS(
        @Body() formData: SendBookingSMSDTO,
    ): Promise<any> {
        return this.service.sendSMS(formData);
    }

    // @UseGuards(CskhGuard)
    @Post('send-sms-nhac-nho-hoan-tien')
    async sendSMSNhacNhoHoanTien(
        @Body() formData: SendBookingSMSDTO,
    ): Promise<any> {
        return this.service.sendSMSNhacNhoHoanTien(formData);
    }

    @Get('/list-booking-error')
    @ApiOperation({
        summary: 'Lấy danh sách booking không có appid và partnerid',
    })
    async getBookingHaveNotPartnerIdAndAppId(): Promise<IBooking[]> {
       return this.service.getBookingHaveNotPartnerIdAndAppId();
    }

    @Delete('/delete-booking-null-appid-partnerid/:id')
    @HttpCode(204)
    @ApiOperation({
        summary: 'Xóa booking không có appid và partnerid',
    })
    @ApiParam({
        name : 'id',
        type: String,
        required: true,
    })
    async deleteBookingHaveNotPartnerIdAndAppId(@Param('id') id: string): Promise<void> {
       return this.service.deleteBookingHaveNotPartnerIdAndAppId(id);
    }

    @Post('get-bookings-and-results')
    async getBookingsAndResults(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: GetBookingResultDto,
    ): Promise<any> {
        return this.service.getBookingsAndResults(formData, appid, partnerId);
    }

    @Get('test-discount-booking-as-partner')
    async discountBookingAsPartnerTest(
        @Query('bookingCode') bookingCode: string,
    ): Promise<void> {
        return this.service.discountBookingAsPartnerTest(bookingCode);
    }

    @Post('cskh/patients-by-phone')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPatientsByPhoneCskh(
        @Body() formData: GetInfoByPhoneDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getPatientsByPhoneCskh(formData, userMongoId);
    }

    @Get('cskh/bookings-by-patient')
    @ApiBearerAuth()
    @UseInterceptors(PartnerCSKHUsersInterceptor, AnKhangCSKHUsersInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getBookingsByPatientCskh(
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;

        const cskkhUserId = user?.userMongoId;

        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        const { userMongoId } = objUser;

        return this.service.getBookingsByPatientCskh(cskhInfo.patientId, userMongoId, cskkhUserId);
    }

    @UseGuards(CskhGuard)
    @Get('cskh/booking-info/copy-to-clipboard')
    async copyInfoBookingToClipboard(@Query('bookingId') bookingId: string): Promise<any> {
        return this.service.copyInfoBookingToClipboard(bookingId);
    }

    @Patch('change-booking-status')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async changeBookingStatus(@Body() formData: ChangeBookingStatusDto, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.changeBookingStatus(formData, userMongoId);
    }

    @Post('update-booking')
    @Permission(PermissionEnum.UPDATE_BOOKING)
    @UseGuards(AuthGuard('user-jwt'), PermissionGuard)
    @ApiBearerAuth()
    @ApiBody({
        type: ListenBookingDateChangedDTO,
    })
    async updateBooking(
        @Headers('partnerid') partnerid: string,
        @Body() formData: ListenBookingDateChangedDTO,
        @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateBooking(userMongoId, partnerid, formData);
    }

    @Get('test-sms')
    async testSmsService(@Query('bookingCode') bookingCode: string): Promise<string> {
        return this.service.testSmsService(bookingCode);
    }

    @Get('decode-short-link')
    async decodeShortLink(@Query('code') code: string): Promise<any> {
        return this.service.decodeShortLink(code);
    }


    @Post('register-cskh-offline')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiBody({
        type: CskhOfflineRegisterDto,
    })
    async registerCSoffline(@Body() formData: CskhOfflineRegisterDto): Promise<any> {
        return this.service.registerCSoffline(formData);
    }

    @Get('booking-update-history')
    @UseGuards(AuthGuard('user-jwt'))
    @ApiBearerAuth()
    async getBookingTrackings(@Query() params: BookingTrackingPageOptionsDto, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingTrackings(params, userMongoId);
    }


    @Get('payment-fee-history')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPaymentFeeHistoryUmc(
        @Req() req,
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('platform') platform?: string,
    ) {
        return this.service.getPaymentFeeHistoryUmc(req.user.userMongoId, partnerid, appid);
    }

    @Get('get-payment')
    async getPayment(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Query('transactionId') transactionId: string,
    ): Promise<any> {
        if (!partnerid) {
            partnerid = appid;
        }
        return this.service.getPayment(partnerid, transactionId);
    }

    @Get('get-payment-status')
    async getPaymentStatus(
        @Query('transactionId') transactionId: string,
    ): Promise<any> {
        return this.service.getPaymentStatus(transactionId);
    }

    @Patch('remind/booking/specify')
    remindTelemdBooking(@Body() payload: IBooking, @Query('minutes') minute: number): Promise<any> {
        if (minute === 60) {
            return this.service.handleRemindTelelemedBooking(payload);
        }
        return this.service.handleRemindBooking(payload);
    }

    @Get('mail-inform-booking')
    async mailInformBooking(
        @Query('transactionId') transactionId: string,
    ): Promise<any> {
        return this.service.mailInformBooking(transactionId);
    }

    @UseGuards(CskhGuard)
    @Get('cskh/bookings-telemed')
    async cskhGetTelemedBookings(@Query() query: string, @Headers('partnerid') partnerId: string) {
        return this.service.cskhGetTelemedBookings(query, partnerId);
    }

    @UseGuards(CskhGuard)
    @Post('check-passcode-booking')
    async isPassCodeValid(@Body() body: { passCode: string }) {
        return this.service.isPassCodeValid(body.passCode);
    }

    @UseGuards(CskhGuard)
    @Get('cskh/all-users')
    async getAllUserCs() {
        return this.service.getAllUserCs();
    }
    
    @UseGuards(CskhGuard, Care247Guard)
    @UseInterceptors(AppCskhActivitiesInterceptor)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.ASSIGN_CARE247_EMPLOYEE })
    @Patch('cskh/medpro-care-instructor')
    medproCareInstructor(@Req() req, @Body() body: MedproCareInstructorDto): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return this.service.medproCareInstructor(body, userId);
    }

    @Post('medpro-care/tracking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async trackingMedproCare(@Req() req, @Body() formData: MedproCareTrackingDTO, @Headers('partnerid') partnerId: string): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.trackingMedproCare(formData, userId, partnerId);
    }

    @Post('check-sync-booking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async checkSyncBooking(@Req() req, @Body() formData: { bookingId: string }): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.checkSyncBooking(formData, userId);
    }

    @Post('complain-booking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async complainBooking(@Req() req, @Body() formData: ComplainBookingDTO): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.complainBooking(formData, userId);
    }

    @Post('medpro-care/reserve')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMedproCare(
        @Body() medproCareWithBooking: MedproCareSlotFormDTO,
        @Req() req,
        @Headers('appid') appId: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('cskhtoken') cskhtoken?: string): Promise<any> {
        const user = req.user;
        // if (!partnerid) {
        //     throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        // }
        // if (!appId) {
        //     appId = partnerid;
        // }
        const cskhInfo = await this.service.verifyCskhToken(cskhtoken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveMedproCare(appId, platform, version, userId, medproCareWithBooking, null, cskhInfo, cskhtoken);
    }

    @Post('medpro-care/addon')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getMedproCareAddon(@Req() req, @Body() data: { transactionId: string }): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.getMedproCareAddon(data);
    }
    
    @Post('medpro-care/addon-cskh')
    async getMedproCareAddonCskh(@Req() req, @Body() data: { transactionId: string }): Promise<any> {
        return await this.service.getMedproCareAddon(data);
    }

    @Post('medpro-care/addon/reserve')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMedproCareAddon(
        // @Headers('partnerid') partnerid: string,
        @Req() req,
        @Body() medproCareAddon: MedproCareAddonFormDTO,
        @Headers('appid') appId: string,
        @Headers('version') version: string,
        @Headers('cskhtoken') cskhtoken?: string): Promise<any> {
        const user = req.user;
        // if (!partnerid) {
        //     throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        // }
        // if (!appId) {
        //     appId = partnerid;
        // }
        const cskhInfo = await this.service.verifyCskhToken(cskhtoken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveMedproCareAddon(appId, version, userId, medproCareAddon, null, cskhInfo, cskhtoken);
    }

    // cs gửi phiếu cho khách hàng thanh toán dịch vụ care247
    @Post('medpro-care/reserve-cskh')
    @UseInterceptors(AppCskhInterceptor)
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMedproCareCskh(
        @Body() medproCareWithBooking: MedproCareSlotFormDTO,
        @Req() req,
        @Headers('appid') appId: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('cskhtoken') cskhtoken?: string): Promise<any> {
        
        return this.service.reserveMedproCareCskh(appId, platform, version, medproCareWithBooking, null, null, cskhtoken);
    }

    // cs gửi phiếu cho khách hàng thanh toán dịch vụ care247 đặt thêm giờ
    @Post('medpro-care/addon/reserve-cskh')
    @UseInterceptors(AppCskhInterceptor)
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMedproCareAddonCskh(
        // @Headers('partnerid') partnerid: string,
        @Req() req,
        @Body() medproCareAddon: MedproCareAddonFormDTO,
        @Headers('appid') appId: string,
        @Headers('version') version: string,
        @Headers('cskhtoken') cskhtoken?: string): Promise<any> {
        
        return this.service.reserveMedproCareAddonCskh(appId, version, medproCareAddon, null, null, cskhtoken);
    }

    @Post('medpro-care/information')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getMedproCareInformation(
        @Req() req, 
        @Body() data: { transactionId: string },
        @Headers('appid') appid: string,
        @Headers('version') version: string,
    ): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.getMedproCareInformation(data, userId, appid, version);
    }

    @Post('medpro-care/information-cskh')
    @UseInterceptors(AppCskhInterceptor)
    async getMedproCareInformationCskh(@Req() req, @Body() data: { transactionId: string }): Promise<any> {
        return await this.service.getMedproCareInformationCskh(data);
    }

    @Post('push-notif-cskh')
    @UseGuards(CskhGuard)
    @UseInterceptors(AppCskhInterceptor)
    async getNotificationFromOneSigNal(@Req() req, @Body() data: { onesignalId: string, content:string }): Promise<any> {
        const {onesignalId, content} = data
        return await this.service.getNotificationFromOneSigNal([onesignalId],content);
    }

    @UseGuards(CskhGuard, Care247Guard)
    // @ActionData({ category: CategoryEnum.QUAN_LY_GIAO_DICH, action: ActionEnum.SEARCH })
    @ApiOperation({ description: 'Quản lý thông tin các bookings', summary: 'Quản lý thông tin các bookings' })
    @Post('bookings')
    async getBookings(@Req() req: any, @Body() body: BookingssQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookings(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    // @ActionData({ category: CategoryEnum.QUAN_LY_GIAO_DICH, action: ActionEnum.SEARCH })
    @ApiOperation({ description: 'Quản lý thông tin các bookings ankhang', summary: 'Quản lý thông tin các bookings ankhang' })
    @Post('booking-cskh-ankhang')
    async getBookingsCskhAnKhang(@Req() req: any, @Body() body: BookingssQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingsCskhAnKhang(userMongoId, body);
    }

    @Post("backoffice/bookings")
    getBackofficeBookings(
        @Headers('secret') secret: string,
        @Req() req: any, 
        @Body() body: BookingssQueryDto
    ) {
        return this.service.getBookings(null, body, secret);
    }

    @Post('medpro-care/update-viewed-payment/:id')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateViewedPayment(
        @Req() req, 
        @Body() data: { viewedPayment: boolean },
        @Param('id') id: string
    ): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.updateViewedPayment(data, userId, id);
    }

    @Post('bvmat-detect-oneday-issues')
    async detectOnedayIssuesBvmat(@Body() formData: any) {
        return this.service.detectOnedayIssuesBvmat(formData);
    }
    
    @Post('seed-constraint-oneday')
    async seedConstraintOneday(@Body() formData: any) {
        return this.service.seedConstraintOneday(formData);
    }

    @Post('deal/reserve')
    @ApiOperation({
        summary: 'Reserve Deal',
        description: 'Reserve Deal',
    })
    async reserveDeal(
        @Body() body: ReserveDealDTO,
        @Headers('appid') appId: string,
        @Headers('version') version: string,
        @Headers('platform') platform: string,): Promise<any> {
        return this.service.reserveDeal(body, appId, version, platform);
    }

    @Post('cskh/update-check-convert')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateBookingCheckConvertUser(
        @Body() formData: { bookingId: string, checkConvertUser: boolean },
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateBookingCheckConvertUser(formData, userMongoId);
    }

    @Post('test-sms-v2')
    async testSmsV2Service(@Body() formData: any): Promise<string> {
        return this.service.testSmsV2Service(formData);
    }

    @Post('test-sms-template')
    async testSmsTemplate(@Body() formData: any): Promise<string> {
        return this.service.testSmsTemplate(formData);
    }

    @Post('share-booking')
    @ApiOperation({ summary: 'Share booking', description: 'Share booking'})
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async shareBooking(
        @Req() req, 
        @Body() body: { bookingId: string },
        @Headers('appid') appId: string,
        @Headers('version') version: string,
        @Headers('platform') platform: string
    ): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return this.service.shareBooking(body, userId);
    }

    @Post('get-share-booking')
    async getBookingWithShare(@Body() formData: { token: string }): Promise<string> {
        return this.service.getBookingWithShare(formData);
    }

    @Post('cskh/update-active-share-booking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateActiveLinkShareOfBooking(
        @Body() formData: { bookingId: string, active: boolean },
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateActiveLinkShareOfBooking(userMongoId, formData);
    }

    @Post('test-notif-app')
    async testPushNotiApp(@Body() body: any) {
        return await this.service.testPushNotiApp(body);
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'), CskhGuard, MedproCSKHGuard)
    @Post('booking-error')
    async getBookingErrorInDate(@Body() body: any, @Req() req) {
        const user = req.user;
        const cskhUserId = user?.userMongoId;
        return await this.service.getBookingErrorInDate(body, cskhUserId);
    }
}
