import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { SchemaNewsController } from "./schema-news.controller";
import { SchemaNewsService } from "./schema-news.service";
import { SCHEMA_NEWS_DESCRIPTION_COLLECTION_NAME } from "./schema/constants";
import { SchemaNewsSchema } from "./schema/shema-news.schema";

@Module({
    imports: [
        MongooseModule.forFeature(
            [
                {
                    name: SCHEMA_NEWS_DESCRIPTION_COLLECTION_NAME,
                    schema: SchemaNewsSchema
                }
            ]
        )
    ],
    controllers: [SchemaNewsController],
    providers: [SchemaNewsService]
})
export class SchemaNewsModule {
}
