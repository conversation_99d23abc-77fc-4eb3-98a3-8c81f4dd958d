import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from './constants';

const schema = mongoose.Schema;

export const HospitalDescriptionSchema = new schema(
    {
        hospitalId: String,
        shortDescription: String,
        description: String,
        workingTime: String,
        body: String,
        images: [String],
        banner: String,
        showPartnerInfo: { type: Boolean, default: false },
        mapView: String,
        rating: { type: Number, default: 4.5 },
        partnerId: String
    },
    {
        collection: HOSPITAL_DESCRIPTION_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
