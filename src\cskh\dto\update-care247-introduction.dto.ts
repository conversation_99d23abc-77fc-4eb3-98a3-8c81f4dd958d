import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsOptional } from 'class-validator';

export class IUpdateCare247IntroductionDto {
  @ApiProperty({ description: 'Name of the Care247 introduction', required: true })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Subname or subtitle of the Care247 introduction', required: false })
  @IsOptional()
  @IsString()
  subname?: string;

  @ApiProperty({ description: 'Medical professional care notes', required: false })
  @IsOptional()
  @IsString()
  medproCareNote?: string;

  @ApiProperty({ description: 'General notes for Care247 introduction', required: false })
  @IsOptional()
  @IsString()
  note?: string;
}
