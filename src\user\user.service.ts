import { Injectable, Inject, HttpService, HttpException, HttpStatus, Logger } from '@nestjs/common';
import * as queryString from 'query-string';
import { JwtService, JwtModuleOptions } from '@nestjs/jwt';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import * as sprintfAPI from 'sprintf-js';
import * as md5 from 'md5';
import * as uuid from 'uuid';
import * as moment from 'moment';
import * as jwt from 'jsonwebtoken';
import { first, get, uniq } from 'lodash';
import * as FormData from 'form-data';
import { AxiosResponse } from 'axios';
import { PKH_PATIENT_CONNECTION } from '../config/pkhPatientConnection';
import { FIREBASE_CONNECTION } from 'src/config/firebaseConnection';
import { FIREBASE_CONNECTION_SECOND } from 'src/config/firebaseConnectionSecond';
import { FIREBASE_CONNECTION_DA_LIEU } from 'src/config/firebaseConnectionDaLieu';
import { FIREBASE_CONNECTION_UMC } from 'src/config/firebaseConnectionUMC';
import { AddSaleCodeDTO } from './dto/addSaleCodeDto';
import { ZaloDTO } from './dto/zaloDto';
import { ZaloConfigService } from 'src/config/config.zalo.service';
import { ZaloDaLieuConfigService } from 'src/config/config.zalo-dalieu.service';
import { ZaloNd1ConfigService } from 'src/config/config.zalo-nd1.service';
import { ZaloUMCConfigService } from 'src/config/config.zalo-umc.service';
import { Observable } from 'rxjs';
import { ZaloStateDTO } from './dto/zaloStateDto';
import { FirebaseDTO } from './dto/firebaseDto';
import { SessionService } from 'src/session/session.service';
import { CheckPhoneDTO } from './dto/checkPhone.dto';
import { CheckPhoneRefDTO } from './dto/checkPhoneRef.dto';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { CheckCodeDTO } from './dto/checkCode.dto';
import { LoginWithUserAccessTokenDTO } from './dto/login-with-user-access-token';
import { UtilService } from 'src/config/util.service';
import { InjectModel } from '@nestjs/mongoose';
import {
    USER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
    USER_REQUEST_COLLECTION_NAME,
    CONTACT_US_COLLECTION_NAME,
    USER_DOCTOR_COLLECTION_NAME,
    USER_LOGIN_LOG_COLLECTION_NAME,
    USER_LOCKED_COLLECTION_NAME,
    VISA_USER_COLLECTION_NAME,
} from './schemas/constants';
import { Model } from 'mongoose';
import { IUser } from './interfaces/user.interface';
import { CheckCodeRefDTO } from './dto/checkCodeRef.dto';
import { UrlConfigService } from 'src/config/config.url.service';
import { PhoneRegisterDTO } from './dto/phone-register.dto';
import { CheckSMSCodeSignInProviderDTO } from './dto/check-sms-code-sign-provider.dto';
import { ApplySignInProviderDTO } from './dto/apply-signin-provider.dto';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { ISignInProvider } from './interfaces/sign-in-provider.interface';
import { MedproLoginDTO } from './dto/medproLogin.dto';
import { MedproLoginSocialDTO } from './dto/medproLoginSocial.dto';
import { ForgotPasswordDTO } from './dto/forgot-password.dto';
import { ForgotPasswordApplyCodeDTO } from './dto/forgot-password-apply-code.dto';
import { ApplyNewPasswordDTO } from './dto/apply-new-password.dto';
import { CheckUserInfoByPhoneDTO } from './dto/checkUserInfoByPhone.dto';
import { SocialRegisterDTO } from './dto/social-register.dto';
import { IUserConstraints } from './interfaces/user-constraints.interface';
import { IProviderConstraints } from './interfaces/provider-constraints.interface';
import { IsCSModeDTO } from './dto/is-cs-mode.dto';
import { UpdateEmployeeIdUserDTO } from './dto/update-employee-id-user.dto';
import { IUserProfile } from './interfaces/user-profile.interface';
import { ApplySignInProviderV2DTO } from './dto/apply-signin-provider-v2.dto';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { IUserAccount } from 'src/user-account/interfaces/user-account.interface';
import { ApplySignInProviderV3DTO } from './dto/apply-signin-provider-v3.dto';
import { FilesService } from 'src/files/files.service';
import { IHocVi } from './interfaces/hoc-vi.interface';
import { SeedHocViDTO } from './dto/seed-hoc-vi.dto';
import { IViTriCongViec } from './interfaces/vi-tri-cong-viec.interface';
import { SeedViTriCongViecDTO } from './dto/seed-vi-tri-cong-viec.dto';
import { CheckUserUpgradeByPhoneDTO } from './dto/checkUserUpgradeByPhone.dto';
import { UpdateUserProfileCbytDTO } from './dto/update-user-profile-cbyt.dto';
import { BOOKING_COLLECTION_NAME, CONVERT_USER_CSKH, PATIENT_TRACKING_EVENT } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ResetUserPhoneDTO } from './dto/resetUserPhone.dto';
import { DevModePartnerEnableDTO } from './dto/dev-mode-partner-enable.dto';
import { UpdateUserProfileCbytTokenDTO } from './dto/update-user-profile-cbyt-token.dto';
import { GetEmployeeDTO } from './dto/get-employee.dto';
import { ISyncUserDaLieuProcess } from 'src/sync-da-lieu-medpro/interfaces/sync-user-dalieu-process.inteface';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import {
    SYNC_DALIEU_PATIENT,
    SYNC_DALIEU_BOOKING,
    SYNC_NHI_DONG_1_PATIENT,
    SYNC_NHI_DONG_1_BOOKING,
    SYNC_DHYD_PATIENT,
    SYNC_DHYD_BOOKING,
} from 'src/event/schemas/constants';
import { ISyncDaLieuPatient } from 'src/event/intefaces/sync-dalieu-patient.inteface';
import { ISyncDaLieuBooking } from 'src/event/intefaces/sync-dalieu-booking.inteface';
import { SendBookingSMSDTO } from 'src/booking-gateway/dto/send-booking-sms.dto';
import { FIREBASE_CONNECTION_ND1 } from 'src/config/firebaseConnectionNd1';
import { ISyncNhiDong1Patient } from 'src/event/intefaces/sync-nhidong1-patient.inteface';
import { ISyncNhiDong1Booking } from 'src/event/intefaces/sync-nhidong1-booking.inteface';
import { IReferralCodeRegister } from './interfaces/referral-code-register.interface';
import { ISyncDHYDPatient } from 'src/event/intefaces/sync-dhyd-patient.inteface';
import { ISyncDHYDBooking } from 'src/event/intefaces/sync-dhyd-booking.inteface';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { FirebaseConfigService } from '../firebase-config/firebase-config.service';
import { ConfigSMSMedproService } from 'src/config/config.sms.medpro.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { IUserApp } from './interfaces/user-app.interface';
import { UserAppDto } from './dto/user-app.dto';
import { SAVE_NEW_USER_APP } from './events/constants';
import { isEmail, isMobilePhone } from 'class-validator';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { ZaloCTCHConfigService } from 'src/config/config.zalo-ctch.service';
import { IUserRequests } from './interfaces/user-requests.interface';
import { ContactUsDto } from './dto/contact-us.dto';
import { IContactUs } from './interfaces/contact-us.interface';
import { larkMsgAddContactUsTemplate } from '../common/template/lark.template';
import { ConfigLarkService } from '../config/config.lark.service';
import { FindMedproIdDto } from './dto/find-medpro-id.dto';
import { UpdateMedproIdDto } from './dto/update-medpro-id.dto';
import { ConfigRecaptchaService } from '../config/config.recapcha.service';
import { IUserDoctor } from './interfaces/user-doctor.inteface';
import { IDoctor } from '../doctor-mongo/interfaces/doctor.interface';
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { IPushDevice } from '../push-device/intefaces/push-device.inteface';
import { PUSH_DEVICE_COLLECTION_NAME } from '../push-device/schemas/constants';
import { ContactUsTypeEnum } from './enums/contact-us.enum';
import { IUserLoginLog } from './interfaces/user-login-log.interface';
import { IUserLocked } from './interfaces/user-locked.interface';
import { UpdateUserMedproDto } from './dto/update-user-medpro.dto';
import { IVisaUser } from './interfaces/visa-user.interface';
import { IConvertUserCskh } from '../booking-gateway/intefaces/convert-user-cskh.interface';
import { CheckConvertUserCskhDTO, convertUserApplyPassword, ConvertUserOtpDTO } from './dto/check-convert-user-cskh.dto';
import { ConvertUserLoginDTO } from './dto/convert-user-login-password.dto';
@Injectable()
export class UserService {
    private readonly logger: Logger = new Logger(UserService.name);
    private getRolesPortalUrl: string;
    private syncV2SkinPatientTableName = 'sync_v2_skin_patient';
    private skinUserPatient = 'skin_user_patient';
    private nd1UserPatient = 'nd1_user_patient';
    private umcUserPatient = 'user_patient';
    private skinBookingTableName = 'skin_booking';
    private nd1BookingTableName = 'nd1_booking';
    private umcBookingTableName = 'booking';
    private secretKeyResetUserPhone = '';
    private umcUserTable = 'user';

    private appBoId: string;
    private appBoKey: string;
    private isActiveMessageHub: boolean;
    private SAVE_USER_PROFILE_APPLY_SIGNIN_PROVIDER: string = 'SAVE_USER_PROFILE_APPLY_SIGNIN_PROVIDER';
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @Inject(FIREBASE_CONNECTION) private readonly firebaseAdmin,
        @Inject(FIREBASE_CONNECTION_SECOND) private readonly firebaseSecondAdmin,
        @Inject(FIREBASE_CONNECTION_DA_LIEU) private readonly firebaseDaLieuAdmin,
        @Inject(FIREBASE_CONNECTION_ND1) private readonly firebaseNd1Admin,
        @Inject(FIREBASE_CONNECTION_UMC) private readonly firebaseUMCAdmin,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(USER_PROFILE_COLLECTION_NAME) private userProfileModel: Model<IUserProfile>,
        @InjectModel(CONSTRAINTS_USER_COLLECTION_NAME) private constraintUser: Model<IUserConstraints>,
        @InjectModel(CONSTRAINTS_PROVIDER_COLLECTION_NAME) private constraintProvider: Model<IProviderConstraints>,
        @InjectModel(SIGNIN_PROVIDER_COLLECTION_NAME) private signInProvider: Model<ISignInProvider>,
        @InjectModel(USER_ACCOUNT_COLLECTION_NAME) private userAccountModel: Model<IUserAccount>,
        @InjectModel(HOC_VI_COLLECTION_NAME) private hocViModel: Model<IHocVi>,
        @InjectModel(SYNC_DALIEU_PATIENT) private syncDaLieuPatientModel: Model<ISyncDaLieuPatient>,
        @InjectModel(SYNC_DALIEU_BOOKING) private syncDaLieuBookingModel: Model<ISyncDaLieuBooking>,
        @InjectModel(VI_TRI_CONG_VIEC_COLLECTION_NAME) private viTriCongViecModel: Model<IViTriCongViec>,
        @InjectModel(SYNC_NHI_DONG_1_PATIENT) private syncNhiDong1PatientModel: Model<ISyncNhiDong1Patient>,
        @InjectModel(SYNC_NHI_DONG_1_BOOKING) private syncNhiDong1BookingModel: Model<ISyncNhiDong1Booking>,

        @InjectModel(SYNC_DHYD_PATIENT) private syncDHYDPatientModel: Model<ISyncDHYDPatient>,
        @InjectModel(SYNC_DHYD_BOOKING) private syncDHYDBookingModel: Model<ISyncDHYDBooking>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private readonly partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(REFERRAL_CODE_REGISTER_COLLECTION_NAME) private referralCodeRegisterModel: Model<IReferralCodeRegister>,
        @InjectModel(USER_APP_COLLECTION_NAME) private readonly userAppModel: Model<IUserApp>,
        @InjectModel(USER_REQUEST_COLLECTION_NAME) private readonly userRequestModel: Model<IUserRequests>,
        @InjectModel(CONTACT_US_COLLECTION_NAME) private readonly contactUsModel: Model<IContactUs>,
        @InjectModel(USER_DOCTOR_COLLECTION_NAME) private userDoctorModel: Model<IUserDoctor>,
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        @InjectModel(USER_LOGIN_LOG_COLLECTION_NAME) private userLoginLogModel: Model<IUserLoginLog>,
        @InjectModel(USER_LOCKED_COLLECTION_NAME) private userLockedModel: Model<IUserLocked>,
        @InjectModel(VISA_USER_COLLECTION_NAME) private visaUserModel: Model<IVisaUser>,
        @InjectModel(CONVERT_USER_CSKH) private convertUserCskhModel: Model<IConvertUserCskh>,

        private readonly zaloConfig: ZaloConfigService,
        private readonly zaloDaLieuConfig: ZaloDaLieuConfigService,
        private readonly zaloNd1Config: ZaloNd1ConfigService,
        private readonly zaloUMCConfig: ZaloUMCConfigService,
        private readonly zaloCTCHConfig: ZaloCTCHConfigService,
        private readonly httpService: HttpService,
        private readonly jwtService: JwtService,
        private readonly sessionService: SessionService,
        private readonly phoneLoginService: PhoneLoginService,
        private readonly smsService: SmsService,
        private readonly utilService: UtilService,
        private readonly urlConfigService: UrlConfigService,
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly filesService: FilesService,
        private readonly referralCodeService: ReferralCodeService,
        @InjectSentry() private readonly clientSentry: SentryService,
        private emitService: EventEmitter2,
        private repoConfigService: ConfigRepoService,
        private firebaseConfigService: FirebaseConfigService,
        private smsConfigService: ConfigSMSMedproService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly larkConfig: ConfigLarkService,
        private readonly recaptchaConfig: ConfigRecaptchaService,
    ) {
        this.getRolesPortalUrl = this.urlConfigService.getRolesPortalUrl();
        this.secretKeyResetUserPhone = this.urlConfigService.getSecretKeyResetUserPhone();

        this.appBoId = this.urlConfigService.getAPIBoId();
        this.appBoKey = this.urlConfigService.getAPIBoKey();
        this.isActiveMessageHub = this.smsConfigService.isActiveMessageHub();
    }

    async getMedproIdUserIdV1(userIdV1: number, medproId: string): Promise<any> {
        return this.userModel.findOne({ userIdV1, medproId }).exec();
    }

    async getEmployee(appId: string, partnerId: string, formData: GetEmployeeDTO): Promise<any> {
        try {
            const resultBoSo = (await this.getEmployeeHis(partnerId, formData.code).toPromise()).data;
            return resultBoSo;
        } catch (error) {
            const { response } = error;
            if (response) {
                const { status = HttpStatus.NOT_FOUND } = response;
                switch (status) {
                    case HttpStatus.NOT_FOUND:
                        throw new HttpException('Không tìm thấy thông tin nhân viên.', HttpStatus.NOT_FOUND);
                    default:
                        throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                }
            } else {
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    getEmployeeHis(partnerId: string, code: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/employee/getByCode/${partnerId}/${code}`;
        return this.httpService.get(url);
    }

    async getUserProfileVerifyPhone(partnerId: string, userId: string): Promise<any> {
        const getUserProfile = await this.userProfileModel
            .findOne({
                user: userId,
                partnerId,
            })
            .exec();

        if (!getUserProfile) {
            return false;
        }
        const getAAA = get(getUserProfile, 'attribute_mode_dev_patner_enable.value', false);
        return getAAA;
    }

    async modeDevPartnerEnable(formData: DevModePartnerEnableDTO): Promise<any> {
        const yourphone = `${formData.phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        /* Kiểm tra xem your phone đã có userId chưa . và tìm kiểm tra xem có đã userProfile hay chưa để enable lên */
        const getUser = await this.checkExistsUserByUsernameMedproId(yourphone);
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin user. Vui lòng đăng nhập', HttpStatus.UNAUTHORIZED);
        }
        /* kiểm tra xem có user-profiles hay chưa */
        const getUserProfile = await this.userProfileModel
            .findOne({
                user: getUser._id,
                partnerId: formData.partnerId,
            })
            .exec();

        const createTime = moment().toDate();
        if (!getUserProfile) {
            /* tạo thông tin user Profile */
            const userProfile = new this.userProfileModel({
                user: getUser._id,
                partnerId: formData.partnerId,
                attribute_mode_dev_patner_enable: {
                    value: true,
                    createTime,
                    updateTime: createTime,
                },
            });
            const insertUserProfile = await userProfile.save();
            const getAAA = get(insertUserProfile, 'attribute_mode_dev_patner_enable.value', null);
            return { mode_dev_patner_enable: getAAA };
        } else {
            /* toggle mode-dev-partner-enable */
            const getToggle = get(getUserProfile, 'attribute_mode_dev_patner_enable.value', false);
            const updateValue = !getToggle;
            await this.userProfileModel
                .findByIdAndUpdate(
                    { _id: getUserProfile._id },
                    {
                        attribute_mode_dev_patner_enable: {
                            value: updateValue,
                            updateTime: createTime,
                        },
                    },
                    { new: true },
                )
                .exec();
            return { mode_dev_patner_enable: updateValue };
        }
    }

    async deleteAccount(appId: string, partnerId: string, user: any, osId: string): Promise<any> {
        try {
            const info = await this.userRequestModel.create({
                userId: user?.userMongoId,
                appId,
                partnerId,
                type: 'ACCOUNT-DELETION',
                deviceId: osId,
            });
            // console.log(info);
        } catch (error) {
            console.log(error);
        }

        return {
            isOK: true,
            message: 'Thao tác thành công.',
        };
    }

    async resetUserPhone(formData: ResetUserPhoneDTO, appId: string = '', appKey: string = ''): Promise<any> {
        const validate = appId === this.appBoId && appKey === this.appBoKey;
        if (!validate) {
            throw new HttpException(`Thông tin xác thực gửi lên không chính xác. Vui lòng kiểm tra lại!`, HttpStatus.UNAUTHORIZED);
        }
        const env = this.urlConfigService.getEnv();
        const checkKey = formData?.secretKey || '';
        if (env !== 'PRODUCTION' || (this.secretKeyResetUserPhone && this.secretKeyResetUserPhone === checkKey)) {
            try {
                const yourphone = `${formData.phone}`
                    .replace(/^[+]84|^0/, '+84')
                    .replace(/^9/, '+849')
                    .replace(/^3/, '+843');

                const users = await this.userModel
                    .find({
                        username: yourphone,
                        medproId: {
                            $in: [null, ''],
                        },
                    })
                    .exec();

                for await (const user of users) {
                    await this.userModel.findByIdAndRemove({ _id: user._id }).exec();
                    /* xóa thông tin booking */
                    await this.bookingModel
                        .deleteMany({
                            user: user._id,
                        })
                        .exec();
                }

                /* xóa thông tin medproId nếu có */
                const usersMedpro = await this.userModel
                    .find({
                        username: yourphone,
                        medproId: `mp${yourphone}`,
                    })
                    .exec();

                for await (const user of usersMedpro) {
                    /* xóa thông tin constraintUser */
                    await this.signInProvider
                        .findOneAndRemove({
                            type: 'password',
                            user: user._id,
                        })
                        .exec();
                    /* Xóa thông tin constraintUser */
                    await this.constraintUser
                        .findOneAndRemove({
                            medproId: user.medproId,
                        })
                        .exec();
                    /* Xóa thông tin constraint Provider */
                    await this.constraintProvider
                        .findOneAndRemove({
                            providerId: `${user.medproId}_password`,
                        })
                        .exec();
                    /* Xóa thông tin user */
                    await this.userModel.findByIdAndRemove({ _id: user._id }).exec();
                    /* xóa thông tin booking */
                    await this.bookingModel
                        .deleteMany({
                            user: user._id,
                        })
                        .exec();
                }
                return true;
            } catch (error) {
                throw new HttpException(error, HttpStatus.BAD_REQUEST);
            }
        }
    }

    // async updateRequiredPass(): Promise<any> {
    //     return this.userModel.updateMany({
    //         nextStep: 'CREATE_NEW_PASSWORD',
    //     }, {
    //         nextStep: 'REQUIRED_PASSWORD',
    //     }).exec();
    // }

    async seedHocVi(appId: string, partnerId: string, formData: SeedHocViDTO): Promise<any> {
        const newHocVi = new this.hocViModel({
            id: uuid.v4().replace(/-/g, ''),
            name: formData.name,
            appId,
            partnerId,
        });
        return newHocVi.save();
    }

    async seedViTriCongViec(appId: string, partnerId: string, formData: SeedViTriCongViecDTO): Promise<any> {
        const newViTriCongViec = new this.viTriCongViecModel({
            id: uuid.v4().replace(/-/g, ''),
            name: formData.name,
            appId,
            partnerId,
        });
        return newViTriCongViec.save();
    }

    async getHocVi(appId: string, partnerId: string): Promise<any> {
        return this.hocViModel.find({ partnerId }).exec();
    }

    async getViTriCongViec(appId: string, partnerId: string): Promise<any> {
        return this.viTriCongViecModel.find({ partnerId }).exec();
    }

    async uploadAvatar(appId: string, userId: string, fileINput: any): Promise<any> {
        const idAvatar = fileINput.id;
        // console.log('idAvatar', idAvatar);
        const file = await this.filesService.findInfo(idAvatar);
        // console.log('file info', file);
        const filestream = await this.filesService.readStream(idAvatar);
        if (!filestream) {
            throw new HttpException('An error occurred while retrieving file', HttpStatus.EXPECTATION_FAILED);
        }
        /* Tiến hành lấy id của GridFS bỏ vào trong user profile */
        await this.userProfileModel
            .findOneAndUpdate(
                {
                    partnerId: appId,
                    user: userId,
                },
                { avatarId: idAvatar },
            )
            .exec();

        return {
            file,
            filestream,
        };
    }

    async syncUserAccount(username: string, userId: string): Promise<any> {
        /* tìm thông tin */
        const createdTime = moment().toDate();
        await this.userAccountModel
            .updateMany(
                { userPhone: username, user: { $in: [null] } },
                {
                    user: userId,
                    updatedDate: createdTime,
                },
            )
            .exec();
    }

    async sendBookingSMS(formData: SendBookingSMSDTO): Promise<any> {
        return this.smsService.sendBookingSMS(formData);
    }

    async testSoap(): Promise<any> {
        return this.utilService.randomNumber(6);
        try {
            return this.smsService.sendSmsMedpro('**********');
        } catch (error) {
            this.clientSentry.instance().captureException(error);
        }
    }

    async isCs(userId: string): Promise<any> {
        const getUser = await this.userModel.findById({ _id: userId }, { isCS: true }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }
        return getUser.isCS;
    }

    async isCSKH(userId: string): Promise<any> {
        try {
            const getUser = await this.userModel.findById({ _id: userId }, { isCS: true }).exec();
            if (!getUser) {
                return false;
            }
            return getUser?.isCS || false;
        } catch (error) {
            return false;
        }
    }

    async updateIsCSMode(formData: IsCSModeDTO): Promise<any> {
        if (formData.key === 'nhandeptrai@2021') {
            const yourphone = `${formData.phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            const info = await this.userModel.findOneAndUpdate(
                { username: yourphone, medproId: `mp${yourphone}`, isCS: { $in: [null, false] } },
                { isCS: true },
                { new: true },
            );
            if (info) {
                return {
                    message: 'Hệ thống thiết lập quyền thành công.',
                };
            } else {
                throw new HttpException('User này đã được set quyền rồi. Vui lòng đăng nhập lại.', HttpStatus.FORBIDDEN);
            }
        } else {
            throw new HttpException('Hệ thống không xử được thao tác này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
        }
    }

    async checkV1(): Promise<any> {
        return this.checkExistsUserByUsernameMongoV1_5('+84845469282');
    }

    // async sendMail(): Promise<any> {
    //    return this.mailerService.sendMailToSupporter(1);
    // }

    async validateUser(username: string, password: string): Promise<any> {
        const user = await this.checkExistsUserByUsername(username);
        const md5Salt = md5(password + user.salt);
        if (user && user.hashpwd === md5Salt) {
            const { hashpwd, salt, ...result } = user;
            return result;
        }
        return null;
    }

    async loginUMC(user: any) {
        const userId = user.id;
        const payload = { email: user.username, sub: userId };
        // kiểm tra xem có tồn tại session hay chưa
        const findSession = await this.sessionService.checkExistsUMCSessionByUserID(userId);
        const session = {
            user_id: userId,
            access_token: '',
        };
        if (findSession) {
            session.access_token = findSession.access_token;
        } else {
            /* tiến hành tạo session */
            const accessToken = md5(moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS'));
            session.access_token = accessToken;
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await this.sessionService.insertUMC({
                user_id: userId,
                access_token: accessToken,
                date_create: currentTime,
                date_update: currentTime,
            });
        }
        return {
            jwt: this.jwtService.sign(payload),
            username: user.username,
            ...session,
        };
    }

    async loginThuDuc(user: any) {
        const userId = user.id;
        const payload = { email: user.username, sub: userId };
        // kiểm tra xem có tồn tại session hay chưa
        const findSession = await this.sessionService.checkExistsThuDucSessionByUserID(userId);
        const session = {
            user_id: userId,
            access_token: '',
        };
        if (findSession) {
            session.access_token = findSession.access_token;
        } else {
            /* tiến hành tạo session */
            const accessToken = md5(moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS'));
            session.access_token = accessToken;
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await this.sessionService.insertThuDuc({
                user_id: userId,
                access_token: accessToken,
                date_create: currentTime,
                date_update: currentTime,
            });
        }
        return {
            jwt: this.jwtService.sign(payload),
            username: user.username,
            ...session,
        };
    }

    async autoGenerateJWT(username: string, userId: number): Promise<any> {
        const payload = { email: username, sub: userId };
        return {
            jwt: this.jwtService.sign(payload),
        };
    }

    async loginWithUserIdAccessToken(loginWithUserAccessTokenDTO: LoginWithUserAccessTokenDTO): Promise<any> {
        const userId = loginWithUserAccessTokenDTO.userId;
        /* tìm lại thông tin user_id */
        const userInfo = await this.getUserInfoByUserId(userId);
        if (!userInfo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        const findSession = await this.sessionService.checkExistsUMCSessionByUserID(userId);
        const session = {
            user_id: userId,
            access_token: '',
        };

        if (findSession) {
            session.access_token = findSession.access_token;
        } else {
            /* tiến hành tạo session */
            const accessToken = md5(moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS'));
            session.access_token = accessToken;
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await this.sessionService.insertUMC({
                user_id: userId,
                access_token: accessToken,
                date_create: currentTime,
                date_update: currentTime,
            });
        }

        const payload = { email: userInfo.username, sub: userId };
        return {
            jwt: this.jwtService.sign(payload),
        };
    }

    async userBusy(userId: number): Promise<any> {
        const resposneData = (await this.callTeleMedpro(userId).toPromise()).data;
        return {
            data: resposneData,
        };
    }

    callTeleMedpro(userID: number): Observable<AxiosResponse<any>> {
        return this.httpService.get(`http://telemed.medpro.com.vn/telemed/userBusy/${userID}`);
    }

    async loginChoRay(user: any) {
        const userId = user.id;
        const payload = { email: user.username, sub: userId };
        // kiểm tra xem có tồn tại session hay chưa
        const findSession = await this.sessionService.checkExistsChoRaySessionByUserID(userId);
        const session = {
            user_id: userId,
            access_token: '',
        };
        if (findSession) {
            session.access_token = findSession.access_token;
        } else {
            /* tiến hành tạo session */
            const accessToken = md5(moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS'));
            session.access_token = accessToken;
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            await this.sessionService.insertChoRay({
                user_id: userId,
                access_token: accessToken,
                date_create: currentTime,
                date_update: currentTime,
            });
        }
        return {
            jwt: this.jwtService.sign(payload),
            username: user.username,
            ...session,
        };
    }

    async getUserInfoByUserId(userId: number): Promise<any> {
        return this.pkhPatientKnex('user')
            .select('id', 'username', 'fullname')
            .where('id', userId)
            .first();
    }

    async md5(): Promise<any> {
        return md5('pkh@123' + 't7djz7i4343v3p7o');
    }

    async autoGenerateUUIDV4(): Promise<any> {
        return uuid.v4().replace(/-/g, '');
    }

    async testUpdated(): Promise<any> {
        return await this.signInProvider
            .findOneAndUpdate({ _id: '5f90f19eb263b500197a3c20' }, { updatedAt: moment().toDate() }, { new: true })
            .exec();
    }

    async checkSMSCodeWithSignInProvider(partnerId: string, formData: CheckSMSCodeSignInProviderDTO, appId: string): Promise<any> {
        const env = this.urlConfigService.getEnv();
        let userInfo = { username: '', error_code: '', error_message: '' };
        if (env !== 'PRODUCTION') {
            const yourphone = `${formData.phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            /* verify code */
            const verifyCode = formData.phone.slice(-6);
            if (verifyCode === formData.code) {
                userInfo.username = yourphone;
            } else {
                throw new HttpException('Không tìm thấy mã code. Vui lòng kiểm tra lại!', HttpStatus.BAD_REQUEST);
            }
        } else {
            if (!this.isActiveMessageHub) {
                userInfo = (await this.checkCodeSMSBrandName(partnerId, formData).toPromise()).data;
            } else {
                const verifyInfo = { partnerId, appId, phone: formData.phone, otpCode: formData.code };
                userInfo = await this.smsService.verifyOTP(verifyInfo);
            }
        }
        if (userInfo.username) {
            const payload = { ...formData, username: userInfo.username };
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            const secretKey = jwt.sign({ ...payload }, jwtOptions.secret, jwtOptions.signOptions);
            return {
                secretKey,
            };
        } else {
            throw new HttpException('Mã OTP không chính xác!', HttpStatus.BAD_REQUEST);
        }
    }

    async socialRegister(formData: SocialRegisterDTO, appId: string, partnerId: string): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let formDataVerify: any = {};
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            formDataVerify = jwtVerify;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }

        let showName = '';
        // const userName = formDataVerify.username;
        const userName = `${formDataVerify.username}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        let result: any = {};
        if (formData.type === 'zalo') {
            const zaloInfo = await this.getZaloInfoAfterVerified(formData.token, partnerId);
            const getName = get(zaloInfo, 'name', '');
            if (getName) {
                showName = getName;
            }
            const checkProviderZaloUniq = await this.signInProvider
                .findOne({ type: formData.type, email: `zl_${zaloInfo.id}`, method: formData.type })
                .exec();
            if (checkProviderZaloUniq) {
                /* Kiểm tra thông tin userName */
                const userZaloCheck = await this.userModel.findById({ _id: checkProviderZaloUniq.user }).exec();
                if (userZaloCheck && userZaloCheck.username !== userName) {
                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                }
            }

            const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(userName);
            /* kiểm tra xem username là số điện thoại đã được đăng ký hay chưa */
            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd('123123123', userSalt);

            result = checkUserMongo;
            try {
                if (!checkUserMongo) {
                    /* Tiến hành lưu user constraints trước */
                    const newUserConstraints = new this.constraintUser({
                        medproId: `mp${userName}`,
                    });
                    await newUserConstraints.save();

                    /* Tạo mới thông tin user trên mongo */
                    const userMongo = new this.userModel({
                        username: userName,
                        password: userHashPwd,
                        salt: userSalt,
                        email: userName,
                        fullname: '',
                        medproId: `mp${userName}`,
                    });
                    result = await userMongo.save();
                }
            } catch (error) {
                console.log(error);
                result = await this.checkExistsUserByUsernameMedproId(userName);
                this.clientSentry.instance().captureException(error);
            }

            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    user: result._id,
                })
                .exec();

            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `zl_${zaloInfo.id}`,
            });
            await newProviderConstraints.save().catch(async errorDetail => {
                const errorCode = errorDetail.code;
                switch (errorCode) {
                    case 11000:
                        const checkProviderZaloUniqa = await this.signInProvider
                            .findOne({ type: formData.type, email: `zl_${zaloInfo.id}`, method: formData.type })
                            .exec();
                        if (checkProviderZaloUniqa) {
                            /* Kiểm tra thông tin userName */
                            const userZaloCheck = await this.userModel.findById({ _id: checkProviderZaloUniqa.user }).exec();
                            if (userZaloCheck && userZaloCheck.username !== userName) {
                                throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                            }
                        }
                        throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
                    default:
                        throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                }
            });

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: formData.type,
                email: `zl_${zaloInfo.id}`,
                user: result._id,
                method: 'zalo',
                displayName: showName,
            });
            await signInProvider.save();
        } else if (formData.type === 'firebase') {
            const { userName: firebaseId, signInProvider: method, displayName } = await this.getFirebaseAfterVerified(formData.token, partnerId);
            const checkProviderUniq = await this.signInProvider.findOne({ type: formData.type, email: firebaseId, method }).exec();
            if (checkProviderUniq) {
                /* Kiểm tra thông tin userName */
                const userCheck = await this.userModel.findById({ _id: checkProviderUniq.user }).exec();
                if (userCheck && userCheck.username !== userName) {
                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                }
            }

            const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(userName);
            /* kiểm tra xem username là số điện thoại đã được đăng ký hay chưa */
            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd('123123123', userSalt);

            result = checkUserMongo;
            if (!checkUserMongo) {
                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: userName,
                    fullname: '',
                    medproId: `mp${userName}`,
                });
                result = await userMongo.save();
            }

            showName = displayName;
            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    method,
                    user: result._id,
                })
                .exec();

            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `${firebaseId}_${method}`,
            });
            await newProviderConstraints.save().catch(async errorDetail => {
                const errorCode = errorDetail.code;
                switch (errorCode) {
                    case 11000:
                        const checkProviderUniqaa = await this.signInProvider.findOne({ type: formData.type, email: firebaseId, method }).exec();
                        if (checkProviderUniqaa) {
                            /* Kiểm tra thông tin userName */
                            const userCheck = await this.userModel.findById({ _id: checkProviderUniqaa.user }).exec();
                            if (userCheck && userCheck.username !== userName) {
                                throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                            }
                        }
                        throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
                    default:
                        throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                }
            });

            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: formData.type,
                email: firebaseId,
                user: result._id,
                method,
                displayName: showName,
            });
            await signInProvider.save();
        }
        /* trả về thông tin token */
        const payload = { username: '', sub: 0, userMongoId: result._id };
        // sync user
        try {
            await this.getUserIdAndCreateSessionV1(appId, partnerId, result);
        } catch (error) {
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
            // this.clientSentry.instance().captureException(error);
        }

        const token = this.jwtService.sign(payload);
        const resultData = {
            userId: result._id,
            userName: result.username,
            fullName: showName,
            number: '',
            email: '',
            token,
            historyBookingCount: 0,
            patientCount: 0,
        };
        return { ...resultData, token };
    }

    async applySignInProviderV2(partnerId: string, formData: ApplySignInProviderV2DTO, appId: string = ''): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let formDataVerify: any = {};
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            formDataVerify = jwtVerify;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(formData.password, userSalt);
        /* tạo thông tin user và sign_in_provider */
        // const userName = formDataVerify.username;
        const userName = `${formDataVerify.username}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        const fullname = formData.fullname ? formData.fullname : formDataVerify.fullname;

        /* kiểm tra xem username là số điện thoại đã được đăng ký hay chưa */
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(userName);
        let result: IUser = checkUserMongo;

        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                });
                await newUserConstraints.save();

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: userName,
                    fullname,
                    medproId: `mp${userName}`,
                });
                result = await userMongo.save();
            }
        } catch (error) {
            console.log(error);
            result = await this.checkExistsUserByUsernameMedproId(userName);
            this.clientSentry.instance().captureException(error);
        }

        /* kiểm tra type */
        if (formData.type === 'password') {
            /* xử lý thông tin password */
            /* kiểm tra xem phương thức gửi lên đã có chưa */
            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    user: result._id,
                })
                .exec();

            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            try {
                /* Tiến hành lưu user constraints trước */
                const newProviderConstraints = new this.constraintProvider({
                    providerId: `mp${userName}_password`,
                });
                await newProviderConstraints.save();

                /* tiến hành tạo thêm thông tin trong sign_in_provider */
                const signInProvider = new this.signInProvider({
                    id: uuid.v4().replace(/-/g, ''),
                    type: formData.type,
                    password: userHashPwd,
                    salt: userSalt,
                    user: result._id,
                    displayName: fullname,
                });
                await signInProvider.save();
                const createTime = moment().toDate();
                /* tiến hành cập nhật thông tin mã cán bộ */
                const newUserProfile = new this.userProfileModel({
                    id: uuid.v4().replace(/-/g, ''),
                    user: result._id,
                    partnerId,
                    attribute_ids_empId: {
                        value: formData.employeeId,
                        createTime,
                        updateTime: createTime,
                    },
                });
                await newUserProfile.save();
            } catch (error) {
                console.log(error);
                // this.clientSentry.instance().captureException(error);
            }
        } /* trả về thông tin token */
        const payload = { username: '', sub: 0, userMongoId: result._id };
        // sync user
        try {
            await this.getUserIdAndCreateSessionV1(appId, partnerId, result);
        } catch (error) {
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
            // this.clientSentry.instance().captureException(error);
        }

        const token = this.jwtService.sign(payload);
        const resultData = {
            userId: result._id,
            userName: result.username,
            fullName: fullname,
            number: '',
            email: '',
            token,
            historyBookingCount: 0,
            patientCount: 0,
        };
        return { ...resultData, token };
    }

    async applySignInProviderV3(partnerId: string, formData: ApplySignInProviderV3DTO, appId: string = ''): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let formDataVerify: any = {};
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            formDataVerify = jwtVerify;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(formData.password, userSalt);
        /* tạo thông tin user và sign_in_provider */
        // const userName = formDataVerify.username;
        const userName = `${formDataVerify.username}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        const fullname = formData.fullname ? formData.fullname : formDataVerify.fullname;

        /* kiểm tra xem username là số điện thoại đã được đăng ký hay chưa */
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(userName);
        let result: IUser = checkUserMongo;

        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                });
                await newUserConstraints.save();

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: userName,
                    fullname,
                    medproId: `mp${userName}`,
                });
                result = await userMongo.save();
            }
        } catch (error) {
            console.log(error);
            result = await this.checkExistsUserByUsernameMedproId(userName);
            this.clientSentry.instance().captureException(error);
        }

        /* kiểm tra type */
        if (formData.type === 'password') {
            /* xử lý thông tin password */
            /* kiểm tra xem phương thức gửi lên đã có chưa */
            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    user: result._id,
                })
                .exec();

            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            try {
                /* Tiến hành lưu user constraints trước */
                const newProviderConstraints = new this.constraintProvider({
                    providerId: `mp${userName}_password`,
                });
                await newProviderConstraints.save();

                /* tiến hành tạo thêm thông tin trong sign_in_provider */
                const signInProvider = new this.signInProvider({
                    id: uuid.v4().replace(/-/g, ''),
                    type: formData.type,
                    password: userHashPwd,
                    salt: userSalt,
                    user: result._id,
                    displayName: fullname,
                });
                await signInProvider.save();
                const createTime = moment().toDate();
                const objAvatar: any = {};
                if (formData.avatar) {
                    objAvatar.avatarId = formData.avatar;
                }
                /* tiến hành cập nhật thông tin mã cán bộ */
                const newUserProfile = new this.userProfileModel({
                    id: uuid.v4().replace(/-/g, ''),
                    user: result._id,
                    partnerId,

                    birthdate: moment(formData.birthdate)
                        .add(7, 'hours')
                        .format('YYYY-MM-DD'),
                    birthyear: moment(formData.birthdate).isValid()
                        ? Number(
                              moment(formData.birthdate)
                                  .add(7, 'hours')
                                  .format('YYYY'),
                          )
                        : formData.birthyear,
                    sex: formData.sex,
                    email: formData.email,
                    professionId: formData.professionId,
                    positionId: formData.positionId,

                    attribute_ids_empId: {
                        value: formData.employeeId,
                        createTime,
                        updateTime: createTime,
                    },
                    ...objAvatar,
                });
                await newUserProfile.save();
            } catch (error) {
                console.log(error);
                // this.clientSentry.instance().captureException(error);
            }
        } /* trả về thông tin token */
        const payload = { username: '', sub: 0, userMongoId: result._id };
        // sync user
        try {
            await this.getUserIdAndCreateSessionV1(appId, partnerId, result);
        } catch (error) {
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
            // this.clientSentry.instance().captureException(error);
        }

        const token = this.jwtService.sign(payload);
        const resultData = {
            userId: result._id,
            userName: result.username,
            fullName: fullname,
            number: '',
            email: '',
            token,
            historyBookingCount: 0,
            patientCount: 0,
        };
        return { ...resultData, token };
    }

    async checkUserUpgradeMedpro11(
        partnerId: string,
        user: IUser,
        formData: ApplySignInProviderDTO,
        formDataVerify: any,
        userSalt: string,
        userHashPwd: string,
        fullname: string = '',
        appId: string = '',
    ): Promise<any> {
        // console.log('formDataVerify', formDataVerify);

        // format user name
        formDataVerify.username = this.utilService.transformPhone(formDataVerify.username);

        const objReset: any = {};
        if (partnerId === 'trungvuong') {
            objReset.tvUser = null;
        }

        if (partnerId === 'dalieuhcm') {
            objReset.daLieuUser = null;
        }

        if (partnerId === 'nhidong1') {
            objReset.nd1User = null;
        }

        if (partnerId === 'ctchhcm') {
            objReset.ctchUser = null;
        }

        if (partnerId === 'umc') {
            objReset.umcUser = null;
        }

        if (formData.type === 'password') {
            const signInProviderUpdate = await this.signInProvider
                .findOneAndUpdate(
                    {
                        type: formData.type,
                        user: user._id,
                    },
                    {
                        password: userHashPwd,
                        salt: userSalt,
                        fullname,
                    },
                    { new: true },
                )
                .exec();
            /* cập nhật lại phần user đã nâng cấp ok */
            const updateUser = await this.userModel
                .findByIdAndUpdate(
                    {
                        _id: user._id,
                    },
                    {
                        // tvUser: null,
                        ...objReset,
                        fullname,
                        nextStep: 'VERIFIED',
                    },
                    { new: true },
                )
                .exec();
        } else if (formData.type === 'zalo') {
            const zaloInfoUniq = await this.getZaloInfoAfterVerified(formData.token, partnerId);
            const checkProviderZaloUniq = await this.signInProvider
                .findOne({ type: formData.type, email: `zl_${zaloInfoUniq.id}`, method: formData.type })
                .exec();
            /* lấy thông tin zalo provider */
            // console.log('checkProviderZaloUniq', checkProviderZaloUniq);
            if (checkProviderZaloUniq) {
                /* Kiểm tra thông tin userName */
                const userZaloCheck = await this.userModel.findById({ _id: checkProviderZaloUniq.user }).exec();
                // console.log('userZaloCheck', userZaloCheck);
                // console.log('user', user);
                if (userZaloCheck && userZaloCheck.username !== user.username) {
                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                }
                /* Tiếp tục kiểm tra số điện thoại nhập lên đã tồn tại chưa */
                // console.log('formDataVerify.username', formDataVerify.username);
                const checkUserMongoAgain: IUser = await this.checkExistsUserByUsernameMedproId(formDataVerify.username);
                // console.log('checkUserMongoAgain', checkUserMongoAgain);
                if (checkUserMongoAgain) {
                    /* Tiến hành merge thông tin user, patient, booking */
                    // console.log('tồn tại số điện thoại này rồi. tiến hành merge patient, booking');
                    const newMergeUser = await this.mergeUserMedproId(user, checkUserMongoAgain);
                    /* tiến hành cập nhật lại booking */
                    await this.bookingModel
                        .updateMany(
                            {
                                userId: user._id,
                            },
                            {
                                userId: checkUserMongoAgain._id,
                                prevUserId: user._id,
                            },
                        )
                        .exec();

                    const updateUser = await this.userModel
                        .findByIdAndUpdate(
                            {
                                _id: user._id,
                            },
                            {
                                // tvUser: null,
                                ...objReset,
                                nextStep: 'VERIFIED',
                            },
                            { new: true },
                        )
                        .exec();

                    /* gán lại user => next user */
                    user = newMergeUser;
                } else {
                    /* formDataVerify.username chua ton tai trong he thong */
                    // console.log('chạy vao buoc nay la do chua tim thay sdt ', formDataVerify.username);
                    /* tao user constraints */
                    const newUserConstraints = new this.constraintUser({
                        medproId: `mp${formDataVerify.username}`,
                    });
                    await newUserConstraints.save();
                    /* Tiến hành cập nhật số điện thoại */
                    const updateUser = await this.userModel
                        .findByIdAndUpdate(
                            {
                                _id: user._id,
                            },
                            {
                                username: formDataVerify.username,
                                medproId: `mp${formDataVerify.username}`,
                                // tvUser: null,
                                ...objReset,
                                nextStep: 'VERIFIED',
                            },
                            { new: true },
                        )
                        .exec();
                }
            } else {
                /* Tiến hành lưu user constraints trước */
                const newProviderConstraints = new this.constraintProvider({
                    providerId: `zl_${zaloInfoUniq.id}`,
                });
                await newProviderConstraints.save().catch(async errorDetail => {
                    const errorCode = errorDetail.code;
                    switch (errorCode) {
                        case 11000:
                            const checkProviderZalo = await this.signInProvider
                                .findOne({ type: formData.type, email: `zl_${zaloInfoUniq.id}`, method: formData.type })
                                .exec();
                            if (checkProviderZalo) {
                                /* Kiểm tra thông tin userName */
                                const userZaloCheck = await this.userModel.findById({ _id: checkProviderZalo.user }).exec();
                                if (userZaloCheck && userZaloCheck.username !== user.username) {
                                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                                }
                            }
                            throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
                        default:
                            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                    }
                });

                /* tiến hành tạo thêm thông tin trong sign_in_provider */
                const signInProvider = new this.signInProvider({
                    id: uuid.v4().replace(/-/g, ''),
                    type: formData.type,
                    email: `zl_${zaloInfoUniq.id}`,
                    user: user._id,
                    method: 'zalo',
                    displayName: fullname,
                });
                await signInProvider.save();

                /* Tiến hành cập nhật số điện thoại */
                const updateUser = await this.userModel
                    .findByIdAndUpdate(
                        {
                            _id: user._id,
                        },
                        {
                            username: formDataVerify.username,
                            // tvUser: null,
                            ...objReset,
                            nextStep: 'VERIFIED',
                        },
                        { new: true },
                    )
                    .exec();
            }
        } else if (formData.type === 'firebase') {
            // console.log('khach hàng firebase');
            const { userName: firebaseUniq, signInProvider: methodSignIn } = await this.getFirebaseAfterVerified(formData.token, partnerId);
            const checkProviderUniq = await this.signInProvider.findOne({ type: formData.type, email: firebaseUniq, method: methodSignIn }).exec();
            // console.log('chay vao toi cho nay fb ', checkProviderUniq);
            if (checkProviderUniq) {
                /* Kiểm tra thông tin userName */
                const userCheck = await this.userModel.findById({ _id: checkProviderUniq.user }).exec();
                // console.log('userCheck', userCheck);
                // console.log('user', user);
                if (userCheck && userCheck.username !== user.username) {
                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                }
                /* Tiếp tục kiểm tra số điện thoại nhập lên đã tồn tại chưa */
                const checkUserMongoAgain: IUser = await this.checkExistsUserByUsernameMedproId(formDataVerify.username);
                if (checkUserMongoAgain) {
                    // console.log('fb tiến hành merge to', checkUserMongoAgain);
                    // console.log('fb merge from ', user);
                    /* Tiến hành merge thông tin user, patient, booking */
                    const newMergeUser = await this.mergeUserMedproId(user, checkUserMongoAgain);
                    /* tiến hành cập nhật lại booking */
                    await this.bookingModel
                        .updateMany(
                            {
                                userId: user._id,
                            },
                            {
                                userId: checkUserMongoAgain._id,
                                prevUserId: user._id,
                            },
                        )
                        .exec();

                    const updateUser = await this.userModel
                        .findByIdAndUpdate(
                            {
                                _id: user._id,
                            },
                            {
                                // tvUser: null,
                                ...objReset,
                                nextStep: 'VERIFIED',
                            },
                            { new: true },
                        )
                        .exec();

                    /* gán lại user => next user */
                    user = newMergeUser;
                } else {
                    /* formDataVerify.username chua ton tai trong he thong */
                    /* tao user constraints */
                    const newUserConstraints = new this.constraintUser({
                        medproId: `mp${formDataVerify.username}`,
                    });
                    await newUserConstraints.save();
                    /* Tiến hành cập nhật số điện thoại */
                    const updateUser = await this.userModel
                        .findByIdAndUpdate(
                            {
                                _id: user._id,
                            },
                            {
                                username: formDataVerify.username,
                                medproId: `mp${formDataVerify.username}`,
                                // tvUser: null,
                                ...objReset,
                                nextStep: 'VERIFIED',
                            },
                            { new: true },
                        )
                        .exec();
                }
            } else {
                const newProviderConstraints = new this.constraintProvider({
                    providerId: `${firebaseUniq}_${methodSignIn}`,
                });
                await newProviderConstraints.save().catch(async errorDetail => {
                    const errorCode = errorDetail.code;
                    switch (errorCode) {
                        case 11000:
                            const checkProviderUniq2 = await this.signInProvider
                                .findOne({ type: formData.type, email: firebaseUniq, method: methodSignIn })
                                .exec();
                            if (checkProviderUniq2) {
                                /* Kiểm tra thông tin userName */
                                const userCheck = await this.userModel.findById({ _id: checkProviderUniq2.user }).exec();
                                if (userCheck && userCheck.username !== user.username) {
                                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                                }
                            }
                            throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
                        default:
                            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                    }
                });

                const signInProvider = new this.signInProvider({
                    id: uuid.v4().replace(/-/g, ''),
                    type: formData.type,
                    email: firebaseUniq,
                    user: user._id,
                    method: methodSignIn,
                    displayName: fullname,
                });
                await signInProvider.save();

                /* Tiến hành cập nhật số điện thoại */
                const updateUser = await this.userModel
                    .findByIdAndUpdate(
                        {
                            _id: user._id,
                        },
                        {
                            username: formDataVerify.username,
                            // tvUser: null,
                            ...objReset,
                            nextStep: 'VERIFIED',
                        },
                        { new: true },
                    )
                    .exec();
            }
        }
        /* Tiến hành tạo token đang nhập */
        /* trả về thông tin token */
        const payload = { username: '', sub: 0, userMongoId: user._id };
        // sync user
        try {
            await this.getUserIdAndCreateSessionV1(appId, partnerId, user);
        } catch (error) {
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
            this.clientSentry.instance().captureException(error);
        }

        const token = this.jwtService.sign(payload);
        const resultData = {
            userId: user._id,
            userName: user.username,
            fullName: fullname,
            number: '',
            email: '',
            token,
            historyBookingCount: 0,
            patientCount: 0,
        };
        return { ...resultData, token };
    }

    async saveUserApp(formData: UserAppDto): Promise<any> {
        // handle for update user apps if this is first time user register medpro
        const { username, partnerId, appId, type, methodId } = formData;
        try {
            const userApp = await this.userAppModel.exists({ username, partnerId, appId, type });
            if (!userApp) {
                await this.userAppModel.create(formData);
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'saveUserApp',
                summary: 'saveUserApp',
                nameParent: 'saveUserApp',
                params: { ...formData },
                errorBody: this.utilService.errorHandler(error),
                message: `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại`,
            });
            console.error(error);
        }
    }

    async applySignInProvider(formData: ApplySignInProviderDTO, appId: string, partnerId: string): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let formDataVerify: any = {};
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            formDataVerify = jwtVerify;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(formData.password, userSalt);
        /* tạo thông tin user và sign_in_provider */
        // const userName = formDataVerify.username;
        const userName = `${formDataVerify.username}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        let fullname = formData.fullname ? formData.fullname : formDataVerify.fullname;
        // console.log('vo toi cho nay ko ne');
        /* kiểm tra xem username là số điện thoại đã được đăng ký hay chưa */
        let checkUserMongo: IUser;
        if (formData.type === 'zalo' || formData.type === 'firebase') {
            if (formData.type === 'zalo') {
                const reCheckZalo = await this.getZaloInfoAfterVerified(formData.token, partnerId);
                const getName = get(reCheckZalo, 'name', '');
                if (getName) {
                    fullname = getName;
                }
                checkUserMongo = await this.checkExistsUserByUsernameMedproId(`zl_${reCheckZalo.id}`);
            } else {
                const { userName: firebaseUniqCheck, displayName: nameSocial } = await this.getFirebaseAfterVerified(formData.token, partnerId);
                if (!!nameSocial) {
                    fullname = nameSocial;
                }
                checkUserMongo = await this.checkExistsUserByUsernameMedproId(firebaseUniqCheck);
            }
            /* không có thì tiếp tục kiểm tra tiếp */
            if (!checkUserMongo) {
                checkUserMongo = await this.checkExistsUserByUsernameMedproId(userName);
            }
        } else {
            checkUserMongo = await this.checkExistsUserByUsernameMedproId(userName);
        }

        let result: IUser = checkUserMongo;
        /* kiểm tra xem user medpro 1.0 hay ko */
        const tvUser = get(result, 'tvUser', null);
        if (tvUser && result) {
            return this.checkUserUpgradeMedpro11(partnerId, result, formData, formDataVerify, userSalt, userHashPwd, fullname, appId); // return in method
        }

        const daLieuUser = get(result, 'daLieuUser', null);
        if (daLieuUser && result) {
            return this.checkUserUpgradeMedpro11(partnerId, result, formData, formDataVerify, userSalt, userHashPwd, fullname, appId); // return in method
        }

        const nd1User = get(result, 'nd1User', null);
        if (nd1User && result) {
            return this.checkUserUpgradeMedpro11(partnerId, result, formData, formDataVerify, userSalt, userHashPwd, fullname, appId); // return in method
        }

        const umcUser = get(result, 'umcUser', null);
        if (umcUser && result) {
            return this.checkUserUpgradeMedpro11(partnerId, result, formData, formDataVerify, userSalt, userHashPwd, fullname, appId); // return in method
        }

        const ctchUser = get(result, 'ctchUser', null);
        if (ctchUser && result) {
            return this.checkUserUpgradeMedpro11(partnerId, result, formData, formDataVerify, userSalt, userHashPwd, fullname, appId); // return in method
        }

        /* kiểm tra xem tài khoản này đang liên kết thế nào */
        if (formData.type === 'zalo') {
            const zaloInfoUniq = await this.getZaloInfoAfterVerified(formData.token, partnerId);
            const checkProviderZaloUniq = await this.signInProvider
                .findOne({ type: formData.type, email: `zl_${zaloInfoUniq.id}`, method: formData.type })
                .exec();
            if (checkProviderZaloUniq) {
                /* Kiểm tra thông tin userName */
                const userZaloCheck = await this.userModel.findById({ _id: checkProviderZaloUniq.user }).exec();
                if (userZaloCheck && userZaloCheck.username !== userName) {
                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                }
            }
        } else if (formData.type === 'firebase') {
            const { userName: firebaseUniq, signInProvider: methodSignIn, displayName: nameSocial } = await this.getFirebaseAfterVerified(
                formData.token,
                partnerId,
            );
            if (!!nameSocial) {
                fullname = nameSocial;
            }
            const checkProviderUniq = await this.signInProvider.findOne({ type: formData.type, email: firebaseUniq, method: methodSignIn }).exec();
            if (checkProviderUniq) {
                /* Kiểm tra thông tin userName */
                const userCheck = await this.userModel.findById({ _id: checkProviderUniq.user }).exec();
                if (userCheck && userCheck.username !== userName) {
                    throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                }
            }
        }
        // for user app
        let methodId: string;
        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                });
                await newUserConstraints.save();
                const { surname, name } = this.utilService.splitName(fullname);
                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: formData.email ? formData.email : '',
                    fullname,
                    surname, 
                    name,
                    medproId: `mp${userName}`,
                });
                result = await userMongo.save();
            }
        } catch (error) {
            console.log(error);
            result = await this.checkExistsUserByUsernameMedproId(userName);
            this.clientSentry.instance().captureException(error);
        }

        /* kiểm tra type */
        if (formData.type === 'password') {
            /* xử lý thông tin password */
            /* kiểm tra xem phương thức gửi lên đã có chưa */
            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    user: result._id,
                })
                .exec();
            // console.log('aaaaa', checkProvider);
            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            try {
                /* Tiến hành lưu user constraints trước */
                const newProviderConstraints = new this.constraintProvider({
                    providerId: `mp${userName}_password`,
                });
                await newProviderConstraints.save();

                /* tiến hành tạo thêm thông tin trong sign_in_provider */
                const signInProvider = new this.signInProvider({
                    id: uuid.v4().replace(/-/g, ''),
                    type: formData.type,
                    password: userHashPwd,
                    salt: userSalt,
                    user: result._id,
                    displayName: fullname,
                });
                await signInProvider.save();
                methodId = signInProvider.method;
            } catch (error) {
                console.log(error);
                this.clientSentry.instance().captureException(error);
            }
        } else if (formData.type === 'zalo') {
            const zaloInfo = await this.getZaloInfoAfterVerified(formData.token, partnerId);
            const getName = get(zaloInfo, 'name', '');
            if (getName) {
                fullname = getName;
            }
            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    user: result._id,
                    email: `zl_${zaloInfo.id}`,
                    method: formData.type,
                })
                .exec();

            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `zl_${zaloInfo.id}`,
            });
            await newProviderConstraints.save().catch(async errorDetail => {
                const errorCode = errorDetail.code;
                switch (errorCode) {
                    case 11000:
                        const checkProviderZaloUniq = await this.signInProvider
                            .findOne({ type: formData.type, email: `zl_${zaloInfo.id}`, method: formData.type })
                            .exec();
                        if (checkProviderZaloUniq) {
                            /* Kiểm tra thông tin userName */
                            const userZaloCheck = await this.userModel.findById({ _id: checkProviderZaloUniq.user }).exec();
                            if (userZaloCheck && userZaloCheck.username !== userName) {
                                throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                            }
                        }
                        throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
                    default:
                        throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                }
            });

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: formData.type,
                email: `zl_${zaloInfo.id}`,
                user: result._id,
                method: 'zalo',
                displayName: fullname,
            });
            methodId = signInProvider.method;
            await signInProvider.save();
        } else if (formData.type === 'firebase') {
            const { userName: firebaseId, signInProvider: method } = await this.getFirebaseAfterVerified(formData.token, partnerId);

            const checkProvider = await this.signInProvider
                .findOne({
                    type: formData.type,
                    email: firebaseId,
                    method,
                    user: result._id,
                    // displayName: fullname,
                })
                .exec();

            if (checkProvider) {
                throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
            }

            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `${firebaseId}_${method}`,
            });
            await newProviderConstraints.save().catch(async errorDetail => {
                const errorCode = errorDetail.code;
                switch (errorCode) {
                    case 11000:
                        const checkProviderUniq = await this.signInProvider.findOne({ type: formData.type, email: firebaseId, method }).exec();
                        if (checkProviderUniq) {
                            /* Kiểm tra thông tin userName */
                            const userCheck = await this.userModel.findById({ _id: checkProviderUniq.user }).exec();
                            if (userCheck && userCheck.username !== userName) {
                                throw new HttpException('Tài khoản này đã liên kết với số điện thoại khác.', HttpStatus.CONFLICT);
                            }
                        }
                        throw new HttpException('Bạn đã đăng ký phương thức đăng nhập này.', HttpStatus.BAD_REQUEST);
                    default:
                        throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
                }
            });

            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: formData.type,
                email: firebaseId,
                user: result._id,
                method,
                displayName: fullname,
            });
            methodId = signInProvider.method;
            await signInProvider.save();
        }
        /* trả về thông tin token */
        const payload = { username: '', sub: 0, userMongoId: result._id };
        // sync user
        try {
            await this.getUserIdAndCreateSessionV1(appId, partnerId, result);
        } catch (error) {
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
            this.clientSentry.instance().captureException(error);
        }
        /* Tiến hành thêm mã giới thiệu */
        const referralCode = (formData?.referralCode || '').toUpperCase();
        if (referralCode) {
            /* Kiếm tra xem mã giới thiệu này có hợp lệ hay ko */
            const existsReferralCode = await this.referralCodeService.checkExistsReferralCode(referralCode, partnerId);

            if (existsReferralCode) {
                const objInsert = {
                    partnerId,
                    appId,
                    userId: result._id,
                    referralCode,
                    uKey: `${result._id}`,
                    createDate: moment().toDate(),
                };
                // console.log('refer', objInsert);
                const registerCode = new this.referralCodeRegisterModel(objInsert);

                await registerCode.save().catch(error => {
                    console.log(error);
                });
            }
        }
        const token = this.jwtService.sign(payload);
        // save user-profiles if request data have extra field
        const { birthyear, birthdate, sex, email } = formData;
        let userProfileInfo: any = {};
        try {
            userProfileInfo = {
                partnerId: 'medpro',
                ...(birthyear && { birthyear }),
                ...(birthdate && { birthdate }),
                ...(sex && { sex }),
                ...(email && { email }),
            };
            const checkCreateAccepted = await this.globalSettingService.findByKeyAndRepoName(this.SAVE_USER_PROFILE_APPLY_SIGNIN_PROVIDER);
            if (checkCreateAccepted === 'ON') {
                const checkUserProfile = await this.userProfileModel
                    .findOne({ user: result._id })
                    .lean()
                    .exec();
                if (checkUserProfile) {
                    await this.userProfileModel.findOneAndUpdate({ user: result._id }, { ...userProfileInfo }).exec();
                } else {
                    await this.userProfileModel.create({ user: result._id, ...userProfileInfo });
                }
            }
        } catch (error) {
            console.error(error);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'create or update userProfile',
                summary: 'Bổ sung userProfile',
                nameParent: 'applySignInProvider',
                params: { userId: result._id, ...userProfileInfo },
                errorBody: this.utilService.errorHandler(error),
                message: `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại`,
            });
        }
        const userAppInfo = { username: userName, type: formData.type, methodId, appId, partnerId };
        await this.saveUserApp(userAppInfo);
        const resultData = {
            userId: result._id,
            userMongoId: result._id,
            userName: result.username,
            fullName: fullname,
            number: '',
            email: '',
            token,
            historyBookingCount: 0,
            patientCount: 0,
        };
        return { ...resultData, token };
    }

    async findReferralCodeByUserId(userId: string): Promise<any> {
        return this.referralCodeRegisterModel.findOne({ uKey: userId }).exec();
    }

    async phoneForgotPassword(appId: string, partnerId: string, formData: ForgotPasswordDTO): Promise<any> {
        // const yourphone = `${formData.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        // const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(yourphone);
        // if (!checkUserMongo) {
        //     throw new HttpException(`Số điện thoại ${yourphone} này chưa đăng ký.`, HttpStatus.NOT_FOUND);
        // }

        if (formData.captchaResponse) {
            if (formData.captchaType === 'cloudflare') {
                await this.verifyRecaptchaCloudflare({ captchaResponse: formData.captchaResponse });
            } else {
                await this.verifyGoogleRecaptcha({ captchaResponse: formData.captchaResponse });
            }
        } else {
            throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
        }

        return this.v1CheckPhone(appId, { deviceId: formData.deviceId, phone: formData.phone }, partnerId);
    }

    async forgotPasswordApplyCode(appId: string, partnerId: string, formData: ForgotPasswordApplyCodeDTO): Promise<any> {
        const env = this.urlConfigService.getEnv();
        let userInfo = { username: '', error_code: '', error_message: '' };
        if (env !== 'PRODUCTION') {
            const yourphone = `${formData.phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            /* verify code */
            const verifyCode = formData.phone.slice(-6);
            if (verifyCode === formData.code) {
                userInfo.username = yourphone;
            } else {
                throw new HttpException('Không tìm thấy mã code. Vui lòng kiểm tra lại!', HttpStatus.BAD_REQUEST);
            }
        } else {
            if (!this.isActiveMessageHub) {
                userInfo = (await this.checkCodeSMSBrandName(appId, formData).toPromise()).data;
            } else {
                const verifyInfo = { partnerId, appId, phone: formData.phone, otpCode: formData.code };
                userInfo = await this.smsService.verifyOTP(verifyInfo);
            }
        }
        if (userInfo.username) {
            /* tiến hành trả về thông tin secret key */
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            /* Kiểm tra lại nếu user chưa đăng ký password thì tạo luôn */
            const yourphone = `${formData.phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            const checkUser: IUser = await this.userModel
                .findOne({
                    username: yourphone,
                    medproId: `mp${yourphone}`,
                })
                .exec();
            let result: IUser;
            if (!checkUser) {
                /* tạo mới user. */
                const userSalt22 = this.generateSalt();
                const userHashPwd222 = this.generateHashPwd('zzzzzz', userSalt22);
                const userMongoInsert = new this.userModel({
                    username: yourphone,
                    password: userHashPwd222,
                    salt: userSalt22,
                    email: yourphone,
                    fullname: '',
                    medproId: `mp${yourphone}`,
                });
                result = await userMongoInsert.save();
            } else {
                result = checkUser;
            }
            const secretKey = jwt.sign(
                {
                    phone: formData.phone,
                },
                jwtOptions.secret,
                jwtOptions.signOptions,
            );
            /* Kiểm tra xem user này có type password hay ko */
            const checkSignInProvider = await this.signInProvider
                .findOne({
                    user: result._id,
                    type: 'password',
                })
                .exec();

            if (checkSignInProvider) {
                return {
                    secretKey,
                    user: {
                        fullname: checkSignInProvider.displayName,
                    },
                };
            } else {
                const userObj = result.toObject();
                const { fullname } = userObj;
                return {
                    secretKey,
                    user: {
                        fullname,
                    },
                };
            }
        } else {
            throw new HttpException('Xác thực không thành công!', HttpStatus.BAD_REQUEST);
        }
    }

    async cskhSetNewPassword(formData: { phone: string; password: string; fullname?: string; secretKey: string }): Promise<any> {
        const getScretKey = await this.globalSettingService.findByKeyAndRepoName('SECRET_KEY_CSKH_SET_NEW_PASSWORD');
        if (getScretKey !== formData.secretKey) {
            throw new HttpException('Vui lòng kiểm tra lại thông tin', HttpStatus.BAD_REQUEST);
        }

        /* lấy được thông tin số phone là username */
        const yourphone = `${formData.phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        /* cập nhật lại fullname nếu cần */
        const checkUUU = await this.userModel
            .findOne({
                username: yourphone,
                medproId: `mp${yourphone}`,
            })
            .exec();

        if (formData.fullname && checkUUU) {
            await this.userModel.findByIdAndUpdate({ _id: checkUUU._id }, { fullname: formData.fullname }).exec();
        }

        if (!checkUUU) {
            throw new HttpException('Không tìm thấy thông tin đăng nhập.', HttpStatus.NOT_FOUND);
        }

        try {
            /* kiểm tra signin provider */
            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd(formData.password, userSalt);

            const findSignInProvide = await this.signInProvider.findOne({ type: 'password', user: checkUUU._id }).exec();
            let resultProvider: ISignInProvider;
            if (!findSignInProvide) {
                /* tiến hành tạo thêm thông tin trong sign_in_provider */
                const signInProvider = new this.signInProvider({
                    id: uuid.v4().replace(/-/g, ''),
                    type: 'password',
                    password: userHashPwd,
                    salt: userSalt,
                    user: checkUUU._id,
                });
                resultProvider = await signInProvider.save();
            } else {
                resultProvider = findSignInProvide;
            }
            /* tiến hành change password */
            await this.signInProvider
                .findByIdAndUpdate(
                    { _id: resultProvider._id },
                    {
                        password: userHashPwd,
                        salt: userSalt,
                    },
                )
                .exec();
            return 'OK';
        } catch (error) {
            this.logger.warn(error);
        }
    }

    async applyNewPassword(formData: ApplyNewPasswordDTO): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let formDataVerify: any = {};
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            formDataVerify = jwtVerify;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        /* lấy được thông tin số phone là username */
        const yourphone = `${formDataVerify.phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        /* cập nhật lại fullname nếu cần */
        const checkUUU = await this.userModel
            .findOne({
                username: yourphone,
                medproId: `mp${yourphone}`,
            })
            .exec();

        if (formData.fullname && checkUUU) {
            await this.userModel.findByIdAndUpdate({ _id: checkUUU._id }, { fullname: formData.fullname }).exec();
        }

        const getUser = await this.userModel
            .findOne({
                username: yourphone,
                medproId: `mp${yourphone}`,
            })
            .exec();

        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin đăng nhập.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra signin provider */
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(formData.password, userSalt);

        const findSignInProvide = await this.signInProvider.findOne({ type: 'password', user: getUser._id }).exec();
        let resultProvider: ISignInProvider;
        if (!findSignInProvide) {
            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: userHashPwd,
                salt: userSalt,
                user: getUser._id,
            });
            resultProvider = await signInProvider.save();
        } else {
            resultProvider = findSignInProvide;
        }
        /* tiến hành change password */
        await this.signInProvider
            .findByIdAndUpdate(
                { _id: resultProvider._id },
                {
                    password: userHashPwd,
                    salt: userSalt,
                },
            )
            .exec();

        /* trả về thông tin đăng nhập */
        const payload = { username: '', sub: 0, userMongoId: getUser._id };
        const token = this.jwtService.sign(payload);
        const resultData = {
            userId: getUser._id,
            userMongoId: getUser._id,
            userName: getUser.username,
            fullName: getUser.fullname,
            number: '',
            email: '',
            token,
            historyBookingCount: 0,
            patientCount: 0,
        };
        return { ...resultData, token };
    }

    async medproLoginPassword(formData: MedproLoginDTO, appId: string, partnerId: string): Promise<any> {
        if (formData.captchaResponse) {
            if (formData.captchaType === 'cloudflare') {
                await this.verifyRecaptchaCloudflare({ captchaResponse: formData.captchaResponse });
            } else {
                await this.verifyGoogleRecaptcha({ captchaResponse: formData.captchaResponse });
            }
        } else {
            throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
        }

        return this.medproLoginSocial({ phone: formData.phone, type: 'password', password: formData.password }, appId, partnerId);
    }
    
    async medproLoginPasswordCskh(formData: MedproLoginDTO, appId: string, partnerId: string): Promise<any> {
        return this.medproLoginSocial({ phone: formData.phone, type: 'password', password: formData.password }, appId, partnerId);
    }

    async medproLoginPasswordV2(formData: MedproLoginDTO, appId: string, partnerId: string): Promise<any> {
        let data: any = {};
        try {
            data = await this.medproLoginSocial({ phone: formData.phone, type: 'password', password: formData.password }, appId, partnerId);
        } catch (error) {
            const { response } = error;
            const statusCode = response?.statusCode || HttpStatus.BAD_REQUEST;
            throw new HttpException('Thông tin đăng nhập không chính xác', statusCode);
        }

        const { userName, userMongoId } = data;
        /* Tiến hành lấy thông tin userProfile */
        const getUserProfile = await this.userProfileModel
            .findOne({
                user: userMongoId,
                partnerId,
            })
            .exec();
        const empObj: any = {};
        if (!getUserProfile) {
            empObj.employeeId = '';
            const payloadS: any = {};
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            payloadS.userMongoId = userMongoId;
            const secretKey = jwt.sign({ ...payloadS }, jwtOptions.secret, jwtOptions.signOptions);
            throw new HttpException(
                {
                    status: HttpStatus.PRECONDITION_FAILED,
                    statusCode: HttpStatus.PRECONDITION_FAILED,
                    secretKey,
                    message: 'Vui lòng bổ sung mã nhân viên.',
                },
                HttpStatus.PRECONDITION_FAILED,
            );
        } else {
            /* lấy thông tin */
            const objUserProfile = getUserProfile.toObject();
            const emplInfo = get(objUserProfile, 'attribute_ids_empId', {});
            if (Object.keys(emplInfo).length > 0) {
                const value = emplInfo?.value || '';
                if (value) {
                    empObj.employeeId = value;
                } else {
                    const payloadS: any = {};
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    payloadS.userMongoId = userMongoId;
                    const secretKey = jwt.sign({ ...payloadS }, jwtOptions.secret, jwtOptions.signOptions);
                    throw new HttpException(
                        {
                            status: HttpStatus.PRECONDITION_FAILED,
                            statusCode: HttpStatus.PRECONDITION_FAILED,
                            secretKey,
                            message: 'Vui lòng bổ sung mã nhân viên.',
                        },
                        HttpStatus.PRECONDITION_FAILED,
                    );
                }
            }
        }

        try {
            /* tiến hành lấy role */
            const userRoles = (await this.httpService.get(`${this.getRolesPortalUrl}/${partnerId}/${userName}`).toPromise()).data;
            const resultRoles = userRoles || [];
            return {
                ...data,
                roles: resultRoles,
                ...empObj,
                message: resultRoles.length === 0 ? 'Tài khoản này chưa được cấp quyền. Vui lòng liên hệ người quản trị.' : '',
            };
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async updateEmployeeIdUser(formData: UpdateUserProfileCbytDTO, partnerId: string): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let formDataVerify: any = {};
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            formDataVerify = jwtVerify;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        const userId = formDataVerify.userMongoId;
        /* kiểm tra xem đã có chưa */
        const userProfile = await this.userProfileModel.findOne({ user: userId, partnerId }).exec();
        const createTime = moment().toDate();
        const objAvatar: any = {};
        if (formData.avatar) {
            objAvatar.avatarId = formData.avatar;
        }
        if (!userProfile) {
            /* thêm mới  */
            const newUserProfile = new this.userProfileModel({
                id: uuid.v4().replace(/-/g, ''),
                user: userId,
                partnerId,
                attribute_ids_empId: {
                    value: formData.employeeId,
                    createTime,
                    updateTime: createTime,
                },
                birthdate: moment(formData.birthdate)
                    .add(7, 'hours')
                    .format('YYYY-MM-DD'),
                birthyear: moment(formData.birthdate).isValid()
                    ? Number(
                          moment(formData.birthdate)
                              .add(7, 'hours')
                              .format('YYYY'),
                      )
                    : formData.birthyear,
                sex: formData.sex,
                email: formData.email,
                professionId: formData.professionId,
                positionId: formData.positionId,
                ...objAvatar,
            });
            await newUserProfile.save();
            const payload = { username: '', sub: 0, userMongoId: 0 };
            payload.userMongoId = userId;
            const token = this.jwtService.sign(payload);
            return {
                token,
            };
        } else {
            /* cập nhật */
            await this.userProfileModel
                .findByIdAndUpdate(
                    {
                        _id: userProfile._id,
                    },
                    {
                        attribute_ids_empId: {
                            value: formData.employeeId,
                            updateTime: createTime,
                        },
                        birthdate: moment(formData.birthdate)
                            .add(7, 'hours')
                            .format('YYYY-MM-DD'),
                        birthyear: moment(formData.birthdate).isValid()
                            ? Number(
                                  moment(formData.birthdate)
                                      .add(7, 'hours')
                                      .format('YYYY'),
                              )
                            : formData.birthyear,
                        sex: formData.sex,
                        email: formData.email,
                        professionId: formData.professionId,
                        positionId: formData.positionId,
                        ...objAvatar,
                    },
                )
                .exec();

            const payload = { username: '', sub: 0, userMongoId: 0 };
            payload.userMongoId = userId;
            const token = this.jwtService.sign(payload);
            return {
                token,
            };
        }
    }

    async updateEmployeeIdUserToken(formData: UpdateUserProfileCbytTokenDTO, partnerId: string, userId: string): Promise<any> {
        /* kiểm tra xem đã có chưa */
        const userProfile = await this.userProfileModel.findOne({ user: userId, partnerId }).exec();
        const createTime = moment().toDate();
        const objAvatar: any = {};
        if (formData.avatar) {
            objAvatar.avatarId = formData.avatar;
        }

        if (formData.fullname) {
            /* cập nhật lại signInProvider type: password */
            await this.signInProvider
                .findOneAndUpdate(
                    {
                        user: userId,
                        type: 'password',
                    },
                    { displayName: formData.fullname },
                    { new: true },
                )
                .exec();
        }

        let objUserProfileReturn: IUserProfile;
        if (!userProfile) {
            /* thêm mới  */
            const newUserProfile = new this.userProfileModel({
                id: uuid.v4().replace(/-/g, ''),
                user: userId,
                partnerId,
                attribute_ids_empId: {
                    value: formData.employeeId,
                    createTime,
                    updateTime: createTime,
                },
                birthdate: moment(formData.birthdate)
                    .add(7, 'hours')
                    .format('YYYY-MM-DD'),
                birthyear: moment(formData.birthdate).isValid()
                    ? Number(
                          moment(formData.birthdate)
                              .add(7, 'hours')
                              .format('YYYY'),
                      )
                    : formData.birthyear,
                sex: formData.sex,
                email: formData.email,
                professionId: formData.professionId,
                positionId: formData.positionId,
                ...objAvatar,
            });
            objUserProfileReturn = await newUserProfile.save();
        } else {
            /* cập nhật */
            objUserProfileReturn = await this.userProfileModel
                .findByIdAndUpdate(
                    {
                        _id: userProfile._id,
                    },
                    {
                        attribute_ids_empId: {
                            value: formData.employeeId,
                            updateTime: createTime,
                        },
                        birthdate: moment(formData.birthdate)
                            .add(7, 'hours')
                            .format('YYYY-MM-DD'),
                        birthyear: moment(formData.birthdate).isValid()
                            ? Number(
                                  moment(formData.birthdate)
                                      .add(7, 'hours')
                                      .format('YYYY'),
                              )
                            : formData.birthyear,
                        sex: formData.sex,
                        email: formData.email,
                        professionId: formData.professionId,
                        positionId: formData.positionId,
                        ...objAvatar,
                    },
                    { new: true },
                )
                .exec();
        }

        const objAvatar2: any = {};
        if (objUserProfileReturn.avatarId) {
            objAvatar2.avatarUrl = `${this.urlConfigService.getBaseUrl()}files/${objUserProfileReturn.avatarId}`;
        }

        const objPosition: any = {};
        const getPostionId = get(objUserProfileReturn, 'positionId', null);
        if (getPostionId) {
            const getPositonInfo = await this.viTriCongViecModel.findOne({ id: getPostionId }).exec();
            if (getPositonInfo) {
                objPosition.position = getPositonInfo.toObject();
            }
        }

        const objProfession: any = {};
        const getProfessionId = get(objUserProfileReturn, 'professionId', null);
        if (getProfessionId) {
            const getPositonInfo = await this.hocViModel.findOne({ id: getProfessionId }).exec();
            if (getPositonInfo) {
                objProfession.profession = getPositonInfo.toObject();
            }
        }

        const objUserProfileResult = {
            ...objAvatar2,
            ...objUserProfileReturn.toObject(),
            ...objPosition,
            ...objProfession,
            sexDisplay: objUserProfileReturn.sex === 1 ? 'Nam' : 'Nữ',
            fullname: formData.fullname,
        };

        return objUserProfileResult;
    }

    async loginAppleID(firebaseId: string, displayName: string, method: string): Promise<any> {
        /* Tiến hành lưu user constraints trước */
        let checkProvider = false;
        let checkUser = false;
        let result: IUser;
        try {
            const newUserConstraints = new this.constraintUser({
                medproId: `mp${firebaseId}`,
            });
            await newUserConstraints.save();

            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd('appleId@pkh.123', userSalt);
            /* Tạo mới thông tin user trên mongo */
            const userMongo = new this.userModel({
                username: firebaseId,
                password: userHashPwd,
                salt: userSalt,
                email: firebaseId,
                fullname: displayName,
                medproId: `mp${firebaseId}`,
            });
            result = await userMongo.save();
            checkUser = true;

            const newProviderConstraints = new this.constraintProvider({
                providerId: `${firebaseId}_${method}`,
            });
            await newProviderConstraints.save();
            checkProvider = true;

            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'firebase',
                email: firebaseId,
                user: result._id,
                method,
                displayName,
            });
            const provider = await signInProvider.save();

            return {
                user: result,
                signInProvider: provider,
            };
        } catch (error) {
            if (!checkUser) {
                /* Trường hợp này đã tồn tại user rồi. */
                const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(firebaseId);
                try {
                    /* tiếp tục kiểm tra cái signInProvider */
                    const newProviderConstraints = new this.constraintProvider({
                        providerId: `${firebaseId}_${method}`,
                    });
                    await newProviderConstraints.save();
                    // checkProvider = true;

                    const signInProvider = new this.signInProvider({
                        id: uuid.v4().replace(/-/g, ''),
                        type: 'firebase',
                        email: firebaseId,
                        user: checkUserMongo._id,
                        method,
                        displayName,
                    });
                    const provider = await signInProvider.save();

                    return {
                        user: checkUserMongo,
                        signInProvider: provider,
                    };
                } catch (error) {
                    /* tìm lại thông tin provider */
                    const getProvider = await this.signInProvider
                        .findOne({
                            email: firebaseId,
                            method,
                            user: checkUserMongo._id,
                        })
                        .exec();

                    return {
                        user: checkUserMongo,
                        signInProvider: getProvider,
                    };
                }
            } else {
                const getProvider2 = await this.signInProvider
                    .findOne({
                        email: firebaseId,
                        method,
                        user: result._id,
                    })
                    .exec();

                return {
                    user: result,
                    signInProvider: getProvider2,
                };
            }
        }
    }

    async medproLoginSocial(formData: MedproLoginSocialDTO, appId: string, partnerId: string, platform?: string, version?: number): Promise<any> {
        // console.log('login ', formData, appId, partnerId);
        const payload = { username: '', sub: 0, userMongoId: 0 };
        const resultData = {
            userId: 0,
            userName: '',
            fullName: '',
            number: '',
            email: '',
            token: '',
            historyBookingCount: 0,
            patientCount: 0,
        };
        if (formData.type === 'password') {
            /* kiểm tra xem mật khẩu thế nào */
            const yourphone = `${formData.phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(yourphone);
            if (!checkUserMongo) {
                throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.BAD_REQUEST);
            }

            const fromDate = moment()
                .utc()
                .subtract(7, 'hours')
                .startOf('day')
                .toDate();

            const loginLogCount = await this.userLoginLogModel
                .count({
                    userId: checkUserMongo._id,
                    createdAt: {
                        $gte: fromDate,
                    },
                })
                .exec();

            if (loginLogCount >= 3) {
                const date = moment()
                    .utc()
                    .add(7, 'hours')
                    .format('YYYYMMDD');
                this.userLockedModel.create({
                    userId: checkUserMongo._id,
                    username: checkUserMongo.username,
                    id: `${checkUserMongo.username}_${date}`,
                });

                throw new HttpException('Bạn đã nhập sai thông tin đăng nhập quá 3 lần, vui lòng liên hệ tổng đài ********', 400);
            }

            /* gán thông tin */
            try {
                await this.getUserIdAndCreateSessionV1(appId, partnerId, checkUserMongo);
            } catch (error) {
                console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                // this.clientSentry.instance().captureException(error);
            }

            /* sync user account */
            try {
                await this.syncUserAccount(checkUserMongo.username, checkUserMongo._id);
            } catch (error) {
                console.log('Phần sync useraccount bị lỗi');
                // this.clientSentry.instance().captureException(error);
            }

            resultData.userName = checkUserMongo.username;
            resultData.fullName = checkUserMongo.fullname;
            payload.userMongoId = checkUserMongo._id;

            const findSignInProvide = await this.signInProvider.findOne({ type: formData.type, user: checkUserMongo._id }).exec();
            if (!findSignInProvide) {
                throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.BAD_REQUEST);
            }
            /* cập nhật lại thông tin của signInProvider */
            await this.signInProvider.findOneAndUpdate({ _id: findSignInProvide._id }, { updatedAt: moment().toDate() }).exec();
            /* lấy displayName trong signInProvider */
            const toObj = findSignInProvide.toObject();
            const getDisplayName = get(toObj, 'displayName', '');
            if (!!getDisplayName) {
                resultData.fullName = getDisplayName;
            }
            /* lấy displayName trong signInProvider */
            const md5Salt = md5(formData.password + findSignInProvide.salt);
            if (findSignInProvide.password === md5Salt) {
                const token = this.jwtService.sign(payload);
                resultData.email = checkUserMongo.email;
                return { ...resultData, token, isCS: checkUserMongo.isCS, userMongoId: checkUserMongo._id };
            } else {
                await this.userLoginLogModel.create({
                    userId: checkUserMongo._id,
                });
                throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.BAD_REQUEST);
            }
        } else if (formData.type === 'zalo') {
            const zaloInfo = await this.getZaloInfoAfterVerified(formData.token, partnerId);
            // console.log('zaloInfo', zaloInfo);
            const email = `zl_${zaloInfo.id}`;
            /* Kiểm tra trường hợp zalo */
            if (appId === 'trungvuong' && partnerId === 'trungvuong') {
                const getUserTrungVuong = await this.userModel
                    .findOne(
                        {
                            medproId: {
                                $in: [null, ''],
                            },
                            username: email,
                        },
                        {
                            id: true,
                            username: true,
                        },
                    )
                    .exec();
                if (getUserTrungVuong) {
                    const zaloProvider: ISignInProvider = await this.createNewZaloProvider(getUserTrungVuong);
                    /* check lại xem zaloProvider.user với getUser._id */
                    if (zaloProvider.user === getUserTrungVuong._id) {
                        /* Tiến hành nâng cấp */
                        await this.upgradeSocialUserTrungVuong(getUserTrungVuong);
                    } else {
                        /* Lấy thông tin hồ sơ medpro 1.0 chuyển qua medpro 1.1 */
                        // tslint:disable-next-line: variable-name
                        const existsMedpro11_2 = await this.getUserByMongoUserId(zaloProvider.user);
                        /* tiến hành merge user */
                        await this.mergeUserMedproId(getUserTrungVuong, existsMedpro11_2);
                        /* tiến hành cập nhật lại booking */
                        await this.bookingModel
                            .updateMany(
                                {
                                    userId: getUserTrungVuong._id,
                                },
                                {
                                    userId: existsMedpro11_2._id,
                                    prevUserId: getUserTrungVuong._id,
                                },
                            )
                            .exec();
                    }
                }
            }

            /* kiểm tra xem có thông tin signin provider hay ko */
            const checkSignInProvider = await this.checkSignInProvider(email, 'zalo');
            // console.log('kiểm tra provider của zalo v1');
            if (!checkSignInProvider) {
                if (appId === 'dalieuhcm' && partnerId === 'dalieuhcm') {
                    /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
                    // console.log('kiểm tra provider của zalo v1', appId, partnerId);
                    const userV1Info = await this.checkExistsUserByUsername(email);
                    if (userV1Info) {
                        const resulDataUser = await this.syncUserV1DaLieuSocialToMedproId(userV1Info);
                        try {
                            // console.log('remind sync patient, booking cua zalo 4444444');
                            await this.remindSyncDaLieu(resulDataUser);
                        } catch (error) {
                            // this.clientSentry.instance().captureException(error);
                            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                        }
                    }
                }

                if (appId === 'nhidong1' && partnerId === 'nhidong1') {
                    /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
                    // console.log('kiểm tra provider của zalo v1', appId, partnerId);
                    const userV1Info = await this.checkExistsUserByUsername(email);
                    if (userV1Info) {
                        const resulDataUser = await this.syncUserV1NhiDong1SocialToMedproId(userV1Info);
                        try {
                            // console.log('remind sync patient, booking cua zalo 4444444');
                            await this.remindSyncNhiDong1(resulDataUser);
                        } catch (error) {
                            console.log(error)
                            // this.clientSentry.instance().captureException(error);
                            // console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                        }
                    }
                }

                if (appId === 'umc' && partnerId === 'umc') {
                    /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
                    // console.log('kiểm tra provider của zalo v1', appId, partnerId);
                    const userV1Info = await this.checkExistsUserByUsername(email);
                    if (userV1Info) {
                        const resulDataUser = await this.syncUserV1DHYDSocialToMedproId(userV1Info);
                        try {
                            // console.log('remind sync patient, booking cua zalo 4444444');
                            await this.remindSyncUMC(resulDataUser);
                        } catch (error) {
                            console.log(error)
                            // this.clientSentry.instance().captureException(error);
                            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                        }
                    }
                }

                return {
                    requiredPhone: true,
                };
            }

            /* cập nhật lại thông tin của signInProvider */
            await this.signInProvider.findOneAndUpdate({ _id: checkSignInProvider._id }, { updatedAt: moment().toDate() }).exec();
            /* tìm lại thông tin user */
            const getUser = await this.userModel.findById({ _id: checkSignInProvider.user }).exec();
            if (!getUser) {
                return {
                    requiredPhone: true,
                };
            } else {
                const getTvUser = get(getUser, 'tvUser', null);
                if (getTvUser) {
                    return {
                        requiredPhone: true,
                    };
                }
                const getdaLieuUser = get(getUser, 'daLieuUser', null);
                if (getdaLieuUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getnd1User = get(getUser, 'nd1User', null);
                if (getnd1User) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getUmcUser = get(getUser, 'umcUser', null);
                if (getUmcUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getCtchUser = get(getUser, 'ctchUser', null);
                if (getCtchUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                /* Trường hợp đã có social này trên v2. Giờ đồng bộ hồ sơ, phiếu khám vào trong user trên v2 */
                const userV1Info = await this.checkExistsUserByUsername(email);
                if (userV1Info) {
                    /* cập nhật lại userId để tiến hành sync patient, booking */
                    const resulDataUser = await this.userModel
                        .findByIdAndUpdate(
                            {
                                _id: getUser._id,
                            },
                            {
                                userIdV1: userV1Info.id,
                            },
                            { new: true },
                        )
                        .exec();
                    try {
                        // console.log('remind sync patient, booking cua zalo 555555');
                        await this.remindSyncDaLieu(resulDataUser);
                        await this.remindSyncNhiDong1(resulDataUser);
                    } catch (error) {
                        console.log(error)
                        // this.clientSentry.instance().captureException(error);
                        console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                    }
                }

                /* gán thông tin */
                payload.userMongoId = getUser._id;
                try {
                    await this.getUserIdAndCreateSessionV1(appId, partnerId, getUser);
                } catch (error) {
                    console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                    // this.clientSentry.instance().captureException(error);
                }

                /* sync user account */
                try {
                    await this.syncUserAccount(getUser.username, getUser._id);
                } catch (error) {
                    console.log('Phần sync useraccount bị lỗi');
                    // this.clientSentry.instance().captureException(error);
                }

                resultData.userName = getUser.username;
                resultData.fullName = getUser.fullname;
                /* lấy displayName trong signInProvider */
                const toObj = checkSignInProvider.toObject();
                const getDisplayName = get(toObj, 'displayName', '');
                if (!!getDisplayName) {
                    resultData.fullName = getDisplayName;
                }
                /* lấy displayName trong signInProvider */

                const token = this.jwtService.sign(payload);
                resultData.email = email;
                /* Kiểm tra xem user này có cần bổ sung thêm gì ko */
                return { ...resultData, token, isCS: getUser.isCS, userMongoId: getUser._id };
            }
        } else if (formData.type === 'firebase') {
            const { userName, signInProvider, displayName } = await this.getFirebaseAfterVerified(
                formData.token,
                partnerId,
                appId,
                platform,
                version,
            );
            /* Tiến hành kiểm tra cập nhật những method = unknown */
            await this.findAndUpdateUnknownMethodFirebase(userName, signInProvider);
            /* Tiến hành kiểm tra user Trung vương */
            if (appId === 'trungvuong' && partnerId === 'trungvuong') {
                const getUserTrungVuong = await this.userModel
                    .findOne(
                        {
                            medproId: {
                                $in: [null, ''],
                            },
                            username: userName,
                        },
                        {
                            id: true,
                            username: true,
                        },
                    )
                    .exec();
                if (getUserTrungVuong) {
                    const firebaseProvider: ISignInProvider = await this.createNewFirebaseProvider(getUserTrungVuong);
                    if (firebaseProvider.user === getUserTrungVuong._id) {
                        await this.upgradeSocialUserTrungVuong(getUserTrungVuong);
                    } else {
                        /* Lấy thông tin hồ sơ medpro 1.0 chuyển qua medpro 1.1 */
                        // tslint:disable-next-line: variable-name
                        const existsMedpro11_2 = await this.getUserByMongoUserId(firebaseProvider.user);
                        /* tiến hành merge user */
                        await this.mergeUserMedproId(getUserTrungVuong, existsMedpro11_2);
                        /* tiến hành cập nhật lại booking */
                        await this.bookingModel
                            .updateMany(
                                {
                                    userId: getUserTrungVuong._id,
                                },
                                {
                                    userId: existsMedpro11_2._id,
                                    prevUserId: getUserTrungVuong._id,
                                },
                            )
                            .exec();
                    }
                }
            }

            // if (appId === 'dalieuhcm' && partnerId === 'dalieuhcm') {
            //     /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
            //     const userV1Info = await this.checkExistsUserByUsername(userName);
            //     if (userV1Info) {
            //         await this.syncUserV1DaLieuSocialToMedproId(userV1Info);
            //     }
            // }

            /*END User Trung Vuong */
            if (signInProvider === 'apple.com') {
                /* Xử lý riêng cho luồng đăng nhập bằng apple ID */
                /* Tiến hành lưu user constraints trước */
                const overrideName = !!displayName ? displayName : 'Người dùng MEDPRO';
                const { user: userApple, signInProvider: providerApple } = await this.loginAppleID(userName, overrideName, signInProvider);
                /* tạo thông tin token đăng nhập */
                payload.userMongoId = userApple._id;

                try {
                    await this.getUserIdAndCreateSessionV1(appId, partnerId, userApple);
                } catch (error) {
                    console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                    this.clientSentry.instance().captureException(error);
                }

                const getDisplayName = get(providerApple.toObject(), 'displayName', '');
                if (!!getDisplayName) {
                    resultData.fullName = getDisplayName;
                }
                /* lấy displayName trong signInProvider */
                const token = this.jwtService.sign(payload);
                resultData.email = userApple.username;
                return { ...resultData, token, isCS: userApple.isCS, userMongoId: userApple._id };
            }
            /* kiểm tra xem có thông tin signin provider hay ko */
            const checkSignInProvider = await this.checkSignInProvider(userName, signInProvider);
            if (!checkSignInProvider) {
                if (appId === 'dalieuhcm' && partnerId === 'dalieuhcm') {
                    /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
                    // console.log('kiểm tra provider của firebase v1', appId, partnerId);
                    const userV1Info = await this.checkExistsUserByUsername(userName);
                    if (userV1Info) {
                        const resulDataUser = await this.syncUserV1DaLieuSocialToMedproId(userV1Info, signInProvider);
                        // await this.syncDaLieuUserV1ToMedproId(userV1Info.id, email, userV1Info.fullname);
                        try {
                            // console.log('remind sync patient, booking cua firebase 4444444');
                            await this.remindSyncDaLieu(resulDataUser);
                        } catch (error) {
                            console.log(error)
                            // this.clientSentry.instance().captureException(error);
                            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                        }
                    }
                }

                if (appId === 'nhidong1' && partnerId === 'nhidong1') {
                    /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
                    // console.log('kiểm tra provider của firebase v1', appId, partnerId);
                    const userV1Info = await this.checkExistsUserByUsername(userName);
                    if (userV1Info) {
                        const resulDataUser = await this.syncUserV1NhiDong1SocialToMedproId(userV1Info, signInProvider);
                        // await this.syncDaLieuUserV1ToMedproId(userV1Info.id, email, userV1Info.fullname);
                        try {
                            // console.log('remind sync patient, booking cua firebase 4444444');
                            await this.remindSyncNhiDong1(resulDataUser);
                        } catch (error) {
                            console.log(error)
                            // this.clientSentry.instance().captureException(error);
                            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                        }
                    }
                }

                if (appId === 'umc' && partnerId === 'umc') {
                    /* Kiểm tra xem social này có tồn tại trong v1 hay ko */
                    // console.log('kiểm tra provider của firebase v1', appId, partnerId);
                    const userV1Info = await this.checkExistsUserByUsername(userName);
                    if (userV1Info) {
                        const resulDataUser = await this.syncUserV1DHYDSocialToMedproId(userV1Info, signInProvider);
                        // await this.syncDaLieuUserV1ToMedproId(userV1Info.id, email, userV1Info.fullname);
                        try {
                            // console.log('remind sync patient, booking cua firebase 4444444');
                            await this.remindSyncUMC(resulDataUser);
                        } catch (error) {
                            console.log(error)
                            // this.clientSentry.instance().captureException(error);
                            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                        }
                    }
                }

                return {
                    requiredPhone: true,
                };
            }
            /* cập nhật lại thông tin của signInProvider */
            await this.signInProvider.findOneAndUpdate({ _id: checkSignInProvider._id }, { updatedAt: moment().toDate() }).exec();
            /* tìm lại thông tin user */
            const getUser = await this.userModel.findById({ _id: checkSignInProvider.user }).exec();
            if (!getUser) {
                return {
                    requiredPhone: true,
                };
            } else {
                // const getNextStep = get(getUser, 'nextStep', null);
                // if (getNextStep) {
                //     return {
                //         requiredPhone: true,
                //     };
                // }

                const getTvUser = get(getUser, 'tvUser', null);
                if (getTvUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getdaLieuUser = get(getUser, 'daLieuUser', null);
                if (getdaLieuUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getnd1User = get(getUser, 'nd1User', null);
                if (getnd1User) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getUmcUser = get(getUser, 'umcUser', null);
                if (getUmcUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                const getCtchUser = get(getUser, 'ctchUser', null);
                if (getCtchUser) {
                    return {
                        requiredPhone: true,
                    };
                }

                // const getDaLieuUser = get(getUser, 'daLieuUser', null);
                // if (getDaLieuUser) {
                //     return {
                //         requiredPhone: true,
                //     };
                // }

                /* Trường hợp đã có social này trên v2. Giờ đồng bộ hồ sơ, phiếu khám vào trong user trên v2 */
                const userV1Info = await this.checkExistsUserByUsername(userName);
                if (userV1Info) {
                    /* cập nhật lại userId để tiến hành sync patient, booking */
                    const resulDataUser = await this.userModel
                        .findByIdAndUpdate(
                            {
                                _id: getUser._id,
                            },
                            {
                                userIdV1: userV1Info.id,
                            },
                            { new: true },
                        )
                        .exec();
                    try {
                        // console.log('remind sync patient, booking cua zalo 555555');
                        await this.remindSyncDaLieu(resulDataUser);
                        await this.remindSyncNhiDong1(resulDataUser);
                        await this.remindSyncUMC(resulDataUser);
                    } catch (error) {
                        console.log(error)
                        // this.clientSentry.instance().captureException(error);
                        console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                    }
                }

                /* gán thông tin */
                payload.userMongoId = getUser._id;
                try {
                    await this.getUserIdAndCreateSessionV1(appId, partnerId, getUser);
                } catch (error) {
                    console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                    this.clientSentry.instance().captureException(error);
                }

                /* sync user account */
                try {
                    await this.syncUserAccount(getUser.username, getUser._id);
                } catch (error) {
                    // console.log('Phần sync useraccount bị lỗi');
                    this.clientSentry.instance().captureException(error);
                }

                resultData.userName = getUser.username;
                resultData.fullName = getUser.fullname;
                /* lấy displayName trong signInProvider */
                const toObj = checkSignInProvider.toObject();
                const getDisplayName = get(toObj, 'displayName', '');
                if (!!getDisplayName) {
                    resultData.fullName = getDisplayName;
                }
                /* lấy displayName trong signInProvider */
                const token = this.jwtService.sign(payload);
                resultData.email = userName;
                return { ...resultData, token, isCS: getUser.isCS, userMongoId: getUser._id };
            }
        }
    }

    async getUserDetail(appId: string, partnerId: string, userId: string): Promise<any> {
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập.', HttpStatus.UNAUTHORIZED);
        }
        // return getUser;
        /* lấy thông tin signin provider */
        const signInProvider = await this.signInProvider
            .find({ user: getUser._id }, { displayName: true, type: true, updatedAt: true, method: true })
            .sort({ updatedAt: 'desc' })
            .exec();
        const mapObj = signInProvider.map(item => {
            const objItem = item.toObject();
            const { type, updatedAt, method, displayName } = objItem;
            return {
                type,
                updatedAt,
                method,
                displayName,
            };
        });
        /* lấy thông tin user profile */
        const getUserProfile = await this.userProfileModel
            .findOne({
                user: getUser._id,
                partnerId,
            })
            .exec();

        let objUserProfile: any = {};
        if (getUserProfile) {
            const objAvatar: any = {};
            if (getUserProfile.avatarId) {
                objAvatar.avatarUrl = `${this.urlConfigService.getBaseUrl()}files/${getUserProfile.avatarId}`;
            }

            const objPosition: any = {};
            const getPostionId = get(getUserProfile, 'positionId', null);
            if (getPostionId) {
                const getPositonInfo = await this.viTriCongViecModel.findOne({ id: getPostionId }).exec();
                if (getPositonInfo) {
                    objPosition.position = getPositonInfo.toObject();
                }
            }

            const objProfession: any = {};
            const getProfessionId = get(getUserProfile, 'professionId', null);
            if (getProfessionId) {
                const getPositonInfo = await this.hocViModel.findOne({ id: getProfessionId }).exec();
                if (getPositonInfo) {
                    objProfession.profession = getPositonInfo.toObject();
                }
            }

            objUserProfile = {
                ...objAvatar,
                ...getUserProfile.toObject(),
                ...objPosition,
                ...objProfession,
                sexDisplay: getUserProfile.sex === 1 ? 'Nam' : 'Nữ',
            };
        }

        return {
            signMethod: mapObj,
            userProfile: { ...objUserProfile },
        };
    }

    async boGetUserDetail(phone: string, appId: string = '', appKey: string = ''): Promise<any> {
        const validate = appId === this.appBoId && appKey === this.appBoKey;
        if (!validate) {
            throw new HttpException(`Thông tin xác thực gửi lên không chính xác. Vui lòng kiểm tra lại!`, HttpStatus.UNAUTHORIZED);
        }
        const username = `${phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        const getUser = await this.checkExistsUserByUsernameMedproId(username);
        const signInProvider = await this.signInProvider
            .find({ user: getUser._id }, { displayName: true, type: true, updatedAt: true, method: true })
            .sort({ updatedAt: 'desc' })
            .exec();
        const mapObj = signInProvider.map(item => {
            const objItem = item.toObject();
            const { type, updatedAt, method, displayName } = objItem;
            return {
                type,
                updatedAt,
                method,
                displayName,
            };
        });

        return {
            userId: getUser._id,
            userName: username,
            signMethod: mapObj,
        };
    }

    async checkSyncPhoneTrungVuongUser(appId: string, partnerId: string, username: string): Promise<any> {
        /* Kiểm tra đồng bộ dữ liệu */
        if (appId === 'trungvuong' && partnerId === 'trungvuong') {
            const getUser = await this.userModel
                .findOne(
                    {
                        medproId: {
                            $in: [null, ''],
                        },
                        username,
                    },
                    {
                        id: true,
                        username: true,
                    },
                )
                .exec();

            if (getUser) {
                /* Kiểm tra xem số điện thoại này đã có trên medpro 1.1 hay ko */
                const existsMedpro11 = await this.checkExistsUserByUsernameMedproId(getUser.username);
                if (existsMedpro11) {
                    /* Lấy thông tin hồ sơ medpro 1.0 chuyển qua medpro 1.1 */
                    await this.mergeUserMedproId(getUser, existsMedpro11);
                    /* tiến hành cập nhật lại booking */
                    await this.bookingModel
                        .updateMany(
                            {
                                userId: getUser._id,
                            },
                            {
                                userId: existsMedpro11._id,
                                prevUserId: getUser._id,
                            },
                        )
                        .exec();
                } else {
                    /* Tiến hành lưu user constraints trước */
                    await this.createUserConstraints(getUser);
                    /* tiến hành update user medpro 1.0 lên 1.1 */
                    await this.upgradeUserTrungVuong(getUser);
                    /* tiến hành tạo signProvider */
                    await this.createNewPasswordProvider(getUser);
                }
            }
        }
    }

    async checkSyncPhoneDaLieuUser(appId: string, partnerId: string, username: string): Promise<any> {
        if (appId === 'dalieuhcm' && partnerId === 'dalieuhcm') {
            const fullname = '';
            const userV1Info = await this.checkExistsUserByUsername(username);
            if (userV1Info) {
                /* Kiểm tra xem userId này đã đồng bộ lên chưa */
                const checkDaLieuUser = await this.userModel.findOne({ daLieuUser: userV1Info.id }).exec();
                if (!checkDaLieuUser) {
                    await this.syncDaLieuUserV1ToMedproId(userV1Info.id, username, fullname);
                }
            }
        }
    }

    async checkSyncPhoneNd1User(appId: string, partnerId: string, username: string): Promise<any> {
        if (appId === 'nhidong1' && partnerId === 'nhidong1') {
            const fullname = '';
            const userV1Info = await this.checkExistsUserByUsername(username);
            if (userV1Info) {
                /* Kiểm tra xem userId này đã đồng bộ lên chưa */
                const checkNd1User = await this.userModel.findOne({ nd1User: userV1Info.id }).exec();
                if (!checkNd1User) {
                    await this.syncNd1UserV1ToMedproId(userV1Info.id, username, fullname);
                }
            }
        }
    }

    async checkSyncPhoneUMCUser(appId: string, partnerId: string, username: string): Promise<any> {
        if (appId === 'umc' && partnerId === 'umc') {
            const fullname = '';
            const userV1Info = await this.checkExistsUserByUsername(username);
            if (userV1Info) {
                /* Kiểm tra xem userId này đã đồng bộ lên chưa */
                const checkUmcUser = await this.userModel.findOne({ umcUser: userV1Info.id }).exec();
                if (!checkUmcUser) {
                    await this.syncUMCUserV1ToMedproId(userV1Info.id, username, fullname);
                }
            }
        }
    }

    async remindSyncDaLieu(user: IUser): Promise<any> {
        //  userId: number
        const userId = user.userIdV1;
        const medproId = user.medproId;
        try {
            /* Lấy hết thông tin hồ sơ của bn */
            const patients = await this.pkhPatientKnex(this.skinUserPatient).where({
                user_id: userId,
            });
            if (patients) {
                const createdDate = moment().toDate();
                const reMapping = patients.map(item => {
                    return {
                        id: item.skin_patient_id,
                        userId,
                        medproId,
                        date_create: createdDate,
                    };
                });
                await this.syncDaLieuPatientModel.insertMany(reMapping);
            }
            /* lấy hết thông tin booking củ bệnh nhân */
            const bookings = await this.pkhPatientKnex(this.skinBookingTableName)
                .select('id')
                .where({
                    user_id: userId,
                });

            if (bookings) {
                const createdDate = moment().toDate();
                const reMappingBooking = bookings.map(item => {
                    return {
                        id: item.id,
                        date_create: createdDate,
                    };
                });
                await this.syncDaLieuBookingModel.insertMany(reMappingBooking);
            }
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
        }
    }

    async remindSyncNhiDong1(user: IUser): Promise<any> {
        //  userId: number
        const userId = user.userIdV1;
        const medproId = user.medproId;
        try {
            /* Lấy hết thông tin hồ sơ của bn */
            const patients = await this.pkhPatientKnex(this.nd1UserPatient).where({
                user_id: userId,
            });
            if (patients) {
                const createdDate = moment().toDate();
                const reMapping = patients.map(item => {
                    return {
                        id: item.nd1_patient_id,
                        userId,
                        medproId,
                        date_create: createdDate,
                    };
                });
                await this.syncNhiDong1PatientModel.insertMany(reMapping);
            }
            /* lấy hết thông tin booking củ bệnh nhân */
            const bookings = await this.pkhPatientKnex(this.nd1BookingTableName)
                .select('id')
                .where({
                    user_id: userId,
                });

            if (bookings) {
                const createdDate = moment().toDate();
                const reMappingBooking = bookings.map(item => {
                    return {
                        id: item.id,
                        date_create: createdDate,
                    };
                });
                await this.syncNhiDong1BookingModel.insertMany(reMappingBooking);
            }
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
        }
    }

    async remindSyncUMC(user: IUser): Promise<any> {
        //  userId: number
        const userId = user.userIdV1;
        const medproId = user.medproId;
        try {
            /* Lấy hết thông tin hồ sơ của bn */
            const patients = await this.pkhPatientKnex(this.umcUserPatient).where({
                user_id: userId,
            });
            if (patients) {
                const createdDate = moment().toDate();
                const reMapping = patients.map(item => {
                    return {
                        id: item.patient_id,
                        userId,
                        medproId,
                        date_create: createdDate,
                    };
                });
                await this.syncDHYDPatientModel.insertMany(reMapping);
            }
            /* lấy hết thông tin booking củ bệnh nhân */
            const bookings = await this.pkhPatientKnex(this.umcBookingTableName)
                .select('id')
                .where({
                    user_id: userId,
                });

            if (bookings) {
                const createdDate = moment().toDate();
                const reMappingBooking = bookings.map(item => {
                    return {
                        id: item.id,
                        date_create: createdDate,
                    };
                });
                await this.syncDHYDBookingModel.insertMany(reMappingBooking);
            }
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
        }
    }

    async insertSyncDaLieuPatient(): Promise<any> {
        const patients = [
            {
                skin_patient_id: 11,
            },
            {
                skin_patient_id: 12,
            },
        ];
        const createdDate = moment().toDate();
        const reMapping = patients.map(item => {
            return {
                id: item.skin_patient_id,
                date_create: createdDate,
            };
        });
        await this.syncDaLieuPatientModel.insertMany(reMapping);
    }

    async insertSyncDaLieuBooking(): Promise<any> {
        const bookings = [
            {
                id: 11,
            },
            {
                id: 12,
            },
        ];
        const createdDate = moment().toDate();
        const reMappingBooking = bookings.map(item => {
            return {
                id: item.id,
                date_create: createdDate,
            };
        });
        await this.syncDaLieuBookingModel.insertMany(reMappingBooking);
    }

    async checkUserInfoByPhone(formData: CheckUserInfoByPhoneDTO, appId: string = '', partnerId: string = ''): Promise<any> {
        if (formData.captchaResponse) {
            if (formData.captchaType === 'cloudflare') {
                await this.verifyRecaptchaCloudflare({ captchaResponse: formData.captchaResponse });
            } else {
                await this.verifyGoogleRecaptcha({ captchaResponse: formData.captchaResponse });
            }
        } else {
            throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
        }

        const yourphone = `${formData.phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        await this.checkSyncPhoneTrungVuongUser(appId, partnerId, yourphone);
        await this.checkSyncPhoneDaLieuUser(appId, partnerId, yourphone);
        await this.checkSyncPhoneNd1User(appId, partnerId, yourphone);
        await this.checkSyncPhoneUMCUser(appId, partnerId, yourphone);
        const checkUserMedpro = `mp${yourphone}`;
        const getUser = await this.userModel
            .findOne({
                medproId: checkUserMedpro,
            })
            .exec();
        if (getUser) {
            /* kiểm tra @trung vuong user */
            const tvUser = get(getUser, 'tvUser', null);
            const objRequiredUpgrade: any = {};
            if (tvUser) {
                objRequiredUpgrade.isRequiredUpgrade = true;
            }

            /* kiểm tra @dalieuhcm user */
            const daLieuUser = get(getUser, 'daLieuUser', null);
            if (daLieuUser) {
                /* tạo job chạy đồng bộ */
                try {
                    await this.remindSyncDaLieu(getUser);
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                    console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                }
                objRequiredUpgrade.isRequiredUpgrade = true;
            }

            /* kiểm tra nhidong1 user */
            const nd1User = get(getUser, 'nd1User', null);
            if (nd1User) {
                /* tạo job chạy đồng bộ */
                try {
                    await this.remindSyncNhiDong1(getUser);
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                    console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                }
                objRequiredUpgrade.isRequiredUpgrade = true;
            }

            const ctchUser = get(getUser, 'ctchUser', null);
            if (ctchUser) {
                objRequiredUpgrade.isRequiredUpgrade = true;
            }

            /* kiểm tra umc user */
            const umcUser = get(getUser, 'umcUser', null);
            if (umcUser) {
                /* tạo job chạy đồng bộ */
                try {
                    await this.remindSyncUMC(getUser);
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                    console.log('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                }
                objRequiredUpgrade.isRequiredUpgrade = true;
            }

            /* Lấy thông tin các phương thức mà người dùng đã đăng nhập */
            const signInProvider = await this.signInProvider
                .find({ user: getUser._id }, { displayName: true, type: true, updatedAt: true, method: true })
                .sort({ updatedAt: 'desc' })
                .exec();
            const mapObj = signInProvider.map(item => {
                const objItem = item.toObject();
                const { type, updatedAt, method, displayName } = objItem;
                return {
                    type,
                    updatedAt,
                    method,
                    displayName,
                };
            });
            /* lấy thông tin fullname */
            const firstDisplayName = first(mapObj);
            return {
                fullName: !!getUser.fullname ? getUser.fullname : firstDisplayName.displayName ? firstDisplayName.displayName : 'Người dùng Medpro',
                signMethod: mapObj,
                ...objRequiredUpgrade,
            };
        } else {
            throw new HttpException('Không tìm thấy thông tin người dùng', HttpStatus.NOT_FOUND);
        }
    }

    async checkUserUpgrade(formData: CheckUserUpgradeByPhoneDTO): Promise<any> {
        return true;
    }

    async checkSignInProvider(identity: string, method: string): Promise<any> {
        return this.signInProvider.findOne({ email: identity, method }).exec();
    }

    async checkSignInProviderPassword(userId: string): Promise<any> {
        return this.signInProvider.findOne({ user: userId, type: 'password' }).exec();
    }

    async phoneRegister(appId: string, phoneRegister: PhoneRegisterDTO, partnerId: string = 'medpro'): Promise<any> {
        if (phoneRegister.captchaResponse) {
            if (phoneRegister.captchaType === 'cloudflare') {
                await this.verifyRecaptchaCloudflare({ captchaResponse: phoneRegister.captchaResponse });
            } else {
                await this.verifyGoogleRecaptcha({ captchaResponse: phoneRegister.captchaResponse });
            }
        } else {
            throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
        }

        /* tiến hành gửi thông tin mã xác nhận */
        const yourphone = `${phoneRegister.phone}`
            .replace(/^[+]84|^0/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(yourphone);
        if (!checkUserMongo) {
            return this.v1CheckPhone(appId, { deviceId: phoneRegister.deviceId, phone: phoneRegister.phone }, partnerId);
        } else {
            /* kiểm tra tiếp cái type: password xem đã đăng ký bằng phương thức mật khẩu hay chưa  */
            const tvUser = get(checkUserMongo, 'tvUser', null);
            const daLieuUser = get(checkUserMongo, 'daLieuUser', null);
            const nd1User = get(checkUserMongo, 'nd1User', null);
            const umcUser = get(checkUserMongo, 'umcUser', null);
            const ctchUser = get(checkUserMongo, 'ctchUser', null);
            // console.log(tvUser, daLieuUser, nd1User);
            if (tvUser || daLieuUser || nd1User || umcUser || ctchUser) {
                return this.v1CheckPhone(appId, { deviceId: phoneRegister.deviceId, phone: phoneRegister.phone }, partnerId);
            }

            if (phoneRegister.type && phoneRegister.type === 'password') {
                const checkPass = await this.checkSignInProviderPassword(checkUserMongo._id);
                if (checkPass) {
                    throw new HttpException(`Tài khoản này đã tồn tại, vui lòng kiểm tra lại thông tin.`, HttpStatus.BAD_REQUEST);
                }
            }
            return this.v1CheckPhone(appId, { deviceId: phoneRegister.deviceId, phone: phoneRegister.phone }, partnerId);
        }
    }

    async addSaleCode(addSaleCodeDTO: AddSaleCodeDTO): Promise<any> {
        const saleTable = 'sale';
        const saleUserTablee = 'sale_user';
        /* Tìm trong bảng Sale */
        const findSaleInfo = this.pkhPatientKnex(saleTable)
            .where(`${saleTable}.code`, addSaleCodeDTO.code)
            .first();
        if (!findSaleInfo) {
            return null;
        }
        /* Tìm trong sale user */
        const findSaleUser = this.pkhPatientKnex(saleUserTablee)
            .where(`${saleUserTablee}.user_id`, addSaleCodeDTO.userId)
            .where(`${saleUserTablee}.sale_id`, findSaleInfo.id)
            .first();
        if (findSaleUser) {
            return null;
        }
        /* Insert vào SaleUser */
        this.pkhPatientKnex(saleUserTablee).insert({
            user_id: addSaleCodeDTO.userId,
            sale_id: findSaleInfo.id,
        });

        return { status: true };
    }

    async deleteByZaloToken(token: string): Promise<any> {
        const getEnv = this.urlConfigService.getEnv();
        if (getEnv !== 'PRODUCTION') {
            return this.signInProvider.findOneAndRemove({ email: token, method: 'zalo' }).exec();
        } else {
            throw new HttpException('Không thao tác được trên production', HttpStatus.BAD_REQUEST);
        }
    }

    getConfigZalo(partnerId: string = ''): any {
        if (partnerId) {
            switch (partnerId) {
                case 'dalieuhcm':
                    return this.zaloDaLieuConfig.getZaloConfig();
                case 'nhidong1':
                    return this.zaloNd1Config.getZaloConfig();
                case 'umc2':
                case 'umcmono':
                case 'umc':
                    return this.zaloUMCConfig.getZaloConfig();
                case 'ctchhcm':
                    return this.zaloCTCHConfig.getZaloConfig();
                default:
                    return this.zaloConfig.getZaloConfig();
            }
        } else {
            return this.zaloConfig.getZaloConfig();
        }
    }

    async getZaloInfoAfterVerified(code: string, partnerId: string = ''): Promise<any> {
        const { appId, appSecret, urlAccessToken, urlZaloInfo } = this.getConfigZalo(partnerId);
        /* GET access_token by code */
        const requestAccessToken = {
            app_id: appId,
            app_secret: appSecret,
            code,
        };
        const url = queryString.stringifyUrl({
            url: urlAccessToken,
            query: requestAccessToken,
        });
        const { access_token } = (await this.verifyAccessToken(url).toPromise()).data; // expires_in
        /* GET zalo user info by access_token */
        const queryParamsGetZaloInfo = {
            access_token,
            fields: 'id,name',
        };
        const urlGetZaloInfo = queryString.stringifyUrl({
            url: urlZaloInfo,
            query: queryParamsGetZaloInfo,
        });
        try {
            const zaloInfo = (await this.getZaloInfo(urlGetZaloInfo).toPromise()).data;
            // console.log('zaloInfo sau khi decode', zaloInfo);
            // this.clientSentry.instance().captureMessage(JSON.stringify(zaloInfo, null, 2));
            const getError = get(zaloInfo, 'error', 0);
            if (getError > 0) {
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'getZaloInfo',
                    summary: 'Lấy thông tin zalo',
                    nameParent: 'getZaloInfoAfterVerified',
                    params: {
                        url,
                        urlGetZaloInfo,
                    },
                    errorBody: getError,
                    response: { soucre: zaloInfo },
                    message: `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại`,
                });
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
            return zaloInfo;
        } catch (error) {
            console.log(error);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'getZaloInfo',
                summary: 'Lấy thông tin zalo',
                nameParent: 'getZaloInfoAfterVerified',
                params: {
                    url,
                    urlGetZaloInfo,
                },
                errorBody: this.utilService.errorHandler(error),
                message: `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại`,
            });
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async zaloLogin(zaloDTO: ZaloDTO): Promise<any> {
        const zaloInfo = await this.getZaloInfoAfterVerified(zaloDTO.code);
        /* Tìm kiếm thông tin người dùng zalo. Nếu ko có thì tạo mới user */
        const userName = `zl_${zaloInfo.id}`;
        let payload = { username: '', sub: 0, userMongoId: null };
        let resultData = {
            userId: 0,
            userName,
            fullName: zaloInfo.name,
            number: '',
            email: '',
            token: '',
            historyBookingCount: 0,
            patientCount: 0,
        };
        /* Kiểm tra xem chưa có bên mongo thì tạo luôn */
        const checkUserMongo = await this.checkExistsUserByUsernameMongo(userName);
        let userMongoId = 0;
        if (!checkUserMongo) {
            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd(userSalt, userSalt);
            /* Tạo mới thông tin user trên mongo */
            const userMongo = new this.userModel({
                username: userName,
                password: userHashPwd,
                salt: userSalt,
                email: userName,
                fullname: zaloInfo.name,
            });
            const result = (await userMongo.save()).toJSON();
            userMongoId = result.id;
        } else {
            userMongoId = checkUserMongo._id;
        }

        payload = {
            ...payload,
            username: userName,
            sub: 0,
            userMongoId,
        };

        resultData = { ...resultData, userId: 0, email: userName };

        const token = this.jwtService.sign(payload);
        return { ...resultData, token };
    }

    async zaloLoginByState(zaloStateDTO: ZaloStateDTO): Promise<any> {
        const userName = `zl_${zaloStateDTO.zaloId}`;
        const findZaloId = await this.checkExistsUserByUsername(userName);
        let payload = { username: '', sub: 0, userMongoId: null };
        let resultData = {
            userId: 0,
            userName,
            fullName: zaloStateDTO.zaloName,
            number: '',
            email: '',
            token: '',
            historyBookingCount: 0,
            patientCount: 0,
        };
        if (!findZaloId) {
            /* Tạo mới thông tin user */
            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd(userSalt, userSalt);
            const [userId] = await this.createUser({
                username: userName,
                fullname: zaloStateDTO.zaloName,
                salt: userSalt,
                hashpwd: userHashPwd,
            });
            /* Tạo mới thông tin user trên mongo */
            const userMongo = new this.userModel({
                username: userName,
                password: userHashPwd,
                salt: userSalt,
                email: userName,
                fullname: zaloStateDTO.zaloName,
            });
            const userMongoResult = (await userMongo.save()).toJSON();

            payload = {
                ...payload,
                username: userName,
                sub: userId,
                userMongoId: userMongoResult.id,
            };
            resultData = { ...resultData, userId };
        } else {
            /* Kiểm tra xem chưa có bên mongo thì tạo luôn */
            const checkUserMongo = await this.checkExistsUserByUsernameMongo(userName);
            let userMongoId = 0;
            if (!checkUserMongo) {
                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);
                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: userName,
                    fullname: zaloStateDTO.zaloName,
                });
                const result = (await userMongo.save()).toJSON();
                userMongoId = result.id;
            } else {
                userMongoId = checkUserMongo._id;
            }
            payload = {
                ...payload,
                username: userName,
                sub: findZaloId.id,
                userMongoId,
            };
            resultData = { ...resultData, userId: findZaloId.id, number: findZaloId.phone, email: findZaloId.email };
        }
        /* Tạo chuỗi Json web token */
        const token = this.jwtService.sign(payload);
        return { ...resultData, token };
    }

    async getFirebaseAfterVerified(token: string, partnerId: string = '', appId?: string, platform?: string, version?: number): Promise<any> {
        let firebaseCon: any;
        const overridePartner = appId || partnerId;
        if (partnerId) {
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: overridePartner }, { firebase: true }).exec();
            if (partnerConfig.firebase) {
                firebaseCon = await this.firebaseConfigService.createFireBaseConnection(partnerConfig);
            } else {
                // console.log('partnerConfig.firebase: NONE');
                switch (partnerId) {
                    case 'vanhanh':
                    case 'choray':
                    case 'dkanphuoc':
                    case 'dkdongnai':
                    case 'minhanh':
                    case 'medicsaigon':
                    case 'binhthanhhcm':
                    case 'nhidonghcm':
                    case 'hoanmytd':
                        firebaseCon = this.firebaseSecondAdmin;
                        break;
                    case 'dalieuhcm':
                        firebaseCon = this.firebaseDaLieuAdmin;
                        break;
                    case 'nhidong1':
                        firebaseCon = this.firebaseNd1Admin;
                        break;
                    case 'umc2':
                    case 'umcmono':
                    case 'umc':
                        firebaseCon = this.firebaseUMCAdmin;
                        break;
                    default:
                        firebaseCon = this.firebaseAdmin;
                        break;
                }
            }
        } else {
            firebaseCon = this.firebaseAdmin;
        }
        const { firebase, name, uid } = await firebaseCon
            .auth()
            .verifyIdToken(token)
            .catch((error: any) => {
                // console.log('lỗi khi verify token firebase', error);
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'verifyIdToken',
                    summary: 'Verify token firebase',
                    nameParent: 'getFirebaseAfterVerified',
                    params: {
                        partnerId,
                        token,
                        appId,
                        platform,
                        version,
                    },
                    errorBody: this.utilService.errorHandler(error),
                    message: `lỗi khi verify token firebase`,
                });
                const { code } = error;
                this.clientSentry.instance().captureException(error);
                switch (code) {
                    case 'auth/id-token-expired':
                        throw new HttpException(
                            'Token đăng nhập tài khoản xã hội hết hạn. Vui lòng đăng nhập lại tài khoản xã hội.',
                            HttpStatus.BAD_REQUEST,
                        );
                    default:
                        throw new HttpException('Hê thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            });
        // console.log('firebase', firebase);
        // console.log('name:', name);
        // console.log('uid:', uid);
        let displayName = name;
        const { identities, sign_in_provider: signInProvider } = firebase;
        const { email: arrUserName } = identities;
        let userName = typeof arrUserName !== typeof undefined ? `${first(arrUserName)}` : null;
        // console.log('userName:', userName);
        if (!!userName === false) {
            const userInfo = await firebaseCon.auth().getUser(uid);
            // console.log('userInfo:', userInfo);
            const { providerData } = userInfo;
            const userFirebase: any = first(providerData);
            userName = userFirebase.email ? userFirebase.email : `fb_${userFirebase.uid}`;
            displayName = userFirebase.displayName ? userFirebase.displayName : '';
        }
        switch (signInProvider) {
            case 'google.com':
            case 'facebook.com':
            case 'apple.com':
            case 'password': // EMAIL LINK
                {
                    if (!userName) {
                        userName = `firebase_${uid}`;
                    }
                }
                break;
            default:
                break;
        }
        if (!userName) {
            throw new HttpException('Xác thực không thành công!', HttpStatus.BAD_REQUEST);
        }
        // console.log('thông tin sau khi decode... .', userName, displayName, signInProvider);
        // console.log('final', { userName, displayName, signInProvider });
        return { userName, displayName, signInProvider };
    }

    async firebaseLogin(firebaseDTO: FirebaseDTO, appId: string, partnerId: string): Promise<any> {
        try {
            const { userName, displayName } = await this.getFirebaseAfterVerified(firebaseDTO.token, partnerId);
            const checkUserMongo = await this.checkExistsUserByUsernameMongo(userName);
            /* Xứ lý trả về json web token */
            let payload = { username: '', sub: 0, userMongoId: null };
            let resultData = {
                userId: 0,
                userName,
                fullName: displayName,
                number: '',
                email: '',
                token: '',
                historyBookingCount: 0,
                patientCount: 0,
            };
            let userMongoId = 0;
            if (!checkUserMongo) {
                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);
                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: userName,
                    fullname: displayName,
                });
                const result = (await userMongo.save()).toJSON();
                userMongoId = result.id;
            } else {
                userMongoId = checkUserMongo._id;
            }
            payload = {
                ...payload,
                username: userName,
                userMongoId,
            };
            /* thông tin tin trên v1 để xử dụng */

            resultData = { ...resultData, email: userName };
            /* Tạo chuỗi Json web token */
            const token = this.jwtService.sign(payload);
            return { ...resultData, token };
        } catch (error) {
            // tslint:disable-next-line: no-console
            console.log(error);
        }
    }

    async v1CheckPhone(appId: string, checkPhoneDTO: CheckPhoneRefDTO, partnerId: string): Promise<any> {
        /* Kiểm tra môi trường */
        const env = this.urlConfigService.getEnv();
        if (env !== 'PRODUCTION') {
            // const code = checkPhoneDTO.phone.slice(-6);
            return {
                // message: `${code} la ma xac thuc tai khoan BV cua ban.`,
                status: true,
                time_left: 20,
            };
        } else {
            if (!this.isActiveMessageHub) {
                const smsInfo = (await this.getCodeSMSBrandName(partnerId, checkPhoneDTO).toPromise()).data;
                return smsInfo;
            } else {
                const smsInfo = { partnerId, appId, phone: checkPhoneDTO.phone };
                return this.smsService.sendOTPMessageHub(smsInfo);
            }
        }
    }

    getCodeSMSBrandName(partnerId: string, checkPhoneDTO: CheckPhoneRefDTO): Observable<AxiosResponse<any>> {
        // https://masteringjs.io/tutorials/axios/form-data
        let bv = 'Medpro';
        let baseUrl = this.urlConfigService.getSMSCheckPhone();
        switch (partnerId) {
            case 'nhidong1':
                bv = 'Nhi Dong 1';
                baseUrl = this.urlConfigService.getSMSCheckPhoneNhiDong1();
                break;
            case 'umc':
                bv = 'Dai Hoc Y Duoc';
                baseUrl = this.urlConfigService.getSMSCheckPhoneUMC();
                break;
            case 'trungvuong':
                bv = 'Trung Vuong';
                break;
            case 'dkkvangiang':
                bv = 'DKKV An Giang';
                break;
            case 'leloi':
                bv = 'Le Loi';
                break;
            case 'vanhanh':
                bv = 'Da Khoa Van Hanh';
                break;
            case 'dkanphuoc':
                bv = 'Da Khoa An Phuoc';
                break;
            case 'dkdongnai':
                bv = 'Da Khoa Dong Nai';
                break;
            case 'medicsaigon':
                bv = 'Medic Sai Gon';
                break;
            case 'bvtwct':
                bv = 'Trung Uong Can Tho';
                break;
            case 'medpro':
                bv = 'Medpro';
                break;
            default:
                break;
        }
        const formData = new FormData();
        formData.append('device_id', checkPhoneDTO.deviceId);
        formData.append('phone', checkPhoneDTO.phone);
        formData.append('hospital_name', bv);
        return this.httpService.post(baseUrl, formData, {
            headers: formData.getHeaders(),
        });
    }

    async v1CheckCode(partnerId: string, checkCodeDTO: CheckCodeRefDTO, appId: string): Promise<any> {
        // const yourPhone = `${checkPhoneDTO.phone}`.replace(/^[+]84|^0/, '84').replace(/^9/, '849').replace(/^3/, '843');
        /* Kiểm tra môi trường */
        const env = this.urlConfigService.getEnv();
        let userInfo = { username: '', error_code: '', error_message: '' };
        if (env !== 'PRODUCTION') {
            const yourphone = `${checkCodeDTO.phone}`
                .replace(/^[+]84|^0/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            /* verify code */
            const verifyCode = checkCodeDTO.phone.slice(-6);
            if (verifyCode === checkCodeDTO.code) {
                userInfo.username = yourphone;
            } else {
                throw new HttpException('Không tìm thấy mã code. Vui lòng kiểm tra lại!', HttpStatus.BAD_REQUEST);
            }
        } else {
            if (!this.isActiveMessageHub) {
                userInfo = (await this.checkCodeSMSBrandName(partnerId, checkCodeDTO).toPromise()).data;
            } else {
                const verifyInfo = { partnerId, appId, phone: checkCodeDTO.phone, otpCode: checkCodeDTO.code };
                userInfo = await this.smsService.verifyOTP(verifyInfo);
            }
        }
        /* check lại */
        if (userInfo.username) {
            const userName = userInfo.username;
            let payload = { username: '', sub: 0, userMongoId: null };
            let resultData = {
                userId: 0,
                userName,
                fullName: '',
                number: '',
                email: '',
                token: '',
                historyBookingCount: 0,
                patientCount: 0,
            };
            const checkUserMongo = await this.checkExistsUserByUsernameMongoV1_5(userName);
            /* tiến hành tạo tài khoản mongo */
            let userMongoId = 0;
            if (!checkUserMongo) {
                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);
                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: userName,
                    fullname: '',
                });
                const result = (await userMongo.save()).toJSON();
                userMongoId = result.id;
            } else {
                userMongoId = checkUserMongo._id;
            }
            payload = {
                ...payload,
                username: userName,
                userMongoId,
            };

            resultData = { ...resultData, email: userName };
            // }
            /* Tạo chuỗi Json web token */
            const token = this.jwtService.sign(payload);
            return { ...resultData, token };
        } else if (userInfo.error_code) {
            throw new HttpException(userInfo.error_message, HttpStatus.BAD_REQUEST);
        }
    }

    async forEachOldHospital(userId: number): Promise<any> {
        const accessToken = md5(moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS'));
        const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');

        /* Nhi đồng 1 */
        try {
            const findSessionND1 = await this.sessionService.checkExistsNhiDong1SessionByUserID(userId);
            if (!findSessionND1) {
                await this.sessionService.insertNhiDong1({
                    user_id: userId,
                    access_token: accessToken,
                    date_create: currentTime,
                    date_update: currentTime,
                });
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'CSKH_CREATE_NEW_SESSION_V1',
                summary: 'CSKH ĐĂNG KÝ MỚI USER V1 Nhi đồng 1',
                nameParent: 'getUserIdAndCreateSessionV1',
                params: {
                    userId,
                    accessToken,
                    currentTime,
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user session v1',
            });
        }

        /* UMC */
        try {
            const findSessionUMC = await this.sessionService.checkExistsUMCSessionByUserID(userId);
            if (!findSessionUMC) {
                await this.sessionService.insertUMC({
                    user_id: userId,
                    access_token: accessToken,
                    date_create: currentTime,
                    date_update: currentTime,
                });
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'CSKH_CREATE_NEW_SESSION_V1',
                summary: 'CSKH ĐĂNG KÝ MỚI USER V1 UMC',
                nameParent: 'getUserIdAndCreateSessionV1',
                params: {
                    userId,
                    accessToken,
                    currentTime,
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user session v1',
            });
        }

        /* CTCH */
        try {
            const findSessionCTCH = await this.sessionService.checkExistsCTCHSessionByUserID(userId);
            if (!findSessionCTCH) {
                await this.sessionService.insertCTCH({
                    user_id: userId,
                    access_token: accessToken,
                    date_create: currentTime,
                    date_update: currentTime,
                });
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'CSKH_CREATE_NEW_SESSION_V1',
                summary: 'CSKH ĐĂNG KÝ MỚI USER V1 CTCH',
                nameParent: 'getUserIdAndCreateSessionV1',
                params: {
                    userId,
                    accessToken,
                    currentTime,
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user session v1',
            });
        }

        /* Da liễu */
        // const findSessionSkin = await this.sessionService.checkExistsSkinSessionByUserID(userId);
        // if (!findSessionSkin) {
        //     await this.sessionService.insertSkin({
        //         user_id: userId,
        //         access_token: accessToken,
        //         date_create: currentTime,
        //         date_update: currentTime,
        //     });
        // }

        /* Thủ Đức */
        // const findSessionThuDuc = await this.sessionService.checkExistsThuDucSessionByUserID(userId);
        // if (!findSessionThuDuc) {
        //     await this.sessionService.insertThuDuc({
        //         user_id: userId,
        //         access_token: accessToken,
        //         date_create: currentTime,
        //         date_update: currentTime,
        //     });
        // }
    }
    /* Chuẩn bị nâng lên version */
    async getUserIdV1AndCreateSessionNhiDong1(userName: string): Promise<any> {
        try {
            const userV1 = await this.checkExistsUserByUsername(userName);
            if (userV1) {
                const userId = Number(userV1.id);
                /* tiến hành tạo session */
                await this.forEachOldHospital(userId); /* Tiến hành tạo session */
            } else {
                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);
                /* Tiến hành tạo user */
                const [userIdResult] = await this.createUser({
                    username: userName,
                    fullname: userName,
                    salt: userSalt,
                    hashpwd: userHashPwd,
                });
                /* tiến hành tạo session */
                await this.forEachOldHospital(userIdResult); /* Tiến hành tạo session */
            }
        } catch (error) {
            throw new HttpException('aaaaa', HttpStatus.BAD_REQUEST);
        }
    }

    async getUserIdAndCreateSessionV1(appId: string, partnerId: string, user: IUser): Promise<any> {
        let userName = user.medproId;

        if (appId === 'umc' && partnerId === 'umc') {
            userName = user.username;
        }
        const userV1 = await this.checkExistsUserByUsername(userName);
        // Check if userName is mobile phone
        const isMobile = isMobilePhone(user.username, 'vi-VN');
        if (isMobile) {
            user.username = this.utilService.transformPhone(user.username);
        }
        try {
            if (userV1) {
                const userId = Number(userV1.id);
                /* tiến hành tạo session */
                await this.forEachOldHospital(userId); /* Tiến hành tạo session */
            } else {
                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);
                /* Tiến hành tạo user */
                // Check if userName is email
                const isEmailProvider = isEmail(userName);
                /* Tiến hành tạo user */
                const [userIdResult] = await this.createUser({
                    username: userName,
                    fullname: userName,
                    ...(isMobile && { phone: userName }),
                    ...(isEmailProvider && { email: userName }),
                    salt: userSalt,
                    hashpwd: userHashPwd,
                });
                /* tiến hành tạo session */
                await this.forEachOldHospital(userIdResult); /* Tiến hành tạo session */
                // update userV1
                await this.userModel.findByIdAndUpdate(user._id, { userIdV1: userIdResult }).exec();
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'CSKH_CREATE_NEW_V1',
                summary: 'CSKH ĐĂNG KÝ MỚI USER V1',
                nameParent: 'getUserIdAndCreateSessionV1',
                params: {
                    ...userV1,
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user v1',
            });
            throw new HttpException('aaaaa', HttpStatus.BAD_REQUEST);
        }
    }

    checkCodeSMSBrandName(partnerId: string, checkCodeDTO: CheckCodeRefDTO): Observable<AxiosResponse<any>> {
        // https://masteringjs.io/tutorials/axios/form-data
        const formData = new FormData();
        formData.append('device_id', checkCodeDTO.deviceId);
        formData.append('phone', checkCodeDTO.phone);
        formData.append('code', checkCodeDTO.code);

        let baseUrl = this.urlConfigService.getSMSCheckCode();
        switch (partnerId) {
            case 'nhidong1':
                baseUrl = this.urlConfigService.getSMSCheckCodeNhiDong1();
                break;
            case 'umc':
                baseUrl = this.urlConfigService.getSMSCheckCodeUMC();
                break;
            default:
                break;
        }

        try {
            return this.httpService.post(baseUrl, formData, {
                headers: formData.getHeaders(),
            });
        } catch (error) {
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    async checkPhoneThuDuc(checkPhoneDTO: CheckPhoneDTO, ipInfo): Promise<any> {
        /* check device_id trong phone login */
        const { countValue } = await this.phoneLoginService.checkDevicePhoneLoginChoRay(checkPhoneDTO.deviceId);
        if (countValue > 0) {
            // vui lòng chờ thêm giây lát
        }
        // tạo code
        const code = Array.from(Array(6))
            .map(() => {
                return this.utilService.getRandomInt(0, 10);
            })
            .join('');
        // insert code vào trong phone login
        const expiredDate = moment()
            .add(2, 'minutes')
            .format('YYYY-MM-DD HH:mm:ss');
        const yourPhone = `${checkPhoneDTO.phone}`
            .replace(/^[+]84|^0/, '84')
            .replace(/^9/, '849')
            .replace(/^3/, '843');
        /* Gửi thông tin sang SMS Brandname */
        const format = '%s la ma xac thuc tai khoan BV Quan Thu Duc cua ban. Ma nay chi có hieu luc trong 2 phut';
        const message = sprintfAPI.sprintf(format, code);
        // const smsInfo = this.smsService.sendSMSBrandNameThuDuc(yourPhone, message);
        // return smsInfo;
        const smsInfo = (await this.smsService.sendSMSBrandNameThuDuc(yourPhone, message).toPromise()).data;
        const [returnId] = await this.phoneLoginService.insertPhoneLoginThuDuc({
            ip: ipInfo.ip,
            device_id: checkPhoneDTO.deviceId,
            phone: yourPhone,
            code: md5(`${'PHONE_SECRET_KEY_111'}${code}`),
            expired_date: expiredDate,
        });
        const phoneLoginInfo = await this.phoneLoginService.getPhoneLoginInfoThuDuc(returnId);
        return { message, now: moment().format('YYYY-MM-DD HH:mm:ss'), expiredDate, phoneLoginInfo, smsInfo };
    }

    async checkCodeThuDuc(checkCodeDTO: CheckCodeDTO, ipInfo): Promise<any> {
        /* tìm lại thông tin */
        const phoneLogin = await this.phoneLoginService.checkCodeSMSBrandNameThuDuc(checkCodeDTO);
    }

    verifyAccessToken(url: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    getZaloInfo(url: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    async checkExistsUserByUsername(userName: any): Promise<any> {
        return this.pkhPatientKnex('user')
            .where('username', userName)
            .first();
    }

    async checkExistsUserByUsernameMongo(userName: any): Promise<any> {
        const users = await this.userModel
            .find({ username: userName })
            .limit(1)
            // .populate('subjects')
            .exec();
        return first(users);
    }

    async checkExistsUserByUsernameMongoV1_5(userName: any): Promise<any> {
        const users = await this.userModel
            .find({ username: userName, medproId: { $eq: null } })
            .limit(1)
            // .populate('subjects')
            .exec();
        return first(users);
    }

    async checkExistsUserByUsernameMedproId(userName: any): Promise<any> {
        const users = await this.userModel
            .find({ username: userName, medproId: `mp${userName}` })
            .limit(1)
            // .populate('subjects')
            .exec();
        return first(users);
    }

    async syncUserV1DaLieuSocialToMedproId(syncProcess: any, method: string = 'unknown'): Promise<any> {
        // ISyncUserDaLieuProcess
        const result: IUser = await this.insertNewMedproIdDaLieuSocial(syncProcess);
        /* tiến hành tạo signInProvider */
        const isZaloProvider = `${syncProcess.username}`.includes('zl_');
        if (isZaloProvider) {
            /* Kiểm tra xem user là zalo hay ko */
            await this.createNewZaloProvider(result);
        } else {
            await this.createNewFirebaseProvider(result, method);
        }
        return result;
    }

    async syncUserV1NhiDong1SocialToMedproId(syncProcess: any, method: string = 'unknown'): Promise<any> {
        // ISyncUserDaLieuProcess
        const result: IUser = await this.insertNewMedproIdNhiDong1Social(syncProcess);
        /* tiến hành tạo signInProvider */
        const isZaloProvider = `${syncProcess.username}`.includes('zl_');
        if (isZaloProvider) {
            /* Kiểm tra xem user là zalo hay ko */
            await this.createNewZaloProvider(result);
        } else {
            await this.createNewFirebaseProvider(result, method);
        }
        return result;
    }

    async syncUserV1DHYDSocialToMedproId(syncProcess: any, method: string = 'unknown'): Promise<any> {
        // ISyncUserDaLieuProcess
        const result: IUser = await this.insertNewMedproIdDHYDSocial(syncProcess);
        /* tiến hành tạo signInProvider */
        // console.log('result', result);
        const isZaloProvider = `${syncProcess.username}`.includes('zl_');
        if (isZaloProvider) {
            /* Kiểm tra xem user là zalo hay ko */
            await this.createNewZaloProvider(result);
        } else {
            await this.createNewFirebaseProvider(result, method);
        }
        return result;
    }

    async insertNewMedproIdDHYDSocial(syncProcess: any): Promise<any> {
        // ISyncUserDaLieuProcess

        const { id: userIdV1, username, fullname } = syncProcess;

        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);

        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${username}`,
                });
                await newUserConstraints.save();

                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${username}`,
                    userIdV1,
                    umcUser: userIdV1,
                    nextStep: 'REQUIRED_PHONE',
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(username);
        }
    }

    async insertNewMedproIdNhiDong1Social(syncProcess: any): Promise<any> {
        // ISyncUserDaLieuProcess

        const { id: userIdV1, username, fullname } = syncProcess;

        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);

        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${username}`,
                });
                await newUserConstraints.save();

                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${username}`,
                    userIdV1,
                    nd1User: userIdV1,
                    nextStep: 'REQUIRED_PHONE',
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(username);
        }
    }

    async insertNewMedproIdDaLieuSocial(syncProcess: any): Promise<any> {
        // ISyncUserDaLieuProcess

        const { id: userIdV1, username, fullname } = syncProcess;

        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);

        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${username}`,
                });
                await newUserConstraints.save();

                const userSalt = this.generateSalt();
                const userHashPwd = this.generateHashPwd(userSalt, userSalt);

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${username}`,
                    userIdV1,
                    daLieuUser: userIdV1,
                    nextStep: 'REQUIRED_PHONE',
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(username);
        }
    }

    async syncUserV1DaLieuToMedproId(userIdV1: number, username: string, fullname: string): Promise<any> {
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        const result: IUser = await this.insertNewMedproId(checkUserMongo, username, userSalt, userHashPwd, fullname, userIdV1);
        /* tiến hành tạo signInProvider */
        await this.insertNewPasswordProvider(result, username, userSalt, userHashPwd, fullname);
        return result;
    }

    async syncDaLieuUserV1ToMedproId(userIdV1: number, username: string, fullname: string): Promise<any> {
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        const result: IUser = await this.insertNewDaLieuMedproId(checkUserMongo, username, userSalt, userHashPwd, fullname, userIdV1);
        /* tiến hành tạo signInProvider */
        await this.insertNewDaLieuPasswordProvider(result, username, userSalt, userHashPwd, fullname);
        return result;
    }

    async insertNewDaLieuMedproId(
        checkUserMongo: IUser,
        userName: string,
        userSalt: string,
        userHashPwd: string,
        fullname: string = '',
        userIdV1: number = 0,
    ): Promise<any> {
        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                    daLieuUser: userIdV1,
                });
                await newUserConstraints.save();

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${userName}`,
                    userIdV1,
                    daLieuUser: userIdV1,
                    nextStep: 'REQUIRED_PASSWORD',
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(userName);
        }
    }

    async insertNewDaLieuPasswordProvider(
        result: IUser,
        userName: string,
        userSalt: string,
        userHashPwd: string,
        fullname: string = '',
    ): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: result._id,
                daLieuUser: result.daLieuUser || 0,
            })
            .exec();

        if (checkProvider) {
            return null;
        }

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `mp${userName}_password`,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: userHashPwd,
                salt: userSalt,
                user: result._id,
                displayName: fullname,
                daLieuUser: result.daLieuUser || 0,
            });
            await signInProvider.save();
            return true;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    async syncNd1UserV1ToMedproId(userIdV1: number, username: string, fullname: string): Promise<any> {
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        const result: IUser = await this.insertNewNd1MedproId(checkUserMongo, username, userSalt, userHashPwd, fullname, userIdV1);
        /* tiến hành tạo signInProvider */
        await this.insertNewNd1PasswordProvider(result, username, userSalt, userHashPwd, fullname);
        return result;
    }

    async syncUMCUserV1ToMedproId(userIdV1: number, username: string, fullname: string): Promise<any> {
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        const result: IUser = await this.insertNewUMCMedproId(checkUserMongo, username, userSalt, userHashPwd, fullname, userIdV1);
        /* tiến hành tạo signInProvider */
        await this.insertNewUMCPasswordProvider(result, username, userSalt, userHashPwd, fullname);
        return result;
    }

    async insertNewUMCMedproId(
        checkUserMongo: IUser,
        userName: string,
        userSalt: string,
        userHashPwd: string,
        fullname: string = '',
        userIdV1: number = 0,
    ): Promise<any> {
        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                    umcUser: userIdV1,
                });
                await newUserConstraints.save();

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${userName}`,
                    userIdV1,
                    umcUser: userIdV1,
                    nextStep: 'REQUIRED_PASSWORD',
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(userName);
        }
    }

    async insertNewUMCPasswordProvider(result: IUser, userName: string, userSalt: string, userHashPwd: string, fullname: string = ''): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: result._id,
                umcUser: result.umcUser || 0,
            })
            .exec();

        if (checkProvider) {
            return null;
        }

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `mp${userName}_password`,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: userHashPwd,
                salt: userSalt,
                user: result._id,
                displayName: fullname,
                umcUser: result.umcUser || 0,
            });
            await signInProvider.save();
            return true;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    async insertNewNd1MedproId(
        checkUserMongo: IUser,
        userName: string,
        userSalt: string,
        userHashPwd: string,
        fullname: string = '',
        userIdV1: number = 0,
    ): Promise<any> {
        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                    nd1User: userIdV1,
                });
                await newUserConstraints.save();

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${userName}`,
                    userIdV1,
                    nd1User: userIdV1,
                    nextStep: 'REQUIRED_PASSWORD',
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(userName);
        }
    }

    async insertNewNd1PasswordProvider(result: IUser, userName: string, userSalt: string, userHashPwd: string, fullname: string = ''): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: result._id,
                nd1User: result.nd1User || 0,
            })
            .exec();

        if (checkProvider) {
            return null;
        }

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `mp${userName}_password`,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: userHashPwd,
                salt: userSalt,
                user: result._id,
                displayName: fullname,
                daLieuUser: result.daLieuUser || 0,
            });
            await signInProvider.save();
            return true;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    async syncUserV1ToMedproId(userIdV1: number, username: string, fullname: string): Promise<any> {
        const checkUserMongo: IUser = await this.checkExistsUserByUsernameMedproId(username);
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        const result: IUser = await this.insertNewMedproId(checkUserMongo, username, userSalt, userHashPwd, fullname, userIdV1);
        /* tiến hành tạo signInProvider */
        await this.insertNewPasswordProvider(result, username, userSalt, userHashPwd, fullname);
        return result;
    }

    async insertNewMedproId(
        checkUserMongo: IUser,
        userName: string,
        userSalt: string,
        userHashPwd: string,
        fullname: string = '',
        userIdV1: number = 0,
    ): Promise<any> {
        try {
            if (!checkUserMongo) {
                /* Tiến hành lưu user constraints trước */
                const newUserConstraints = new this.constraintUser({
                    medproId: `mp${userName}`,
                });
                await newUserConstraints.save();

                /* Tạo mới thông tin user trên mongo */
                const userMongo = new this.userModel({
                    username: userName,
                    password: userHashPwd,
                    salt: userSalt,
                    email: '',
                    fullname,
                    medproId: `mp${userName}`,
                    userIdV1,
                });
                return await userMongo.save();
            } else {
                return checkUserMongo;
            }
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(userName);
        }
    }

    async cskhCreateUser(userName: string): Promise<any> {
        /* Tiến hành lưu user constraints trước */
        try {
            const newUserConstraints = new this.constraintUser({
                medproId: `mp${userName}`,
            });
            await newUserConstraints.save();

            const userSalt = this.generateSalt();
            const userHashPwd = this.generateHashPwd(userSalt, userSalt);

            /* Tạo mới thông tin user trên mongo */
            const userMongo = new this.userModel({
                username: userName,
                password: userHashPwd,
                salt: userSalt,
                email: '',
                fullname: userName,
                medproId: `mp${userName}`,
            });
            return await userMongo.save();
        } catch (error) {
            return await this.checkExistsUserByUsernameMedproId(userName);
        }
    }

    async cskhCreateNewPasswordProvider(result: IUser): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: result._id,
            })
            .exec();

        if (checkProvider) {
            return null;
        }

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `mp${result.username}_password`,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: result.password,
                salt: result.salt,
                user: result._id,
                displayName: result.fullname,
            });
            await signInProvider.save();
            return true;
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'CSKH_CREATE_NEW_V1',
                summary: 'CSKH ĐĂNG KÝ MỚI USER V1',
                nameParent: 'cskhCreateNewPasswordProvider',
                params: {
                    ...result.toObject(),
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user v1',
            });
            return null;
        }
    }

    async insertNewPasswordProvider(result: IUser, userName: string, userSalt: string, userHashPwd: string, fullname: string = ''): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: result._id,
            })
            .exec();

        if (checkProvider) {
            return null;
        }

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `mp${userName}_password`,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: userHashPwd,
                salt: userSalt,
                user: result._id,
                displayName: fullname,
            });
            await signInProvider.save();
            return true;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    generateHashPwd(str = '', salt = '') {
        return md5(`${str}${salt}`);
    }

    generateSalt(vLength = 16) {
        const alphabet = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = alphabet.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, alphabet[n]];
        }
        return pass.join('');
    }

    async createUser(userData: any): Promise<any> {
        const traceId = uuid.v4().replace(/-/g, '');
        let username = userData?.username;

        if (isMobilePhone(username, 'vi-VN')) {
            username = this.utilService.transformPhone(username);
        }
        try {
            const user = await this.pkhPatientKnex('user')
                .where('username', username)
                .first();

            if (!user) {
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'createUser',
                    summary: 'Tao user v1',
                    nameParent: 'createUser',
                    params: {
                        ...userData,
                        username,
                        traceId,
                    },
                    message: 'Tạo mới thông tin account user v1',
                });
                return this.pkhPatientKnex('user').insert(userData);
            }

            return [user.id];
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'createUser',
                summary: 'Tao user v1',
                nameParent: 'createUser',
                params: {
                    ...userData,
                    username,
                    traceId,
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user v1',
            });
        }
    }

    async checkMomoId(momoId: string): Promise<any> {
        return this.userModel.findOne({ momoId }).exec();
    }

    async checkZaloIdExist(zaloId: string): Promise<any> {
        return this.userModel.findOne({ zaloId }).exec();
    }

    async checkViettelMoneyIdExist(viettelMoneyId: string): Promise<any> {
        return this.userModel.findOne({ viettelMoneyId }).exec();
    }
    
    async createUserMoMo(userName: string): Promise<any> {
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        /* Tạo mới thông tin user trên mongo */
        const userMongo = new this.userModel({
            username: userName,
            password: userHashPwd,
            salt: userSalt,
            email: userName,
            fullname: '',
            momoId: userName,
            medproId: `mp${userName}`,
        });
        return userMongo.save();
    }
    async createUserZalo(userName: string): Promise<any> {
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);
        /* Tạo mới thông tin user trên mongo */
        const userMongo = new this.userModel({
            username: userName,
            password: userHashPwd,
            salt: userSalt,
            email: userName,
            fullname: '',
            zaloId: userName,
            medproId: `mp${userName}`,
        });
        return userMongo.save();
    }

    async getUserByMongoUserId(userId: string): Promise<any> {
        return this.userModel.findById({ _id: userId }).exec();
    }

    async getUserByPhone(username: string): Promise<any> {
        return this.userModel
            .find({
                username,
                medproId: `mp${username}`,
            })
            .exec();
    }

    async createUserConstraints(user: IUser): Promise<any> {
        const medproId = `mp${user.username}`;
        try {
            const newUserConstraints = new this.constraintUser({
                medproId,
                tvUser: user._id,
            });
            return newUserConstraints.save();
        } catch (error) {
            return this.constraintUser.findOne({ medproId }).exec();
        }
    }

    async upgradeUserTrungVuong(user: IUser): Promise<any> {
        const medproId = `mp${user.username}`;
        return this.userModel
            .findByIdAndUpdate(
                {
                    _id: user._id,
                },
                { medproId, tvUser: user._id, nextStep: 'REQUIRED_PASSWORD' },
                { new: true },
            )
            .exec();
    }

    async upgradeSocialUserTrungVuong(user: IUser): Promise<any> {
        const medproId = `mp${user.username}`;
        return this.userModel
            .findByIdAndUpdate(
                {
                    _id: user._id,
                },
                { medproId, tvUser: user._id, nextStep: 'REQUIRED_PHONE' },
                { new: true },
            )
            .exec();
    }

    /* Dành cho User bệnh viện Da liễu */
    async createDaLieuUserConstraints(user: IUser): Promise<any> {
        const medproId = `mp${user.username}`;
        try {
            const newUserConstraints = new this.constraintUser({
                medproId,
                daLieuUser: user._id,
            });
            return newUserConstraints.save();
        } catch (error) {
            return this.constraintUser.findOne({ medproId }).exec();
        }
    }

    async upgradeDaLieuUserTrungVuong(user: IUser): Promise<any> {
        const medproId = `mp${user.username}`;
        return this.userModel
            .findByIdAndUpdate(
                {
                    _id: user._id,
                },
                { medproId, daLieuUser: user._id, nextStep: 'REQUIRED_PASSWORD' },
                { new: true },
            )
            .exec();
    }

    async upgradeSocialDaLieuUserTrungVuong(user: IUser): Promise<any> {
        const medproId = `mp${user.username}`;
        return this.userModel
            .findByIdAndUpdate(
                {
                    _id: user._id,
                },
                { medproId, daLieuUser: user._id, nextStep: 'REQUIRED_PHONE' },
                { new: true },
            )
            .exec();
    }
    /* END Dành cho User bệnh viện Da liễu */

    async createNewPasswordProvider(user: IUser): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const userSalt = this.generateSalt();
        const userHashPwd = this.generateHashPwd(userSalt, userSalt);

        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: user._id,
            })
            .exec();

        if (checkProvider) {
            return checkProvider;
        }

        const providerId = `mp${user.username}_password`;
        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: userHashPwd,
                salt: userSalt,
                user: user._id,
                displayName: user.fullname,
                tvUser: user._id,
            });
            return signInProvider.save();
        } catch (error) {
            return this.constraintProvider.findOne({ providerId }).exec();
        }
    }

    async createNewZaloProvider(user: IUser): Promise<any> {
        const providerId = `${user.username}`;
        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId,
            });
            await newProviderConstraints.save();

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'zalo',
                email: `${user.username}`,
                user: user._id,
                method: 'zalo',
                displayName: user.fullname,
            });
            return signInProvider.save();
        } catch (error) {
            return this.signInProvider.findOne({ email: providerId }).exec();
        }
    }

    async findAndUpdateUnknownMethodFirebase(username: string, method: string): Promise<any> {
        try {
            const findSignInProvide = await this.signInProvider
                .findOneAndUpdate(
                    {
                        email: username,
                        method: 'unknown',
                    },
                    {
                        method,
                    },
                    { new: true },
                )
                .exec();

            if (findSignInProvide) {
                const providerId = `${username}_unknown`;
                /* tiến hành cập nhật lại constraints provider */
                await this.constraintProvider
                    .findOneAndUpdate(
                        {
                            providerId,
                        },
                        {
                            providerId: `${username}_${method}`,
                        },
                        { new: true },
                    )
                    .exec();
            }

            return true;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return true;
        }
    }

    async createNewFirebaseProvider(user: IUser, method: string = 'unknown'): Promise<any> {
        // let method = 'unknown';
        const firebaseId = user.username;
        /* Tìm trong signInProvider trước để lấy lại cái method cho đúng flow */
        const findSignInProvider = await this.signInProvider
            .findOne({
                email: user.username,
            })
            .exec();

        if (findSignInProvider) {
            method = findSignInProvider.method;
        }

        const providerId = `${firebaseId}_${method}`;

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId,
            });
            await newProviderConstraints.save();

            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'firebase',
                email: firebaseId,
                user: user._id,
                method,
                displayName: user.fullname,
            });
            return signInProvider.save();
        } catch (error) {
            return this.signInProvider.findOne({ email: firebaseId, method }).exec();
        }
    }

    async mergeUserMedproId(user10: IUser, user11: IUser): Promise<any> {
        const userFrom = await this.userModel.findById({ _id: user10._id }).exec();
        const userTo = await this.userModel.findById({ _id: user11._id }).exec();
        const userFromObj = userFrom.toObject();
        const userToObj = userTo.toObject();
        const patientsFrom = userFromObj.patients;
        const patientsTo = userToObj.patients;
        const joinPatients = [...patientsFrom, ...patientsTo];
        const uniquePatients = uniq(joinPatients);
        /* tiến hành cập nhật thông tin patients cho user */
        const newUserTo = await this.userModel
            .findByIdAndUpdate(
                {
                    _id: userTo._id,
                },
                {
                    patients: uniquePatients,
                    prevPatients: userTo.patients,
                    prevUser: userFrom._id,
                },
                { new: true },
            )
            .exec();
        /* tiến hành cập nhật lại userFrom */
        await this.userModel.findByIdAndUpdate(
            {
                _id: userFrom._id,
            },
            {
                username: `${userFrom.username}_upgrade`,
                medproId: `mp${userFrom.username}_upgrade`,
            },
            { new: true },
        );
        return newUserTo;
    }

    async uploadAvatarTelemed(partnerId: string, fileINput: any, secretKey: string, userId: string, email: string): Promise<any> {
        try {
            const allow = secretKey === this.repoConfigService.getTelemedSecret() ? true : false;
            if (!allow) {
                throw new HttpException('Không được phép ! Xin nhập secret key !', 403);
            }
            const idAvatar = fileINput.id;
            const file = await this.filesService.findInfo(idAvatar);
            const filestream = await this.filesService.readStream(idAvatar);
            if (!filestream) {
                throw new HttpException('An error occurred while retrieving file', HttpStatus.EXPECTATION_FAILED);
            }

            const existProfile = await this.userProfileModel
                .findOne({
                    partnerId,
                    user: userId,
                })
                .exec();

            if (!existProfile) {
                const user = new this.userProfileModel({
                    id: uuid.v4().replace(/-/g, ''),
                    user: userId,
                    partnerId,
                    email,
                });
                await user.save();
            }

            await this.userProfileModel.findOneAndUpdate(
                {
                    partnerId,
                    user: userId,
                },
                { avatarId: idAvatar },
            );

            return {
                file,
                filestream,
            };
        } catch (error) {
            throw error;
        }
    }

    async getAvatarTelemedInfo(doctor: string, partnerId: string): Promise<any> {
        try {
            const profile = await this.userProfileModel.findOne({ user: doctor, partnerId }).exec();
            if (profile && profile?.avatarUrl) {
                return {
                    avatarUrl: profile?.avatarUrl,
                };
            }
            if (profile && profile.avatarId) {
                const { avatarId: idAvatar } = profile;
                try {
                    const file = await this.filesService.findInfo(idAvatar);
                    return {
                        ...file,
                        avatarUrl: `${this.urlConfigService.getBaseUrl()}files/${idAvatar}`,
                    };
                } catch (error) {
                    return { avatarUrl: '' };
                }
            }
            return {};
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'getAvatarTelemedInfo',
                summary: 'Lấy thông tin avatar profile',
                nameParent: 'getAvatarTelemedInfo',
                params: {
                    doctor,
                    partnerId,
                },
                errorBody: this.utilService.errorHandler(error),
                message: `Lỗi lấy image`,
            });
            return {};
        }
    }

    async checkZaloId(zaloId: string): Promise<any> {
        const signIn: ISignInProvider = await this.signInProvider
            .findOne({
                method: 'zalo',
                email: zaloId,
            })
            .exec();
        if (signIn) {
            /* Tìm lại thông tin user */
            return this.userModel
                .findById({
                    _id: signIn.user,
                })
                .exec();
        }
        return null;
    }

    async checkFirebaseIdV2(firebaseId: string): Promise<any> {
        const signIn: ISignInProvider = await this.signInProvider
            .findOne({
                email: firebaseId,
            })
            .exec();
        if (signIn) {
            /* Tìm lại thông tin user */
            return this.userModel
                .findById({
                    _id: signIn.user,
                })
                .exec();
        }
        return null;
    }

    async generateQRCode(text: string): Promise<any> {
        return this.utilService.generateQRCode(text);
    }

    async generateBarCode(text: string): Promise<any> {
        return this.utilService.generateBarCode(text);
    }

    async findByUsername(username: string, momoId: string): Promise<any> {
        try {
            const user = await this.userModel.findOne({ username }).exec();
            if (user) {
                return await this.userModel.findByIdAndUpdate({ _id: user._id }, { momoId }).exec();
            }
            return user;
        } catch (error) {
            this.logger.error(`Error when exec isExistsByUserName with username: ${username}. Cause: ${error.message}`);
            throw error;
        }
    }

    async verifyCskhToken(userMongoId: string) {
        try {
            const payload = { username: '', sub: 0, userMongoId: 0 };
            const resultData = {
                userId: 0,
                userName: '',
                fullName: '',
                number: '',
                email: '',
                token: '',
                historyBookingCount: 0,
                patientCount: 0,
            };
            const checkUserMongo: IUser = await this.userModel.findById(userMongoId);
            if (!checkUserMongo) {
                throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.BAD_REQUEST);
            }
            resultData.userName = checkUserMongo.username;
            resultData.fullName = checkUserMongo.fullname;
            payload.userMongoId = checkUserMongo._id;

            const findSignInProvide = await this.signInProvider.findOne({ type: 'password', user: checkUserMongo._id }).exec();
            if (!findSignInProvide) {
                throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.BAD_REQUEST);
            }

            await this.signInProvider.findOneAndUpdate({ _id: findSignInProvide._id }, { updatedAt: moment().toDate() }).exec();
            /* lấy displayName trong signInProvider */
            const toObj = findSignInProvide.toObject();
            const getDisplayName = get(toObj, 'displayName', '');
            if (!!getDisplayName) {
                resultData.fullName = getDisplayName;
            }

            resultData.email = checkUserMongo.email;
            const token = this.jwtService.sign(payload);
            return { ...resultData, token, isCS: checkUserMongo.isCS, userMongoId: checkUserMongo._id };
        } catch (error) {
            throw error;
        }
    }

    async cskhOfflineCreateNewPasswordProvider(result: IUser, password: string): Promise<any> {
        /* kiểm tra xem phương thức gửi lên đã có chưa */
        const checkProvider = await this.signInProvider
            .findOne({
                type: 'password',
                user: result._id,
            })
            .exec();

        if (checkProvider) {
            return null;
        }

        try {
            /* Tiến hành lưu user constraints trước */
            const newProviderConstraints = new this.constraintProvider({
                providerId: `mp${result.username}_password`,
            });
            await newProviderConstraints.save();

            const genPassword = this.generateHashPwd(password, result.salt);

            /* tiến hành tạo thêm thông tin trong sign_in_provider */
            const signInProvider = new this.signInProvider({
                id: uuid.v4().replace(/-/g, ''),
                type: 'password',
                password: genPassword,
                salt: result.salt,
                user: result._id,
                displayName: result.fullname,
            });
            await signInProvider.save();
            return true;
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'CSKH_CREATE_NEW_V1',
                summary: 'CSKH ĐĂNG KÝ MỚI USER V1',
                nameParent: 'cskhCreateNewPasswordProvider',
                params: {
                    ...result.toObject(),
                },
                errorBody: this.utilService.errorHandler(error),
                message: 'Lỗi tạo mới thông tin account user v1',
            });
            return null;
        }
    }

    checkUserUMCByUserIdV1(userIdV1: number): Promise<any> {
        return this.pkhPatientKnex(this.umcUserTable)
            .where('id', Number(userIdV1))
            .first();
    }

    async syncInfoForUMCUser(user: IUser): Promise<void> {
        try {
            // find user, if user is owned by umc then try to sync from v1
            const userV1 = (await this.checkUserUMCByUserIdV1(user?.userIdV1)) || (await this.checkExistsUserByUsername(user?.username));

            if (userV1) {
                // pull booking and patient + user from v1
                this.logger.verbose(`Run sync umc for userV1: ${user.userIdV1}, username: ${user.username}`);
                await this.remindSyncUMC(user);
            }
        } catch (error) {
            this.logger.error(`Error when exec syncInfoForUMCUser. Cause: ${error.message}`);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'syncForUser',
                summary: 'Đồng bộ useInfo cho UMC',
                nameParent: 'syncForUser',
                params: {
                    user,
                },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message,
            });
        }
    }

    async syncForUser(phone: string): Promise<unknown> {
        const traceId = uuid.v4().replace(/-/g, '');

        try {
            const username = this.utilService.transformPhone(phone);
            const user: IUser = await this.userModel.findOne({ username }).exec();

            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'syncForUser',
                summary: 'Đồng bộ useInfo cho mode CSKH',
                nameParent: 'syncForUser',
                params: {
                    phone,
                    username,
                    user,
                    traceId,
                },
            });
            return this.syncInfoForUMCUser(user);
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'syncForUser',
                summary: 'Đồng bộ useInfo cho mode CSKH',
                nameParent: 'syncForUser',
                params: {
                    phone,
                    traceId,
                },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message,
            });

            this.logger.error(`Error when exec syncForUser. Cause: ${error.message}`);
        }
    }

    async getMedproIdUserByToken(userMongoId: string, token?:string): Promise<any> {
        const user = await this.userModel
            .findById(userMongoId, { username: true, fullname: true, belongToOrgs: true, isCS: true, email: true })
            .populate('belongToOrgs')
            .exec();
        const resultData = {
            ...user.toObject(),
            userId: user._id,
            fullName: user?.fullname,
            userMongoId: user._id,
            number: '',
            token: token,
            historyBookingCount: 0,
            patientCount: 0,
            id: user._id
            
        };
        return resultData
    }

    async saveContactUs(body: ContactUsDto, appid: string, partnerid = 'medpro'): Promise<IContactUs> {
        const { captchaResponse, ...data } = body;
    
        if (appid === 'medpro') {
            if (!body?.captchaResponse) {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
            await this.verifyGoogleRecaptcha({ captchaResponse: body.captchaResponse });
        }
    
        // Kiểm tra các trường bắt buộc dựa trên type
        switch (body.type) {
            case ContactUsTypeEnum.contact:
            case ContactUsTypeEnum.collab:
                if (!body.note || !body.email) {
                    throw new HttpException('Ghi chú và Email là bắt buộc.', HttpStatus.BAD_REQUEST);
                }
                break;
            case ContactUsTypeEnum.advertise:
                if (!body.typeBussiness) {
                    throw new HttpException('Loại hình kinh doanh là bắt buộc.', HttpStatus.BAD_REQUEST);
                }
                break;
            case ContactUsTypeEnum.package:
                if (!body.company || !body.employees) {
                    throw new HttpException('Công ty và số nhân viên là bắt buộc.', HttpStatus.BAD_REQUEST);
                }
                break;
            default:
                break;
        }
    
        return await this.contactUsModel.create(data).then(async res => {
            const larkEnv = this.larkConfig.larkEnv === 'PRODUCTION' ? '' : ` [${this.larkConfig.larkEnv}] `;
            let isMedproId = false;
    
            if (res.phone) {
                const thisPhoneNumber = res.phone
                    .replace(/^[+]84|^0/, '+84')
                    .replace(/^84/, '+84')
                    .replace(/^9/, '+849')
                    .replace(/^3/, '+843');
                isMedproId = await this.userModel.exists({ username: thisPhoneNumber, medproId: `mp${thisPhoneNumber}` });
            }
    
            const msgContent = larkMsgAddContactUsTemplate(
                larkEnv,
                moment(res['createdAt']).utc().add(7, 'hours').format(`DD/MM/YYYY HH:mm:ss`),
                res.fullname || '',
                res.email || '',
                res.phone || '',
                res.note || '',
                isMedproId,
                data.type === ContactUsTypeEnum.package
                    ? 'Doanh nghiệp'
                    : data.type === ContactUsTypeEnum.advertise
                    ? 'Quảng cáo'
                    : data.type === ContactUsTypeEnum.collab
                    ? 'Cộng tác viên'
                    : 'Tham gia medpro',
                data.company,
                data.typeBussiness,
                data.employees,
            );
    
            this.httpService
                .post(this.larkConfig.larkReceiveContactUsUrl, {
                    msg_type: 'interactive',
                    card: msgContent,
                })
                .toPromise()
                .then(() => {
                    console.log('[SUCCESS] push message to lark');
                })
                .catch(e => {
                    console.error('[ERROR] push message to lark: ', e);
                });
    
            return res;
        });
    }
    

    async findMedproId(filter: FindMedproIdDto): Promise<any> {
        const isPhone = isMobilePhone(filter.phone, 'vi-VN');
        const username = `${filter.phone}`
            .replace(/^[+]84|^0/, '84')
            .replace(/^9/, '849')
            .replace(/^3/, '843');

        let filterSql = isPhone
            ? {
                  username: `+${username}`,
              }
            : {
                  username: { $regex: `${username}`, $options: 'i' },
              };

        const total = await this.userModel.countDocuments(filterSql);
        let getUsers = await this.userModel
            .find(filterSql, {
                password: false,
                salt: false,
            })
            .skip(filter.skip)
            .limit(filter.limit)
            .lean();

        const rows = await Promise.all(
            getUsers.map(async (u: any) => {
                return {
                    ...u,
                    signMethod: await this.signInProvider
                        .find({ user: u._id }, { salt: false, password: false })
                        .sort({ updatedAt: 'desc' })
                        .exec(),
                };
            }),
        );

        return {
            page: filter.page,
            limit: filter.limit,
            rows,
            total,
        };
    }

    async updateMedproId(id: string, data: UpdateMedproIdDto, user: any): Promise<any> {
        const dataTracking: any = {
            action: 'UPDATE_USER_INFO',
            dataBefore: {},
            dataAfter: {},
            userAction: user._id,
            userPatient: id,
        };

        let thisMedproId = await this.userModel.findOne({ _id: id }).exec();
        if (!thisMedproId) {
            throw new HttpException('Tài khoản này không tồn tại!', 404);
        }

        const signInMethodIds = data.signMethod?.map(i => i.id) || [];
        const signInProviders = await this.signInProvider.find({ id: { $in: signInMethodIds } }).exec();

        dataTracking.dataBefore = {
            user: thisMedproId.toObject(),
            signInProviders: signInProviders.map(sg => sg.toObject()),
        };

        if (!!data.fullname) thisMedproId.fullname = data.fullname;
        if (!!data.username) thisMedproId.username = data.username;
        if (!!data.medproId) thisMedproId.medproId = data.medproId;
        if (!!data.email) thisMedproId.email = data.email;
        data.signMethod?.forEach(p => {
            this.signInProvider.updateOne({ id: p.id }, { displayName: p.displayName }).exec();
        });

        await thisMedproId.save();

        const userAfter = await this.userModel.findOne({ _id: id }).exec();
        const signInProviderAfter = await this.signInProvider.find({ id: { $in: signInMethodIds } }).exec();

        dataTracking.dataAfter = {
            user: userAfter?.toObject(),
            signInProviders: signInProviderAfter.map(sg => sg.toObject()),
        };

        this.emitService.emit(PATIENT_TRACKING_EVENT, dataTracking);

        return { isOk: true };
    }

    async updateVisaUser(userId: string, data: UpdateUserMedproDto): Promise<any> {
        try {
            const visaUser = new this.visaUserModel({
                userId: userId,
                user: userId,
                email: data.email,
                phone: data.username,
                fullname: data.fullname
            });
            await visaUser.save();
        } catch (error) {
            console.log('error', error);
            const existVisaUser = await this.visaUserModel.findOne({ userId }).read('primary').exec();
            await this.visaUserModel.findByIdAndUpdate(
                { _id: existVisaUser._id }, 
                {   
                    email: data.email,
                    phone: data.username,
                    fullname: data.fullname
                },
            ).read('primary').exec();
        }
        return { isOk: true }
    }

    async verifyGoogleRecaptcha(formData: { captchaResponse: string }) {
        try {
            const { data } = await this.httpService
                .post(
                    'https://www.google.com/recaptcha/api/siteverify',
                    {},
                    {
                        params: {
                            secret: this.recaptchaConfig.getSecret(),
                            response: formData.captchaResponse,
                        },
                    },
                )
                .toPromise();

            if (data.success !== true) {
                throw new HttpException({ message: 'Lỗi xác thực reCAPTCHA, vui lòng thử lại!', recaptchaError: true }, HttpStatus.BAD_REQUEST);
            }
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }

            console.error('error verifyGoogleRecapcha: ', err);

            throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
        }
    }
    
    async verifyRecaptchaCloudflare(formData: { captchaResponse: string }) {
        try {
            const { data } = await this.httpService
                .post(
                    'https://challenges.cloudflare.com/turnstile/v0/siteverify',
                    {
                        secret: this.recaptchaConfig.getSecretCloudflare(),
                        response: formData.captchaResponse,
                    },
                )
                .toPromise();
            
            if (data.success !== true) {
                throw new HttpException({ message: 'Lỗi xác thực reCAPTCHA 1, vui lòng thử lại!', recaptchaError: true }, HttpStatus.BAD_REQUEST);
            }
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }

            console.error('error verifyGoogleRecapcha: ', err);

            throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau 1', HttpStatus.BAD_REQUEST);
        }
    }

    async getDoctorList(partnerId: string = 'digimed') {
        let data = await this.userDoctorModel
            .find({ partnerId }, { doctorId: true, userId: true, email: true, partnerId: true, doctor: true, createdAt: true, updatedAt: true })
            .exec();
        const thisDoctors = await this.doctorModel.find({ _id: { $in: data.map(d => d.doctor) } }).exec();
        const thisUsers = await this.userModel.find({ _id: { $in: data.map(d => d.userId) } }).exec();
        return data.map(item => {
            const { ...rest } = item.toObject() as any;
            const thisUser = thisUsers.find(u => String(u._id) == String(item.userId));
            return { ...rest, phone: thisUser?.phone || thisUser?.username, name: thisDoctors.find(d => String(d._id) == String(item.doctor))?.name };
        });
    }

    async getLoggedInDevice(id: string) {
        const thisUserDoctor = await this.userDoctorModel.findOne({ _id: id }).exec();
        if (!thisUserDoctor) {
            throw new HttpException('Không tìm thấy thông tin', 404);
        }
        return this.pushDeviceModel.find({ userId: thisUserDoctor.userId }).exec();
    }

    async getPhoneList(locale: string = 'vi') {
        if (locale === 'km') {
            return JSON.parse(await this.globalSettingService.findByKeyAndRepoName('CONFIG_PHONE_USER_COUNTRY_KM'));
        } else {
            return JSON.parse(await this.globalSettingService.findByKeyAndRepoName('CONFIG_PHONE_USER_COUNTRY'));
        }
    }

    async getPhoneListPatient() {
        const cf = JSON.parse(await this.globalSettingService.findByKeyAndRepoName('CONFIG_PHONE_USER_COUNTRY_PATIENT'));
        return Object.keys(cf).map(k => cf[k]);
    }

    async sendOtpToConvertUser(formData: ConvertUserOtpDTO): Promise<any> {
        const smsInfo = { partnerId: 'medpro', appId: 'medpro', phone: formData.phone };

        //check convert user
        const convertUser = await this.convertUserCskhModel.findOne({ userId: formData?.userId }).exec();

        if (convertUser?.isConverted) {
            return { isConverted: true }
        }

        return this.smsService.sendOTPMessageHub(smsInfo);
    }

    async forgotPasswordConvertUser(formData: ConvertUserOtpDTO): Promise<any> {
        const smsInfo = { partnerId: 'medpro', appId: 'medpro', phone: formData.phone };
        return this.smsService.sendOTPMessageHub(smsInfo);
    }

    async checkConvertUserCskh(formData: CheckConvertUserCskhDTO): Promise<any> {
        const dataCheck = {
            fullname: 'người dùng medpro',
            deviceId: formData.deviceId,
            phone: formData.phone,
            code: formData.code,
            type: ''
        }
        const { secretKey } = await this.checkSMSCodeWithSignInProvider('medpro', dataCheck, 'medpro')

        return {
            secretKey
        }
    }

    async convertUserApplyNewPassword(formData: convertUserApplyPassword): Promise<any> {
        try {
            const data = await this.applyNewPassword(formData);
            
            const convertUser = await this.convertUserCskhModel.findOneAndUpdate(
                { userId: formData.userId }, { isConverted: true }, { new: true })
                .exec();

            return { ...data, convertUser }
        } catch (error) {
            
        }
    }

    async convertUserLoginPassword(formData: ConvertUserLoginDTO, appId: string, partnerId: string): Promise<any> {
        return this.medproLoginSocial({ phone: formData.phone, type: 'password', password: formData.password }, appId, partnerId);
    }
}
