import {
    Controller,
    Post,
    Body,
    Get,
    Request,
    UseGuards,
    HttpCode,
    HttpStatus,
    Req,
    Headers,
    Query,
    Delete,
    HttpException,
    UseInterceptors,
    UploadedFile,
    Res,
    Header,
    Patch,
    Put,
    Param
} from "@nestjs/common";
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiBody, ApiBearerAuth, ApiConsumes, ApiHeader, ApiResponse } from '@nestjs/swagger';
import { UserService } from './user.service';
import { AddSaleCodeDTO } from './dto/addSaleCodeDto';
import { ZaloDTO } from './dto/zaloDto';
import { ZaloStateDTO } from './dto/zaloStateDto';
import { FirebaseDTO } from './dto/firebaseDto';
import { UserLoginDTO } from './dto/userLogin.dto';
import { CheckPhoneDTO } from './dto/checkPhone.dto';
import { CheckCodeDTO } from './dto/checkCode.dto';
import { CheckPhoneRefDTO } from './dto/checkPhoneRef.dto';
import { CheckCodeRefDTO } from './dto/checkCodeRef.dto';
import { PhoneRegisterDTO } from './dto/phone-register.dto';
import { CheckSMSCodeSignInProviderDTO } from './dto/check-sms-code-sign-provider.dto';
import { ApplySignInProviderDTO } from './dto/apply-signin-provider.dto';
import { MedproLoginDTO } from './dto/medproLogin.dto';
import { MedproLoginSocialDTO } from './dto/medproLoginSocial.dto';
import { ForgotPasswordDTO } from './dto/forgot-password.dto';
import { ForgotPasswordApplyCodeDTO } from './dto/forgot-password-apply-code.dto';
import { ApplyNewPasswordDTO } from './dto/apply-new-password.dto';
import { CheckUserInfoByPhoneDTO } from './dto/checkUserInfoByPhone.dto';
import { SocialRegisterDTO } from './dto/social-register.dto';
import { IsCSModeDTO } from './dto/is-cs-mode.dto';
import { UpdateEmployeeIdUserDTO } from './dto/update-employee-id-user.dto';
import { ApplySignInProviderV2DTO } from './dto/apply-signin-provider-v2.dto';
import { ApplySignInProviderV3DTO } from './dto/apply-signin-provider-v3.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { FilesService } from 'src/files/files.service';
import { SeedHocViDTO } from './dto/seed-hoc-vi.dto';
import { SeedViTriCongViecDTO } from './dto/seed-vi-tri-cong-viec.dto';
import { UrlConfigService } from 'src/config/config.url.service';
import { CheckUserUpgradeByPhoneDTO } from './dto/checkUserUpgradeByPhone.dto';
import { UpdateUserProfileCbytDTO } from './dto/update-user-profile-cbyt.dto';
import { ResetUserPhoneDTO } from './dto/resetUserPhone.dto';
import { DevModePartnerEnableDTO } from './dto/dev-mode-partner-enable.dto';
import { UpdateUserProfileCbytTokenDTO } from './dto/update-user-profile-cbyt-token.dto';
import { GetEmployeeDTO } from './dto/get-employee.dto';
import { UserDetailMedproIdDto } from './dto/user-detail-medpro-id.dto';
// import { MedproIdGuard } from 'src/shared/guards/medpro-id.guard';
import { ContactUsDto } from './dto/contact-us.dto';
import { IContactUs } from './interfaces/contact-us.interface';
import { FindMedproIdDto } from "./dto/find-medpro-id.dto";
import { UpdateMedproIdDto } from "./dto/update-medpro-id.dto";
import { CskhGuard } from "../common/guards/cskh.guard";
import { UpdateUserMedproDto } from "./dto/update-user-medpro.dto";
import { CheckConvertUserCskhDTO, convertUserApplyPassword, ConvertUserOtpDTO } from "./dto/check-convert-user-cskh.dto";
import { ConvertUserLoginDTO } from "./dto/convert-user-login-password.dto";

@Controller('user')
@ApiTags('Người dùng - Khám bệnh - Quản lý Người dùng Đăng ký khám bệnh')
export class UserController {
    constructor(
        private readonly userService: UserService,
        private readonly urlConfig: UrlConfigService,
    ) { }

    // @Post('update-required-passs')
    // async updateRequiredPass(): Promise<any> {
    //     return this.userService.updateRequiredPass();
    // }

    @Get('insert-sync-dalieu-patient')
    async insertSyncDaLieuPatient(): Promise<any> {
        return this.userService.insertSyncDaLieuPatient();
    }

    @Get('insert-sync-dalieu-booking')
    async insertSyncDaLieuBooking(): Promise<any> {
        return this.userService.insertSyncDaLieuBooking();
    }

    @Post('get-employee')
    async getEmployee(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: GetEmployeeDTO,
    ): Promise<any> {
        return this.userService.getEmployee(appId, partnerId, formData);
    }

    @Post('mode-dev-patner-toggle')
    async modeDevPartnerEnable(
        @Body() formData: DevModePartnerEnableDTO,
    ): Promise<any> {
        return this.userService.modeDevPartnerEnable(formData);
    }

    @Post('delete-account')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async deleteAccount(
        // @Body() formData: ResetUserPhoneDTO,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Headers('osid') osid: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        return this.userService.deleteAccount(appId, partnerId, user, osid);
    }

    @Post('reset-user-phone')
    async resetUserPhone(
        @Body() formData: ResetUserPhoneDTO,
        @Headers('appboid') appBoId?: string,
        @Headers('appbokey') appBoKey?: string,
    ): Promise<any> {
        return this.userService.resetUserPhone(formData, appBoId, appBoKey);
    }

    @Post('merge-patients')
    async mergePatients(): Promise<any> {
        const userFrom = await this.userService.getUserByMongoUserId('5f2b9ddd45372a001ae2ad38');
        const userTo = await this.userService.getUserByMongoUserId('5f9a1512cb9fca001958236c');
        return this.userService.mergeUserMedproId(userFrom, userTo);
    }

    @Post('seed-hoc-vi')
    async seedHocVi(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: SeedHocViDTO,
    ): Promise<any> {
        return this.userService.seedHocVi(appId, partnerId, formData);
    }

    @Post('hoc-vi')
    async getHocVi(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
    ): Promise<any> {
        return this.userService.getHocVi(appId, partnerId);
    }

    @Post('seed-vi-tri-cong-viec')
    async seedViTriCongViec(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: SeedViTriCongViecDTO,
    ): Promise<any> {
        return this.userService.seedViTriCongViec(appId, partnerId, formData);
    }

    @Post('vi-tri-cong-viec')
    async getViTriCongViec(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
    ): Promise<any> {
        return this.userService.getViTriCongViec(appId, partnerId);
    }

    @Post('upload-file-not-authen')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    @ApiOperation({ summary: 'upload avatar không cần đăng nhập', description: 'upload avatar không cần đăng nhập. Cung cấp ***Bearer token*** header.' })
    async uploadAvatarNotAuthen(
        @UploadedFile() file,
    ): Promise<any> {
        return {
            ...file,
            avatarUrl: `${this.urlConfig.getBaseUrl()}files/${file.id}`,
        };
    }

    @Post('upload-file')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    @ApiBearerAuth()
    @ApiOperation({ summary: 'upload avatar', description: 'upload avatar. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async uploadAvatar(
        @Headers('appid') appId: string,
        @UploadedFile() file,
        @Request() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const { file: fileInfo, filestream } = await this.userService.uploadAvatar(appId, userMongoId, file);
        return {
            ...file,
            avatarUrl: `${this.urlConfig.getBaseUrl()}files/${file.id}`,
        };
    }

    @Post('test-sync-user-account')
    async testSyncUserAccount(): Promise<any> {
        return this.userService.syncUserAccount('+***********', '5f2775ae054f2200198dd2f9');
    }

    @Post('update-iscs-mode')
    async updateIsCSMode(
        @Body() formData: IsCSModeDTO,
    ): Promise<any> {
        return this.userService.updateIsCSMode(formData);
    }

    @Get('test-soap')
    async testSoap(): Promise<any> {
        return this.userService.testSoap();
    }

    @Get('decode-zalo-token')
    async deCode(
        @Query('code') code: string,
    ): Promise<any> {
        return this.userService.getZaloInfoAfterVerified(code);
    }

    @Get('decode-firebase-token')
    async deFirebaseCode(
        @Query('token') token: string,
        @Query('partnerid') partnerId: string,
    ): Promise<any> {
        return this.userService.getFirebaseAfterVerified(token, partnerId);
    }

    @Get('delete-by-zalo-token')
    async deleteByZaloToken(
        @Query('token') token: string,
    ): Promise<any> {
        return this.userService.deleteByZaloToken(token);
    }

    @Get('check-v1')
    async checkV1(): Promise<any> {
        return this.userService.checkV1();
    }

    @Post('add_sale_code')
    @ApiOperation({ summary: 'Sale User - Thêm thông tin sale code vào users.', description: 'Sale User - Thêm thông tin sale code vào users.' })
    async addSaleCode(@Body() addSaleCodeDTO: AddSaleCodeDTO): Promise<any> {
        return await this.userService.addSaleCode(addSaleCodeDTO);
    }

    @Post('phone-register')
    @ApiOperation({ summary: 'Đăng ký phone làm tài khoản đăng nhập.', description: 'Đăng ký phone làm tài khoản đăng nhập.' })
    async phoneRegister(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() phoneRegister: PhoneRegisterDTO,
    ): Promise<any> {
        if (!!partnerId === false) {
            partnerId = appId;
        }
        return await this.userService.phoneRegister(appId, phoneRegister, partnerId);
    }

    @Post('social-register')
    @ApiOperation({ summary: 'Đăng ký phone làm tài khoản đăng nhập.', description: 'Đăng ký phone làm tài khoản đăng nhập.' })
    async socialRegister(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() socialRegister: SocialRegisterDTO): Promise<any> {
        return await this.userService.socialRegister(socialRegister, appid, partnerid);
    }

    @Post('check-sms-code-with-sign-in-provider')
    @ApiOperation({
        summary: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
        description: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
    })
    async checkSMSCodeWithSignInProvider(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: CheckSMSCodeSignInProviderDTO,
    ): Promise<any> {
        if (!!partnerid === false) {
            partnerid = appid;
        }
        return await this.userService.checkSMSCodeWithSignInProvider(partnerid, formData, appid);
    }

    @Post('apply-sign-in-provider')
    @ApiOperation({
        summary: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
        description: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
    })
    async applySignInProvider(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: ApplySignInProviderDTO,
        @Headers('refcode') refcode?: string,
    ): Promise<any> {
        return await this.userService.applySignInProvider({ ...formData, referralCode: refcode }, appid, partnerid);
    }

    @Post('apply-sign-in-provider-v2')
    @ApiOperation({
        summary: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
        description: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
    })
    async applySignInProviderV2(
        @Headers('partnerid') partnerId: string,
        @Headers('appid') appid: string,
        @Body() formData: ApplySignInProviderV2DTO,
    ): Promise<any> {
        return await this.userService.applySignInProviderV2(partnerId, formData, appid);
    }

    @Post('apply-sign-in-provider-v3')
    @ApiOperation({
        summary: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
        description: 'Sau khi verify sms code ok thì cho người dùng chọn sign in provider.',
    })
    async applySignInProviderV3(
        @Headers('partnerid') partnerId: string,
        @Headers('appid') appid: string,
        @Body() formData: ApplySignInProviderV3DTO,
    ): Promise<any> {
        return await this.userService.applySignInProviderV3(partnerId, formData, appid);
    }

    @Post('medpro-login-password')
    @ApiOperation({
        summary: 'Medpro login bằng số điện thoại và mật khẩu.',
        description: 'Medpro login bằng số điện thoại và mật khẩu.',
    })
    async medproLoginPassword(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: MedproLoginDTO): Promise<any> {
        return await this.userService.medproLoginPassword(formData, appid, partnerid);
    }

    @Post('medpro-login-password-cskh')
    @ApiOperation({
        summary: 'Medpro login bằng số điện thoại và mật khẩu.',
        description: 'Medpro login bằng số điện thoại và mật khẩu.',
    })
    async medproLoginPasswordCskh(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: any): Promise<any> {
        return await this.userService.medproLoginPasswordCskh(formData, appid, partnerid);
    }

    @Post('medpro-login-password-v2')
    @ApiOperation({
        summary: 'Medpro login bằng số điện thoại và mật khẩu.',
        description: 'Medpro login bằng số điện thoại và mật khẩu.',
    })
    async medproLoginPasswordV2(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: MedproLoginDTO): Promise<any> {
        return await this.userService.medproLoginPasswordV2(formData, appid, partnerId);
    }

    @Post('update-employee-id-user')
    @ApiOperation({ summary: 'Cập nhật thông tin user profile dùng secret key', description: 'Cập nhật thông tin user profile dùng secret key. Cung cấp ***Bearer token*** header.' })
    async updateEmployeeIdUser(
        @Headers('partnerid') partnerId: string,
        @Body() formData: UpdateUserProfileCbytDTO,
    ): Promise<any> {
        return await this.userService.updateEmployeeIdUser(formData, partnerId);
    }

    @Post('update-employee-id-user-token')
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Cập nhật thông tin user profile dùng token', description: 'Cập nhật thông tin user profile dùng token. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async updateEmployeeIdUserToken(
        @Headers('partnerid') partnerId: string,
        @Body() formData: UpdateUserProfileCbytTokenDTO,
        @Request() req,
    ): Promise<any> {
        return await this.userService.updateEmployeeIdUserToken(formData, partnerId, req.user.userMongoId);
    }

    @Post('forgot-password')
    @ApiOperation({ summary: 'Quên mật khẩu.', description: 'Quên mật khẩu.' })
    async forgotPassword(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerId: string,
        @Body() phoneForgetPassword: ForgotPasswordDTO,
    ): Promise<any> {
        if (!!partnerId === false) {
            partnerId = appid;
        }
        return await this.userService.phoneForgotPassword(appid, partnerId, phoneForgetPassword);
    }

    @Post('forgot-password-check-code')
    @ApiOperation({ summary: 'Quên mật khẩu - check sms code.', description: 'Quên mật khẩu - check sms code.' })
    async forgotPasswordApplyCode(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerId: string,
        @Body() phoneForgetPasswordCode: ForgotPasswordApplyCodeDTO,
    ): Promise<any> {
        if (!!partnerId === false) {
            partnerId = appid;
        }
        return await this.userService.forgotPasswordApplyCode(appid, partnerId, phoneForgetPasswordCode);
    }

    @Post('apply-new-password')
    @ApiOperation({ summary: 'Nhập lại mật khẩu mới.', description: 'Nhập lại mật khẩu mới.' })
    async applyNewPassword(@Body() applyPass: ApplyNewPasswordDTO): Promise<any> {
        return await this.userService.applyNewPassword(applyPass);
    }

    @Post('cskh/apply-new-password')
    async cskhApplyNewPassword(@Body() applyPass: { phone: string, password: string, fullname?: string, secretKey: string}): Promise<any> {
        return await this.userService.cskhSetNewPassword(applyPass);
    }

    @Post('medpro-login-social')
    @ApiOperation({
        summary: 'Medpro login bằng số điện thoại và mật khẩu.',
        description: 'Medpro login bằng số điện thoại và mật khẩu.',
    })
    async medproLoginSocial(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: MedproLoginSocialDTO,
        @Headers('platform') platform?: string,
        @Headers('version') version?: number,
        ): Promise<any> {
        return await this.userService.medproLoginSocial(formData, appid, partnerid, platform, version);
    }

    @Post('get-user-detail')
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Lấy thông tin user - user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async getUserDetail(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Request() req): Promise<any> {
        return this.userService.getUserDetail(appId, partnerId, req.user.userMongoId);
    }

    @Get('bo-get-user-detail')
    // @ApiBearerAuth()
    @ApiOperation({ summary: 'Lấy thông tin user - user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    // @UseGuards(AuthGuard('user-jwt'))
    async boGetUserDetail(
        @Query() query: UserDetailMedproIdDto,
        @Headers('appboid') appBoId?: string,
        @Headers('appbokey') appBoKey?: string,
        ): Promise<any> {
        return this.userService.boGetUserDetail(query.phone, appBoId, appBoKey);
    }

    @Post('check-user-info-by-phone')
    // @ApiOperation({
    //     summary: 'Medpro login bằng số điện thoại và mật khẩu.',
    //     description: 'Medpro login bằng số điện thoại và mật khẩu.',
    // })
    async checkUserInfoByPhone(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: CheckUserInfoByPhoneDTO,
    ): Promise<any> {
        return await this.userService.checkUserInfoByPhone(formData, appId, partnerId);
    }

    // @Post('check-user-upgrade')
    // async checkUserUpgrade(@Body() formData: CheckUserUpgradeByPhoneDTO): Promise<any> {
    //     return this.userService.checkUserUpgrade(formData);
    // }

    @Post('zalo-login')
    @ApiOperation({ summary: 'Login bằng Zalo.', description: 'Login bằng Zalo.' })
    async zaloLogin(@Body() zaloDTO: ZaloDTO): Promise<any> {
        return await this.userService.zaloLogin(zaloDTO);
    }

    @Post('firebase-login')
    @ApiOperation({ summary: 'Login bằng firebase', description: 'Login bằng firebase.' })
    async firebaseLogin(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() firebaseDTO: FirebaseDTO): Promise<any> {
        return await this.userService.firebaseLogin(firebaseDTO, appid, partnerid);
    }

    @Post('zalo-login-state')
    @ApiOperation({ summary: 'Login bằng Zalo.', description: 'Login bằng Zalo.' })
    async zaloLoginByState(@Body() zaloStateDTO: ZaloStateDTO): Promise<any> {
        return await this.userService.zaloLoginByState(zaloStateDTO);
    }

    @Get('info')
    @ApiBearerAuth()
    @ApiOperation({ summary: 'Lấy thông tin user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    // @UseGuards()
    async getUserInfo(@Request() req) {
        return req.user;
        const user = req.user;
        return this.userService.getUserInfoByUserId(user.id);
    }

    @Post('login-umc')
    @ApiOperation({ summary: 'Đăng nhập vào hệ thống - User', description: 'Đăng nhập vào hệ thống - User' })
    @ApiBody({
        description: 'Đăng nhập vào hệ thống - User.',
        type: UserLoginDTO,
    })
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard('user-local'))
    async loginUMC(@Request() req) {
        return this.userService.loginUMC(req.user);
    }

    @Post('login-thu-duc')
    @ApiOperation({ summary: 'Đăng nhập vào hệ thống - User', description: 'Đăng nhập vào hệ thống - User' })
    @ApiBody({
        description: 'Đăng nhập vào hệ thống - User.',
        type: UserLoginDTO,
    })
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard('user-local'))
    async loginThuDuc(@Request() req) {
        return this.userService.loginThuDuc(req.user);
    }

    @Post('login-cho-ray')
    @ApiOperation({ summary: 'Đăng nhập vào hệ thống - User', description: 'Đăng nhập vào hệ thống - User' })
    @ApiBody({
        description: 'Đăng nhập vào hệ thống - User.',
        type: UserLoginDTO,
    })
    @HttpCode(HttpStatus.OK)
    @UseGuards(AuthGuard('user-local'))
    async loginChoRay(@Request() req) {
        return this.userService.loginChoRay(req.user);
    }

    @Post('check-phone-thu-duc')
    @ApiOperation({ summary: 'Đăng nhập bằng SMS Brandname', description: 'Đăng nhập bằng SMS Brandname' })
    @ApiBody({
        description: 'Đăng nhập bằng SMS Brandname',
        type: CheckPhoneDTO,
    })
    @HttpCode(HttpStatus.OK)
    // @UseGuards(AuthGuard('user-local'))
    async checkPhoneThuDuc(@Body() checkPhoneDTO: CheckPhoneDTO, @Req() req) {
        return this.userService.checkPhoneThuDuc(checkPhoneDTO, req.ipInfo);
    }

    @Post('v1/check-phone')
    async v1CheckPhone(@Headers('partnerid') partnerId: string, @Headers('appid') appId: string, @Body() checkPhoneDTO: CheckPhoneRefDTO) {
        if (!!partnerId === false) {
            partnerId = appId;
        }
        return this.userService.v1CheckPhone(partnerId, checkPhoneDTO, appId);
    }

    @Post('v1/check-code')
    async v1CheckCode(@Headers('partnerid') partnerId: string, @Headers('appid') appId: string, @Body() checkCodeDTO: CheckCodeRefDTO) {
        if (!!partnerId === false) {
            partnerId = appId;
        }
        return this.userService.v1CheckCode(partnerId, checkCodeDTO, appId);
    }

    @Post('check-code-thu-duc')
    @ApiOperation({ summary: 'Kiểm tra code trong sms brand name gửi lên', description: 'Kiểm tra code trong sms brand name gửi lên' })
    @ApiBody({
        description: 'Kiểm tra code trong sms brand name gửi lên',
        type: CheckPhoneDTO,
    })
    @HttpCode(HttpStatus.OK)
    // @UseGuards(AuthGuard('user-local'))
    async checkCodeThuDuc(@Body() checkCodeDTO: CheckCodeDTO, @Req() req) {
        return this.userService.checkCodeThuDuc(checkCodeDTO, req.ipInfo);
    }

    @Get('md5')
    async md5() {
        return this.userService.md5();
    }

    @Get('auto-create-uuid')
    async autoGenerateUUIDV4() {
        return this.userService.autoGenerateUUIDV4();
    }

    @Get('test-updated')
    async testUpdated(): Promise<any> {
        return this.userService.testUpdated();
    }

    @Post('/upload-file-telemed')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    @ApiOperation({ summary: 'upload avatar telemed', description: 'upload avatar. Cung cấp ***Bearer token*** header.' })
    async uploadAvatarTelemed(
        @Headers('partnerid') partnerid: string,
        @Headers('secret') secret: string,
        @UploadedFile() file,
        @Headers('doctorid') doctorid?: any,
        @Headers('doctorEmail') doctorEmail?: any,
    ): Promise<any> {
        try {
            await this.userService.uploadAvatarTelemed(partnerid, file, secret, doctorid, doctorEmail);
            return {
                ...file,
                avatarUrl: `${this.urlConfig.getBaseUrl()}files/${file.id}`,
            };
        } catch (error) {
            throw error;
        }

    }

    @Get('/avatar-info-telemed')
    async getAvatarTelemedInfo(
        @Headers('doctor') doctor: string,
        @Headers('partnerid') partnerid: string,
    ) {
        return this.userService.getAvatarTelemedInfo(doctor, partnerid);
    }
    // @Get('test-send-mail')
    // async testSendgrid() {
    //     return await this.userService.sendMail();
    // }

    @Post('qrcode')
    async generateQRCode(@Query('text') text: string): Promise<any> {
        return this.userService.generateQRCode(text);
    }

    @Post('barcode')
    async generateBarCode(@Query('text') text: string): Promise<any> {
        return this.userService.generateBarCode(text);
    }

    @Get('verify-cskh-token')
    @UseGuards(AuthGuard('user-jwt'))
    async verifyCskhToken(
        @Request() req,
    ): Promise<any> {
        return this.userService.verifyCskhToken(req.user.userMongoId);
    }

    @Patch('sync-user-by-phone')
    syncUserByPhone(@Body() data: any): Promise<any> {
        return this.userService.syncForUser(data.phone);
    }

    @Get('detail-info')
    @UseGuards(AuthGuard('user-jwt'))
    getMedproIdUserByToken(@Request() req): Promise<any> {
        const token = req.headers['authorization']?.split(' ')[1];
        return this.userService.getMedproIdUserByToken(req?.user?.userMongoId, token);
    }

    @Post('contact-us')
    @ApiOperation({ summary: 'Nhận thông tin Hợp tác.', description: 'Nhận Thông tin Hợp tác.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: ContactUsDto,
        description: 'Nhận thông tin hợp tác.',
    })
    async allContactUss(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() body: ContactUsDto
    ): Promise<IContactUs> {
        return await this.userService.saveContactUs(body, appid, partnerid);
    }

    @Get('find')
    @ApiBearerAuth()
    @UseGuards(CskhGuard)
    find(@Query() query: FindMedproIdDto) {
      return this.userService.findMedproId(query);
    }

    @Put('update/:id')
    @ApiBearerAuth()
    @UseGuards(CskhGuard)
    update(@Param('id') id: string, @Body() body: UpdateMedproIdDto, @Req() req: any) {
        const user = req.user;
        return this.userService.updateMedproId(id, body, user);
    }

    //user-visa
    @Post('update-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    updateVisaUser(@Body() body: UpdateUserMedproDto, @Req() req: any) {
        const user = req.user;
        const userId = user.userMongoId;
        console.log('userId', userId)
        return this.userService.updateVisaUser(userId, body);
    }

    @UseGuards(CskhGuard)
    @Get('user-doctor/list')
    getDoctorList(@Headers('partnerid') partnerid: string) {
        return this.userService.getDoctorList(partnerid);
    }

    @UseGuards(CskhGuard)
    @Get('logged-in-device/:id')
    getLoggedInDevice(@Param('id') id: string) {
        return this.userService.getLoggedInDevice(id);
    }

    @Get('phone-locale-list')
    getPhoneList(@Headers('locale') locale: string) {
        return this.userService.getPhoneList(locale);
    }

    @Get('phone-locale-list-patient')
    getPhoneListPatient() {
        return this.userService.getPhoneListPatient();
    }

    @Post('convert-user/sms-otp')
    @ApiOperation({ summary: 'Kích hoạt send OTP cho người dùng vào phiếu khám từ cskh booking, nếu đã chuyển đổi thì không cần send otp.' })
    async sendOtpToConvertUser(@Body() formData: ConvertUserOtpDTO): Promise<any> {
        return await this.userService.sendOtpToConvertUser(formData);
    }
    
    @Post('convert-user/check-otp')
    @ApiOperation({ summary: 'Verify otp và kiểm tra convert user đã tạo mật khẩu chưa.'})
    async checkConvertUserCskh(@Body() formData: CheckConvertUserCskhDTO): Promise<any> {
        return await this.userService.checkConvertUserCskh(formData);
    }

    @Post('convert-user/forgot-password')
    @ApiOperation({ summary: 'Kích hoạt send OTP khi người dùng đã hoàn tất convert nhưng quên mật khẩu.' })
    async forgotPasswordConvertUser(@Body() formData: ConvertUserOtpDTO): Promise<any> {
        return await this.userService.forgotPasswordConvertUser(formData);
    }

    @Post('convert-user/apply-new-password')
    @ApiOperation({ summary: 'Nhập lại mật khẩu mới.', description: 'Nhập lại mật khẩu mới.' })
    async convertUserApplyNewPassword(@Body() applyPass: convertUserApplyPassword): Promise<any> {
        return await this.userService.convertUserApplyNewPassword(applyPass);
    }

    @Post('convert-user/login')
    @ApiOperation({ summary: 'Convert User login bằng số điện thoại và mật khẩu.' })
    async convertUserLoginPassword(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() formData: ConvertUserLoginDTO): Promise<any> {
        return await this.userService.convertUserLoginPassword(formData, appid, partnerid);
    }
}
