import { Document } from 'mongoose';

export interface ICskhSurveyCare247 extends Document {
    userId: string;                       // ID người dùng thực hiện khảo sát
    user: string; 
    surveyDate: Date;                     // Ng<PERSON>y thực hiện khảo sát
    callOutcome: string;                  // Kết quả cuộc gọi: <PERSON>h<PERSON><PERSON> công, <PERSON>h<PERSON><PERSON> nghe máy, v.v.
    isServiceUsed?: boolean;              // Người dùng có sử dụng dịch vụ không (tùy chọn)
    supportDuration?: string;             // Thời gian hỗ trợ: D<PERSON><PERSON>i 2 tiếng, T<PERSON>m 2-3 tiếng, v.v.
    serviceEase?: string;                 // Đ<PERSON>h giá quy trình đặt dịch vụ: <PERSON><PERSON> dàng, <PERSON><PERSON><PERSON> khăn
    scheduleConfirmation?: boolean;       // Nhân viên có gọi xác nhận lịch trình không (tùy chọn)
    notes?: string;                       // <PERSON><PERSON> chú thêm (tùy chọn)
    bookingCare247: string;
    staffAttitudeRating: number;          //M<PERSON><PERSON> độ hài lòng với dịch vụ
}
