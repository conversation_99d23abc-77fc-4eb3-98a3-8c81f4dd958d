import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
    HttpStatus,
} from '@nestjs/common';
import { PatientInfoService } from './patient-info.service';
import { CreatePatientInfoDto } from './dto/create-patient-info.dto';
import { UpdatePatientInfoDto } from './dto/update-patient-info.dto';
import { QueryPatientInfoDto } from './dto/query-patient-info.dto';
import { ScanQrCodeDto, UpdateVitalSignsByQrDto } from './dto/health-metrics.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Patient Info - Quản lý thông tin bệnh nhân')
@Controller('patient-info')
export class PatientInfoController {
    constructor(private readonly patientInfoService: PatientInfoService) {}

    @Post()
    @ApiOperation({ summary: 'Tạo thông tin bệnh nhân mới' })
    @ApiResponse({
        status: HttpStatus.CREATED,
        description: 'Tạo thông tin bệnh nhân thành công',
    })
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        description: 'Dữ liệu đầu vào không hợp lệ',
    })
    async create(@Body() createPatientInfoDto: CreatePatientInfoDto) {
        const result = await this.patientInfoService.create(createPatientInfoDto);
        return {
            success: true,
            message: 'Tạo thông tin bệnh nhân thành công',
            data: result,
        };
    }

    @Get()
    @ApiOperation({ summary: 'Lấy danh sách thông tin bệnh nhân' })
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Lấy danh sách thông tin bệnh nhân thành công',
    })
    async findAll(@Query() queryDto: QueryPatientInfoDto) {
        const result = await this.patientInfoService.findAll(queryDto);
        return result;
    }

    @Get(':id')
    @ApiOperation({ summary: 'Lấy thông tin bệnh nhân theo ID' })
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Lấy thông tin bệnh nhân thành công',
    })
    @ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thông tin bệnh nhân',
    })
    async findOne(@Param('id') id: string) {
        const result = await this.patientInfoService.findOne(id);
        return {
            success: true,
            message: 'Lấy thông tin bệnh nhân thành công',
            data: result,
        };
    }

    @Patch(':id')
    @ApiOperation({ summary: 'Cập nhật thông tin bệnh nhân' })
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Cập nhật thông tin bệnh nhân thành công',
    })
    @ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thông tin bệnh nhân',
    })
    async update(
        @Param('id') id: string,
        @Body() updatePatientInfoDto: UpdatePatientInfoDto,
    ) {
        const result = await this.patientInfoService.update(id, updatePatientInfoDto);
        return {
            success: true,
            message: 'Cập nhật thông tin bệnh nhân thành công',
            data: result,
        };
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Xóa thông tin bệnh nhân' })
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Xóa thông tin bệnh nhân thành công',
    })
    @ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thông tin bệnh nhân',
    })
    async remove(@Param('id') id: string) {
        const result = await this.patientInfoService.remove(id);
        return {
            success: true,
            message: result.message,
        };
    }

    @Post('scan-qr')
    @ApiOperation({ summary: 'Quét QR code để lấy thông tin bệnh nhân' })
    async scanQrCode(@Body() scanQrCodeDto: ScanQrCodeDto) {
        const result = await this.patientInfoService.scanQrCode(scanQrCodeDto);
        return {
            success: true,
            message: 'Lấy thông tin bệnh nhân thành công',
            data: result,
        };
    }

    @Post('update-vital-signs')
    @ApiOperation({ summary: 'Cập nhật thông tin bệnh nhân qua QR code' })
    async updateVitalSignsByQr(@Body() updateVitalSignsByQrDto: UpdateVitalSignsByQrDto) {
        const result = await this.patientInfoService.updateVitalSignsByQr(updateVitalSignsByQrDto);
        return {
            success: true,
            message: 'Cập nhật thông tin bệnh nhân thành công',
            data: result,
        };
    }

    @Get(':id/qr-code')
    @ApiOperation({ summary: 'Lấy QR code của bệnh nhân' })
    @ApiResponse({
        status: HttpStatus.OK,
        description: 'Lấy QR code thành công',
    })
    @ApiResponse({
        status: HttpStatus.NOT_FOUND,
        description: 'Không tìm thấy thông tin bệnh nhân',
    })
    async getQrCodeImage(@Param('id') id: string) {
        const result = await this.patientInfoService.getQrCodeImage(id);
        return {
            success: true,
            message: 'Lấy QR code thành công',
            data: result,
        };
    }
}
