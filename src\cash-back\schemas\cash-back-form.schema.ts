import * as mongoose from 'mongoose';
import { CASH_BACK_FORM_COLLECTION_NAME } from '../constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { ICashBackForm } from '../interfaces/cash-back-form.interface';
import { uuidV4Factory } from '../helpers/uuid-v4';

const Schema = mongoose.Schema;

export const CashBackFormSchema = new Schema<ICashBackForm>({
        id: { type: String, required: false, default: uuidV4Factory },
        bankName: { type: String, required: true },
        bankAccountNumber: { type: String, required: true },
        bankAccountName: { type: String, required: true },
        totalAmount: { type: String, required: true },
        invoiceImages: { type: [String], required: true },
        partnerId: { type: String },
        userId: { type: String },
        bookingId: { type: String, required: true, unique: true },
        ratingHospital: { type: String, required: false },
        ratingMedpro: { type: String, required: false },
        // ? Populate
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME }, 
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME }, 
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    },
    {
        collection: CASH_BACK_FORM_COLLECTION_NAME,
        timestamps: true
    }
)

