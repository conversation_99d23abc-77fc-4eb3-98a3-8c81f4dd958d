import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsBoolean, IsOptional, IsDate } from 'class-validator';

export class CskhSurveyCare247Dto {
    @ApiProperty({
        description: '<PERSON><PERSON><PERSON> thực hiện khảo sát',
        example: '2025-01-14',
    })
    @IsNotEmpty()
    @IsString()
    surveyDate: string;

    @ApiProperty({
        description: 'Kết quả cuộc gọi: Thành công, Không nghe máy, <PERSON>hu<PERSON> bao, v.v.',
        example: 'Thành công',
    })
    @IsNotEmpty()
    @IsString()
    callOutcome: string;

    @ApiProperty({
        description: 'Người dùng có sử dụng dịch vụ không',
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    isServiceUsed?: boolean;

    @ApiProperty({
        description: 'Thời gian hỗ trợ: <PERSON>ưới 2 tiếng, Tầm 2-3 tiếng, v.v.',
        example: 'Tầm 2-3 tiếng',
        required: false,
    })
    @IsOptional()
    @IsString()
    supportDuration?: string;

    @ApiProperty({
        description: 'Đánh giá quy trình đặt dịch vụ: Dễ dàng, Khó khăn',
        example: 'Dễ dàng',
        required: false,
    })
    @IsOptional()
    @IsString()
    serviceEase?: string;

    @ApiProperty({
        description: 'Nhân viên có gọi xác nhận lịch trình không',
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    scheduleConfirmation?: boolean;

    @ApiProperty({
        description: 'Ghi chú thêm từ khảo sát',
        example: 'Dịch vụ rất tốt và hài lòng.',
        required: false,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: 'bookingId',
        required: false,
        type: String,
        default: 0,
    })
    @IsNotEmpty()
    @IsString()
    bookingCare247: String;

    @ApiProperty({
        description: 'Mức độ hài lòng với thái độ phục vụ của nhân viên (1-5)',
        example: 5,
        required: false,
    })
    staffAttitudeRating: number;
}
