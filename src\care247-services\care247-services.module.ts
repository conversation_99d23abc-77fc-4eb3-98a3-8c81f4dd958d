import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from 'src/config/config.module';
import { CskhModule } from 'src/cskh/cskh.module';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { UserModule } from 'src/user/user.module';
import { Care247SeviceController } from './care247-services.controller';
import { Care247Service } from './care247-services.service';
import { Care247ConsultationRegistrationSchema } from './schemas/care247-consultation-registration.schema';
import { CARE247_CONSULTATION_REGISTRATIONS } from './schemas/constants';
@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: CARE247_CONSULTATION_REGISTRATIONS, schema: Care247ConsultationRegistrationSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema }
        ]),
        UserModule,
        CskhModule,
        ConfigModule,
    ],
    controllers: [Care247SeviceController],
    providers: [Care247Service],
    exports: [Care247Service],
})
export class Care247ServiceModule {}
