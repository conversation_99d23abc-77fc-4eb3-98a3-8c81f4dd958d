import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CskhModule } from 'src/cskh/cskh.module';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { UserModule } from 'src/user/user.module';
import { Care247SeviceController } from './care247-services.controller';
import { Care247Service } from './care247-services.service';
import { Care247ServicesPartnerSchema } from './schemas/care247-service-partners.schema';
import { Care247ServicesSchema } from './schemas/care247-services.schema';
import { CARE247_SERVICE_CHANGES, CARE247_SERVICES, CARE247_SERVICES_PARTNER_CONTRAINTS, CARE247_SERVICES_PARTNERS } from './schemas/constants';
import { Care247ServicesPartnerContraintSchema } from './schemas/care247-service-partner-contraints.schema';
import { Care247ServiceChangesSchema } from './schemas/care247-service-changes.schema';

@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: CARE247_SERVICES, schema: Care247ServicesSchema },
            { name: CARE247_SERVICES_PARTNERS, schema: Care247ServicesPartnerSchema },
            { name: CARE247_SERVICES_PARTNER_CONTRAINTS, schema: Care247ServicesPartnerContraintSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: CARE247_SERVICE_CHANGES, schema: Care247ServiceChangesSchema },
        ]),
        UserModule,
        CskhModule,
    ],
    controllers: [Care247SeviceController],
    providers: [Care247Service],
    exports: [Care247Service],
})
export class Care247ServiceModule {}
