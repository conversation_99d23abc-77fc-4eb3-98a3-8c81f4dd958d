import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsArray, IsPhoneNumber, IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';

export class CooperationClinicDto {
    @ApiProperty({
        description: 'Họ và tên',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Họ và tên không được để trống' })
    readonly fullname: string;

    @ApiProperty({
        description: 'Email',
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Email không được để trống' })
    @IsEmail()
    readonly email: string;

    @ApiProperty({
        description: 'Điện thoại',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
    readonly phone: string;

    @ApiProperty({
        description: 'Tên phòng mạch muốn hợp tác',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Tên phòng mạch không được để trống' })
    readonly name: string;

    @ApiProperty({
        description: 'Ghi chú',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Ghi chú không được để trống' })
    readonly note: string;

    @ApiProperty({
        description: 'captchaResponse',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'captchaResponse không được để trống' })
    captchaResponse?: string;
}
