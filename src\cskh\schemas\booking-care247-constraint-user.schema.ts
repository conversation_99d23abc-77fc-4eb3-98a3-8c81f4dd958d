import * as mongoose from 'mongoose';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { BOOKING_CARE_247_CONSTRAINT_USER } from './constants';
import { BOOKING_CARE_247 } from '../../booking-gateway/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const BookingCare247ConstraintUserSchema = new Schema(
    {
        userId: { type: String, unique: true, required: true },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        transactionIds: [{ type: String }],
        times: { type: Number, default: 1 },
        care247: [{ type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 }],
        multiple: { type: Number, default: 1 },
        latestPartner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        latestPartnerTime: { type: Date }
    },
    {
        collection: BOOKING_CARE_247_CONSTRAINT_USER,
        timestamps: true,
    },
);

