import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class Care247ConstraintUserHistoryQueryDto {
    @ApiProperty({
        description: 'id',
        required: true,
        type: String,
    })
    @IsNotEmpty({ message: 'id is required' })
    id: string;

    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @Transform(value => Number(value))
    pageIndex?: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
