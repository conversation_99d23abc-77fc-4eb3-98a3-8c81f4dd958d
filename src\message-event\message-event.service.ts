import { EVENT_COLLECTION_NAME, MESSAGE_EVENT_NOTIF_INFORM_COLLECTION, NOTIFICATION_COLLECTION } from './../event/schemas/constants';
import { IEvent } from './../event/intefaces/event.inteface';
import { HttpException, HttpService, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { map } from 'lodash';
import { Model } from 'mongoose';
import * as https from 'https';
import { isEmail } from 'class-validator';
import { IMessageEventProcessFailed } from 'src/event/intefaces/message-event-process-failed.interface';
import { IMessageEventProcess } from 'src/event/intefaces/message-event-process.interface';
import { IMessageEventSuccess } from 'src/event/intefaces/message-event-success.interface';
import { IMessageEvent } from 'src/event/intefaces/message-event.interface';
import {
    MESSAGE_EVENT_COLLECTION,
    MESSAGE_EVENT_PROCESS_COLLECTION,
    MESSAGE_EVENT_PROCESS_FAILED_COLLECTION,
    MESSAGE_EVENT_SUCCESS_COLLECTION,
} from 'src/event/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPushDevice } from 'src/push-device/intefaces/push-device.inteface';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import {
    EVENT_MAIL_BOOKING_NEW_NOTICE,
    EVENT_SURVEY_FORM_NOTY_DALIEU,
    HANDLE_UPDATE_STATUS_BOOKING_V1_FAIL,
    MESSAGE_EVENT,
    SYNC_USER_INFO,
    SYNC_V1_CANCEL_BOOKING_EVENT,
    SYNC_V1_EVENT,
    TransporterEvent,
    UPDATE_BOOKING_V1_EVENT,
    EVENT_CLOSE_ORDER_PAYMENT_HUB,
    EVENT_TIMEOUT_TRANSACTION_LOGS,
    FORCE_PAYMENT_HUB_CALLBACK_BE,
    GET_CHECK_IN_ROOM_BOOKING_NOTE,
    PUSH_NOTIF_AND_SEND_MAIL_BOOKING_SUCCESS,
    EVENT_MAIL_BOOKING_NEW_NOTICE_V2,
    UPDATE_CONSTRAINT_AND_BOOKING_RULE,
    SYNC_BOOKING_TRONG_NGAY,
    FIND_REFERRAL_CODE,
    TRACKING_CANCEL_RESERVATIONS,
    REPAYMENT_SUCCESS_EVENT,
    BOOKING_FULL_SLOT_EVENT,
    MESSAGE_EVENT_NOTIF_INFORM,
    EVENT_CREATE_KPI_CSKH,
    EVENT_BOOKING_SUCCESS,
    EVENT_RETRY_SYNC_BOOKING,
} from './constant';
import { CreateMessageEventDto } from './dto/create-message-event.dto';
import { MessageEventDTO } from './dto/message-event.dto';
import { BaseResponse } from './interfaces/base-response.interface';
import { SendgridConfigService } from 'src/config/config.sendgrid.sevice';
import {
    BOOKING_COLLECTION_NAME,
    CRON_REMIDER_BOOKING_NOT_PAYMENT,
    PAYMENT_COLLECTION_NAME,
    RETRY_TRASACTION_COLLECTION_NAME,
    CRON_SYNC_PATIENT,
    TIMEOUT_TRANSACTION_LOGS_COLLECTION_NAME,
    CONSTRAINTS_COLLECTION_NAME,
    PATIENT_TRACKING_EVENT,
} from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import * as moment from 'moment';
import { CronJob } from 'cron';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CreateEventDTO } from 'src/event/dto/create-event.dto';
import * as uuid from 'uuid';
import { CreateMessageEventDataMailDto } from 'src/booking-gateway/dto/create-message-event-data-mail.dto';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import { REFERRAL_CODE_REGISTER_COLLECTION_NAME, USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { CreateMessageEventPushDto } from 'src/booking-gateway/dto/create-message-event-push.dto';
import {LOG_SERVICE_EVENT, THIRD_PARTY_ERROR_LOG} from '../audit-log/constant';
import { UrlConfigService } from '../config/config.url.service';
import { UserService } from '../user/user.service';
import { UpdateStatusDTO } from 'src/booking-gateway/dto/update-status.dto';
import { IRetryTransaction } from 'src/booking-gateway/intefaces/retry-transactions.interface';
import { RetryUpdateStatusDto } from 'src/booking-gateway/dto/retry-update-status.dto';
import * as queryString from 'query-string';
import {SyncPatientEvent} from "../booking-gateway/dto/sync-patient.event";
import {HisGatewayService} from "../his-gateway/his-gateway.service";
import { ClientUtilService } from 'src/config/client-util.service';
import { ITimeoutTransactionLogs } from 'src/booking-gateway/intefaces/timeout-transactions.interface';
import { Observable } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { get } from 'lodash';
import { UtilService } from 'src/config/util.service';
import { IConstraints } from 'src/booking-gateway/intefaces/constraints.inteface';
import { SENDGRID_CONNECTION } from 'src/config/sendgridConnection';
import { TrackingCancelReserveDto } from './dto/tracking-cancel-reserve.dto';
import { REPO_NAME_BETA } from '../common/constants';
import { ConfigRepoService } from '../config/config.repo.service';
import { RETRY_PAYMENT_BOOKING } from '../cskh/schemas/constants';
import { IReferralCodeRegister } from '../user/interfaces/referral-code-register.interface';


@Injectable()
export class MessageEventService {
    private logger = new Logger(MessageEventService.name);

    private readonly repoName: string;
    constructor(
        @InjectModel(MESSAGE_EVENT_COLLECTION) private messageEventModel: Model<IMessageEvent>,
        @InjectModel(MESSAGE_EVENT_NOTIF_INFORM_COLLECTION) private messageEventNotifInformModel: Model<IMessageEvent>,
        @InjectModel(MESSAGE_EVENT_PROCESS_COLLECTION) private messageEventProcessModel: Model<IMessageEventProcess>,
        @InjectModel(MESSAGE_EVENT_PROCESS_FAILED_COLLECTION) private messageEventProcessFailModel: Model<IMessageEventProcessFailed>,
        @InjectModel(MESSAGE_EVENT_SUCCESS_COLLECTION) private messageEventSuccessModel: Model<IMessageEventSuccess>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        @InjectModel(EVENT_COLLECTION_NAME) private eventModel: Model<IEvent>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(NOTIFICATION_COLLECTION) private notificationModel: Model<IEvent>,
        @InjectModel(CONSTRAINTS_COLLECTION_NAME) private constraintsModel: Model<IConstraints>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @Inject(SENDGRID_CONNECTION) private readonly sendgrid,
        @InjectModel(RETRY_TRASACTION_COLLECTION_NAME) private readonly retryTransactionModel: Model<IRetryTransaction>,
        @InjectModel(TIMEOUT_TRANSACTION_LOGS_COLLECTION_NAME) private timeoutTransactionLogsModel: Model<ITimeoutTransactionLogs>,
        @InjectModel(RETRY_PAYMENT_BOOKING) private retryPaymentBookingModel: Model<any>,
        @InjectModel(REFERRAL_CODE_REGISTER_COLLECTION_NAME) private referralCodeRegisterModel: Model<IReferralCodeRegister>,
        private eventEmmiter: EventEmitter2,
        private globalSettingService: GlobalSettingService,
        private sendgridConfigService: SendgridConfigService,
        private scheduler: SchedulerRegistry,
        private readonly utilService: UtilService,
        private http: HttpService,
        private urlConfigService: UrlConfigService,
        private readonly userService: UserService,
        private readonly hisGatewayService: HisGatewayService,
        private readonly client: ClientUtilService,
        private readonly repoConfigService: ConfigRepoService,
    ) {
        this.repoName = this.repoConfigService.getRepoName();
    }

    @OnEvent(MESSAGE_EVENT)
    async onMessageEvent(payload: CreateMessageEventDto) {
        // kiểm tra các trường hợp sẽ có event data riêng
        let transporter: string;
        let allowSaveMessage = false;
        const { isPushNotif, topic: topicId, type, userId, appId, isSendMail, eventData, ...data } = payload;
        const resultData: any = { url: 'https://medpro.com.vn' };
        if (isPushNotif && [0, 1, 2, 5, 7].includes(type)) {
            allowSaveMessage = true;
            transporter = TransporterEvent.PUSH;

            if (topicId === 'bookings.confirm' || topicId === 'bookings.cancel' || topicId === 'bookings.reminder' ||  topicId === 'bookings.not.payment.yet.reminder') {
                resultData.transactionId = eventData.transactionId;
                resultData.type = eventData.type;
            }
            if (topicId === 'orders.confirm' || topicId === 'orders.cancel') {
                resultData.transactionId = eventData.transactionId;
                resultData.type = eventData.type;
            }
            if (topicId === 'his.confirm') {
                resultData.transactionId = eventData.transactionId;
                resultData.type = eventData.type;
            }
            /* thêm phần xem thông tin hóa đơn */
            if (topicId === 'invoice.confirm') {
                resultData.transactionId = eventData.transactionId;
                resultData.type = eventData.type;
                resultData.invoiceUrl = eventData.invoiceUrl;
                resultData.topicId = topicId;
            }
            if (topicId === 'messages.push-inform-notif') {
                resultData.type = eventData.type;
                resultData.content = data.title;
            }
            const [
                partners,
                deviceInfos,
            ] = await Promise.all([
                this.partnerConfigModel.findOne({ partnerId: appId }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec(),
                this.pushDeviceModel.find({ userId, appId }).exec(),
            ]);
            if (allowSaveMessage && partners && deviceInfos) {
                const clientIds = map(deviceInfos, 'clientId');
                const { notifApiKey, notifAppId } = partners;
                const document = new this.messageEventModel({
                    ...data,
                    appId,
                    topic: topicId,
                    type,
                    userId,
                    notifAppId,
                    notifApiKey,
                    clientIds,
                    eventData: resultData,
                    transporter,
                });
                await document.save();
            }
        } else if (isSendMail) {
            const topicSet = new Set([
                'clinic.register',
                'sync-booking.failed',
                'tranform-data-schedule-v1.failed',
                'send.mail.payment.umc',
                'invoice.confirm',
                'bookings.confirm',
                'bookings.reExam',
                'update.booking.failed',
            ]);
            if (topicSet.has(topicId)) {
                allowSaveMessage = true;
                transporter = TransporterEvent.MAIL;
            }
            if (allowSaveMessage) {
                const document = await this.messageEventModel.create({
                    ...data,
                    appId,
                    topic: topicId,
                    userId,
                    notifAppId: ``,
                    notifApiKey: this.sendgridConfigService.getApiKey(),
                    eventData,
                    transporter,
                });
                await document.save();
            }
        }
        return;
        // else if (
        //     isPushNotif && topicId === 'messages.push-notif' && type === 3
        // ) {
        //     allowSaveMessage = true;
        //     transporter = TransporterEvent.PUSH;
        // } else if (
        //     isPushNotif && topicId === 'messages.push-inform-notif' && type === 100
        // ) {
        //     allowSaveMessage = true;
        //     transporter = TransporterEvent.PUSH;
        // } else if (
        //     isPushNotif && topicId === 'his.confirm' && type === 5
        // ) {
        //     allowSaveMessage = true;
        //     transporter = TransporterEvent.PUSH;
        // } else if (isPushNotif === true && type === 99) {
        //     allowSaveMessage = true;
        //     transporter = TransporterEvent.PUSH;
        // }
        // } else if (
        //     isSendMail && topicId === 'bookings.reExam' && type === 8
        // ) {
        //     // to do
        //     // allowSaveMessage = true;
        //     // transporter = TransporterEvent.MAIL;
        // } else if (
        //     isSendMail && topicId === 'invoice.confirm'
        // ) {
        //     // to do
        //     // allowSaveMessage = true;
        //     // transporter = TransporterEvent.MAIL;
        // } else if (
        //     isSendMail && topicId === 'bookings.confirm' && [1, 2].includes(type)
        // ) {
        //     // to do
        //     // allowSaveMessage = true;
        //     // transporter = TransporterEvent.MAIL;
        // }
    }

    @OnEvent(MESSAGE_EVENT_NOTIF_INFORM)
    async onMessageNotifInform(payload: CreateMessageEventDto) {
        // kiểm tra các trường hợp sẽ có event data riêng
        let transporter = TransporterEvent.PUSH;
        const { isPushNotif, topic: topicId, type, userId, appId, isSendMail, eventData, ...data } = payload;

        const [
            partnerConfig,
            pushDevices,
        ] = await Promise.all([
            this.partnerConfigModel.findOne({ partnerId: appId }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec(),
            this.pushDeviceModel.find({ userId, appId }).exec(),
        ]);
        if (partnerConfig && pushDevices.length > 0) {
            const clientIds = map(pushDevices, 'clientId');
            const { notifApiKey, notifAppId } = partnerConfig;
            const document = new this.messageEventNotifInformModel({
                ...data,
                eventData,
                appId,
                topic: topicId,
                type,
                userId,
                notifAppId,
                notifApiKey,
                clientIds,
                transporter,
            });
            await document.save();
        }
    }

    async getMessageEventSuccess(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventSuccessModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventSuccessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getMessageEventProcess(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventProcessModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventProcessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventProcess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getMessageEventProcessFailed(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventProcessFailModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventProcessFailModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventProcessFailed()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async rollBackMessageEventFailService(processId: string): Promise<any> {
        try {
            const messageEventFail = await this.messageEventProcessFailModel
                .findOne({ processId })
                .lean()
                .exec();
            if (!messageEventFail) {
                throw new HttpException(`Không tìm thấy dữ liệu với processId là ${processId} `, 404);
            }
            const { processId: aaaa, synStatus, ...data } = messageEventFail;
            await this.messageEventModel.create({ ...data });
            await this.messageEventProcessFailModel.deleteOne({ processId }).exec();
        } catch (error) {
            this.logger.error(`Error when exec rollBackMessageEventFailService()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getMessageEvents(fromData: MessageEventDTO): Promise<BaseResponse<any>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.messageEventModel
                    .find({}, { notifAppId: false, notifApiKey: false })
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.messageEventModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            };
        } catch (error) {
            this.logger.error(`Error when exec getMessageEventSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    // @OnEvent(CRON_REMIDER_BOOKING_NOT_PAYMENT)
    // async remindBookingNotPaymentYet(bookingBefore: IBooking, defaultMinutes: number = 15): Promise<any> {
    //     let notPaymentYetReminder = defaultMinutes;
    //     const [getPartnerConfig, messageRemiderNotPamentGlobal] = await Promise.all([
    //         this.partnerConfigModel.findOne({ partnerId: bookingBefore.partnerId }, { notPaymentYetReminder: true }).exec(),
    //         this.globalSettingService.findByKeyAndRepoName('TITLE_NOT_PAYMENT_YET_REMINDER'),
    //     ]);
    //     if (!getPartnerConfig.notPaymentYetReminder) {
    //         notPaymentYetReminder = Number(await this.globalSettingService.findByKeyAndRepoName('NOT_PAYMENT_YET_REMINDER'));
    //     }

    //     const remindTime = moment()
    //         .add(notPaymentYetReminder, 'minutes')
    //         .toDate();
    //     const name = `reminder_booking_not_payment_yet_${bookingBefore.bookingCode}_${remindTime.valueOf()}`;
    //     const job = new CronJob(
    //         remindTime,
    //         async () => {
    //             /* Tìm lại thông tin booking */
    //             const booking = await this.bookingModel.findById({ _id: bookingBefore._id }).exec();
    //             const bookingObj = booking.toObject();
    //             /* tiến hành kiểm tra xem trạng thái phiếu khám thế nào */
    //             const setSatusBooking = new Set([0, 6]);
    //             if (setSatusBooking.has(booking.status)) {
    //                 const messageRemiderNotPament = messageRemiderNotPamentGlobal.replace('{boookingCode}', `${booking?.bookingCodeV1 || booking.bookingCode}`);
    //                 /* tiến hành bắn event */
    //                 await this.createEvent(
    //                     {
    //                         topicId: 'bookings.not.payment.yet.reminder',
    //                         createTime: moment().toISOString(),
    //                         userId: booking.userId,
    //                         appId: booking.appId,
    //                         title: `${messageRemiderNotPament}`,
    //                         partnerId: booking.partnerId,
    //                         eventData: {
    //                             ...bookingObj,
    //                             type: 1,
    //                         },
    //                         type: 1,
    //                     },
    //                     true,
    //                     true,
    //                     false,
    //                 );

    //                 // this.eventEmmiter.emit(MESSAGE_EVENT, {
    //                 //     topic: 'bookings.not.payment.yet.reminder',
    //                 //     userId: booking.userId,
    //                 //     appId: booking.appId,
    //                 //     title: `${messageRemiderNotPament}`,
    //                 //     partnerId: booking.partnerId,
    //                 //     eventData: {
    //                 //         ...bookingObj,
    //                 //         type: 1,
    //                 //     },
    //                 //     type: 1,
    //                 //     isPushNotif: true,
    //                 //     isSendMail: false,
    //                 // });

    //                 this.scheduler.deleteCronJob(name);
    //             } else {
    //                 // const bookingObje = booking.toObject();
    //                 // const statusText = this.utilService.getBookingText(bookingObje);
    //                 this.scheduler.deleteCronJob(name);
    //             }
    //         },
    //         () => {
    //             this.logger.log(`Complete CronJob push notify reminder for Booking: ${bookingBefore.bookingCode}`);
    //         },
    //     );
    //     try {
    //         this.scheduler.addCronJob(name, job);
    //         this.logger.log(`CronJob reminder for Booking: ${bookingBefore.bookingCode} is registered`);
    //         job.start();
    //     } catch (error) {
    //         // this.clientSentry.instance().captureException(error);
    //     }
    // }

    @OnEvent(CRON_REMIDER_BOOKING_NOT_PAYMENT, { async: true })
    async remindBookingNotPaymentYet(bookingBefore: IBooking) {
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/remind/booking-not-payment`;
        try {
            const result = (await this.http.patch(url, { ...bookingBefore }).toPromise()).data;
            return result;
        } catch (error) {
            const msg = {
                // to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Lỗi khi push lark notif - CRON_REMIDER_BOOKING_NOT_PAYMENT!',
                html: `<p>Từ API WEB => PUSH NOTIF V2 để đăng ký sự kiện push nhắc khách thanh toán- Có lỗi khi gọi sang VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                        ${JSON.stringify(bookingBefore)}`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(PATIENT_TRACKING_EVENT, { async: true })
    async patientTrackingEvent(payload: any) {
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/patient-tracking`;
        try {
            await this.http.post(url, payload).toPromise();
        } catch (error) {
            console.error('err message-event/patient-tracking', error)
        }
    }

    async createEvent(data: CreateEventDTO, isNotif?: boolean, isPushNotif?: boolean, isSendMail?: boolean): Promise<any> {
        const id = uuid.v4().replace(/-/g, '');
        const obj: any = {};
        if (data.userId) {
            obj.userId = data.userId;
        }
        const { type = 0 } = data;
        /* lấy thông tin bookingDate */
        const { eventData }: any = data;
        const objRecommend: any = {};
        if (eventData) {
            if (typeof eventData.type !== typeof undefined && eventData.type === 1) {
                objRecommend.bookingDate = eventData.date;
                objRecommend.type = eventData.type;
                objRecommend.isRecommended = true;
            }
        }
        const eventInfo = {
            id,
            createTime: moment(data.createTime).toDate(),
            topicId: data.topicId,
            ...obj,
            ...objRecommend,
            appId: data.appId,
            partnerId: data.appId,
            title: data.title,
            isNotif: isNotif ? true : false,
            isPushNotif: isPushNotif ? true : false,
            isSendMail: isSendMail ? true : false,
            eventData: {
                ...data.eventData,
            },
            type,
        };
        const event = new this.eventModel(eventInfo);
        // For notification
        const notification = new this.notificationModel(eventInfo);
        event.save().catch(error => Logger.log(error));
        const listTopicId = new Set([
            'invoice.confirm',
            'his.confirm',
            'bookings.confirm',
            'bookings.cancel',
            'deposit.confirm',
            'orders.confirm',
            'orders.cancel',
            'bookings.reminder',
            'bookings.reExam',
            'bookings.telemed.reminder',
            'bookings.not.payment.yet.reminder',
            'sync-booking.failed',
        ]);
        const isTopicIdNotif = listTopicId.has(eventInfo.topicId);
        if (isTopicIdNotif) {
            // đẩy sang chỗ MESAGE EVENT xử lý riêng
            // this.eventEmitter.emit(MESSAGE_EVENT, {
            //     eventId: event._id,
            //     appId: data.appId,
            //     topic: data.topicId,
            //     partnerId: data.partnerId,
            //     type,
            //     title: data.title,
            //     transactionId: eventInfo.eventData?.transactionId,
            //     userId: data.userId,
            //     isPushNotif: isPushNotif ? true : false,
            //     isSendMail: isSendMail ? true : false,
            //     eventData: eventInfo.eventData,
            // });
            notification.save().catch(error => Logger.error(error));
        }
        return true;
    }

    async createEventV2(data: CreateEventDTO, isNotif?: boolean, isPushNotif?: boolean, isSendMail?: boolean): Promise<any> {
        try {
            const id = uuid.v4().replace(/-/g, '');
            const obj: any = {};
            if (data.userId) {
                obj.userId = data.userId;
            }
            const { type = 0 } = data;
            /* lấy thông tin bookingDate */
            const { eventData }: any = data;
            const objRecommend: any = {};
            if (eventData) {
                if (typeof eventData.type !== typeof undefined && eventData.type === 1) {
                    objRecommend.bookingDate = eventData.date;
                    objRecommend.type = eventData.type;
                    objRecommend.isRecommended = true;
                }
            }
            const eventInfo = {
                id,
                createTime: moment(data.createTime).toDate(),
                topicId: data.topicId,
                ...obj,
                ...objRecommend,
                appId: data.appId,
                partnerId: data.appId,
                title: data.title,
                isNotif: isNotif ? true : false,
                isPushNotif: isPushNotif ? true : false,
                isSendMail: isSendMail ? true : false,
                eventData: {
                    ...data.eventData,
                },
                type,
            };
            const event = new this.eventModel(eventInfo);
            // For notification
            const notification = new this.notificationModel(eventInfo);
            event.save().catch(error => Logger.log(error));
            const listTopicId = new Set([
                'invoice.confirm',
                'his.confirm',
                'bookings.confirm',
                'bookings.cancel',
                'deposit.confirm',
                'orders.confirm',
                'orders.cancel',
                'booking.update',
            ]);
            const isTopicIdNotif = listTopicId.has(eventInfo.topicId);
            if (isTopicIdNotif) {
                // đẩy sang chỗ MESAGE EVENT xử lý riêng
                this.eventEmmiter.emit(MESSAGE_EVENT, {
                    appId: data.appId,
                    topic: data.topicId,
                    partnerId: data.partnerId,
                    type,
                    title: data.title,
                    transactionId: eventInfo.eventData?.transactionId,
                    userId: data.userId,
                    isPushNotif: isPushNotif ? true : false,
                    isSendMail: isSendMail ? true : false,
                    eventData: eventInfo.eventData,
                });
                notification.save().catch(error => Logger.error(error));
            }
            return true;
        } catch (error) {
            console.log(error);
        }

    }

    @OnEvent(UPDATE_BOOKING_V1_EVENT)
    async updateBooking(paramRequestV1: any) {
        const { bookingTimeIdv1, buoiv1, scheduleIdv1, subjectIdV1, roomIdV1, doctorIdV1, serviceIdV1, bookingId, error_message } = paramRequestV1;
        const [htmlMail, findBooking] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('HTML_UPDATE_BOOKING_FAIL'),
            this.bookingModel
                .findOne({ id: bookingId })
                .populate('partner')
                .populate('patient')
                .exec(),
        ]);
        const findBookingObj = findBooking.toObject();
        const overrideBookingCode = findBookingObj?.bookingCodeV1 || findBookingObj.bookingCode;
        const { partner, patient } = findBookingObj;
        const empty = 'Empty';
        const html = htmlMail
            .replace('{hospitalName}', partner.name)
            .replace('{bookingCode}', overrideBookingCode)
            .replace('{fullName}', `${patient.surname} ${patient.name}`)
            .replace(`{bookingTimeIdv1}`, bookingTimeIdv1 || empty)
            .replace(`{buoiv1}`, buoiv1 || empty)
            .replace(`{scheduleIdv1}`, scheduleIdv1 || empty)
            .replace(`{subjectIdV1}`, subjectIdV1 || empty)
            .replace(`{roomIdV1}`, roomIdV1 || empty)
            .replace(`{doctorIdV1}`, doctorIdV1 || empty)
            .replace(`{serviceIdV1}`, serviceIdV1 || empty)
            .replace(`{error_message}`, error_message);
        const topic = 'update.booking.failed';
        await this.sendEventByTransportMail(topic, {
            from: {
                name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                email: '<EMAIL>',
            },
            subject: `[${findBookingObj.partnerId.toUpperCase()}] Cập nhật phiếu khám ${overrideBookingCode} không thành công xuống Api V1`,
            html,
        });
    }

    async sendEventByTransportMail(topic: string, eventData: CreateMessageEventDataMailDto): Promise<any> {
        const groupMail = await this.globalSettingService.findByKeyAndRepoName('MAIL_SUPPORT_PAYMENT_GROUP');
        if (groupMail) {
            eventData.to = groupMail.split(',');
        }
        this.logger.debug(`Email Event Send To: ${eventData.to}`);
        const payload = {
            topic,
            type: -1,
            isPushNotif: false,
            isSendMail: true,
            eventData,
        };
        this.eventEmmiter.emit(MESSAGE_EVENT, payload);
    }

    @OnEvent(SYNC_V1_EVENT)
    async eventSynV1(bookingObj: any) {
        this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
            name: 'eventSynV1',
            summary: 'Cập nhật trạng thái phiếu khám xuống v1.',
            nameParent: 'updatePaymentStatus',
            params: {
                bookingObj,
            },
            response: {},
        });
        const {transactionId, actionSyncV1 = 2 } = bookingObj;
        const [htmlMail, findBooking, payment, user] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('HTML_SYNC_V1_FAIL'),
            this.bookingModel
                .findOne({id: bookingObj.id})
                .populate('partner')
                .populate('patient'),
            this.paymentModel.findOne({transactionId}).exec(),
            this.userModel.findById(bookingObj.userId).exec(),
        ]);
        const findBookingObj = findBooking.toObject();
        const overrideBookingCode = findBookingObj?.bookingCodeV1 || findBookingObj.bookingCode;
        const {partner, patient} = findBookingObj;
        const html = htmlMail
            .replace('{booking.bookingCode}', overrideBookingCode)
            .replace(
                '{booking.date}',
                moment(findBookingObj.date)
                    .add(7, 'hours')
                    .format('DD-MM-yyyy'),
            )
            .replace('{hospitalName}', partner.name)
            .replace('{payment.transactionId}', payment.transactionId)
            .replace('{payment.subTotal}', `${payment.subTotal}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' VND')
            .replace('{username}', user.username)
            .replace('{patients.name}', patient.name)
            .replace('{patients.surname}', patient.surname)
            .replace('{patients.sex}', patient.sex === 1 ? 'NỮ' : 'NAM')
            .replace('{patients.phone}', patient.mobile);

        const topic = 'sync-booking.failed';

        let title = '';
        let subject = '';
        switch (actionSyncV1) {
            case 1:
                title = `Hủy phiếu ${overrideBookingCode} không thành công! Vui lòng liên hệ tổng đài 1900 2115 để được hổ trợ!`;
                subject = `[${findBookingObj.partnerId.toUpperCase()} - Hủy phiếu] Mã phiếu ${overrideBookingCode} chưa hủy được. CSKH sẽ gọi lại cho khách sau.`;
                break;
            case 2:
                title = `Mã giao dịch ${transactionId} đã thanh toán thành công, chúng tôi đang xử lý và xác nhận phiếu khám!`;
                subject = `[${findBookingObj.partnerId.toUpperCase()}] Phiếu khám ${overrideBookingCode} đã thanh toán nhưng không đồng bộ được xuống V1`;
                break;
        }
        // await Promise.all([
        //     this.createEvent(
        //         {
        //             topicId: topic,
        //             userId: bookingObj.userId,
        //             partnerId: bookingObj.partnerId,
        //             appId: bookingObj.appId,
        //             title,
        //             eventData: {
        //                 ...bookingObj,
        //                 type: 100,
        //                 content: title,
        //                 url: '',
        //             },
        //             type: 100,
        //             createTime: moment().toISOString(),
        //         },
        //         true,
        //         true,
        //         false,
        //     ),
        //     this.sendEventByTransportMail(topic, {
        //         from: {
        //             name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
        //             email: '<EMAIL>',
        //         },
        //         subject,
        //         html,
        //     }),
        // ]);
        await this.sendEventByTransportMail(topic, {
            from: {
                name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                email: '<EMAIL>',
            },
            subject,
            html,
        });
    }

    sendEventByTransportPush(data: CreateMessageEventPushDto): void {
        const payload = {
            ...data,
            isPushNotif: true,
            isSendMail: false,
        };
        this.eventEmmiter.emit(MESSAGE_EVENT, payload);
    }

    @OnEvent(TRACKING_CANCEL_RESERVATIONS)
    async trackingCancelReservation(payload: TrackingCancelReserveDto): Promise<void> {
        try {
            const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/tracking-cancel-reservations`
            // console.log('trackingCancelReservation url: ', url);
            await this.http.post(url, payload).toPromise();
        } catch (err) {
            console.error('trackingCancelReservation err', err);
        }
    }

    async testEvent(): Promise<any> {
        const booking = await this.bookingModel.findOne({transactionId: 'TTDev-2203181435'}).exec();
        return this.eventSynV1(booking.toObject());
    }

    @OnEvent(SYNC_USER_INFO, { async: true })
    async syncUserInfo(data: any): Promise<any> {
        const url = `${this.urlConfigService.getUrlAPI119}/user/sync-user-by-phone`;
        return this.http.patch(url, { phone: data?.phone }).toPromise();
    }

    @OnEvent(HANDLE_UPDATE_STATUS_BOOKING_V1_FAIL)
    async handleUpdateStatusBookingFailV1(payload: RetryUpdateStatusDto): Promise<any> {
        try {
            const { bookingObjId, ...data } = payload;
            const retryTransactionNew = new this.retryTransactionModel({
                dataPaymentUpdate: {
                    ...data,
                },
                booking: bookingObjId,
                constraint: `constraint_${data.transactionId}_${bookingObjId}`,
            });
            await retryTransactionNew.save();
        } catch (error) {
            this.logger.log(error.message);
        }
    }

    @OnEvent(EVENT_SURVEY_FORM_NOTY_DALIEU)
    async sendEvenSurveyFormNoty(idMongoBooking: string) {
        const configUrl = await this.globalSettingService.findByKeyAndRepoName('SURVEY_URL');
        if (!configUrl) {
            return;
        }
        const url = `${configUrl}/message-event/survey-form-dalieu`;
        const urlParam = queryString.stringifyUrl({
            url,
            query: {
                id: idMongoBooking,
            },
        });
        return this.http.patch(urlParam).toPromise();
    }

    @OnEvent(CRON_SYNC_PATIENT, {async: true})
    async syncPatientEventHandler(event: SyncPatientEvent): Promise<any> {
        this.hisGatewayService.syncPatient(event)
    }

    @OnEvent(GET_CHECK_IN_ROOM_BOOKING_NOTE, { async: true })
    async getCheckInRoomAndBookingNote({ booking, endpoint }): Promise<any> {
        let resultRoomCheckIn = {};
        try {
            const { addonServices = [] } = booking;
            const addonServiceIds = addonServices.length ? addonServices.map(service => service.id) : [];
            resultRoomCheckIn =
                (await this.getCheckinRoomInfo(
                    endpoint,
                    booking.partnerId,
                    booking.serviceId,
                    booking.subjectId,
                    booking.roomId,
                    booking.insuranceType,
                    addonServiceIds,
                )
                    .toPromise()).data;
        } catch (error) {
        }
        /* Tiến hành lấy thông tin booking Note */
        const bookingNoteObj: any = {};
        try {
            const bookingNoteData = (await this.getBookingNote(endpoint, booking.partnerId).toPromise()).data;
            bookingNoteObj.bookingNote = bookingNoteData;
        } catch (error) {
        }

        let infoLine2: any = {};
        try {
            infoLine2 = (await this.getBookingInfoLine2(endpoint, { partnerId: booking.partnerId, serviceId: booking.serviceId}).toPromise()).data;
        } catch (error) {
            console.log('error get infoLine2: ', error);
        }

        /* cập nhật vào trong booking */
        try {
            await this.bookingModel.findByIdAndUpdate({ _id: booking._id }, { checkInRoom: resultRoomCheckIn, ...bookingNoteObj, serviceInfo: { ...booking.serviceInfo, infoLine2 }}).exec();
        } catch (error) {
        }
    }

    @OnEvent(FIND_REFERRAL_CODE, { async: true })
    async findReferralCode({ booking, referralCode }: { booking: IBooking, referralCode: string }): Promise<any> {
        try {
            const referralCodeRegister = await this.userService.findReferralCodeByUserId(booking.userId);
            if (referralCodeRegister) {
                await this.bookingModel.findByIdAndUpdate({_id: booking._id}, {referralCode: referralCodeRegister.referralCode}).read('primary').exec()
            } else {
                if (referralCode) {
                    try {
                        const objInsert = {
                            partnerId: booking.partnerId,
                            appId: booking.appId,
                            userId: booking.userId,
                            referralCode,
                            uKey: `${booking.userId}`,
                            createDate: moment().toDate(),
                        };
                        const registerCode = new this.referralCodeRegisterModel(objInsert);
                        await registerCode.save();
                        await this.bookingModel.findByIdAndUpdate({ _id: booking._id }, { referralCode }).read('primary').exec()
                    } catch (error) {
                        console.log('FIND_REFERRAL_CODE', error);
                    }
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    getBookingNote(baseUrl: string, partnerId: string): Observable<AxiosResponse<any>> {
        const url = `${baseUrl}/schedule/v2/booking/booking-note/${partnerId}`;
        return this.http.get(url);
    }

    getBookingInfoLine2(baseUrl: string, params: any): Observable<AxiosResponse<any>> {
        const url = `${baseUrl}/schedule/v2/booking/info-line-2`;
        return this.http.get(url, { params });
    }

    getCheckinRoomInfo(
        baseUrl: string,
        partnerId: string,
        serviceId: string,
        subjectId: string,
        roomId: string,
        insuranceType: string,
        addonServiceIds: string[] = [],
    )
        : Observable<AxiosResponse<any>> {
        return this.http.post(`${baseUrl}/schedule/v1/booking/room`, {
            partnerId,
            serviceId,
            subjectId,
            roomId,
            insuranceType,
            addonServiceIds,
        });
    }

    
    @OnEvent(PUSH_NOTIF_AND_SEND_MAIL_BOOKING_SUCCESS, { async: true })
    async pushNotifAndSendMailBookingSuccess(payload: any): Promise<any> {
        // this.logger.log('PUSH_NOTIF_AND_SEND_MAIL_BOOKING_SUCCESS EVENT')
        // this.logger.log(JSON.stringify(payload, null, 2) )
        const { booking, listAppId, transactionId, statusObj, message, status } = payload

        /* kiểm tra xem booking trên android hoặc ios thì mới đăng ký push Notif */
        const isPushNotif = (booking.platform === 'android' || booking.platform === 'ios') ? true : false;
        /* Kiểm tra xem booking này. người dùng có email ko để send mail */
        let emailPatient = '';
        try {
            const bookingWithPatient: any = await (await this.bookingModel
                .findById({ _id: booking._id }, { patient: true })).populate('patient').execPopulate();
            emailPatient = bookingWithPatient.patient.email;
        } catch (error) {
            emailPatient = '';
        }
        const isSendMail = !!emailPatient ? (isEmail(emailPatient) ? true : false) : false;

        /* tiến hành insert vào trong transacton event */
        try {

            /* tiến hành push notif */
            let titleMesasge = `Bạn đã đăng ký khám bệnh thành công. Mã phiếu: ${booking.bookingCode}`;
            if (listAppId.has(booking.appId)) {
                const getPartner = get(booking, 'partner.name', '');
                titleMesasge = `Bạn đã đăng ký khám bệnh thành công tại ${getPartner}. Mã phiếu: ${booking.bookingCode}`;
            }
            /* tiến hành bắn event */
            await this.createEventV2({
                topicId: 'bookings.confirm',
                createTime: moment().toISOString(),
                userId: booking.userId,
                partnerId: booking.partnerId,
                appId: booking.appId,
                title: titleMesasge,
                eventData: {
                    ...booking,
                    paymentStatus: status,
                    paymentMessage: message,
                    ...statusObj,
                    transactionId,
                    type: 1,
                    emailPatient,
                },
                type: 1,
            }, true, isPushNotif, isSendMail);

        } catch (error) {
            console.log(error)
        }

    }


    @OnEvent(FORCE_PAYMENT_HUB_CALLBACK_BE, {async: true})
    async forcePaymentHubCallbackBackend(booking: any): Promise<any> {
        const transactionId = booking?.transactionId || 'NONE';
        const paymentHubData = { transactionId, isCallback: 1 };
        const urlPaymentHubTransaction = this.urlConfigService.getPaymentTransactionUrl();
        /* tìm lại bên payment hub */
        let result = { data: { TransactionInfo: {} } };
        try {
            result = (await this.http.post(urlPaymentHubTransaction, { ...paymentHubData }).toPromise()).data;
            // console.log(result);
            // const { data: { TransactionInfo = {} } } = result;
            // return TransactionInfo;
        } catch (error) {
            // this.clientSentry.instance().captureException(error);
            throw new HttpException(error, HttpStatus.BAD_REQUEST);
        }
    }

    @OnEvent(SYNC_BOOKING_TRONG_NGAY, { async: true })
    async syncBookingTrongNgay(payload: any) {
        const { booking, endpoint } = payload;
        this.logger.log(`${endpoint}_SYNC_BOOKING_TRONG_NGAY EVENT_${booking.id}`)
        try {

            const resultBoSo = (await this.confirmBoSo(endpoint, booking.id).toPromise()).data;
            this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
                name: 'SYNC_BOOKING_TRONG_NGAY',
                summary: 'SYNC_BOOKING_TRONG_NGAY',
                nameParent: 'sendMailBookingNotice',
                params: payload,
                response: resultBoSo,
                errorBody: null,
                message: '',
            });
        } catch (error) {
            this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
                name: 'SYNC_BOOKING_TRONG_NGAY',
                summary: 'SYNC_BOOKING_TRONG_NGAY',
                nameParent: 'sendMailBookingNotice',
                params: payload,
                response: {},
                errorBody: this.utilService.errorHandler(error),
                message: '',
            });
        }
    }

    confirmBoSo(url: string, id: string): Observable<AxiosResponse<any>> {
        const baseUrl = `${url}/${id}`;
        // console.log('baseUrl', baseUrl);
        return this.http.get(baseUrl);
    }

    @OnEvent(UPDATE_CONSTRAINT_AND_BOOKING_RULE, { async: true })
    async updateConstraintAndBookingRule(payload: any) {
        try {
            // this.logger.log('UPDATE_CONSTRAINT_AND_BOOKING_RULE EVENT')
            // this.logger.log(JSON.stringify(payload, null, 2))
            const { booking } = payload;
            /* Tiến hành cập nhật lại thông tin constraints */
            await this.constraintsModel.findOneAndUpdate({
                idBooking: booking.id,
                status: { $ne: 1 },
            }, { status: 1 }).exec();

            await this.updateSuccessConstraintsBookingRules(booking.appId, booking.partnerId, { idBooking: booking.id });
        } catch (error) {
            console.log(error)
        }
    }

    async updateSuccessConstraintsBookingRules(appId: string, partnerId: string, bookingData: any): Promise<any> {
        try {
            return await this.proxyUpdateSuccessConstraintsBookingRules({ ...bookingData, appId, partnerId });
        } catch (error) {
            console.log('updateSuccessConstraintsBookingRules error', error);
            // this.clientSentry.instance().captureException(error);
        }
    }

    async getBookingRuleUrl() {
        // if (REPO_NAME_BETA.includes(this.repoName)) {
            return this.urlConfigService.getUrlCheckBookingRules();
        // } else {
        //     return this.globalSettingService.findByKeyAndRepoName('BOOKING_RULES_URL');
        // }
    }
    async proxyUpdateSuccessConstraintsBookingRules(params: any) {
        const baseUrl = await this.getBookingRuleUrl();
        const url = `${baseUrl}/booking-rules/update-success-constraints`;
        const httpsAgent = new https.Agent({ rejectUnauthorized: false });
        const { data } = await this.http.post(url, { ...params }, { httpsAgent }).toPromise();
        return data;
    }

    @OnEvent(EVENT_BOOKING_SUCCESS, { async: true })
    async sendMailBookingSuccesssNewNoticeCAM(transactionId: string) {
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/mail-booking-success-notice-cam`;
        const urlQuery = queryString.stringifyUrl({
            url,
            query: {
                transactionid: transactionId,
            },
        });
        try {
            const result = (await this.http.patch(urlQuery).toPromise()).data;
            return result;
        } catch (error) {
            const msg = {
                // to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Lỗi khi push lark notif - UPDATE STATUS - EVENT_MAIL_BOOKING_NEW_NOTICE!',
                html: `<p>Từ UPDATE STATUS sendMailBookingSuccesssNewNoticeCAM => PUSH NOTIF V2 - Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                        <i>Chạy lại lệnh sau khi restart docker: </i><code>curl --location --request PATCH '${urlQuery}'</code>`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(EVENT_RETRY_SYNC_BOOKING, { async: true })
    async eventRetrySyncBooking(payload: any) {
        const { transactionId, userId, userActionId } = payload;
        try {
            await this.retryPaymentBookingModel
                .findOneAndUpdate({ transactionId }, { status: 1, userAction: { date: moment().toDate(), user: userActionId } })
                .exec();

            await this.http.post(`${this.urlConfigService.getUrlPushNotifV2}/message-event/lark-notif-retry-sync-booking`, 
                { transactionId, userId, userActionId }).toPromise();
        } catch (error) {
            console.log('message-event/create-kpi-cskh err', error)
        }
    }

    @OnEvent(EVENT_MAIL_BOOKING_NEW_NOTICE_V2, { async: true })
    async sendMailBookingSuccesssNewNoticeV2(payload: any) {
        // this.logger.log('EVENT_MAIL_BOOKING_NEW_NOTICE_V2 EVENT')
        // this.logger.log(JSON.stringify(payload, null, 2))
        const { booking } = payload;
        try {
            const envConfig = await this.globalSettingService.findByKeyAndRepoName('ENV_MAIL_BOOKING_NOTICE');
            const envConfigObj = JSON.parse(envConfig);
            const configDomainProperty = get<boolean>(
                envConfigObj?.constraint, `${booking.appId}|${booking.partnerId}|${booking?.treeId}`.toLowerCase(),
                false,
            );
            // console.log('envConfigObj?.env', envConfigObj?.env)
            // console.log('configDomainProperty', configDomainProperty)
            if (envConfigObj?.env === 'ON' && configDomainProperty === true) {
                // console.log('EVENT_MAIL_BOOKING_NEW_NOTICE', booking.transactionId)
                const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/mail-booking-success-notice`;
                const urlQuery = queryString.stringifyUrl({
                    url,
                    query: {
                        transactionid: booking.transactionId,
                    },
                });
                try {
                    const result = (await this.http.patch(urlQuery).toPromise()).data;
                    return result;
                } catch (error) {
                    const msg = {
                        // to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                        to: '<EMAIL>',
                        from: '<EMAIL>',
                        subject: 'Lỗi khi push lark notif - EVENT_MAIL_BOOKING_NEW_NOTICE!',
                        html: `<p>Từ API WEB => PUSH NOTIF V2 - Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                                <i>Chạy lại lệnh sau khi restart docker: </i><code>curl --location --request PATCH '${urlQuery}'</code>`,
                    };
                    await this.sendgrid.send(msg);
                    throw error;
                }
            }
        } catch (error) {
            this.eventEmmiter.emit(LOG_SERVICE_EVENT, {
                name: 'Process Send Booking Notice',
                summary: 'Process Send Booking Notice',
                nameParent: 'sendMailBookingNotice',
                params: { booking },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.`,
            });
        }
    }

    @OnEvent(EVENT_MAIL_BOOKING_NEW_NOTICE)
    async sendMailBookingSuccesssNewNotice(transactionId: string) {
        const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/mail-booking-success-notice`;
        const urlQuery = queryString.stringifyUrl({
            url,
            query: {
                transactionid: transactionId,
            },
        });
        try {
            const result = (await this.http.patch(urlQuery).toPromise()).data;
            return result;
        } catch (error) {
            const msg = {
                // to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                to: '<EMAIL>',
                from: '<EMAIL>',
                subject: 'Lỗi khi push lark notif - EVENT_MAIL_BOOKING_NEW_NOTICE!',
                html: `<p>Từ API WEB => PUSH NOTIF V2 - Có lỗi khi push lark notif. kiểm tra VM 2.10  - containerName api-notif-v2-111-live port 14001. Cần thiết thì restart lại <b>docker restart api-notif-v2-111-live</b></p>
                        <i>Chạy lại lệnh sau khi restart docker: </i><code>curl --location --request PATCH '${urlQuery}'</code>`,
            };
            await this.sendgrid.send(msg);
            return true;
        }
    }

    @OnEvent(REPAYMENT_SUCCESS_EVENT, { async: true })
    async eventRepaymentSuccess(payload: { bookingCode: string, bookingId: string }) {
        try {
            const url = `${this.urlConfigService.getUrlPushNotifV2}/message-event/repayment-success`;
            await this.http.post(`${url}`, payload).toPromise();
        } catch (err) {
            console.log('err eventRepaymentSuccess: ', this.utilService.errorToJson(err));
        }
    }


    async closeTransaction(transactionId): Promise<any> {
        const payment = await this.paymentModel.findOne({ transactionId, status: 1, type: 1 }).exec();
        const api = `${this.urlConfigService.getPaymentHubUrl()}/payment/v1/payin/close-order`;
        const formdata = {
            transactionId: payment?.transactionId,
            description: `Medpro đóng giao dịch ${payment?.transactionId} - ${payment?.bookingCode} phiếu ${payment?.bookingId}`,
        };
        return this.client.postNoContent(api, formdata);
    }


    @OnEvent(EVENT_CLOSE_ORDER_PAYMENT_HUB, { async: true })
    async closeOrder(bookingId: string): Promise<void> {
        try {
            if(bookingId){
                const payments = await this.paymentModel.find({ bookingId, status: 1, type: 1, paymentMethod: { $ne: 'SHARE_PAYMENT' } }).exec();
                if (payments.length === 0) {
                    return;
                }
                await Promise.all(payments.map(async payment => {
                    const api = `${this.urlConfigService.getPaymentHubUrl()}/payment/v1/payin/close-order`;
                    const formdata = {
                        transactionId: payment?.transactionId,
                        description: `Medpro đóng giao dịch ${payment?.transactionId} - ${payment?.bookingCode} phiếu ${payment?.bookingId}`,
                    };
                    await this.client.postNoContent(api, formdata);
                    this.logger.log(`Medpro đóng giao dịch ${payment?.transactionId} - ${payment?.bookingCode} phiếu ${payment?.bookingId}`);
                }));
            }
        } catch (error) {
            this.logger.log(error?.message || `ERROR: @OnEvent(EVENT_CLOSE_ORDER_PAYMENT_HUB): bookingID: ${bookingId}`);
        }
    }

    @OnEvent(EVENT_TIMEOUT_TRANSACTION_LOGS, { async: true })
    createTimeoutTransactionLogs(data: any) {
        void this.timeoutTransactionLogsModel.create({...data}).catch(error => this.logger.warn(error?.message))
    }

    @OnEvent(THIRD_PARTY_ERROR_LOG)
    async thirdPartyErrorLogs(payload: any) {
        const { subjectMail = '', ...rest } = payload;
        const groupMail = await this.globalSettingService.findByKeyAndRepoName('MAIL_THIRD_PARTY_ERROR');
        let arrTo: any = []
        if (groupMail) {
            arrTo = groupMail.split(',');
        }

        try {
            // ? GỬI THÔNG BÁO LỖI VÀO LARK GROUP
            const data = JSON.stringify(rest);              
            const config: AxiosRequestConfig = {
                method: 'post',
                url: `${this.urlConfigService.getUrlPushNotifV2}/message-event/notification/lark-payment-error`,
                headers: { 
                    'Content-Type': 'application/json'
                },
                data : data
            };

            const response = await this.http.request<
            {
                isOK: boolean,
                error?: string
            }
            >(config).toPromise();

            this.logger.log(`Send mail and push notif to lark success: ${response.data}`);

            const msg = {
                to: [
                    ...arrTo,
                ],
                // to: '<EMAIL>',
                from: '<EMAIL>',
                subject: subjectMail || 'No Subject',
                html: `<code>${JSON.stringify(rest, null, 2)}</code>`,
            };
            await this.sendgrid.send(msg);
        } catch (error) {
            console.log(error)
        }

    }

    @OnEvent(BOOKING_FULL_SLOT_EVENT)
    async bookingFullSlotEvent(payload: any) {
        try {
            await this.http.post(`${this.urlConfigService.getUrlPushNotifV2}/message-event/booking-full-slot`, payload).toPromise();
        } catch (error) {
            console.log('message-event/booking-full-slot err', error)
        }

    }

    @OnEvent(EVENT_CREATE_KPI_CSKH)
    async createKPICSKH(payload: any) {
        try {
            await this.http.post(`${this.urlConfigService.getUrlPushNotifV2}/message-event/create-kpi-cskh`, payload).toPromise();
        } catch (error) {
            console.log('message-event/create-kpi-cskh err', error)
        }
    }
}
