import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { get } from 'lodash';
import { Observable } from 'rxjs';

@Injectable()
export class ViettelPayInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const appId = get(request.headers, 'appid');
    request.headers.appid = appId === 'viettelpay' || appId === 'zalopay' ? 'medpro' : appId;
    return next.handle();
  }
}
