import { Controller, Get, Headers, Post, Query } from '@nestjs/common';
import { HeadersDto } from '../common/base/headers.dto';
import { DanhMucBangGiaService } from './danh-muc-bang-gia.service';

@Controller('danh-muc-bang-gia')
export class DanhMucBangGiaController {
    constructor(private service: DanhMucBangGiaService) {}

    @Post('seed-umc-data')
    seedUmcData() {
        return this.service.seedUmcData();
    }

    @Get()
    getByPartner(@Query() query: any, @Headers() headers: HeadersDto) {
        return this.service.getByPartner({ ...query, partnerId: headers.partnerid });
    }
}
