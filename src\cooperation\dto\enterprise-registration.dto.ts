import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class EnterpriseRegistrationDto {
    // Họ và tên người đăng ký
    @ApiProperty({
        description: 'Họ và tên người đăng ký',
        example: 'Nguyễn Văn A',
    })
    @IsString()
    @IsNotEmpty()
    fullName: string;

    // Tên công ty/tổ chức
    @ApiProperty({
        description: 'Tên công ty hoặc tổ chức',
        example: 'Công ty TNHH ABC',
    })
    @IsString()
    @IsNotEmpty()
    company: string;

    // Số lượng người khám
    @ApiProperty({
        description: 'Số lượng người tham gia khám',
        example: 10,
    })
    @IsNumber()
    @IsNotEmpty()
    quantity: number;

    // Số điện thoại liên hệ
    @ApiProperty({
        description: 'Số điện thoại liên hệ',
        example: '0909123456',
    })
    @IsString()
    @IsNotEmpty()
    phoneNumber: string;

    // Ghi chú (tùy chọn)
    @ApiProperty({
        description: 'Ghi chú',
        example: 'Khám vào buổi sáng',
        required: false,
    })
    @IsString()
    @IsNotEmpty()
    note: string;

    @ApiProperty({
        description: 'captchaResponse',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'captchaResponse không được để trống' })
    captchaResponse: string;
}
