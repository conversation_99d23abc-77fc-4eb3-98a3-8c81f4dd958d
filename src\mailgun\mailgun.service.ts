import { Injectable } from '@nestjs/common';
import { MailgunService } from 'nestjs-mailgun';
import { MailgunConfigService } from '../config/config.mailgun.sevice';

@Injectable()
export class MailService {
  constructor(
    private readonly mailgunService: MailgunService,
    private mailgunConfigService: MailgunConfigService,
) {}

  async sendEmail(body: any) {
    const domain = this.mailgunConfigService.getMailgunDomain()
    const { to, subject, text } = body;
    const data = {
      from: 'User From <<EMAIL>>',
      to, // Địa chỉ email nhận
      subject, // Tiêu đề email
      text, // Nội dung email (đơn giản)
    };
    try {
      const result = await this.mailgunService.createEmail(domain, data);
      console.log('Email sent:', result);
      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }
}
