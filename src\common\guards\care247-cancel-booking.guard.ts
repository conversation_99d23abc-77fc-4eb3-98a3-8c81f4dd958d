import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtModuleOptions } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { last, size } from 'lodash';
import { JwtUserConfigService } from '../../config/config.user.jwt.service';
import { GlobalSettingService } from '../../global-setting/global-setting.service';

@Injectable()
export class Care247CancelBookingGuard implements CanActivate {
    constructor(
        private jwtUserConfigService: JwtUserConfigService, 
        private readonly globalSettingService: GlobalSettingService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();

        const { authorization } = request.headers;

        if (size(authorization) > 0) {
            try {
                const jwtVerify: any = jwt.verify(last(authorization.split(' ')), jwtOptions.secret);
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const care247CancelBooking = await this.globalSettingService.findByKeyAndRepoName('CARE247_CANCEL_PERMISSION_VIEW');
                    const care247CancelBookingList = new Set(care247CancelBooking.split(','));
                    
                    if (care247CancelBookingList.has(userMongoId)) {
                        return true
                    } else {
                        throw new HttpException('Bạn không có quyền thực hiện thao tác này!', HttpStatus.FORBIDDEN);
                    }
                }
                return true;
            } catch (error) {
                throw new HttpException('Bạn không có quyền thực hiện thao tác này!', HttpStatus.FORBIDDEN);
            }
        }
    }
}