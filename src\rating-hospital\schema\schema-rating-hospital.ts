import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';

import { RATING_HOSPITAL_COLLECTION_NAME } from './constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const RatingHospitalSchema = new Schema(
    {
        customerName: { type: String, required: true },
        rating: { type: Number, required: true, min: 1, max: 5 },
        comment: { type: String, required: true },
        bookingCode: { type: String, required: true },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME},
        isPrioritize: { type: Boolean, default: false },    //ưu tiên hiển thị
        status: { type: Number, default: 2 },           //trạng thái    0:<PERSON><PERSON><PERSON> duy<PERSON>, 1: <PERSON><PERSON>, 2:<PERSON><PERSON> duyệt
        hospitalId: { type: String, required: true },
        hospital: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME},
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME, required: false },
    },
    {
        collection: RATING_HOSPITAL_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
