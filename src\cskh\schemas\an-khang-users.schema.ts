import * as mongoose from 'mongoose';
import { AN_KHANG_BRANCHES_COLLECTION, AN_KHANG_USERS_COLLECTION } from './constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const AnKhangUserSchema = new Schema(
  {
    username: { type: String, required: true },
    user: { type: mongoose.Schema.Types.ObjectId, ref: USER_COLLECTION_NAME, required: true },
    branch: { type: mongoose.Schema.Types.ObjectId, ref: AN_KHANG_BRANCHES_COLLECTION, required: true },
    branchId: { type: String, required: true },
    code: { type: String, required: true, unique: true },
  },
  {
    collection: AN_KHANG_USERS_COLLECTION,
    timestamps: true,
  },
); 