import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';

import { SCHEMA_SPECIALTY_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

const CtaSchema = new Schema({
    url: { type: String, required: true },
    target: { type: String },
    action: { type: String },
    browser: { type: Boolean, default: false },
    dataIndex: { type: Number },
    type: { type: String, required: false },
    screen: { type: String, required: false },
    name: { type: String, required: false },
    partnerId: { type: String, required: false },
    treeId: { type: String, required: false },
    subjectId: { type: String, default: null },  // Có thể là null
    serviceId: { type: String, default: null },  // Có thể là null
    doctorId: { type: String, required: false },
    roomId: { type: String, default: null },  // Có thể là null
});

export default CtaSchema;

export const SpecialtySchema = new Schema(
    {
        name: {
            type: String,
            required: true,
            unique: true,
        },
        icon: {
            type: String,
            required: true,
        },
        status: {
            type: Boolean,
            default: true,
        },
        cta: CtaSchema,
    },
    {
        collection: SCHEMA_SPECIALTY_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
