import { ApiProperty } from '@nestjs/swagger';

export class BookingCskhRevenueQueryDto {
    @ApiProperty({
        description: 'cskhUserId',
        required: false,
        type: String,
    })
    cskhUserId?: string;
    
    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId?: string;

    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toDate?: string;
}
