import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsDateString } from 'class-validator';

export class QueryPatientInfoDto {
    @ApiProperty({ description: 'Tì<PERSON> kiếm theo tên bệnh nhân', required: false })
    @IsOptional()
    @IsString()
    patientName?: string;

    @ApiProperty({ description: 'Tìm kiếm theo số điện thoại', required: false })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiProperty({ description: 'Lọc theo giới tính', enum: [0,1], required: false })
    @IsOptional()
    @IsEnum([0,1])
    gender?: 0 | 1;

    @ApiProperty({ description: 'Lọc từ ngày tạo', required: false })
    @IsOptional()
    @IsDateString()
    fromDate?: string;

    @ApiProperty({ description: 'Lọc đến ngày tạo', required: false })
    @IsOptional()
    @IsDateString()
    toDate?: string;

    @ApiProperty({ description: 'Trang hiện tại', example: 1, required: false })
    @IsOptional()
    page?: number;

    @ApiProperty({ description: 'Số lượng bản ghi mỗi trang', example: 10, required: false })
    @IsOptional()
    limit?: number;
}
