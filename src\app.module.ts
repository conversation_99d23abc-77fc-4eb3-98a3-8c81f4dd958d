import { TransactionLogsModule } from './transaction-log/transaction-log.module';
import {HttpModule, HttpService, Module, RequestMethod} from '@nestjs/common';
import { CommandModule } from 'nestjs-command';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { ServeStaticModule } from '@nestjs/serve-static';
import { SentryModule } from '@ntegral/nestjs-sentry';
// import { LogLevel } from '@sentry/types';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DashboardController } from './dashboard/dashboard.controller';
import { DashboardService } from './dashboard/dashboard.service';
import { ConfigModule } from './config/config.module';
import { PkhConfigService } from './config/config.pkh.service';
import { PatientConfigService } from './config/config.patient.service';
import { ThuDucHospitalConfigService } from './config/config.thuduc.hospital.service';
import { ChoRayHospitalConfigService } from './config/config.choray.hospital.service';
import { KnexAdminModule } from './config/pkhConnection/index';
import { KnexPatientModule } from './config/pkhPatientConnection/index';
import { KnexThuDucHospitalModule } from './config/thuDucHospitalConnection/index';
import { KnexChoRayHospitalModule } from './config/choRayHospitalConnection';
import { FirebaseAdminModule } from './config/firebaseConnection';
import { FirebaseAdminSecondModule } from './config/firebaseConnectionSecond';
import { FirebaseAdminDaLieuModule } from './config/firebaseConnectionDaLieu';
import { FirebaseAdminNd1Module } from './config/firebaseConnectionNd1';
import { FirebaseAdminUMCModule } from './config/firebaseConnectionUMC';
import { FirebaseConfigService } from './config/config.firebase.sevice';
import { FirebaseSecondConfigService } from './config/config.firebase-second.sevice';
import { FirebaseDaLieuConfigService } from './config/config.firebase-dalieu.sevice';
import { FirebaseUMCConfigService } from './config/config.firebase-umc.sevice';
import { FirebaseNd1ConfigService } from './config/config.firebase-nd1.sevice';
import { AuthModule } from './auth/auth.module';
import { AdminUserModule } from './admin-user/admin-user.module';
import { PermissionModule } from './permission/permission.module';
import { HospitalModule } from './hospital/hospital.module';
import { PatientModule } from './patient/patient.module';
import { SaleModule } from './sale/sale.module';
import { TrackingSaleModule } from './tracking-sale/tracking-sale.module';
import { TrackingSaleDetailModule } from './tracking-sale-detail/tracking-sale-detail.module';
import { BookingModule } from './booking/booking.module';
import { ResourceModule } from './resource/resource.module';
import { SubjectModule } from './subject/subject.module';
import { UserModule } from './user/user.module';
import { SendgridMailModule } from './config/sendgridConnection';
import { SendgridConfigService } from './config/config.sendgrid.sevice';
import { MailerService } from './mailer/mailer.service';
import { TaskModule } from './task/task.module';
// import { SystemExceptionModule } from './system-exception/system-exception.module';
import { PaymentMethodModule } from './payment-method/payment-method.module';
import { FeeModule } from './fee/fee.module';
import { PaymentFeeModule } from './payment-fee/payment-fee.module';
import { BankModule } from './bank/bank.module';
import { MomoModule } from './momo/momo.module';
// import { MedproModule } from './medpro/medpro.module';
// import { MedproService } from './medpro/medpro.service';
// import { MedproBookingModule } from './medpro-booking/medpro-booking.module';
// import { Booking } from './medpro-booking/schemas/booking.schema';
// import { BookingSlot } from './medpro-booking/schemas/booking-slot.schema';
// import { MedproBookingService } from './medpro-booking/medpro-booking.service';
// import { Patient } from './medpro-booking/schemas/patient.schema';
import { NgayNghiLeModule } from './ngay-nghi-le/ngay-nghi-le.module';
import { SessionModule } from './session/session.module';
// import { DKDBCommand } from './automation-test/thu-duc/dkkb.command';
import { DetectRouterModule } from './detect-router/detect-router.module';
import { PhoneLoginModule } from './phone-login/phone-login.module';
import { SmsModule } from './sms/sms.module';
import { TeleMedicineModule } from './tele-medicine/tele-medicine.module';
import { DoctorModule } from './doctor/doctor.module';
import { PushDeviceModule } from './push-device/push-device.module';
import { NotificationSettingModule } from './notification-setting/notification-setting.module';
import { PushNotifModule } from './push-notif/push-notif.module';
import { UserBanModule } from './user-ban/user-ban.module';
import { FeatureModule } from './feature/feature.module';
import { RoomModule } from './room/room.module';
import { SchedulePartnerModule } from './schedule/schedule.module';
import { SubjectMongoModule } from './subject-mongo/subject-mongo.module';
import { ConfigMongoService } from './config/config.mongo.service';
import { HospitalMongoModule } from './hospital-mongo/hospital-mongo.module';
import { ServiceMongoModule } from './service-mongo/service-mongo.module';
import { RoomMongoModule } from './room-mongo/room-mongo.module';
import { DoctorMongoModule } from './doctor-mongo/doctor-mongo.module';
import { ServicePriceModule } from './service-price/service-price.module';
import { BookingGatewayModule } from './booking-gateway/booking-gateway.module';
import { HisGatewayModule } from './his-gateway/his-gateway.module';
import { FeatureMongoModule } from './feature-mongo/feature-mongo.module';
import { PatientMongoModule } from './patient-mongo/patient-mongo.module';
import { RelationshipMongoModule } from './relationship-mongo/relationship-mongo.module';
import { CountryMongoModule } from './country-mongo/country-mongo.module';
import { ProfessionMongoModule } from './profession-mongo/profession-mongo.module';
import { NationMongoModule } from './nation-mongo/nation-mongo.module';
import { CityMongoModule } from './city-mongo/city-mongo.module';
import { DistrictMongoModule } from './district-mongo/district-mongo.module';
import { WardMongoModule } from './ward-mongo/ward-mongo.module';
import { PaymentModule } from './payment/payment.module';
import { HolidayMongoModule } from './holiday-mongo/holiday-mongo.module';
import { EventModule } from './event/event.module';
import { PartnerConfigModule } from './partner-config/partner-config.module';
import { ReExamModule } from './re-exam/re-exam.module';
import { PaymentFeeGatewayModule } from './payment-fee-gateway/payment-fee-gateway.module';
import { MessageConfigModule } from './message-config/message-config.module';
import { KhaiBaoYteModule } from './khai-bao-yte/khai-bao-yte.module';
import { TrackingOrderModule } from './tracking-order/tracking-order.module';
import { RelativeModule } from './relative/relative.module';
import { UserMomoModule } from './user-momo/user-momo.module';
import { FilterProcessModule } from './filter-process/filter-process.module';
import { ConfigSentryService } from './config/config.sentry.service';
import { VaccineModule } from './vaccine/vaccine.module';
import { UserAccountModule } from './user-account/user-account.module';
import { EventProccessorModule } from './event-proccessor/event-proccessor.module';
import { SyncBookingModule } from './sync-booking/sync-booking.module';
import { SyncUserModule } from './sync-user/sync-user.module';
import { FilesModule } from './files/files.module';
import { FilesService } from './files/files.service';
import { EventShareModule } from './event-share/event-share.module';
import { SyncTrungVuongMedproModule } from './sync-trung-vuong-medpro/sync-trung-vuong-medpro.module';
import { SyncDaLieuMedproModule } from './sync-da-lieu-medpro/sync-da-lieu-medpro.module';
import { ReferralCodeModule } from './referral-code/referral-code.module';
import { CacheManagerModule } from './cache-manager/cache-manager.module';
import { SyncDhydModule } from './sync-dhyd/sync-dhyd.module';
import { BookingCovidModule } from './booking-covid/booking-covid.module';
import { AppIdModule } from './app-id/app-id.module';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { GlobalInterceptor } from './middleware/globlal.interceptor';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AuditLogModule } from './audit-log/audit-log.module';
import { FirebaseConfigModule } from './firebase-config/firebase-config.module';
import { VersionAppModule } from './version-app/version-app.module';
import { SyncPatientModule } from './sync-patient/sync-patient.module';
import { MessageEventModule } from './message-event/message-event.module';
import { UploadFileModule } from './upload-file/upload-file.module';
import { GlobalSettingModule } from './global-setting/global-setting.module';
import { FixMedproIdModule } from './fix-medpro-id/fix-medpro-id.module';
import { HospitalReferralsModule } from './hospital-referral/hospital-referral.module';
import { ClinicModule } from './clinic/clinic.module';
import { BookingScheduleModule } from './booking-schedule/booking-schedule.module';
import { GuidePatientModule } from './guide-patient/guide-patient.module';
import { UserPermissionModule } from './user-permission/user-permission.module';
import { HospitalDescriptionModule } from './hospital-description/hospital-description.module';
import { ConfigMongoServices } from './config/config.mongo.services.service';
import { ConfigLarkService } from "./config/config.lark.service";
import { notifyDatabaseDisconnectTemplate } from "./common/template/lark.template";
import { ConfigRepoService } from './config/config.repo.service';
import {UtilService} from "./config/util.service";
import { MongoServerSelectionErrorFilter } from "./firebase-config/filter/mongo-server-selection-error.filter";
import { MongoErrorFilter } from "./firebase-config/filter/mongo-error.filter";
import { MongooseErrorFilter } from "./firebase-config/filter/mongoose-error.filter";
import {CskhModule} from './cskh/cskh.module';
import { DynamicLinkModule } from './dynamic-link/dynamic-link.module';
import { DoctorDescriptionModule } from './doctor-description/doctor-description.module';
import { TrafficModule } from './traffic/traffic.module';
import { ConfigMongoBackofficeService } from './config/config.mongo.backoffice.service';
import { InstructionModule } from './instruction/instruction.module';
import { SchemaNewsModule } from "./schema-news/schema-news.module";
import { DanhMucBangGiaModule } from './danh-muc-bang-gia/danh-muc-bang-gia.module';
import { InstructionProcessModule } from './instruction-process/instruction-process.module';
import { UserZaloModule } from './user-zalo/user-zalo.module';
import { ZaloPayModule } from './zalo-pay/zalo-pay.module';
import { JotformModule } from './jotform/jotform.module';
import { ServiceSubscriptionModule } from './service-subscription/service-subscription.module';
import { LarkFormModule } from './lark-form/lark-form.module';
import { UserViettelMoneyModule } from './user-viettel-money/user-viettel-money.module';
import { ViettelPayModule } from './viettel-pay/viettel-pay.module';
import { CashBackModule } from './cash-back/cash-back.module';
import { HomePageModule } from './home-page/home-page.module';
import { Care247ServiceModule } from './care247-services/care247-services.module';
import { CooperationModule } from './cooperation/cooperation.module';
@Module({
  imports: [
    HttpModule,
    CommandModule,
    ConfigModule,
    ScheduleModule.forRoot(),
    TaskModule,
    KnexAdminModule.registerAsync({
      useExisting: PkhConfigService,
    }),
    KnexPatientModule.registerAsync({
      useExisting: PatientConfigService,
    }),
    KnexThuDucHospitalModule.registerAsync({
      useExisting: ThuDucHospitalConfigService,
    }),
    KnexChoRayHospitalModule.registerAsync({
      useExisting: ChoRayHospitalConfigService,
    }),
    FirebaseAdminModule.registerAsync({
      useExisting: FirebaseConfigService,
    }),
    FirebaseAdminSecondModule.registerAsync({
      useExisting: FirebaseSecondConfigService,
    }),
    FirebaseAdminDaLieuModule.registerAsync({
      useExisting: FirebaseDaLieuConfigService,
    }),
    FirebaseAdminNd1Module.registerAsync({
      useExisting: FirebaseNd1ConfigService,
    }),
    FirebaseAdminUMCModule.registerAsync({
      useExisting: FirebaseUMCConfigService,
    }),
    SendgridMailModule.registerAsync({
      useExisting: SendgridConfigService,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule, HttpModule],
      inject: [ConfigMongoService, ConfigRepoService, UtilService],
      useFactory: async (config: ConfigMongoService, cfgRePo: ConfigRepoService, utilService: UtilService) => {
        return {
          ...config.createMongooseOptions(),
          // retryAttempts: -1,
          // retryDelay: 3000,
          // server: {
          //   reconnectTries: Number.MAX_VALUE,
          //   autoReconnect: true,
          //   reconnectInterval: 10000, // wait for 10s before retry
          // },
          connectionFactory: (connection: any, name: string) => {
            // Success
            ['connected'].forEach(e => {
              console.log(`[SUCCESS][database][${cfgRePo.getRepoName()}] Connected to mongo server.`);
              utilService.sendDatabaseEventToLark(connection, e, `[${cfgRePo.getRepoName()}] Connected to mongo server.`);
            });

            // Error
            ['error', 'disconnected', 'close'].forEach(e => {
              console.error(`[ERROR][database][${cfgRePo.getRepoName()}] Have an error from mongo server.\n${JSON.stringify(e, null, 2)}`);
              utilService.sendDatabaseEventToLark(connection, e, `[${cfgRePo.getRepoName()}] Have an error from mongo server.\n${JSON.stringify(e, null, 2)}`);
            });

            return connection;
          },
        }
      },
    }),
    MongooseModule.forRootAsync({
      useExisting: ConfigMongoServices,
      connectionName: 'services'
    }),
    MongooseModule.forRootAsync({
        useExisting: ConfigMongoBackofficeService,
        connectionName: 'backoffice'
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'client'),
      // serveRoot: '/cdn',
      exclude: ['/api*'],
      serveStaticOptions: {
        index: false,
      },
    }),
    SentryModule.forRootAsync({
      useExisting: ConfigSentryService,
    }),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      ignoreErrors: false,
      maxListeners: Infinity,
    }),
    AuthModule,
    AdminUserModule,
    PermissionModule,
    HospitalModule,
    PatientModule,
    SaleModule,
    TrackingSaleModule,
    TrackingSaleDetailModule,
    BookingModule,
    ResourceModule,
    SubjectModule,
    UserModule,
    // SystemExceptionModule,
    PaymentMethodModule,
    FeeModule,
    PaymentFeeModule,
    BankModule,
    MomoModule,
    NgayNghiLeModule,
    SessionModule,
    DetectRouterModule,
    PhoneLoginModule,
    SmsModule,
    TeleMedicineModule,
    DoctorModule,
    PushDeviceModule,
    NotificationSettingModule,
    PushNotifModule,
    UserBanModule,
    FeatureModule,
    RoomModule,
    SchedulePartnerModule,
    SubjectMongoModule,
    HospitalMongoModule,
    ServiceMongoModule,
    RoomMongoModule,
    DoctorMongoModule,
    ServicePriceModule,
    BookingGatewayModule,
    HisGatewayModule,
    FeatureMongoModule,
    PatientMongoModule,
    RelationshipMongoModule,
    CountryMongoModule,
    ProfessionMongoModule,
    NationMongoModule,
    CityMongoModule,
    DistrictMongoModule,
    WardMongoModule,
    PaymentModule,
    HolidayMongoModule,
    EventModule,
    PartnerConfigModule,
    ReExamModule,
    PaymentFeeGatewayModule,
    MessageConfigModule,
    KhaiBaoYteModule,
    TrackingOrderModule,
    RelativeModule,
    UserMomoModule,
    FilterProcessModule,
    VaccineModule,
    UserAccountModule,
    EventProccessorModule,
    SyncBookingModule,
    SyncUserModule,
    FilesModule,
    EventShareModule,
    SyncTrungVuongMedproModule,
    SyncDaLieuMedproModule,
    ReferralCodeModule,
    SyncDhydModule,
    TransactionLogsModule,
    // MedproModule,
    // MedproBookingModule,
    CacheManagerModule,
    BookingCovidModule,
    AppIdModule,
    AuditLogModule,
    FirebaseConfigModule,
    VersionAppModule,
    SyncPatientModule,
    MessageEventModule,
    UploadFileModule,
    GlobalSettingModule,
    FixMedproIdModule,
    HospitalReferralsModule,
    ClinicModule,
    BookingScheduleModule,
    GuidePatientModule,
    UserPermissionModule,
    HospitalDescriptionModule,
    CskhModule,
    TrafficModule,
    DynamicLinkModule,
    InstructionModule,
    DoctorDescriptionModule,
    SchemaNewsModule,
    DanhMucBangGiaModule,
    InstructionProcessModule,
    UserZaloModule,
    ZaloPayModule,
    JotformModule,
    ServiceSubscriptionModule,
    LarkFormModule,
    UserViettelMoneyModule,
    ViettelPayModule,
    // CashBackModule,
    HomePageModule,
    Care247ServiceModule,
    // CooperationModule
  ],
  controllers: [AppController, DashboardController],
  providers: [AppService, DashboardService, MailerService,
    // DKDBCommand,
    // , MedproService, MedproBookingService
    {
      provide: APP_INTERCEPTOR,
      useClass: GlobalInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: MongoServerSelectionErrorFilter,
    },
    {
      provide: APP_FILTER,
      useClass: MongoErrorFilter,
    },
    {
      provide: APP_FILTER,
      useClass: MongooseErrorFilter,
    },
  ],
})
export class AppModule { }
