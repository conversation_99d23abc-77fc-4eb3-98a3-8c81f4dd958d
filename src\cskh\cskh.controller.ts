import { Body, Controller, Delete, Get, HttpException, HttpStatus, Param, Patch, Post, Put, Query, Req, Res, UseGuards, UseInterceptors, Headers, UploadedFile } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { CskhService } from './cskh.service';
import { CskhGuard } from '../common/guards/cskh.guard';
import { ResetOtpLogDto } from './dto/reset-otp-log.dto';
import { CalledInfoDto } from './dto/called-info.dto';
import { BookingCare247QueryDto } from './dto/booking-care247-query.dto';
import { BookingCare247CskhRevenueQueryDto } from './dto/booking-care247-cskh-revenue-query.dto';
import { MedproCareCancelDto } from './dto/medpro-care-cancel.dto';
import { CskhPushNotif } from './dto/cskh-push-notif.dto';
import { IUpdateEmployeeCare247Dto } from './dto/update-employee-care247.dto';
import { SearchUserCskh } from './dto/search-user-cskh.dto';
import { IUpdateEmployeeCskhDto } from './dto/update-employee-cskh.dto';
import { CskhCare247SalaryGuard } from '../common/guards/cskh-care247-salary.guard';
import { TransactionsQueryDto } from './dto/transactions-query.dto';
import { MedproCareRefundDto } from './dto/medpro-care-refund.dto';
import { Care247RefundGuard } from '../common/guards/care247-refund.guard';
import { AdsCare247Dto } from './dto/ads-care247.dto';
import { UpdateMultipleKey } from './dto/update-multiple-key.dto';
import { Care247Guard } from '../common/guards/care247.guard';
import { AppCskhActivitiesInterceptor } from '../middleware/app-cskh-activities.interceptor';
import { ActionData } from './decorator/cskh-action.decorator';
import { Care247DiaryQueryDto } from './dto/care247-diary.dto';
import { ActionEnum, CategoryEnum } from './decorator/action-data.enum';
import { IDailyTransaction } from './dto/query-daily-transaction-report.dto';
import { MailService } from '../mailgun/mailgun.service';
import { IUpdateCare247IntroductionDto } from './dto/update-care247-introduction.dto';
import { Care247IntroductionConfigGuard } from '../common/guards/care247-introduction-config.guard';
import { ExamResultGuard } from '../common/guards/exam-result-permission.guard';
import { DailyTransactionGuard } from '../common/guards/daily-transaction-permission.guard';
import { DiaryCare247Guard } from '../common/guards/diary-care247-permission.guard';
import { BookingCskhRevenueQueryDto } from './dto/booking-cskh-revenue-query.dto';
import { Care247ExportExcelGuard } from '../common/guards/care247-export-excel.guard';
import { Care247ConstraintUserHistoryQueryDto } from './dto/care247-constraint-user-history-query.dto';
import { Care247CancelBookingGuard } from 'src/common/guards/care247-cancel-booking.guard';
import { CskhContactCustomersDto } from './dto/cskh-contact-customers.dto';
import { CskhTransactionGuard } from '../common/guards/cskh-transaction.guard';
import { CskhScheduleCategoriesDto } from './dto/cskh-schedule-categories.dto';
import { CskhSchedulersDto } from './dto/cskh-schedulers.dto';
import { MedproCareConfirmDto } from './dto/medpro-care-confirm.dto';
import { CskhSurveyCare247Dto } from './dto/cskh-survey-care247.dto';
import { RefundSmsTransactionGuard } from '../common/guards/refund-sms-transaction.guard';
import { ClearLogsGuard } from '../common/guards/clear-logs.guard';
import { AnKhangTransactionsQueryDto } from './dto/an-khang-transactions-query.dto';
import { AnKhangReportQueryDto } from './dto/an-khang-report-query.dto';
import { UploadHealthDataDto, UploadHealthDataResponseDto } from './dto/upload-health-data.dto';
import { AnKhangPermissionGuard } from 'src/common/guards/an-khang-permission.guard';

@Controller('cskh')
@ApiTags('CSKH')
// @UseGuards(CskhGuard)
@UseInterceptors(AppCskhActivitiesInterceptor)
export class CskhController {
    constructor(
        private readonly service: CskhService, 
        private readonly mailService: MailService
    ) {}

    @UseGuards(CskhGuard)
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.RESET_OTP })
    @Post('reset-otp-log')
    async resetOtp(@Req() req: any, @Body() body: ResetOtpLogDto) {
        const userId = req.user._id;
        return this.service.resetOtp(userId, body);
    }

    @UseGuards(CskhGuard)
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.ADD_PHONE_BLACKLIST_SMS })
    @Post('add-phone-blacklist-sms')
    async addPhoneBlacklistSms(@Body() body: ResetOtpLogDto) {
        return this.service.addPhoneBlacklistSms(body);
    }

    @UseGuards(CskhGuard)
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.REMOVE_PHONE_BLACKLIST_SMS })
    @Post('remove-phone-blacklist-sms')
    async removePhoneBlacklistSms(@Body() body: ResetOtpLogDto) {
        return this.service.removePhoneBlacklistSms(body);
    }

    @UseGuards(CskhGuard)
    @Get('phone-blacklist-sms')
    async getPhoneBlacklistSms() {
        return this.service.getPhoneBlacklistSms();
    }

    @UseGuards(CskhGuard)
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.ADD_BLOCKED_PHONE })
    @Post('add-blocked-phone')
    async addBlockedPhone(@Body() body: ResetOtpLogDto) {
        return this.service.addBlockedPhone(body);
    }

    @UseGuards(CskhGuard)
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.REMOVE_BLOCKED_PHONE })
    @Post('remove-blocked-phone')
    async removeBlockedPhone(@Body() body: ResetOtpLogDto) {
        return this.service.removeBlockedPhone(body);
    }

    @UseGuards(CskhGuard)
    @Get('blocked-phone')
    async getBlockedPhone() {
        return this.service.getBlockedPhone();
    }

    @UseGuards(CskhGuard)
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.UNLOCK_LOGIN })
    @Post('unlock-login')
    async unlockLogin(@Body() body: ResetOtpLogDto) {
        return this.service.unlockLogin(body);
    }

    @UseGuards(CskhGuard)
    @Get('repayment-log')
    async getRepaymentLog(@Query() query: any) {
        return this.service.getRepaymentLog(query);
    }

    @UseGuards(CskhGuard)
    @Put('repayment-log')
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.UPDATE_REPAYMENT_LOG })
    async updateRepaymentLog(@Body() body: any) {
        return this.service.updateRepaymentLog(body);
    }

    @Post('called-info')
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.CALLED_INFO })
    @UseGuards(CskhGuard)
    async getBookingInfoCSKH(@Body() formData: CalledInfoDto, @Req() req): Promise<any> {
        const user = req.user;
        return this.service.addCalledInfo(formData, user);
    }

    @UseGuards(CskhGuard)
    @Get('locked-booking')
    async getLockedBooking(@Query() query: any) {
        return this.service.getBookingLocked(query);
    }

    @UseGuards(CskhGuard)
    @Post('unlock-booking')
    @ActionData({ category: CategoryEnum.CHAM_SOC_KHACH_HANG, action: ActionEnum.UNLOCK_BOOKING })
    async unlockBooking(@Body() query: any) {
        return this.service.unlockBooking(query);
    }

    //Care247 module
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.SEARCH })
    @Post('bookings/medpro-care')
    async getBookingMedproCare(@Req() req: any, @Body() body: BookingCare247QueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCare(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, Care247ExportExcelGuard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.EXPORT_EXCEL_CARE247 })
    @Post('bookings/medpro-care/export-excel')
    async bookingMedproCareExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.bookingMedproCareExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }
    
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.SEARCH })
    @Post('bookings/medpro-care/cskh-revenue')
    async getBookingMedproCareCskhRevenue(@Req() req: any, @Body() body: BookingCare247CskhRevenueQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCareCskhRevenue(userMongoId, body);
    }
    
    @UseGuards(CskhGuard, AnKhangPermissionGuard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.AN_KHANG })
    @Post('an-khang/transactions')
    async getAnKhangTransactions(@Req() req: any, @Body() body: AnKhangTransactionsQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getAnKhangTransactions(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.EXPORT_ANKHANG })
    @Post('ankhang-report')
    async getAnKhangReport(@Req() req: any, @Body() body: AnKhangReportQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getAnKhangReport(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.EXPORT_EXCEL_ANKHANG })
    @Post('ankhang-report/export-excel')
    async anKhangReportExportExcel(@Req() req: any, @Body() formData: AnKhangReportQueryDto, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.anKhangReportExportExcel(userMongoId, formData);
        const name = 'ankhang-report-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.THU_NHAP_CARE247, action: ActionEnum.SEARCH })
    @Post('bookings/medpro-care/care247-revenue')
    async getBookingMedproCareCare247Revenue(@Req() req: any, @Body() body: BookingCare247CskhRevenueQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCareCare247Revenue(userMongoId, body);
    }
    
    @UseGuards(CskhGuard, Care247Guard, Care247CancelBookingGuard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.CANCEL_CARE247 })
    @Post('bookings/medpro-care/cancel')
    async cancelBookingMedproCare(@Req() req: any, @Body() body: MedproCareCancelDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.cancelBookingMedproCare(userMongoId, body);
    }
    
    @UseGuards(CskhGuard, Care247Guard, Care247RefundGuard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.REFUND_CARE247 })
    @Post('bookings/medpro-care/refund')
    async refundBookingMedproCare(@Req() req: any, @Body() body: MedproCareRefundDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.refundBookingMedproCare(userMongoId, body);
    }

    // @Post('booking-care247-success')
    // async insertBookingCare247Success() {
    //     return this.service.insertBookingCare247Success();
    // }
    
    @UseGuards(CskhGuard, Care247Guard)
    @Post('seed-booking-care247')
    async seedDataBookingCare247() {
        return this.service.seedDataBookingCare247();
    }

    @UseGuards(CskhGuard, Care247Guard)
    @Post('update-payment-care247')
    async updatePaymentCare247(): Promise<any> {
        return this.service.updatePaymentCare247();
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.CSKH_REVENUE_REPORT })
    @Post('bookings/medpro-care/cskh-revenue/report')
    async cskhRevenueReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.cskhRevenueReport(userMongoId, formData);
    }
    
    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.THU_NHAP_CARE247, action: ActionEnum.CARE247_REVENUE_REPORT })
    @Post('bookings/medpro-care/care247-revenue/report')
    async care247RevenueReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.care247RevenueReport(userMongoId, formData);
    }

    // @UseGuards(CskhCare247SalaryGuard)
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.THONG_KE, action: ActionEnum.STATISTIC_CARE247_BY_MONTH_IN_YEAR })
    @Post('bookings/medpro-care/annualBookingReport')
    async getAnnualBookingReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getAnnualBookingReport(userMongoId, formData);
    }

    // @UseGuards(CskhCare247SalaryGuard)
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.THONG_KE, action: ActionEnum.STATISTIC_CARE247_BY_DAY_IN_MONTH })
    @Post('bookings/medpro-care/bookingReport')
    async getMonthBookingReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getMonthBookingReport(userMongoId, formData);
    }

    // @UseGuards(CskhCare247SalaryGuard)
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.THONG_KE, action: ActionEnum.STATISTIC_CARE247_BY_EMPLOYEE_IN_MONTH })
    @Post('bookings/medpro-care/statisticsEmployeesByMotnh')
    async getStatisticsEmployeesByMotnh(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getStatisticsEmployeesByMotnh(userMongoId, formData);
    }
    
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.THONG_KE, action: ActionEnum.STATISTIC_CARE247_BY_CSKH_IN_MONTH })
    @Post('bookings/medpro-care/statisticsCskhByMotnh')
    async getStatisticsEmployeesCskhByMotnh(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getStatisticsEmployeesCskhByMotnh(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.EXPORT_EXCEL_CSKH_REVENUE })
    @Post('bookings/medpro-care/cskh-revenue/export-excel')
    async cskhRevenueExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.cskhRevenueExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }
    
    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.THU_NHAP_CARE247, action: ActionEnum.EXPORT_EXCEL_CARE247_REVENUE })
    @Post('bookings/medpro-care/care247-revenue/export-excel')
    async care247RevenueExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.care247RevenueExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }
    
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.PUSH_NOTIF, action: ActionEnum.PUSH_NOTIF_CSKH })
    @Post('push-notif-cskh')
    async pushNotifCskh(@Req() req: any, @Body() formData: CskhPushNotif): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.pushNotifCskh(userMongoId, formData);
    }

    // @UseGuards(CskhGuard)
    // @Post('setting-salary-care247')
    // async createSettingSalaryCare247() {
    //     return this.service.createSettingSalaryCare247();
    // }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.THU_NHAP_CARE247, action: ActionEnum.GET_SETTING_SALARY_CARE247 })
    @Get('get-setting-salary-care247')
    async getSettingSalaryCare247() {
        return this.service.getSettingSalaryCare247();
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.GET_LIST_USER })
    @Get('get-list-user')
    async getListUser(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getListUser(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.GET_LIST_USER_CSKH })
    @Get('get-list-user-cs')
    async getListUserCs(@Req() req: any, @Query('type') type: string) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getListUserCs(userMongoId, type);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.ADD_CARE247_EMPLOYEE })
    @Post('update-list-user-care247')
    async updateListUserCare247(@Req() req: any, @Body() formData: IUpdateEmployeeCare247Dto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateListUserCare247(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.REMOVE_CARE247_EMPLOYEE })
    @Post('remove-list-user-care247')
    async deleteListUserCare247(@Req() req: any, @Body() formData: IUpdateEmployeeCare247Dto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.removeListUserCare247(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.ADD_CSKH_EMPLOYEE })
    @Post('update-list-user-cskh')
    async updateListUserCskh(@Req() req: any, @Body() formData: IUpdateEmployeeCskhDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateListUserCskh(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.REMOVE_CSKH_EMPLOYEE })
    @Post('remove-list-user-cskh')
    async deleteListUserCskh(@Req() req: any, @Body() formData: IUpdateEmployeeCskhDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.removeListUserCskh(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.ADD_KUMHO_EMPLOYEE })
    @Post('update-list-user-kumho')
    async updateListUserKumho(@Req() req: any, @Body() formData: { id: string }) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateListUserKumho(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.REMOVE_KUMHO_EMPLOYEE })
    @Post('remove-list-user-kumho')
    async removeListUserKumho(@Req() req: any, @Body() formData: { id: string }) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.removeListUserKumho(userMongoId, formData);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.SEARCH })
    @Post('search')
    async searchListUserCare247(@Req() req: any, @Body() formData: SearchUserCskh) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.searchUserCskhByFullname(userMongoId, formData);
    }
    
    @Post('calculate-salary-care247-test')
    async calculateSalaryCare247(@Body() formData: { bookings: number, type: string }) {
        return this.service.calculateSalaryCare247(formData.bookings, formData.type);
    }

    @UseGuards(CskhGuard)
    @Post('notify-view-result-choray')
    async notifyViewExamResultChoray(@Body() formData: any): Promise<any> {
        return this.service.notifyViewExamResultChoray(formData);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhTransactionGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_GIAO_DICH, action: ActionEnum.SEARCH })
    @Post('transactions')
    async getTransactions(@Req() req: any, @Body() body: TransactionsQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getTransactions(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.ADS_CARE247, action: ActionEnum.GET_ADS_CARE247 })
    @Get('ads-care247')
    async getAdsCare247(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getAdsCare247(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard, CskhCare247SalaryGuard)
    @ActionData({ category: CategoryEnum.CAU_HINH_CARE247, action: ActionEnum.CONFIG_ADS_CARE247 })
    @Post('ads-care247')
    async updateAdsCare247(@Req() req: any, @Body() body: AdsCare247Dto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateAdsCare247(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, DiaryCare247Guard)
    @ActionData({ category: CategoryEnum.NHAT_KY_CARE247, action: ActionEnum.GET_CARE247_DIARY })
    @Post('care247-diary')
    async getCare247Diary(@Req() req: any, @Body() body: Care247DiaryQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247Diary(userMongoId, body);
    }
    
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.SETTING_CARE247, action: ActionEnum.UPDATE_MULTIPLE_KEY_GLOBAL_SETTING })
    @Post('update-multiple-key')
    async updateMultipleKeyGlobalSetting(@Req() req: any, @Body() body: UpdateMultipleKey) {
        throw new HttpException('chờ review sau nha. kaka', HttpStatus.BAD_REQUEST)
        // const user = req.user;
        // const { userMongoId } = user;
        // return this.service.updateMultipleKeyGlobalSetting(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.SETTING_CARE247, action: ActionEnum.GET_MULTIPLE_KEY_GLOBAL_SETTING })
    @Get('get-multiple-key')
    async getMultipleKeyGlobalSetting() {
        return this.service.getMultipleKeyGlobalSetting();
    }
    
    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.CARE247_SERVICES })
    @Post('care247-services')
    async getCare247Services(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247Services(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.CARE247_SERVICES_PARTNER })
    @Post('care247-services/partner')
    async getCare247ServicesPartner(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247ServicesPartner(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, DailyTransactionGuard)
    @ActionData({ category: CategoryEnum.GIAO_DICH_HANG_NGAY, action: ActionEnum.GET_DAILY_TRANSACTION_REPORT })
    @Get('daily-transaction-report')
    async getDailyTransactionReport(@Query() query: IDailyTransaction) {
        return this.service.getDailyTransactionReport(query);
    }
    @Post('send-test-mailgun')
    async sendMail(@Body() body: any) {
        console.log('sendMail', body);
        return await this.mailService.sendEmail(body);
    }

    @UseGuards(CskhGuard, Care247Guard, ExamResultGuard)
    @ActionData({ category: CategoryEnum.TRA_CUU_KET_QUA, action: ActionEnum.GET_NOTIF_TCKQ_CHORAY })
    @Post('noti-tckq-choray')
    async getNotifTckqChoray(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getNotifTckqChoray(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, ExamResultGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_THONG_BAO_CARE247, action: ActionEnum.GET_NOTIF_CARE247 })
    @Post('noti-care247')
    async getNotifCare247(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getNotifCare247(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, ExamResultGuard)
    @ActionData({ category: CategoryEnum.QUAN_LY_THONG_BAO_CARE247, action: ActionEnum.GET_REPORT_NOTIF_CARE247 })
    @Post('report-noti-care247')
    async getReportNotifCare247(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getReportNotifCare247(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, ExamResultGuard)
    @ActionData({ category: CategoryEnum.TRA_CUU_KET_QUA, action: ActionEnum.NOTI_TCKQ_CHORAY_REPORT })
    @Post('noti-tckq-choray/report')
    async reportNotifTckqChoray(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.reportNotifTckqChoray(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, ExamResultGuard)
    @ActionData({ category: CategoryEnum.TRA_CUU_KET_QUA, action: ActionEnum.SYNC_NOTIF_TCKQ_CHORAY })
    @Post('noti-tckq-choray/sync')
    async syncNotifTckqChoray(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.syncNotifTckqChoray(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.GET_CARE247_INTRODUCTION })
    @Get('care247-introduction')
    async getCare247Introduction(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247Introduction(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard, Care247IntroductionConfigGuard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.UPDATE_CARE247_INTRODUCTION })
    @Post('care247-introduction')
    async updateCare247Introduction(@Req() req: any, @Body() body: IUpdateCare247IntroductionDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateCare247Introduction(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, Care247IntroductionConfigGuard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.GET_CARE247_INTRODUCTION_CHANGES })
    @Get('care247-introduction-changes')
    async getCare247IntroductionChanges(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247IntroductionChanges(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.GET_CSKH_REVENUE })
    @Post('revenue-cskh-care247')
    async cskh(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCskhsRevenue(userMongoId);
    }
    

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.GET_BOOKING_CSKH_REVENUE })
    @Post('bookings/cskh-revenue')
    async getBookingCskhRevenue(@Req() req: any, @Body() body: BookingCskhRevenueQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingCskhRevenueV2(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.GET_BOOKING_CSKH_REVENUE })
    @Post('bookings/cskh-revenue-test')
    async getBookingCskhRevenueTest(@Req() req: any, @Body() body: BookingCskhRevenueQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingCskhRevenueV2(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard, Care247ExportExcelGuard)
    @ActionData({ category: CategoryEnum.DOANH_THU_CSKH, action: ActionEnum.EXPORT_EXCEL_CARE247 })
    @Post('bookings/cskh-revenue/export-excel')
    async bookingCskhRevenueExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.bookingCskhRevenueExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.KHACH_HANG_DUNG_LAI_CARE247, action: ActionEnum.GET_CARE247_USER_REBOOKING })
    @Post('bookings/care247-user-rebooking')
    async getCare247UserRebooking(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247UserRebooking(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.KHACH_HANG_DUNG_LAI_CARE247, action: ActionEnum.GET_CARE247_USER_REBOOKING_HISTORY })
    @Post('bookings/care247-user-rebooking/history')
    async getCare247UserRebookingHistory(@Req() req: any, @Body() body: Care247ConstraintUserHistoryQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247UserRebookingHistory(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.ADD_FLAG_BOOKING_CARE247 })
    @Post('bookings/care-247/add-flag')
    async addFlagBookingCare247(@Req() req: any, @Body() body: { id: string }) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.addFlagBookingCare247(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.REMOVE_FLAG_BOOKING_CARE247 })
    @Post('bookings/care-247/remove-flag')
    async removeFlagBookingCare247(@Req() req: any, @Body() body: { id: string }) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.removeFlagBookingCare247(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.FIND_BOOKING_BY_MEDPROCARE_ID })
    @Post('find-booking-by-medpro-care-id')
    async findBookingByMedproCareId(@Req() req: any,@Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.findBookingByMedproCareId(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.CREATE_CONTACT_CUSTOMERS })
    @ApiOperation({ summary: 'Tạo thông tin liên hệ khách hàng của nhân viên CSKH', description: 'Tạo thông tin liên hệ khách hàng của nhân viên CSKH' })
    @Post('cskh-contact-customers')
    async createContactCustomers(@Req() req: any,@Body() body: CskhContactCustomersDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.createContactCustomers(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.GET_CONTACT_CUSTOMERS })
    @ApiOperation({ summary: 'Lấy thông tin liên hệ khách hàng của nhân viên CSKH', description: 'Lấy thông tin liên hệ khách hàng của nhân viên CSKH' })
    @Get('cskh-contact-customers/:id')
    async getContactCustomers(@Req() req: any, @Param('id') id: string) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getContactCustomers(userMongoId, id);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.HISTORY_CONTACT_CUSTOMERS })
    @ApiOperation({ summary: 'Lịch sử liên hệ khách hàng của nhân viên CSKH', description: 'Lịch sử liên hệ khách hàng của nhân viên CSKH' })
    @Get('history-cskh-contact-customers/:id')
    async historyContactCustomers(@Req() req: any, @Param('id') id: string) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.historyContactCustomers(userMongoId, id);
    }

    @Post('test-time-care247')
    async testTimeCare247(@Body() body: any): Promise<any> {
        return this.service.testTimeCare247(body);
    }

    @Post('cskh-schedule-categories')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Tạo mới lịch trình CSKH' })
    async createCskhScheduleCategory(
        @Req() req: any,
        @Body() categoryDto: CskhScheduleCategoriesDto,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.createCskhScheduleCategory(userMongoId, categoryDto);
    }

    @Put('cskh-schedule-categories/:id')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Cập nhật lịch trình CSKH' })
    async updateCskhScheduleCategory(
        @Req() req: any,
        @Param('id') id: string,
        @Body() updateData: CskhScheduleCategoriesDto,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.updateCskhScheduleCategory(userMongoId, id, updateData);
    }

    @Get('cskh-schedule-categories')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Lấy tất cả lịch trình CSKH' })
    async getAllCategories(@Req() req: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.getAllCategories(userMongoId);
    }

    @Get('cskh-schedule-categories/:id')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Lấy lịch trình CSKH theo ID' })
    async getCategoryById(
        @Req() req: any,
        @Param('id') id: string,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.getCategoryById(userMongoId, id);
    }

    @Delete('cskh-schedule-categories/:id')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Xóa lịch trình CSKH theo ID' })
    async deleteCategoryById(
        @Req() req: any,
        @Param('id') id: string,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.deleteCategoryById(userMongoId, id);
    }

    @Post('cskh-schedulers')
    @UseGuards(CskhGuard, Care247Guard)
    async createScheduler(
        @Req() req: any,
        @Body() body: CskhSchedulersDto,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.createCskhScheduler(userMongoId, body);
    }

    @Put('cskh-schedulers/:id')
    @UseGuards(CskhGuard, Care247Guard)
    async updateScheduler(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: CskhSchedulersDto,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.updateCskhScheduler(userMongoId, id, body);
    }

    @Delete('cskh-schedulers/:id')
    @UseGuards(CskhGuard, Care247Guard)
    async deleteScheduler(@Req() req: any, @Param('id') id: string): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.deleteCskhScheduler(userMongoId, id);
    }

    @Get('cskh-schedulers')
    @UseGuards(CskhGuard, Care247Guard)
    async getSchedulers(@Req() req: any, @Query() query: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getCskhSchedulers(userMongoId, query);
    }

    @Get('cskh-schedulers-report')
    @UseGuards(CskhGuard, Care247Guard)
    async getSchedulersReport(@Req() req: any, @Query() query: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getCskhReport(userMongoId, query);
    }

    @Patch('cskh-schedulers/:id')
    @UseGuards(CskhGuard, Care247Guard)
    async updateLockedScheduler(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: { locked: boolean },
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.updateLockedScheduler(userMongoId, id, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    // @ActionData({ category: CategoryEnum.DICH_VU_CARE247, action: ActionEnum.ASSIGN_CARE247_EMPLOYEE })
    @Patch('medpro-care-confirm')
    medproCareInstructor(@Req() req, @Body() body: MedproCareConfirmDto): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return this.service.medproCareCskhConfirm(body, userId);
    }

    @Get('get-list-partner-care247')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Lấy danh sách partner triển khai care247' })
    async getPartnerCare247(
        @Req() req: any
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.getPartnerCare247(userMongoId);
    }

    @Post('call-feedback-survey')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Tạo mới khảo sát khách hàng' })
    async createSurvey(@Req() req, @Body() body: CskhSurveyCare247Dto) {
        const userMongoId = req.user.userMongoId;
        return await this.service.createCallFeedbackSurvey(userMongoId, body);
    }

    @Put('call-feedback-survey/:id')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Cập nhật khảo sát khách hàng' })
    async updateSurvey(
        @Req() req,
        @Param('id') surveyId: string,
        @Body() body: Partial<CskhSurveyCare247Dto>
    ) {
        const userId = req.user.userMongoId;
        return await this.service.updateCallFeedbackSurvey(userId, surveyId, body);
    }

    @Get('call-feedback-surveys/:id')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Lấy danh sách khảo sát khách hàng' })
    async getSurveys(
        @Req() req,
        @Param('id') bookingCare247: string,  // Param 'id' là bookingCare247
    ) {
        const userId = req.user.userMongoId;
        return await this.service.getCallFeedbackSurveys(userId, bookingCare247);
    }
    
    
    @Delete('call-feedback-survey/:id')
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Xóa khảo sát khách hàng' })
    async deleteSurvey(@Req() req, @Param('id') surveyId: string) {
        const userId = req.user.userMongoId;
        return await this.service.deleteCallFeedbackSurvey(userId, surveyId);
    }
        
    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Lấy danh sách khách hàng quan tâm & đặt lần đầu care247' })
    @Post('bookings/medpro-care/sales')
    async getBookingMedproCareSales(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCareSales(userMongoId, body);
    }

    @Post('test-time-schedulers-cskh')
    async testTimeSchedulersCskh(@Body() body: any): Promise<any> {
        return this.service.testTimeSchedulersCskh(body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    // @ActionData({ category: CategoryEnum.KHACH_HANG_DUNG_LAI_CARE247, action: ActionEnum.GET_CARE247_USER_REBOOKING })
    @Post('bookings/retry-payment-bookings')
    async getRetryPaymentBooking(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getRetryPaymentBooking(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ApiOperation({ summary: 'Cập nhật trạng thái phiếu khám Chợ Rẫy' })
    @Post('bookings/update-status-choray')
    async updateStatusBookingChoray(@Req() req: any, @Body() body: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateStatusBookingChoray(userMongoId, body);
    }

    @Post('call-bot/create')
    async callBotCskh(
        @Headers('appid') appid: string,
        @Headers('secret') secret: string, 
        @Body() body: any): Promise<any> {
        return this.service.callBotCskh(appid, secret, body);
    }

    @Post('call-bot/get')
    @UseGuards(CskhGuard)
    async getCallBotCskhByPhone(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getCallBotCskhByPhone(userMongoId, body);
    }

    @Post('bookings/ignore-convert')
    @UseGuards(CskhGuard)
    async getBookingIgnoreConvert(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getBookingIgnoreConvert(userMongoId, body);
    }

    @Post('users-convert')
    @UseGuards(CskhGuard)
    async getUserConvertCskh(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getUserConvertCskh(userMongoId, body);
    }

    @Post('reserve-booking-logs')
    @UseGuards(CskhGuard)
    async getReserverBookingLogs(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getReserverBookingLogs(userMongoId, body);
    }

    @Post('reserve-booking-logs/clear')
    @UseGuards(CskhGuard, ClearLogsGuard)
    async clearReserverBookingLogs(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.clearReserverBookingLogs(userMongoId, body);
    }

    @Post('refund-transaction-sms/search')
    @UseGuards(CskhGuard, Care247Guard, RefundSmsTransactionGuard)
    async searchRefundTransaction(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.searchRefundTransaction(userMongoId, body);
    }

    @Post('refund-transaction-sms')
    @UseGuards(CskhGuard, Care247Guard, RefundSmsTransactionGuard)
    async getRefundTransactionSms(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.getRefundTransactionSms(userMongoId, body);
    }

    @Post('refund-transaction-sms/insert')
    @UseGuards(CskhGuard, Care247Guard, RefundSmsTransactionGuard)
    async insertRefundTransactionSms(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.insertRefundTransactionSms(userMongoId, body);
    }

    @Post('refund-transaction-sms/send-sms')
    @UseGuards(CskhGuard, Care247Guard, RefundSmsTransactionGuard)
    async sendRefundTransactionSms(@Req() req: any, @Body() body: any): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.sendRefundTransactionSms(userMongoId, body);
    }

    @Put('refund-transaction-sms/:id')
    @UseGuards(CskhGuard, Care247Guard, RefundSmsTransactionGuard)
    async updateRefundTransactionSms(
        @Req() req: any, 
        @Param('id') id: string,
        @Body() body: any
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return await this.service.updateRefundTransactionSms(userMongoId, id, body);
    }

    @Delete('refund-transaction-sms/:id')
    @UseGuards(CskhGuard, Care247Guard, RefundSmsTransactionGuard)
    async deleteRefundTransactionSms(
        @Req() req: any,
        @Param('id') id: string,
    ): Promise<any> {
        const userMongoId = req.user.userMongoId;
        return this.service.deleteRefundTransactionSms(userMongoId, id);
    }

    @UseGuards(CskhGuard, Care247Guard, Care247ExportExcelGuard)
    @Post('hospital/export-excel')
    async exportExcelHospital(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.exportExcelHospital(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }

    @Post('seed-job-update-kpi')
    async seedUpdateKpiCskhPayment(@Body() body: any): Promise<any> {
        return this.service.seedUpdateKpiCskhPayment(body);
    }

    @UseGuards(CskhGuard)
    @Post('care247-independent/create')
    async createCare247PaymentIndependent(@Body() body: { bookingId: string }): Promise<any> {
        return this.service.createCare247PaymentIndependent(body);
    }

    @UseGuards(CskhGuard)
    @Post('care247-independent/sms')
    async sendSmsCare247PaymentIndependent(@Req() req: any, @Body() body: { token: string, mobile: string }): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.sendSmsCare247PaymentIndependent(body, userMongoId);
    }

    @Post('care247-independent')
    async getCare247PaymentIndependent(@Body() body: { token: string }): Promise<any> {
        return this.service.getCare247PaymentIndependent(body);
    }

    @Post('care247-independent/create-payment')
    async createPaymentCare247PaymentIndependent(@Body() body: { token: string }): Promise<any> {
        return this.service.createPaymentCare247PaymentIndependent(body);
    }

    @Post('patient-files/health-data')
    @ApiOperation({ summary: 'Upload patient health data Excel file' })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        description: 'Upload Excel file containing patient health data',
        type: UploadHealthDataDto,
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadHealthData(
        @UploadedFile() file: Express.Multer.File,
        @Res() res,
    ): Promise<any> {
        if (!file) {
            throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
        }

        // Validate file type
        const allowedMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
        ];

        if (!allowedMimeTypes.includes(file.mimetype)) {
            throw new HttpException(
                'Invalid file type. Only Excel files (.xlsx, .xls) are allowed',
                HttpStatus.BAD_REQUEST,
            );
        }

        try {
            const result = await this.service.uploadHealthData(file);

            // If result contains error file (Excel), send it as download
            if (result.errorFile) {
                res.setHeader('Content-Type', result.errorFile.contentType);
                res.setHeader('Content-Disposition', `attachment; filename=${result.errorFile.filename}`);
                res.setHeader('Content-Length', result.errorFile.buffer.length);
                return res.send(result.errorFile.buffer);
            }

            // Otherwise, return JSON response
            return res.json(result);
        } catch (error) {
            throw error;
        }
    }

    @Get('patient-files/pending-data')
    @UseGuards(CskhGuard)
    @ApiOperation({ summary: 'Lấy danh sách dữ liệu sức khỏe đang chờ liên kết' })
    async getPendingHealthData(@Req() req): Promise<any> {
        return this.service.getPendingHealthData();
    }

    @Post('import-employee-excel')
    @UseGuards(CskhGuard)
    @UseInterceptors(FileInterceptor('file'))
    @ApiOperation({
        summary: 'Import danh sách nhân viên từ file Excel',
        description: 'Import danh sách nhân viên từ file Excel với format: STI | MÃ SIÊU THỊ | TÊN SIÊU THỊ | MÃ NHÂN VIÊN | TÊN NHÂN VIÊN | CHỨC VỤ | SĐT. Kiểm tra đăng ký Medpro và thêm user ID vào AN_KHANG_CSKH_USER'
    })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                    description: 'File Excel với format: A=STI, B=MÃ SIÊU THỊ, C=TÊN SIÊU THỊ, D=MÃ NHÂN VIÊN, E=TÊN NHÂN VIÊN, F=CHỨC VỤ, G=SĐT'
                }
            },
            required: ['file']
        }
    })
    async importEmployeeExcel(
        @UploadedFile() file: Express.Multer.File,
        @Req() req: any,
        @Res() res: any
    ): Promise<any> {
        if (!file) {
            throw new HttpException('Vui lòng chọn file Excel', HttpStatus.BAD_REQUEST);
        }

        // Validate file type
        const allowedMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
        ];

        if (!allowedMimeTypes.includes(file.mimetype)) {
            throw new HttpException(
                'File không đúng định dạng. Chỉ chấp nhận file Excel (.xlsx, .xls)',
                HttpStatus.BAD_REQUEST,
            );
        }

        try {
            // Process import directly from buffer
            const result = await this.service.importEmployeeFromExcelBuffer({
                fileBuffer: file.buffer,
                originalName: file.originalname
            });

            // Trả về JSON response với thông tin import
            return res.json(result);

        } catch (error) {
            console.error('Error in import employee Excel:', error);
            throw new HttpException(
                error?.message || 'Lỗi khi import file Excel',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('an-khang-users')
    @ApiOperation({
        summary: 'Lấy danh sách AN_KHANG_CSKH_USER',
        description: 'API để lấy danh sách user ID trong AN_KHANG_CSKH_USER từ global setting'
    })
    @UseGuards(CskhGuard)
    async getAnKhangUsers(): Promise<any> {
        try {
            const result = await this.service.getAnKhangUsersList();
            return {
                success: true,
                message: 'Lấy danh sách AN_KHANG_CSKH_USER thành công',
                data: result
            };
        } catch (error) {
            console.error('Error in getAnKhangUsers:', error);
            throw new HttpException(
                error?.message || 'Lỗi khi lấy danh sách AN_KHANG_CSKH_USER',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('an-khang-users/add')
    @ApiOperation({
        summary: 'Thêm user vào danh sách AN_KHANG_CSKH_USER',
        description: 'API để thêm một user ID vào danh sách AN_KHANG_CSKH_USER'
    })
    @UseGuards(CskhGuard)
    async addAnKhangUser(@Body() body: { userId: string }): Promise<any> {
        try {
            const { userId } = body;
            if (!userId) {
                throw new HttpException('userId là bắt buộc', HttpStatus.BAD_REQUEST);
            }

            const result = await this.service.addUserToAnKhangList(userId);
            return {
                success: true,
                message: 'Thêm user vào danh sách AN_KHANG_CSKH_USER thành công',
                data: result
            };
        } catch (error) {
            console.error('Error in addAnKhangUser:', error);
            throw new HttpException(
                error?.message || 'Lỗi khi thêm user vào danh sách AN_KHANG_CSKH_USER',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Delete('an-khang-users/remove')
    @ApiOperation({
        summary: 'Xóa user khỏi danh sách AN_KHANG_CSKH_USER',
        description: 'API để xóa một user ID khỏi danh sách AN_KHANG_CSKH_USER'
    })
    @UseGuards(CskhGuard)
    async removeAnKhangUser(@Body() body: { userId: string }): Promise<any> {
        try {
            const { userId } = body;
            if (!userId) {
                throw new HttpException('userId là bắt buộc', HttpStatus.BAD_REQUEST);
            }

            const result = await this.service.removeUserFromAnKhangList(userId);
            return {
                success: true,
                message: 'Xóa user khỏi danh sách AN_KHANG_CSKH_USER thành công',
                data: result
            };
        } catch (error) {
            console.error('Error in removeAnKhangUser:', error);
            throw new HttpException(
                error?.message || 'Lỗi khi xóa user khỏi danh sách AN_KHANG_CSKH_USER',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
