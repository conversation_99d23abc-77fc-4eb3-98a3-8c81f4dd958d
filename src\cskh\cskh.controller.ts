import { Body, Controller, Get, Post, Put, Query, Req, Res, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CskhService } from './cskh.service';
import { CskhGuard } from '../common/guards/cskh.guard';
import { ResetOtpLogDto } from './dto/reset-otp-log.dto';
import { CalledInfoDto } from './dto/called-info.dto';
import { BookingCare247QueryDto } from './dto/booking-care247-query.dto';
import { BookingCare247CskhRevenueQueryDto } from './dto/booking-care247-cskh-revenue-query.dto';
import { MedproCareCancelDto } from './dto/medpro-care-cancel.dto';
import { CskhPushNotif } from './dto/cskh-push-notif.dto';

@Controller('cskh')
@ApiTags('CSKH')
@UseGuards(CskhGuard)
export class CskhController {
    constructor(private readonly service: CskhService) {}

    @UseGuards(CskhGuard)
    @Post('reset-otp-log')
    async resetOtp(@Req() req: any, @Body() body: ResetOtpLogDto) {
        const userId = req.user._id;
        return this.service.resetOtp(userId, body);
    }

    @UseGuards(CskhGuard)
    @Post('add-phone-blacklist-sms')
    async addPhoneBlacklistSms(@Body() body: ResetOtpLogDto) {
        return this.service.addPhoneBlacklistSms(body);
    }

    @UseGuards(CskhGuard)
    @Post('remove-phone-blacklist-sms')
    async removePhoneBlacklistSms(@Body() body: ResetOtpLogDto) {
        return this.service.removePhoneBlacklistSms(body);
    }

    @UseGuards(CskhGuard)
    @Get('phone-blacklist-sms')
    async getPhoneBlacklistSms() {
        return this.service.getPhoneBlacklistSms();
    }

    @UseGuards(CskhGuard)
    @Post('add-blocked-phone')
    async addBlockedPhone(@Body() body: ResetOtpLogDto) {
        return this.service.addBlockedPhone(body);
    }

    @UseGuards(CskhGuard)
    @Post('remove-blocked-phone')
    async removeBlockedPhone(@Body() body: ResetOtpLogDto) {
        return this.service.removeBlockedPhone(body);
    }

    @UseGuards(CskhGuard)
    @Get('blocked-phone')
    async getBlockedPhone() {
        return this.service.getBlockedPhone();
    }

    @Get('repayment-log')
    async getRepaymentLog(@Query() query: any) {
        return this.service.getRepaymentLog(query);
    }

    @Put('repayment-log')
    async updateRepaymentLog(@Body() body: any) {
        return this.service.updateRepaymentLog(body);
    }

    @Post('called-info')
    @UseGuards(CskhGuard)
    async getBookingInfoCSKH(@Body() formData: CalledInfoDto, @Req() req): Promise<any> {
        const user = req.user;
        return this.service.addCalledInfo(formData, user);
    }

    @Get('locked-booking')
    async getLockedBooking(@Query() query: any) {
        return this.service.getBookingLocked(query);
    }

    @Post('unlock-booking')
    async unlockBooking(@Body() query: any) {
        return this.service.unlockBooking(query);
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care')
    async getBookingMedproCare(@Req() req: any, @Body() body: BookingCare247QueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCare(userMongoId, body);
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/export-excel')
    async bookingMedproCareExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.bookingMedproCareExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }
    
    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/cskh-revenue')
    async getBookingMedproCareCskhRevenue(@Req() req: any, @Body() body: BookingCare247CskhRevenueQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCareCskhRevenue(userMongoId, body);
    }
    
    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/care247-revenue')
    async getBookingMedproCareCare247Revenue(@Req() req: any, @Body() body: BookingCare247CskhRevenueQueryDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingMedproCareCare247Revenue(userMongoId, body);
    }
    
    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/cancel')
    async cancelBookingMedproCare(@Req() req: any, @Body() body: MedproCareCancelDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.cancelBookingMedproCare(userMongoId, body);
    }

    // @Post('booking-care247-success')
    // async insertBookingCare247Success() {
    //     return this.service.insertBookingCare247Success();
    // }
    
    @Post('seed-booking-care247')
    async seedDataBookingCare247() {
        return this.service.seedDataBookingCare247();
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/cskh-revenue/report')
    async cskhRevenueReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.cskhRevenueReport(userMongoId, formData);
    }
    
    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/care247-revenue/report')
    async care247RevenueReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.care247RevenueReport(userMongoId, formData);
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/annualBookingReport')
    async getAnnualBookingReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getAnnualBookingReport(userMongoId, formData);
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/bookingReport')
    async getMonthBookingReport(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getMonthBookingReport(userMongoId, formData);
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/statisticsEmployeesByMotnh')
    async getStatisticsEmployeesByMotnh(@Req() req: any, @Body() formData: any): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getStatisticsEmployeesByMotnh(userMongoId, formData);
    }

    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/cskh-revenue/export-excel')
    async cskhRevenueExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.cskhRevenueExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }
    
    @UseGuards(CskhGuard)
    @Post('bookings/medpro-care/care247-revenue/export-excel')
    async care247RevenueExportExcel(@Req() req: any, @Body() formData: any, @Res() res): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        const stream: Buffer = await this.service.care247RevenueExportExcel(userMongoId, formData);
        const name = 'export-file-excel';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${name}.xlsx`);
        res.setHeader('Content-Length', stream.length);
        res.send(stream);
    }
    
    @UseGuards(CskhGuard)
    @Post('push-notif-cskh')
    async pushNotifCskh(@Req() req: any, @Body() formData: CskhPushNotif): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.pushNotifCskh(userMongoId, formData);
    }
}
