import { IBooking } from "src/booking-gateway/intefaces/booking.inteface";
import { IHospital } from "src/hospital-mongo/interfaces/hospital.interface";
import { IPatient } from "src/patient-mongo/intefaces/patient.inteface";
import { IUser } from "src/user/interfaces/user.interface";

export type BookingRelation = Omit<IBooking, "patient"> & {
    partner: Pick<IHospital, "name" | "partnerId">;
    patient: Pick<IPatient, "name" | "surname" | "sex">
    user: IUser
}