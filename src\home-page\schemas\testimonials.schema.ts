import * as mongoose from 'mongoose';
import { TESTIMONIALS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const TestimonialsSchema = new Schema(
    {
        repo: {
            type: String,
            required: true,
        },
        status: {
            type: Boolean,
            default: false,
        },
        name: { type: String, required: true },
        content: { type: String, required: true },
        avatar: { type: String, required: true },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: TESTIMONIALS_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
