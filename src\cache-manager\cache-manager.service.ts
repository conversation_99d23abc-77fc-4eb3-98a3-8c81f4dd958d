import { CACHE_MANAGER, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { Cache, CachingConfig } from 'cache-manager';
import { SECOND } from './cache-manager.constant';
import { ConfigCacheManagerService } from '../config/config.cache-manager.service';
import { REPO_NAME_BETA } from '../common/constants';
import { ConfigRepoService } from '../config/config.repo.service';

@Injectable()
export class CacheManagerService {
    DFATULT_TTL: number = 5 * 60;
    repoName: string;

    constructor(
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configRedisService: ConfigCacheManagerService,
        private configRepoService: ConfigRepoService,
    ) {
        this.DFATULT_TTL = configRedisService.getCacheTtlDefault();
        this.repoName = this.configRepoService.getRepoName();
    }

    async get(key: string): Promise<any> {
        if (REPO_NAME_BETA.includes(this.repoName)) {
            return null;
        }

        return await this.cacheManager.get(key);
    }

    async set(key: string, value: any, options?: CachingConfig): Promise<void> {
        if (REPO_NAME_BETA.includes(this.repoName)) {
            return;
        }

        await this.cacheManager.set(key, value, options || { ttl: this.DFATULT_TTL });
    }

    async delByPattern(pattern: string | RegExp): Promise<any> {
        let regex = new RegExp(pattern);

        const keys = await this.cacheManager.store.keys();

        try {
            return keys.filter((key) => key.match(regex)).map((key) => this.cacheManager.del(key));
        } catch (err) {
            console.log(`error del key by pattern ${pattern}: `, err);
            throw err;
        }
    }

    async findByKeyAndDelete(cache: string, type?: string): Promise<any> {
        try {
            if (!cache) {
                throw new HttpException(`Cache không được truyền vào`, HttpStatus.BAD_REQUEST);
            }
            if (type === 'pattern') {
                const matchingKeys = await this.cacheManager.store.keys();
                const keys = matchingKeys.filter(key => key.includes(cache));
                if(keys.length === 0) {
                    throw new HttpException(`Cache không tồn tại`, HttpStatus.BAD_REQUEST);
                }
                await this.delByPattern(cache);
                return {
                    message: `Đã xóa ${keys.length} cache với pattern ${cache}`,
                    keys: keys,
                };
            } else {
                const findKey = await this.cacheManager.get(cache);
                if (!findKey) {
                    throw new HttpException(`Cache không tồn tại !`, HttpStatus.BAD_REQUEST);
                }
                await this.cacheManager.del(cache);
                return {
                    message: `Đã xóa cache ${cache}`,
                    key: cache,
                };
            }
        } catch (error) {
            console.log(error);
            throw new HttpException(`${error?.message}` || 'Lỗi xóa cache!', error?.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
