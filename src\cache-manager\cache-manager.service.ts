import { CACHE_MANAGER, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { Cache, CachingConfig } from 'cache-manager';
import { SECOND } from './cache-manager.constant';
import { ConfigCacheManagerService } from '../config/config.cache-manager.service';
import { REPO_NAME_BETA } from '../common/constants';
import { ConfigRepoService } from '../config/config.repo.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { CreateCacheDto } from './dto/createCache.dto';

@Injectable()
export class CacheManagerService {
    DFATULT_TTL: number = 5 * 60;
    repoName: string;

    constructor(
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configRedisService: ConfigCacheManagerService,
        private configRepoService: ConfigRepoService,
        private globalSettingService: GlobalSettingService
    ) {
        this.DFATULT_TTL = configRedisService.getCacheTtlDefault();
        this.repoName = this.configRepoService.getRepoName();
    }

    async get(key: string): Promise<any> {
        if (REPO_NAME_BETA.includes(this.repoName)) {
            return null;
        }

        return await this.cacheManager.get(key);
    }

    async set(key: string, value: any, options?: CachingConfig): Promise<void> {
        if (REPO_NAME_BETA.includes(this.repoName)) {
            return;
        }

        await this.cacheManager.set(key, value, options || { ttl: this.DFATULT_TTL });
    }

    async delByPattern(pattern: string | RegExp): Promise<any> {
        let regex = new RegExp(pattern);

        const keys = await this.cacheManager.store.keys();

        try {
            return keys.filter((key) => key.match(regex)).map((key) => this.cacheManager.del(key));
        } catch (err) {
            console.log(`error del key by pattern ${pattern}: `, err);
            throw err;
        }
    }

    async getListCacheGlobal(): Promise<any> {
        try {
            const listCache = await this.globalSettingService.findByKeyAndRepoName('CACHE_MANAGER_CONFIG');
            return listCache ? JSON.parse(listCache) : {};
        } catch (error) {
            console.log(error)
            return {}
        }
    }

    async findByKeyAndDelete(cache: string, type?: string): Promise<any> {
        try {
            if (!cache) {
                throw new HttpException(`Cache không được truyền vào`, HttpStatus.BAD_REQUEST);
            }
            if (type === 'pattern') {
                const matchingKeys = await this.cacheManager.store.keys();
                const keys = matchingKeys.filter(key => key.includes(cache));
                if(keys.length === 0) {
                    throw new HttpException(`Cache không tồn tại`, HttpStatus.BAD_REQUEST);
                }
                await this.delByPattern(cache);
                return {
                    message: `Đã xóa ${keys.length} cache với pattern ${cache}`,
                    keys: keys,
                };
            } else {
                const findKey = await this.cacheManager.get(cache);
                if (!findKey) {
                    throw new HttpException(`Cache không tồn tại !`, HttpStatus.BAD_REQUEST);
                }
                await this.cacheManager.del(cache);
                return {
                    message: `Đã xóa cache ${cache}`,
                    key: cache,
                };
            }
        } catch (error) {
            console.log(error);
            throw new HttpException(`${error?.message}` || 'Lỗi xóa cache!', error?.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

  async createByCache(body: CreateCacheDto): Promise<any> {
      try {
          let cacheArray: string[];
          if (typeof body?.cache === 'string') {
              cacheArray = body.cache
                  .split(',')
                  .map(item => item.trim())
                  .filter(item => item);
          } else if (Array.isArray(body.cache)) {
              cacheArray = body.cache;
          } else {
              throw new HttpException('Field cache must be a string or array', HttpStatus.BAD_REQUEST);
          }

          // Chuẩn bị dữ liệu mới
          const newCacheData = {
              title: body.title,
              page: body.page,
              key: body.key,
              method: body.method,
              api: body.api,
              cache: cacheArray,
          };

          const listCacheResponseObj = await this.globalSettingService.findByKeyAndRepoName('CACHE_MANAGER_CONFIG');

          let listCacheResponse = [];

          if (listCacheResponseObj) {
              listCacheResponse = JSON.parse(listCacheResponseObj);
          }

          if (!listCacheResponse || !Array.isArray(listCacheResponse)) {
              throw new HttpException('Không thể lấy danh sách cache', HttpStatus.INTERNAL_SERVER_ERROR);
          }

          let listCache: any[] = listCacheResponse;

          const isDuplicate = listCache.some(cache => cache?.key === newCacheData?.key);
          if (isDuplicate) {
              throw new HttpException(`Cache với key ${newCacheData?.key} đã tồn tại!`, HttpStatus.BAD_REQUEST);
          }

          listCache.push(newCacheData);

          const dataUpdate = JSON.stringify(listCache);
          await this.globalSettingService.updateByKeyAndRepoName({ key: 'CACHE_MANAGER_CONFIG' }, dataUpdate);

          return {
              status: 'success',
              message: 'Tạo cache thành công',
              data: newCacheData,
          };
      } catch (error) {
          console.log('error', error);
          throw new HttpException(error.message || 'Lỗi tạo cache!', HttpStatus.INTERNAL_SERVER_ERROR);
      }
  }
  async updateByCache(body: CreateCacheDto): Promise<any> {
      try {
          // Lấy danh sách cache hiện có từ globalSettingService
          const listCacheResponseObj = await this.globalSettingService.findByKeyAndRepoName('CACHE_MANAGER_CONFIG');
          let listCacheResponse = [];
          if (listCacheResponseObj) {
              listCacheResponse = JSON.parse(listCacheResponseObj);
          }

          if (!listCacheResponse || !Array.isArray(listCacheResponse)) {
              throw new HttpException('Không thể lấy danh sách cache', HttpStatus.INTERNAL_SERVER_ERROR);
          }

          let listCache: any[] = listCacheResponse;

          const cacheIndex = listCache.find(cache => cache?.key === body.key);
          if (cacheIndex === -1) {
              throw new HttpException(`Cache với key ${body.key} không tồn tại!`, HttpStatus.NOT_FOUND);
          }

          let cacheArray: string[] | undefined;
          if (typeof body?.cache === 'string') {
              cacheArray = body.cache
                  .split(',')
                  .map(item => item.trim())
                  .filter(item => item);
          } else if (Array.isArray(body.cache)) {
              cacheArray = body.cache;
          }

          // Cập nhật dữ liệu cache
          const updatedCacheData: CreateCacheDto = {
              title: body.title ?? listCache[cacheIndex].title,
              page: body.page ?? listCache[cacheIndex].page,
              key: body.key,
              method: body.method ?? listCache[cacheIndex].method,
              api: body.api ?? listCache[cacheIndex].api,
              cache: cacheArray ?? listCache[cacheIndex].cache,
          };

          listCache[cacheIndex] = updatedCacheData;

          const dataUpdate = JSON.stringify(listCache);

          await this.globalSettingService.updateByKeyAndRepoName({ key: 'CACHE_MANAGER_CONFIG' }, dataUpdate);

          return {
              status: 'success',
              message: 'Cập nhật cache thành công',
              data: updatedCacheData,
          };
      } catch (error) {
          console.log('error', error);
          throw new HttpException(error.message || 'Lỗi cập nhật cache!', HttpStatus.INTERNAL_SERVER_ERROR);
      }
  }
  async deleteByCacheGlobal(key:string): Promise<{ status: string; message: string }> {
    try {
      // Lấy danh sách cache hiện có từ globalSettingService
      const listCacheResponseObj = await this.globalSettingService.findByKeyAndRepoName('CACHE_MANAGER_CONFIG');
      let listCacheResponse = [];
      if (listCacheResponseObj) {
        listCacheResponse = JSON.parse(listCacheResponseObj);
      }

      if (!listCacheResponse || !Array.isArray(listCacheResponse)) {
        throw new HttpException('Không thể lấy danh sách cache', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      let listCache: any[] = listCacheResponse;

      // Tìm cache cần xóa dựa trên key
      const cacheIndex = listCache.findIndex(cache => cache?.key === key);
      if (cacheIndex === -1) {
        throw new HttpException(`Cache với key ${key} không tồn tại!`, HttpStatus.NOT_FOUND);
      }

      // Xóa cache khỏi danh sách
      listCache.splice(cacheIndex, 1);

      // Chuyển đổi danh sách cache thành chuỗi JSON để lưu
      const dataUpdate = JSON.stringify(listCache);

      // Lưu danh sách cache đã cập nhật
      await this.globalSettingService.updateByKeyAndRepoName(
        { key: 'CACHE_MANAGER_CONFIG' },
        dataUpdate
      );

      return {
        status: 'success',
        message: 'Xóa cache thành công'
      };
    } catch (error) {
      console.log('error', error);
      throw new HttpException(
        error.message || 'Lỗi xóa cache!',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
