import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { RESERVE_BOOKING_LOGS_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PATIENT_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const ReserveBookingLogsSchema = new Schema({
    uuid: { type: String, required: true, unique: true },
    userId: { type: String, required: true },
    user: { type: mongoose.Schema.Types.ObjectId, ref: USER_COLLECTION_NAME, required: true },
    medproId: { type: String, required: true, index: true },
    action: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
    partnerId: { type: String, required: false },
    appId: { type: String, required: false },
    bookingData: { type: Object, default: {} },
    cskhInfo: { type: Object, default: {} },
    url: { type: String, required: false },
    method: { type: String, required: false },
    headers: { type: Object, required: false },
    params: { type: Object, required: false },
    body: { type: Object, required: false },
    result: { type: Object, required: false },
    error: { type: Object, required: false },
    process: { type: [Schema.Types.Mixed], required: false },
    query: { type: Object, required: false },
    nameRepo: { type: String, required: false },
    amount: { type: Number, required: false },
    bookingSlotId: { type: String, required: false },
    doctorId: { type: String, required: false },
    endTime: { type: Date, required: false },
    filterCheckData: { type: Array, default: [] },
    groupId: { type: Number, required: false },
    hasInsuranceCode: { type: Boolean, required: false },
    insuranceChoice: { type: String, required: false },
    insuranceCode: { type: String, required: false },
    maxSlot: { type: Number, required: false },
    methodId: { type: String, required: false },
    patientId: { type: String, required: false },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    paymentTypeDetail: { type: String, required: false },
    platform: { type: String, required: false },
    redirectUrl: { type: String, required: false },
    serviceId: { type: String, required: false },
    startTime: { type: Date, required: false },
    startTimeString: { type: String, required: false },
    subTotal: { type: Number, required: false },
    subjectId: { type: String, required: false },
    totalFee: { type: Number, required: false },
    treeId: { type: String, required: false },
    roomId: { type: String, required: false },
    sectionId: { type: String, required: false },
    type: { type: String, required: true },
    insuranceId: { type: String },
    status: { type: Number, default: 0 },
}, {
    collection: RESERVE_BOOKING_LOGS_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
