import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class MedproCareRefundDto {
    @ApiProperty({ description: 'id care247', required: true })
    @IsNotEmpty({ message: 'id is required' })
    id: string;
    
    @ApiProperty({ description: 'note hoan tien care247', required: true })
    @IsNotEmpty({ message: 'refundNote is required' })
    refundNote: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: true,
        type: String,
    })
    @IsOptional()
    // @IsNotEmpty({ message: 'refundCare247Date is required' })
    refundCare247Date?: string;
}
