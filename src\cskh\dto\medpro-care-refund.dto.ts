import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class MedproCareRefundDto {
    @ApiProperty({ description: 'id care247', required: true })
    @IsNotEmpty({ message: 'id is required' })
    id: string;
    
    @ApiProperty({ description: 'note hoan tien care247', required: true })
    @IsNotEmpty({ message: 'refundNote is required' })
    refundNote: string;
}
