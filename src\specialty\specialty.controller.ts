import { CreateSpecialtyDto } from './dto/create-specialty.dto';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SpecialtyService } from './specialty.service';

@Controller('specialty')
@ApiTags('specialty - Q<PERSON>ản lý chuyên khoa')
export class SpecialtyController {
    constructor(
        private readonly specialtyService: SpecialtyService,
    ) { }

    @Get()
    async getAllSpecialty(): Promise<any> {
        return await this.specialtyService.getAllSpecialty();
    }

    @Post()
    async createSpecialty(@Body() createSpecialtyDto: CreateSpecialtyDto): Promise<any> {
        return await this.specialtyService.createSpecialty(createSpecialtyDto);
    }

    @Get('sync-portal')
    async syncSpecialtyPortal(): Promise<any> {
        return await this.specialtyService.syncSpecialtyPortal();
    }
}
