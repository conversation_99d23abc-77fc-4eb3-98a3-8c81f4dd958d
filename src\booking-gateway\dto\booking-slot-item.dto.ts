
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose } from 'class-transformer';
import { IsNotEmpty, IsDateString, ValidateIf } from 'class-validator';

export class BookingSlotItemDTO {

    @ApiProperty({
        description: 'Id Dịch vụ',
        required: false,
        type: String,
        default: 'service0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.serviceId)
    serviceId: string;

    @ApiProperty({
        description: 'Id chuyên khoa',
        required: false,
        type: String,
        default: 'subject0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.subjectId)
    subjectId: string;

    @ApiProperty({
        description: 'Id phòng khám',
        required: false,
        type: String,
        default: 'room001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.roomId)
    roomId: string;

    @ApiProperty({
        description: 'Id Bác sĩ',
        required: false,
        type: String,
        default: 'doctor0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.doctorId)
    doctorId: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.startTimeString)
    startTimeString?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.startTime)
    startTime?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.endTime)
    endTime?: string;

    @ApiProperty({
        description: 'Booking Slot Id',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    bookingSlotId?: string;

    @Expose()
    @ApiProperty({
        description: 'availableSlot',
        required: false,
        type: Number,
    })
    availableSlot: number;

    @ApiProperty({
        description: 'treeId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    treeId?: string;

    @ApiProperty({
        description: 'idReExam',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @ValidateIf(o => o.idReExam)
    idReExam?: string;

    @ApiProperty({
        description: 'optionBHYT',
        type: Number,
    })
    @Transform(value => Number(value))
    optionBHYT?: number;
}
