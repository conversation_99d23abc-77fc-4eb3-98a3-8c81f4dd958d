import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { PUSH_NOTIF_CSKH } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const PushNotifCskhSchema = new Schema(
    {
        onesignalId: { type: String, required: true },
        userId: { type: String, required: true },
    },
    {
        collection: PUSH_NOTIF_CSKH,
        timestamps: true,
    },
).plugin(jsonMongo);
