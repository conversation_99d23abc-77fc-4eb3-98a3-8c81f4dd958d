import * as mongoose from 'mongoose';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { BOOKING_CARE_247_FLAGS } from './constants';
import { BOOKING_CARE_247 } from '../../booking-gateway/schemas/constants';

const Schema = mongoose.Schema;

export const BookingCare247FlagsSchema = new Schema(
    {
        userId: { type: String },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        care247Id: { type: String },
        care247: { type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 },
        constraint: { type: String, unique: true, required: true },
    },
    {
        collection: BOOKING_CARE_247_FLAGS,
        timestamps: true,
    },
);