import { Schema } from 'mongoose';

export const HealthHistorySchema = new Schema({
    patientInfoId: {
        type: String,
        required: true,
        index: true,
    },
    patientId: {
        type: String,
        default: null,
        index: true,
    },
    recordDate: {
        type: Date,
        required: true,
        default: Date.now,
        index: true,
    },
    recordType: {
        type: String,
        enum: ['INITIAL', 'CHECKUP', 'EMERGENCY', 'FOLLOW_UP'],
        required: true,
        default: 'INITIAL',
    },
    vitalSigns: {
        weight: { type: Number },
        height: { type: Number },
        temperature: { type: Number },
        pulse: { type: Number },
        spO2: { type: Number },
        bloodPressure: { type: String },
    },
    notes: {
        type: String,
        default: '',
    },
    createdBy: {
        type: String,
        required: true,
        default: 'system',
    },
}, {
    timestamps: true,
    collection: 'patient_health_histories',
});

// Indexes for better query performance
HealthHistorySchema.index({ patientInfoId: 1, recordDate: -1 });
HealthHistorySchema.index({ patientId: 1, recordDate: -1 });
HealthHistorySchema.index({ recordType: 1, recordDate: -1 });
