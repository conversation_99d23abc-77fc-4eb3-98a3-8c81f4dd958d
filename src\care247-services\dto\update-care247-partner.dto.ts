import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ICare247ServiceDto } from './create-care247-service.dto';
import { Type } from 'class-transformer';

export class IUpdateCare247HospitalDto {
  @ApiProperty({
    description: 'Trạng thái của dịch vụ',
    example: true,
    default: false,
  })
  @IsBoolean({ message: 'Trạng thái phải là giá trị boolean' })
  @IsOptional()
  status: boolean;

  @ApiProperty({
    description: 'Trạng thái của dịch vụ',
    example: true,
    default: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ICare247ServiceDto)
  addonServices: ICare247ServiceDto[];

  customPrice?: boolean;
}
