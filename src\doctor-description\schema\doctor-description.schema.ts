import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';

import { DOCTOR_DESCRIPTION_COLLECTION_NAME } from './constants';

const schema = mongoose.Schema;

export const DoctorDescriptionSchema = new schema(
    {
        refHospital: String,
        doctorId: String,
        position: String,
        rating: schema.Types.Mixed,
        introduction: String,
        description: String,
        slug: String,
        bookingTotal: Number,
        isDetailVisible: Boolean,
        disabled: { type: Boolean, default: false },
        message: { type: String },
    },
    {
        collection: DOCTOR_DESCRIPTION_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
