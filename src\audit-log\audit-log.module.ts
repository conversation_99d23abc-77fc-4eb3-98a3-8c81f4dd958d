import { ConfigModule } from 'src/config/config.module';
import { AuditLogController } from './audit-log.controller';
import { AUDIT_LOG_NAME, AUDIT_LOG_GET_NAME, CANCEL_BOOKING_LOG_NAME, SERVICE_LOG_NAME, SERVICE_TRACKING_LOG_NAME } from './constant';
import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';
import { AuditLogSchema } from './schemas/audit-log.schema';
import { AuditLogService } from './audit-log.service';
import { ServiceLogSchema } from './schemas/service-log.schema';
import { CancelBookingLogSchema } from './schemas/cancel-booking-logs';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { AuditLogGetSchema } from './schemas/audit-log-get.schema';
import { ServiceTrackingLogSchema } from './schemas/service-tracking-log.schema';

@Module({
    imports: [
        ConfigModule,
        MongooseModule.forFeature([
            { name: AUDIT_LOG_NAME, schema: AuditLogSchema },
            { name: AUDIT_LOG_GET_NAME, schema: AuditLogGetSchema },
            { name: SERVICE_LOG_NAME, schema: ServiceLogSchema },
            { name: CANCEL_BOOKING_LOG_NAME, schema: CancelBookingLogSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: SERVICE_TRACKING_LOG_NAME, schema: ServiceTrackingLogSchema },
        ]),
    ],
    controllers: [AuditLogController],
    providers: [AuditLogService],
})
export class AuditLogModule {}
