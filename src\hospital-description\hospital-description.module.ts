import { Module } from '@nestjs/common';
import { HospitalDescriptionController } from './hospital-description.controller';
import { HospitalDescriptionService } from './hospital-description.service';
import { MongooseModule } from '@nestjs/mongoose';
import { HospitalDescriptionSchema } from './schema/hospital-description.schema';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from './schema/constants';
import { HOSPITAL_COLLECTION_NAME } from '../hospital-mongo/schemas/constants';
import { HospitalSchema } from '../hospital-mongo/schemas/hospital.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: HOSPITAL_DESCRIPTION_COLLECTION_NAME, schema: HospitalDescriptionSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
        ]),
    ],
    controllers: [HospitalDescriptionController],
    providers: [HospitalDescriptionService],
})
export class HospitalDescriptionModule {}
