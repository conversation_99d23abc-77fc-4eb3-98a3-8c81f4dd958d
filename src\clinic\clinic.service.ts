import { IClinic } from './interface/clinic.interface';
import { CLINIC_COLLECTION_NAME, CLINIC_PARTNER, REGISTER_CLINIC_EVENT } from './constant';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Injectable, HttpService, Logger, HttpStatus } from '@nestjs/common';
import { RegisterClinicDto } from './dto/register-clinic.dto';
import { HttpException } from '@nestjs/common';
import { UrlConfigService } from '../config/config.url.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { UtilService } from 'src/config/util.service';
import { MESSAGE_EVENT } from 'src/message-event/constant';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { get } from 'lodash';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { IDistrict } from 'src/district-mongo/interfaces/district.interface';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { IWard } from 'src/ward-mongo/interfaces/ward.interface';
import * as FormData from 'form-data';

@Injectable()
export class ClinicService {
    private logger = new Logger(ClinicService.name);

    constructor(
        private http: HttpService,
        private urlConfigService: UrlConfigService,
        private utilService: UtilService,
        private eventEmitter: EventEmitter2,
        private globalSettinService: GlobalSettingService,
        @InjectModel(CLINIC_COLLECTION_NAME) private clinicModel: Model<IClinic>,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(DISTRICT_COLLECTION_NAME) private districtModel: Model<IDistrict>,
        @InjectModel(WARD_COLLECTION_NAME) private wardModel: Model<IWard>,
    ) { }

    async registerClinic(formData: RegisterClinicDto): Promise<any> {
        try {
            const portalProxy = this.urlConfigService.getPortalProxyUrl();
            const result = await this.http.post(`${portalProxy}/clinic-service/api/clinic/create`, formData).toPromise();
            if (!result.data && result.status < 200 && result.status >= 400) {
                throw new HttpException(`Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`, 500);
            }
            const { data } = result;
            const newClinic = new this.clinicModel({
                ...formData,
            });
            await newClinic.save();
            this.eventEmitter.emit(REGISTER_CLINIC_EVENT, data?.clinicInfo);
            return data;
        } catch (error) {
            if (error.response) {
                const { data } = error.response;
                this.logger.error(`Error when exec registerClinic()\n Error: ${data?.message}`);
                const errorMessage = data?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
                const statusCode = error.response?.status || HttpStatus.BAD_REQUEST;
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'registerClinic',
                    summary: 'Đăng ký tài khoản phòng khám',
                    nameParent: 'registerClinic',
                    params: { ...formData },
                    errorBody: this.utilService.errorHandler(error),
                    response: { ...data },
                    message: data?.message || 'Lỗi từ Portal!',
                });
                throw new HttpException(errorMessage, statusCode);
            }
        }
    }

    @OnEvent(REGISTER_CLINIC_EVENT)
    async onRegisterClinic(payload: RegisterClinicDto) {
        const { email, phone: inputPhone } = payload;
        const clinic = await this.clinicModel.findOne({ email, phone: inputPhone }).exec();
        if (!clinic) {
            this.logger.log(`Không tìm thấy thông tin phòng khám !`);
            return;
        }
        const { fullName, clinicName, phone } = clinic;
        const globalSetting = await this.globalSettinService.findAll();
        const to = this.globalSettinService.getValueGlobalByKey('CLINIC_REGISTER_MAIL_RECEIVER', globalSetting).split(',');
        const fullAdrress = await this.getFullAddress(clinic);
        const html = this.globalSettinService.getValueGlobalByKey('CLINIC_REGISTER_MAIL_HTML', globalSetting)
            .replace('{FULLNAME}', fullName)
            .replace('{CLINICNAME}', clinicName)
            .replace('{ADDRESS}', fullAdrress)
            .replace('{PHONE}', phone);

        const mailInfo = {
            to,
            from: {
                name: 'Medpro - Giải pháp tiếp cận y tế thông minh',
                email: '<EMAIL>',
            },
            subject: `V/v Đăng ký tài khoản quản lý phòng mạch`,
            html,
        };
        this.eventEmitter.emit(MESSAGE_EVENT, {
            appId: ``,
            topic: `clinic.register`,
            partnerId: ``,
            type: ``,
            title: ``,
            transactionId: ``,
            userId: ``,
            isPushNotif: false,
            isSendMail: true,
            eventData: {
                ...mailInfo,
            },
        });
    }

    async getFullAddress(clinic: IClinic): Promise<string> {
        let fullAddress = '';
        if (!!clinic.address) {
            fullAddress = `${clinic.address}`.trim();
        }
        const findCity = await this.cityModel.findOne({ id: clinic.city }).exec();
        if (findCity) {
            const cityObject = findCity.toObject();
            fullAddress = `${fullAddress}, ${cityObject.name}`;
        }
        const findDistrict = await this.districtModel.findOne({ id: clinic.district }).exec();
        if (findDistrict) {
            const districtObj = findDistrict.toObject();
            fullAddress = `${fullAddress}, ${districtObj.name}`;
        }
        const findWard = await this.wardModel.findOne({ id: clinic.ward }).exec();
        if (findWard) {
            const wardObj = findWard.toObject();
            fullAddress = `${fullAddress}, ${wardObj.name}`;
        }
        return fullAddress;
    }

    async uploadFileClinic(files: any): Promise<any> {
        try {
            const { file } = files;
            const formData = new FormData();
            formData.append('file', file[0].buffer, file[0].originalname);
            const api = `${this.urlConfigService.FilterCheckRestfulAPI()}/upload-file`;
            const res = await this.http.post<any>(api, formData, {
                headers: {
                    ...formData.getHeaders(),
                    partnerid: CLINIC_PARTNER,
                },
            }).toPromise();
            return {
                url: `${this.urlConfigService.getBaseUrl()}${res.data.url}`,
            };
        } catch (error) {
            throw error;
        }
    }
}
