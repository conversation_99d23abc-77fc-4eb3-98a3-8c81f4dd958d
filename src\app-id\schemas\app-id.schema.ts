import * as mongoose from 'mongoose';
import { APP_ID_COLLECTION_NAME } from './constants';
import * as uuid from 'uuid';
import { HospitalInAppSchema } from './hospital-in-app.schema';
import { FeatureInAppSchema } from './feature-in-app.schema';

const Schema = mongoose.Schema;

const id = () => {
    const uuidv4 = uuid.v4();
    const idv4 = uuidv4.replace(/-/g, '');
    return idv4;
};

export const AppIdSchema = new Schema(
    {
        id: { type: String, default: id },
        name: { type: String },
        appId: { type: String, required: true },
        description: { type: String },
        country: { type: String },
        detail: [HospitalInAppSchema],
        features: { type: [FeatureInAppSchema], default: [] },
    },
    {
        collection: APP_ID_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
