import { Injectable, Inject, HttpService, HttpException, HttpStatus } from '@nestjs/common';
import { FeeSearchDTO } from './dto/feeSearchDto';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import * as moment from 'moment';
import { FeeConfigService } from 'src/config/config.fee.service';
import { PatientService } from 'src/patient/patient.service';
import { FeeCreateOrderDTO } from './dto/feeCreateOrderDto';
import { BankService } from 'src/bank/bank.service';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { PaymentFeeService } from 'src/payment-fee/payment-fee.service';
import { THU_DUC_HOSPITAL_CONNECTION } from 'src/config/thuDucHospitalConnection';
import { MomoService } from 'src/momo/momo.service';

@Injectable()
export class FeeService {

    constructor(
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly hospitalKnex,
        private readonly httpService: HttpService,
        private readonly feeConfigService: FeeConfigService,
        private readonly patientService: PatientService,
        private readonly bankService: BankService,
        private readonly paymentMethodService: PaymentMethodService,
        private readonly paymentFeeService: PaymentFeeService,
        private readonly momoService: MomoService,
    ) { }

    async getFee(feeSearchDTO: FeeSearchDTO): Promise<any> {
        const params = {
            UserID: feeSearchDTO.feeNumber,
            ProviderID: '',
            ServiceID: '',
            Password: null,
            RequestTime: moment().format('DD/MM/YYYY HH:mm'),
            Signature: '',
        };
        const url = this.feeConfigService.getUrlGenerateSecurityParams();
        const { data: Data, signature: Signature } = (await this.getSecurityParams(url, params).toPromise()).data; // expires_in
        const partnerUrl = this.feeConfigService.getFeeUrl();
        const paramsGetFee = { Data, Signature };
        const { successful, result } = (await this.getFeeThirdParty(partnerUrl, paramsGetFee).toPromise()).data; // expires_in
        /* check successful */
        if (successful) {
            /* tiếp tục */
            const result2 = '0|HỒ THỊ THU HÀ|45700|11/12/2019 19:33:19|ID thanh toán: 191211000000187. Hinh thuc: Ngoai tru|0|15151515|2|2|Xét nghiệm DV';
            const slitResult = result2.split('|');
            /* check xem có tồn tai fee_number trong bảng fee hay ko */
            const checkFee = await this.checkFeeNumber(feeSearchDTO.feeNumber);
            const patientCode = slitResult[6];
            const dataCreateOrUpdate = {
                amount: slitResult[2],
                patient_code: patientCode,
                fullname: slitResult[1],
                subject_id: slitResult[5],
                place: slitResult[9],
                number: slitResult[8],
                result: result2,
            };
            if (!checkFee) {
                /* insert */
                this.insertFee({
                    ...dataCreateOrUpdate,
                    fee_number: feeSearchDTO.feeNumber,
                });
            } else {
                /* update */
                const updateData = this.updateFee(feeSearchDTO.feeNumber, dataCreateOrUpdate);
            }
            /* Tìm lại thông tin patient */
            const checkPatient = this.patientService.checkPatient(patientCode);
            if (!checkPatient) {
                /* tìm lại trong Booking ở bên thứ 3 */
                const paramsGetPatient = {
                    id: patientCode,
                };
                const { data: Data2, signature: Signature2 } = (await this.getSecurityParams(url, params).toPromise()).data;
                const urlGetPatientByIdThirdParty = this.feeConfigService.getPatientByIdUrl();
                const paramsGetPatientById = {
                    Data: Data2,
                    Signature: Signature2,
                };
                const { successful: isOK, result: PatientInfo } =
                    (await this.getFeeThirdParty(urlGetPatientByIdThirdParty, paramsGetPatientById).toPromise()).data;
                if (isOK) {
                    /* Insert Patient */
                    const medproId = this.patientService.generateMedproID();
                    this.patientService.insertPatient({
                        medpro_id: medproId,
                        bv_id: patientCode,
                        name: PatientInfo.name,
                        surname: PatientInfo.surname,
                        sex: Number(PatientInfo.sex),
                        birthyear: PatientInfo.birthyear,
                        birthdate: PatientInfo.birthdate,
                        mobile: PatientInfo.mobile,
                        country_code: PatientInfo.country_code,
                        city_id: PatientInfo.city_id,
                        district_id: PatientInfo.district_id,
                        ward_id: PatientInfo.ward_id,
                        address: PatientInfo.address,
                        bhyt_code: PatientInfo.bhyt_code,
                    });
                }

            }
            /* Trả về thông tin Fee */
            return this.getFeeInfo(feeSearchDTO.feeNumber);

        }
        return null;

    }

    async getFeeList(feeIdList): Promise<any> {
        return await this.getListFee(feeIdList);
    }

    async createFeeOrder(feeCreateOrderDTO: FeeCreateOrderDTO): Promise<any> {
        const userId = 111111;
        const bankInfo = this.bankService.getBankInfoById(feeCreateOrderDTO.bankdId);
        if (!bankInfo) {
            throw new HttpException('BadRequestException', HttpStatus.BAD_REQUEST);
        }
        const feeInfo = await this.getFeeInfoById(feeCreateOrderDTO.id);
        if (!feeInfo) {
            throw new HttpException('BadRequestException', HttpStatus.BAD_REQUEST);
        }
        let amountAdd = 0;
        let rate = 0;
        const {
            vp_rate_atm,
            vp_add_atm,
            vp_sevice_fee,
            vp_rate_int_card,
            vp_add_int_card,
            // vp_rate_offline,
            // vp_rate_qr,
            // vp_add_qr,
        } = await this.paymentMethodService.getPaymentMethodRate();
        switch (feeCreateOrderDTO.methodId) {
            case 2: {
                amountAdd = vp_add_int_card;
                rate = vp_rate_int_card;
                break;
            }
            case 3: {
                amountAdd = vp_add_atm;
                rate = vp_rate_atm;
                break;
            }
            default: {
                amountAdd = 0;
                rate = 0;
            }

        }
        const amount = Number(feeInfo.amount + (feeInfo.amount * rate) + amountAdd + vp_sevice_fee);
        /* insert payment feee */
        const paymentFeeInsert = {
            method_id: feeCreateOrderDTO.methodId,
            amount,
            amount_original: feeInfo.amount,
            amount_add: amountAdd,
            amount_rate: rate,
            amount_medpro: vp_sevice_fee,
            user_id: userId,
            fee_id: feeCreateOrderDTO.id,
            app: 'CHỗ này cần xác nhận lại khi chạy chung 1 source',
        };
        const [idPaymentFee] = await this.paymentFeeService.insertPaymentFee(paymentFeeInsert);
        /* Tìm lại thông tin để cập nhật lại transaction_code_tt */
        const transactionCodeTT = this.paymentFeeService.generateTransactionCodeTT(idPaymentFee);
        await this.paymentFeeService.updateTransactionCodeTT(idPaymentFee, transactionCodeTT);
        /* Tìm lại thông tin patient */
        const patientInfo = await this.patientService.checkPatient(feeInfo.patient_code);
        switch (feeCreateOrderDTO.methodId) {
            case 7: {
                /* Kiểm tra platform */
                if (feeCreateOrderDTO.platform === 'web') {
                    const pFeeOrder = {
                        amount,
                        transaction_code_tt: transactionCodeTT,
                        info: `Thanh toán Viện phí: ${transactionCodeTT}`,
                        extra: feeCreateOrderDTO.platform,
                    };
                    const momoData = this.momoService.createFeeVienPhiOrderPlatformWeb(pFeeOrder);
                } else {
                    const MobileAppParams = {
                        amount,
                        transaction_code_tt: transactionCodeTT,
                        info: `Thanh toán Viện phí: ${transactionCodeTT}`,
                        extra: feeCreateOrderDTO.platform,
                    };

                    const momoSDKData = this.momoService.momoDeepLink(MobileAppParams);

                }
                break;
            }
            default:
                {
                    return false;
                }
        }
        return true;
    }

    getSecurityParams(url, params: object): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, params);
    }

    getFeeThirdParty(url, params: object): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, params);
    }

    getPatientIdThirdParty(url, params: object): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, params);
    }

    checkFeeNumber(feeNumber) {
        return this.hospitalKnex('fee')
            .where('fee_number', feeNumber)
            .first();
    }

    insertFee(data): Promise<any> {
        return this.hospitalKnex('fee').insert(data);
    }

    updateFee(feeNumber, data): Promise<any> {
        return this.hospitalKnex('fee').where('fee_number', feeNumber).update(data);
    }

    getFeeInfo(feeNumber): Promise<any> {
        return this.hospitalKnex('fee').where('fee_number', feeNumber).first();
    }

    getFeeInfoById(id: number): Promise<any> {
        return this.hospitalKnex('fee').where('id', id).first();
    }

    async getListFee(listFee: []): Promise<any> {
        return this.hospitalKnex('fee')
            .whereIn('id', listFee);
    }

}
