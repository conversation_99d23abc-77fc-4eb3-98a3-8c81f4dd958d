import { Body, Controller, Get, Headers, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { HomePageServiceInject } from "./persistence/home-page.service";
import { HomePageService } from "./home-page.service";
import { HomePageHeaderDto } from "./dto/get-home-page-data.dto";
import { get } from "lodash";
import { CacheManagerService } from "src/cache-manager/cache-manager.service";
@ApiTags("Home Page")
@Controller("/home-page")
export class HomePageController {
    constructor(
        @HomePageServiceInject()
        private readonly homePageService: HomePageService,
         private cacheService: CacheManagerService
    ) {}

    @Post("cash-back/popup")
    async getCashBackPopup(@Body() body) { // ? partnerIds=dalieuhcm,dkanphuoc
        return this.homePageService.getPartnerCashBackPopupList();
    }

    @Post("delete-cache")
    deleteCache(@Headers() headers: HomePageHeaderDto) {
        return this.homePageService.deleteCache();
    }
    

    @Post("data-set-cache")
    async resetCache (@Headers() headers: HomePageHeaderDto) {
        const config = await this.homePageService.getHomePageGlobalSettingConfig(headers?.appid)
        const enabledCache = get(config, 'enableCache', false);
        const cacheKey = get(config, 'cacheKey', 'home-page-all-new');
        
        if(enabledCache) {
            const data = await this.homePageService.getHomePageWithGlobalSettingConfig({ config , headers });
            await this.homePageService.setCacheIfMiss(cacheKey, data, 3600)
            return {
                message: "Cache đã được set lại"
            };
        }

        return {
            message: "Cache không được bật"
        }        
    }

    @Post('/data')
    async getHomePage(@Headers() headers: HomePageHeaderDto) {
        const config = await this.homePageService.getHomePageGlobalSettingConfig(headers?.appid)
        const enabledCache = get(config, 'enableCache', false);
        const cacheKey = get(config, 'cacheKey', 'home-page-all-new');
        const cacheData = await this.homePageService.readCacheIfExist(cacheKey) 

        if(cacheData) {
            return cacheData
        }
        const data = await this.homePageService.getHomePageWithGlobalSettingConfig({ config , headers });


        await this.homePageService.setCacheIfMiss(cacheKey, data, 3600)
        return data;
    }

    @Post('/subjects')
    async getSubjects(@Headers() headers: HomePageHeaderDto) {
        return this.homePageService.getSubjects();
    }

    @Get('/data')
    async getHomePageData(@Headers() headers: HomePageHeaderDto) {
       const key = `home-page:data:${headers.appid}`;
        const value = await this.cacheService.get(key);

        if (value) {
            return value;
        }

        const result = await this.homePageService.getHomePageDataV2(headers);
        this.cacheService.set(key, result);
        return result;
    }

     @Get('reset-cache')
    async resetCacheHomePageV2(@Headers() headers: HomePageHeaderDto) {
        try {
            const key = `home-page:data:${headers.appid}`;
            this.cacheService.findByKeyAndDelete(key);
            return {
                message: 'Cache đã được set lại',
            };
        } catch (error) {
            console.error('Error resetting cache:', error);
            return {
                message: 'Đã có lỗi xảy ra khi reset cache',
            };
        }
    }

    @Get('seed-data')
    async seedData() {
        return await this.homePageService.cloneBetaToLive();
    }
}
