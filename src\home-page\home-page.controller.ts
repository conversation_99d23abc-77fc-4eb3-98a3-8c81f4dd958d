import { Body, Controller, Get, Headers, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { HomePageServiceInject } from "./persistence/home-page.service";
import { HomePageService } from "./home-page.service";
import { HomePageHeaderDto } from "./dto/get-home-page-data.dto";
import { get } from "lodash";
@ApiTags("Home Page")
@Controller("/home-page")
export class HomePageController {
    constructor(
        @HomePageServiceInject()
        private readonly homePageService: HomePageService,
    ) {}

    @Post("cash-back/popup")
    async getCashBackPopup(@Body() body) { // ? partnerIds=dalieuhcm,dkanphuoc
        return this.homePageService.getPartnerCashBackPopupList();
    }

    @Post("delete-cache")
    deleteCache(@Headers() headers: HomePageHeaderDto) {
        return this.homePageService.deleteCache();
    }
    

    @Post("data-set-cache")
    async resetCache (@Headers() headers: HomePageHeaderDto) {
        const config = await this.homePageService.getHomePageGlobalSettingConfig(headers?.appid)
        const enabledCache = get(config, 'enableCache', false);
        const cacheKey = get(config, 'cacheKey', 'home-page-all-new');
        
        if(enabledCache) {
            const data = await this.homePageService.getHomePageWithGlobalSettingConfig({ config , headers });
            await this.homePageService.setCacheIfMiss(cacheKey, data, 3600)
            return {
                message: "Cache đã được set lại"
            };
        }

        return {
            message: "Cache không được bật"
        }        
    }

    @Post('/data')
    async getHomePage(@Headers() headers: HomePageHeaderDto) {
        const config = await this.homePageService.getHomePageGlobalSettingConfig(headers?.appid)
        const enabledCache = get(config, 'enableCache', false);
        const cacheKey = get(config, 'cacheKey', 'home-page-all-new');
        // const cacheData = await this.homePageService.readCacheIfExist(cacheKey) 
        
        // if (cacheData) {
        //     return cacheData
        // }
        const data = await this.homePageService.getHomePageWithDataCreated();

        await this.homePageService.setCacheIfMiss(cacheKey, data, 3600)
        return data;
    }

    @Post('create-data')
    async createHomePageData(@Headers() headers: HomePageHeaderDto): Promise<any> {
        const config = await this.homePageService.getHomePageGlobalSettingConfig(headers?.appid)
        return this.homePageService.createHomePageData({ config , headers });
    }
}