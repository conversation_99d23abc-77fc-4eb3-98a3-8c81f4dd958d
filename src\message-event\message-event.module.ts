import { EventSchema } from './../event/schemas/event.schema';
import { EVENT_COLLECTION_NAME, MESSAGE_EVENT_NOTIF_INFORM_COLLECTION, NOTIFICATION_COLLECTION } from './../event/schemas/constants';
import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  MESSAGE_EVENT_COLLECTION,
  MESSAGE_EVENT_PROCESS_COLLECTION,
  MESSAGE_EVENT_PROCESS_FAILED_COLLECTION,
  MESSAGE_EVENT_SUCCESS_COLLECTION,
} from 'src/event/schemas/constants';
import { MessageEventProcessFailedSchema } from 'src/event/schemas/message-event-process-failed.schema';
import { MessageEventProcessSchema } from 'src/event/schemas/message-event-process.schema';
import { MessageEventSuccessSchema } from 'src/event/schemas/message-event-success.schema';
import { MessageEventSchema } from 'src/event/schemas/message-event.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { PushDeviceSchema } from 'src/push-device/schemas/push-device.schema';
import { MessageEventController } from './message-event.controller';
import { MessageEventService } from './message-event.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import {
    BOOKING_COLLECTION_NAME,
    HOSPITAL_FEE_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    RETRY_TRASACTION_COLLECTION_NAME,
    EXPIRED_BOOKING_COLLECTION_NAME,
    TIMEOUT_TRANSACTION_LOGS_COLLECTION_NAME,
    CONSTRAINTS_COLLECTION_NAME,
    BOOKING_CARE_247,
} from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { PaymentSchema } from 'src/booking-gateway/schemas/payment.schema';
import { REFERRAL_CODE_REGISTER_COLLECTION_NAME, USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { HospitalFeeSchema } from 'src/booking-gateway/schemas/hospital-fee.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { NotificationSchema } from 'src/event/schemas/notification.schema';
import { GlobalSettingModule } from 'src/global-setting/global-setting.module';
import { PatientMongoModule } from 'src/patient-mongo/patient-mongo.module';
import { SessionModule } from 'src/session/session.module';
import { UserModule } from '../user/user.module';
import { RetryTransactionSchema } from 'src/booking-gateway/schemas/retry-transactions.schema';
import { ExpiredBookingSchema } from '../booking-gateway/schemas/expired-booking.schema';
import {
    BOOKING_HIS_COLLECTION_NAME,
    BOOKING_NUMBER_COLLECTION_NAME,
    BOOKING_SLOT_COLLECTION_NAME,
    CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME,
} from '../his-gateway/schemas/constants';

import { BookingSlotSchema } from '../his-gateway/schemas/booking-slot.schema';
import { BookingHISSchema } from '../his-gateway/schemas/booking.schema';
import { ConstraintsSequenceNumberSchema } from '../his-gateway/schemas/constraints-sequence-number.schema';
import { BookingNumberSchema } from '../his-gateway/schemas/booking-numbers.schema';
import { HisGatewayService } from '../his-gateway/his-gateway.service';
import { PatientSchema } from '../patient-mongo/schemas/patient.schema';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { TimeoutTransactionLogsSchema } from 'src/booking-gateway/schemas/timeout-transactions.schema';
import { ConstraintsSchema } from 'src/booking-gateway/schemas/constraints.schema';
import { MessageEventNotifInformSchema } from '../event/schemas/message-event-notif-inform.schema';
import { BookingCare247Schema } from '../booking-gateway/schemas/booking-care-247.schema';
import { BOOKING_CARE_247_CONSTRAINT_USER, RETRY_PAYMENT_BOOKING } from '../cskh/schemas/constants';
import { BookingCare247ConstraintUserSchema } from '../cskh/schemas/booking-care247-constraint-user.schema';
import { RetryPaymentBookingSchema } from '../cskh/schemas/retry-payment-bookings.schema';
import { ReferralCodeRegisterSchema } from '../user/schemas/referral-code-register';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([
      { name: MESSAGE_EVENT_COLLECTION, schema: MessageEventSchema },
      { name: MESSAGE_EVENT_PROCESS_COLLECTION, schema: MessageEventProcessSchema },
      { name: MESSAGE_EVENT_PROCESS_FAILED_COLLECTION, schema: MessageEventProcessFailedSchema },
      { name: MESSAGE_EVENT_SUCCESS_COLLECTION, schema: MessageEventSuccessSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
      { name: EVENT_COLLECTION_NAME, schema: EventSchema },
      // { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema},
      { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema},
      { name: USER_COLLECTION_NAME, schema: UserSchema},
      { name: HOSPITAL_FEE_COLLECTION_NAME, schema: HospitalFeeSchema},
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema},
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: NOTIFICATION_COLLECTION, schema: NotificationSchema },
      { name: RETRY_TRASACTION_COLLECTION_NAME, schema: RetryTransactionSchema },
        {
            name: EXPIRED_BOOKING_COLLECTION_NAME,
            schema: ExpiredBookingSchema,
        },
        { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
        {
            name: CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME,
            schema: ConstraintsSequenceNumberSchema,
        },
        { name: BOOKING_SLOT_COLLECTION_NAME, schema: BookingSlotSchema },
        { name: BOOKING_HIS_COLLECTION_NAME, schema: BookingHISSchema },
        { name: BOOKING_NUMBER_COLLECTION_NAME, schema: BookingNumberSchema },
        { name: TIMEOUT_TRANSACTION_LOGS_COLLECTION_NAME, schema: TimeoutTransactionLogsSchema },
        { name: CONSTRAINTS_COLLECTION_NAME, schema: ConstraintsSchema },
        { name: MESSAGE_EVENT_NOTIF_INFORM_COLLECTION, schema: MessageEventNotifInformSchema },
        { name: BOOKING_CARE_247, schema: BookingCare247Schema },
        { name: BOOKING_CARE_247_CONSTRAINT_USER, schema: BookingCare247ConstraintUserSchema },
        { name: RETRY_PAYMENT_BOOKING, schema: RetryPaymentBookingSchema },
        { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
    ]),
    GlobalSettingModule,
    PatientMongoModule,
    SessionModule,
    UserModule,
  ],
  controllers: [MessageEventController],
    providers: [MessageEventService, HisGatewayService],
})
export class MessageEventModule { }
