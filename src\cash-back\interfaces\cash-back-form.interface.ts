import { Document } from "mongoose";
import { IBooking } from "src/booking-gateway/intefaces/booking.inteface";
import { IHospital } from "src/hospital-mongo/interfaces/hospital.interface";
import { IUser } from "src/user/interfaces/user.interface";

export interface ICashBackForm extends Document {
    readonly id: string
    readonly bankName: string
    readonly bankAccountNumber: string
    readonly bankAccountName: string
    readonly totalAmount: string
    readonly invoiceImages: string[]
    readonly partnerId: string
    readonly userId: string
    readonly bookingId: string
    readonly ratingHospital: string
    readonly ratingMedpro: string

    readonly partner: IHospital
    readonly user: IUser
    readonly booking: IBooking
}