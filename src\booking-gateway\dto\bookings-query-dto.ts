import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class BookingssQueryDto {
    @ApiProperty({
        description: 'transactionId',
        required: false,
        type: String,
    })
    transactionId?: string;
    
    @ApiProperty({
        description: 'bookingCode',
        required: false,
        type: String,
    })
    bookingCode?: string;

    @ApiProperty({
        description: 'appId',
        required: false,
        type: String,
    })
    appId?: string;

    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId?: string;

    @ApiProperty({
        description: 'platform',
        required: false,
        type: String,
    })
    platform?: string;
    
    @ApiProperty({
        description: 'status',
        required: false,
        type: String,
    })
    status?: number;

    @ApiProperty({
        description: 'paymentMethod',
        required: false,
        type: String,
    })
    paymentMethod?: string;

    @ApiProperty({
        description: 'cskhUserId',
        required: false,
        type: String,
    })
    cskhUserId?: string;
    
    @ApiProperty({
        description: 'type',
        required: false,
        type: String,
    })
    type?: number;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toDate?: string;

    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromBookingDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toBookingDate?: string;

    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @Transform(value => Number(value))
    pageIndex?: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
