import { HttpModule, Module } from '@nestjs/common';
import { CashBackController } from './cash-back.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { CashBackServiceProvider } from './persistences/cash-back.service';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { CASH_BACK_FORM_COLLECTION_NAME } from './constants';
import { CashBackFormSchema } from './schemas/cash-back-form.schema';
import { PushNotifService } from 'src/push-notif/push-notif.service';
import { NotificationSettingService } from 'src/notification-setting/notification-setting.service';
import { PushDeviceService } from 'src/push-device/push-device.service';
import { ReceiverListSchema } from 'src/message-config/schemas/receiver-list.schema';
import { MESSAGE_GROUP_PHONE_COLLECTION_NAME, MESSAGE_ITEM_COLLECTION_NAME, RECEIVER_LIST_COLLECTION_NAME, RECEIVER_VERSION_COLLECTION_NAME } from 'src/message-config/schemas/constants';
import { ReceiverVersionListSchema } from 'src/message-config/schemas/receiver-version.schema';
import { MessageItemSchema } from 'src/message-config/schemas/message-item.schema';
import { EventSchema } from 'src/event/schemas/event.schema';
import { EVENT_COLLECTION_NAME, MESSAGE_SEND_RECORD_COLLECTION_NAME } from 'src/event/schemas/constants';
import { MessageGroupPhoneSchema } from 'src/message-config/schemas/message-group-phone.schema';
import { MessageSendRecordSchema } from 'src/event/schemas/message-send-record.schema';
import { UserSchema } from 'src/user/schemas/user.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PushDeviceSchema } from 'src/push-device/schemas/push-device.schema';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: CASH_BACK_FORM_COLLECTION_NAME, schema: CashBackFormSchema },
      { name: RECEIVER_LIST_COLLECTION_NAME, schema: ReceiverListSchema },
      { name: RECEIVER_VERSION_COLLECTION_NAME, schema: ReceiverVersionListSchema },
      { name: MESSAGE_ITEM_COLLECTION_NAME, schema: MessageItemSchema },
      { name: EVENT_COLLECTION_NAME, schema: EventSchema }, 
      { name: MESSAGE_GROUP_PHONE_COLLECTION_NAME, schema: MessageGroupPhoneSchema },
      { name: MESSAGE_SEND_RECORD_COLLECTION_NAME, schema: MessageSendRecordSchema },
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
    ]),
  ],
  controllers: [CashBackController],
  providers: [
    CashBackServiceProvider,
    PushNotifService,
    NotificationSettingService,
    PushDeviceService,
    GlobalSettingService
  ]
})
export class CashBackModule {}
