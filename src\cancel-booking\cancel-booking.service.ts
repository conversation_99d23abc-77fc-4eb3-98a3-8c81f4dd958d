import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { Connection, Model } from 'mongoose';
import { BookingGatewayService } from 'src/booking-gateway/booking-gateway.service';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { CSKHTokenVerifyDTO } from 'src/patient-mongo/dto/cskh-token-verify.dto';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { IPayment } from '../booking-gateway/intefaces/payment.inteface';
import { BookingQueryDto } from './dto/bookings-query-dto';
import { CancelBookingResponseDto } from './dto/cancel-booking-response.dto';
import { CancelBookingDto } from './dto/cancel-booking.dto';
import {
    IBankAccountRequirement,
    IBookingCancellation,
    ICancellationReasonConfig,
    IExecuteCancellationResult,
    IReasonValidation,
} from './interfaces/cancel-booking.interfaces';
import { BOOKING_CANCELLATION_COLLECTION_NAME } from './schemas/constants';
import moment = require('moment');
import * as Excel from 'exceljs';

@Injectable()
export class CancelBookingService {
    private readonly logger = new Logger(CancelBookingService.name);

    constructor(
        @InjectModel(BOOKING_CANCELLATION_COLLECTION_NAME) private bookingCancellationModel: Model<IBookingCancellation>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectConnection() private connection: Connection,
        private patientMongoService: PatientMongoService,
        private readonly bookingGatewayService: BookingGatewayService,
        private globalSettingService: GlobalSettingService,
    ) {}

    async cancelBooking(
        bookingId: string,
        dto: CancelBookingDto,
        partnerId: string,
        appid: string,
        userId: string,
        ipInfo: any,
        cskhInfo: CSKHTokenVerifyDTO,
        platform?: string,
    ): Promise<CancelBookingResponseDto> {
        try {
            const booking = await this.bookingModel.findOne({ _id: bookingId, userId }).populate('partner').populate('patient').exec();
            if (!booking) {
                throw new HttpException('Không tìm thấy thông tin phiếu khám.', HttpStatus.NOT_FOUND);
            }

            // Get cancellation reasons from global setting and validate
            const cancellationReasons = await this.getCancellationReasons();

            const reasonValidation = this.validateReasonIds(dto.reasonIds, dto.otherContent, cancellationReasons);
            if (!reasonValidation.isValid) {
                throw new BadRequestException(`Lý do huỷ không hợp lệ : ${reasonValidation.errors.join(', ')}`);
            }

            // Build reasons data from IDs and global setting
            const reasonsData = this.buildReasonsData(dto.reasonIds, dto.otherContent, cancellationReasons);

            const paymentInfo = await this.getPaymentInfo(booking);

            if (!paymentInfo) {
                this.logger.warn(`Không tìm thấy thông tin thanh toán cho booking: ${bookingId}`);
            }
            await this.checkExistingCancellation(booking);
            const result = await this.executeCancellationTransaction(
                booking,
                dto,
                paymentInfo,
                reasonsData,
                appid,
                userId,
                ipInfo,
                cskhInfo,
                platform,
            );

            return new CancelBookingResponseDto(true, 'Huỷ phiếu thành công!', result.cancelReasonId, result.bankAccountId);
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Huỷ phiếu thất bại. Vui lòng thử lại sau.');
        }
    }

    async cancelReservationCskh(
        bookingId: string,
        dto: CancelBookingDto,
        partnerId: string,
        appid: string,
        userId: string,
        ipInfo: any,
        cskhInfo: CSKHTokenVerifyDTO,
        platform?: string,
    ): Promise<CancelBookingResponseDto> {
        try {
            const booking = await this.bookingModel.findOne({ _id: bookingId, userId }).populate('partner').populate('patient').exec();
            if (!booking) {
                throw new HttpException('Không tìm thấy thông tin phiếu khám.', HttpStatus.NOT_FOUND);
            }

            // Get cancellation reasons from global setting and validate
            const cancellationReasons = await this.getCancellationReasonsCskh();

            const reasonValidation = this.validateReasonIds(dto.reasonIds, dto.otherContent, cancellationReasons);
            if (!reasonValidation.isValid) {
                throw new BadRequestException(`Lý do huỷ không hợp lệ : ${reasonValidation.errors.join(', ')}`);
            }

            // Build reasons data from IDs and global setting
            const reasonsData = this.buildReasonsData(dto.reasonIds, dto.otherContent, cancellationReasons);

            const paymentInfo = await this.getPaymentInfo(booking);

            if (!paymentInfo) {
                this.logger.warn(`Không tìm thấy thông tin thanh toán cho booking: ${bookingId}`);
            }
            await this.checkExistingCancellationCskh(booking);
            const result = await this.executeCancellationTransactionCskh(
                booking,
                dto,
                paymentInfo,
                reasonsData,
                appid,
                userId,
                ipInfo,
                cskhInfo,
                platform,
            );

            return new CancelBookingResponseDto(true, 'Yêu cầu hủy thành công!', result.cancelReasonId, result.bankAccountId);
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Yêu cầu hủy phiếu thất bại. Vui lòng thử lại sau.');
        }
    }

    private async getPaymentInfo(booking: IBooking): Promise<IPayment | null> {
        try {
            const payment = await this.paymentModel
                .findOne({
                    transactionId: booking?.transactionId,
                    status: 2,
                })
                .lean();
            if (!payment) {
                return null;
            }
            return payment;
        } catch (error) {
            this.logger.warn(`Lấy thông tin thanh toán thất bại cho transactionId ${booking?.transactionId}:`, error.message);
            return null;
        }
    }

    private async requiresBankAccountInfo(gatewayId?: string, paymentMethodDetail?: string, subTotal?: number): Promise<boolean> {
        try {
            if (!gatewayId || !paymentMethodDetail) {
                return false;
            }
            const requirementsJson = await this.globalSettingService.findByKeyAndRepoName('PAYMENT_METHOD_REQUIRED_REFUND_BANK');
            if (!requirementsJson) {
                throw new HttpException('Không thể lấy yêu cầu tài khoản ngân hàng! Vui lòng thử lại sau.', HttpStatus.NOT_FOUND);
            }
            const requirements = JSON.parse(requirementsJson) as IBankAccountRequirement[];

            const isRequired =
                requirements.some(req => req.gatewayId === gatewayId && req.paymentMethodDetail === paymentMethodDetail) && subTotal > 0;
            return isRequired;
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new HttpException(errorMsg || 'Có lỗi xảy ra khi. Vui lòng thử lại sau.', HttpStatus.NOT_FOUND);
        }
    }

    private async checkExistingCancellation(booking: IBooking): Promise<void> {
        const existingCancellation = await this.bookingCancellationModel.findOne({ bookingId: booking.bookingId }).lean();

        if (existingCancellation) {
            throw new BadRequestException('Phiếu khám đã được huỷ trước đó. Không thể huỷ lại.');
        }
    }

    private async checkExistingCancellationCskh(booking: IBooking): Promise<void> {
        const existingCancellation = await this.bookingCancellationModel.findOne({ bookingId: booking.bookingId }).lean();

        if (existingCancellation) {
            throw new BadRequestException('Phiếu khám đã được yêu cầu hủy. Không thể huỷ lại.');
        }
    }

    private async executeCancellationTransaction(
        booking: any,
        dto: CancelBookingDto,
        paymentInfo: IPayment | null,
        reasonsData: any[],
        appId: string,
        userId: string,
        extraInfo: any,
        cskhInfo?: CSKHTokenVerifyDTO,
        platformContext: string = '',
    ): Promise<IExecuteCancellationResult> {
        const [session, accountTypeObj] = await Promise.all([
            this.connection.startSession(),
            this.globalSettingService.findByKeyAndRepoName('ACCOUNT_TYPE'),
        ]);
        let cancelledBy: string;
        if (extraInfo?.username) {
            cancelledBy = 'PORTAL';
        } else if (cskhInfo?.cskhUserId) {
            cancelledBy = 'CSKH';
        } else {
            cancelledBy = 'USER';
        }
        if (!accountTypeObj) {
            throw new HttpException('Không tìm thấy thông tin loại tài khoản!', HttpStatus.NOT_FOUND);
        }
        const accountType = JSON.parse(accountTypeObj);
        try {
            let cancelReasonId: string;
            let bankAccountId: string | undefined;
            let updateResult: any;

            await session.withTransaction(async () => {
                // Kiểm tra xem có cần thông tin tài khoản ngân hàng không
                const requiresBankAccount = await this.requiresBankAccountInfo(
                    paymentInfo?.gatewayId,
                    paymentInfo?.paymentMethodDetail,
                    paymentInfo?.subTotal,
                );

                // Validate bank account info if required
                if (requiresBankAccount) {
                    const missingFields: string[] = [];
                    if (!dto.bankCode || !dto.bankCode?.trim()) {
                        missingFields.push('Mã ngân hàng');
                    }
                    if (!dto.bankName || !dto.bankName?.trim()) {
                        missingFields.push('Tên ngân hàng');
                    }
                    if (!dto.bankNumber || !dto.bankNumber?.trim()) {
                        missingFields.push('Số tài khoản');
                    }
                    if (!dto.accountName || !dto.accountName?.trim()) {
                        missingFields.push('Tên chủ tài khoản');
                    }
                    if (dto?.accountType && !accountType.find((item: any) => item.key === dto.accountType?.trim())) {
                        throw new BadRequestException(`Loại tài khoản không hợp lệ!`);
                    }
                    if (missingFields.length > 0) {
                        throw new BadRequestException(`Vui lòng nhập: ${missingFields.join(', ')} để hoàn tất huỷ phiếu.`);
                    }
                }
                const overrideBookingCode = booking?.bookingCodeV1 || booking?.bookingCode;
                const cancellationData = {
                    bookingId: booking.bookingId,
                    booking: booking._id,
                    partnerId: booking.partnerId,
                    appId: booking.appId,
                    userId: booking.userId,

                    reasonIds: dto.reasonIds,
                    reasonsData: reasonsData,
                    otherContent: dto.otherContent,
                    ...(requiresBankAccount && {
                        bankCode: dto.bankCode?.trim(),
                        bankName: dto.bankName?.trim(),
                        bankNumber: dto.bankNumber?.trim(),
                        accountName: dto.accountName?.trim(),
                        accountType: dto?.accountType?.trim(),
                        gatewayId: paymentInfo?.gatewayId || undefined,
                        paymentMethodDetail: paymentInfo?.paymentMethodDetail || undefined,
                        paymentMethod: paymentInfo?.paymentMethod || undefined, 
                        subTotal: paymentInfo?.subTotal,
                        message: `REFUND ${paymentInfo?.transactionId || booking.transactionId} ${booking?.patient?.surname} ${booking?.partner?.name}`?.toUpperCase(),
                    }),
                    hasBankAccount: requiresBankAccount,
                    cskh: userId,
                    cskhUserId: userId,
                    cancelledBy: cancelledBy,
                    userActionId: cskhInfo?.cskhUserId || userId,
                    status: 0,
                    platform: booking?.platform,
                    transactionId: paymentInfo?.transactionId || booking.transactionId || undefined,
                    bookingCode: overrideBookingCode,
                    bookingDate: booking.date || undefined,
                    bookingCreateAt: booking.createdAt || undefined,
                };

                const cancellationResult = await this.bookingCancellationModel.create([cancellationData], { session });
                cancelReasonId = cancellationResult[0]._id.toString();

                // For backward compatibility, also set bankAccountId if bank info was provided
                if (requiresBankAccount) {
                    bankAccountId = cancelReasonId; // Use same ID since it's all in one document
                }

                // Cập nhật trạng thái phiếu khám
                updateResult = await this.bookingGatewayService.cancelReservation2(
                    booking?.partnerId,
                    appId,
                    userId,
                    booking.id,
                    extraInfo,
                    cskhInfo,
                    platformContext,
                );
            });

            return { cancelReasonId, bankAccountId, updateResult };
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Lưu dữ liệu huỷ đặt lịch thất bại');
        } finally {
            await session.endSession();
        }
    }

    private async executeCancellationTransactionCskh(
        booking: any,
        dto: CancelBookingDto,
        paymentInfo: IPayment | null,
        reasonsData: any[],
        appId: string,
        userId: string,
        extraInfo: any,
        cskhInfo?: CSKHTokenVerifyDTO,
        platformContext: string = '',
    ): Promise<any> {
        const [session, accountTypeObj] = await Promise.all([
            this.connection.startSession(),
            this.globalSettingService.findByKeyAndRepoName('ACCOUNT_TYPE'),
        ]);
        let cancelledBy: string;
        if (extraInfo?.username) {
            cancelledBy = 'PORTAL';
        } else if (cskhInfo?.cskhUserId) {
            cancelledBy = 'CSKH';
        } else {
            cancelledBy = 'USER';
        }
        if (!accountTypeObj) {
            throw new HttpException('Không tìm thấy thông tin loại tài khoản!', HttpStatus.NOT_FOUND);
        }
        const accountType = JSON.parse(accountTypeObj);
        try {
            let cancelReasonId: string;
            let bankAccountId: string | undefined;

            await session.withTransaction(async () => {
                // Kiểm tra xem có cần thông tin tài khoản ngân hàng không
                const requiresBankAccount = await this.requiresBankAccountInfo(
                    paymentInfo?.gatewayId,
                    paymentInfo?.paymentMethodDetail,
                    paymentInfo?.subTotal,
                );

                // Validate bank account info if required
                if (requiresBankAccount) {
                    const missingFields: string[] = [];
                    if (!dto.bankCode || !dto.bankCode?.trim()) {
                        missingFields.push('Mã ngân hàng');
                    }
                    if (!dto.bankName || !dto.bankName?.trim()) {
                        missingFields.push('Tên ngân hàng');
                    }
                    if (!dto.bankNumber || !dto.bankNumber?.trim()) {
                        missingFields.push('Số tài khoản');
                    }
                    if (!dto.accountName || !dto.accountName?.trim()) {
                        missingFields.push('Tên chủ tài khoản');
                    }
                    if (dto?.accountType && !accountType.find((item: any) => item.key === dto.accountType?.trim())) {
                        throw new BadRequestException(`Loại tài khoản không hợp lệ!`);
                    }
                    if (missingFields.length > 0) {
                        throw new BadRequestException(`Vui lòng nhập: ${missingFields.join(', ')} để hoàn tất huỷ phiếu.`);
                    }
                }
                const overrideBookingCode = booking?.bookingCodeV1 || booking?.bookingCode;
                const cancellationData = {
                    bookingId: booking.bookingId,
                    booking: booking._id,
                    partnerId: booking.partnerId,
                    appId: booking.appId,
                    userId: booking.userId,

                    reasonIds: dto.reasonIds,
                    reasonsData: reasonsData,
                    otherContent: dto.otherContent,
                    ...(requiresBankAccount && {
                        bankCode: dto.bankCode?.trim(),
                        bankName: dto.bankName?.trim(),
                        bankNumber: dto.bankNumber?.trim(),
                        accountName: dto.accountName?.trim(),
                        accountType: dto?.accountType?.trim(),
                        gatewayId: paymentInfo?.gatewayId || undefined,
                        paymentMethodDetail: paymentInfo?.paymentMethodDetail || undefined,
                        paymentMethod: paymentInfo?.paymentMethod || undefined, 
                        subTotal: paymentInfo?.subTotal,
                        message: `REFUND ${paymentInfo?.transactionId || booking.transactionId} ${booking?.patient?.surname} ${booking?.partner?.name}`?.toUpperCase(),
                    }),
                    hasBankAccount: requiresBankAccount,
                    cskh: userId,
                    cskhUserId: userId,
                    cancelledBy: cancelledBy,
                    userActionId: cskhInfo?.cskhUserId || userId,
                    status: 0,
                    platform: booking?.platform,
                    transactionId: paymentInfo?.transactionId || booking.transactionId || undefined,
                    bookingCode: overrideBookingCode,
                    bookingDate: booking.date || undefined,
                    bookingCreateAt: booking.createdAt || undefined,
                };

                const cancellationResult = await this.bookingCancellationModel.create([cancellationData], { session });
                cancelReasonId = cancellationResult[0]._id.toString();

                // For backward compatibility, also set bankAccountId if bank info was provided
                if (requiresBankAccount) {
                    bankAccountId = cancelReasonId; // Use same ID since it's all in one document
                }
            });

            return { cancelReasonId, bankAccountId };
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Lưu dữ liệu huỷ đặt lịch thất bại');
        } finally {
            await session.endSession();
        }
    }

    async getCancellationReasons(): Promise<ICancellationReasonConfig[]> {
        try {
            const reasonsJson = await this.globalSettingService.findByKeyAndRepoName('CANCELLATION_REASONS');
            if (!reasonsJson) {
                throw new BadRequestException('Không tìm thấy lý do huỷ trong globalSettingService');
            }

            const reasons = JSON.parse(reasonsJson) as ICancellationReasonConfig[];
            return reasons;
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Không thể lấy lý do huỷ! Vui lòng thử lại sau.');
        }
    }

    async getCancellationReasonsCskh(): Promise<ICancellationReasonConfig[]> {
        try {
            const reasonsJson = await this.globalSettingService.findByKeyAndRepoName('CANCELLATION_REASONS_CSKH');
            if (!reasonsJson) {
                throw new BadRequestException('Không tìm thấy lý do huỷ trong globalSettingService');
            }

            const reasons = JSON.parse(reasonsJson) as ICancellationReasonConfig[];
            return reasons;
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Không thể lấy lý do huỷ! Vui lòng thử lại sau.');
        }
    }

    private validateReasonIds(reasonIds: string[], otherContent: string | undefined, allowedReasons: ICancellationReasonConfig[]): IReasonValidation {
        const errors: string[] = [];

        if (!reasonIds || reasonIds.length === 0) {
            errors.push('Ít nhất một lý do hủy là bắt buộc');
            return { isValid: false, errors };
        }

        // Create a map of allowed reason keys for quick lookup
        const allowedReasonMap = new Map(allowedReasons.map(r => [r.key, r]));

        // Check for duplicates
        const uniqueReasonIds = new Set(reasonIds);
        if (uniqueReasonIds.size !== reasonIds.length) {
            errors.push('Không được chọn trùng lý do');
        }

        // Validate each reason ID
        for (const reasonId of uniqueReasonIds) {
            if (!reasonId || reasonId.trim().length === 0) {
                errors.push('ID lý do không được để trống');
                continue;
            }

            // Check if reason ID is allowed
            const allowedReason = allowedReasonMap.get(reasonId);
            if (!allowedReason) {
                errors.push(`Lý do "${reasonId}" không tồn tại trong cấu hình!`);
                continue;
            }

            // Check if "other" reason requires content
            if (allowedReason.requiresInput && allowedReason.input?.required) {
                if (!otherContent || otherContent.trim().length === 0) {
                    errors.push(`Lý do "${allowedReason.label}" yêu cầu nội dung chi tiết`);
                }
            }
        }

        // Log multiple selection for monitoring
        if (uniqueReasonIds.size > 1) {
            this.logger.log(`Multiple reasons selected: ${Array.from(uniqueReasonIds).join(', ')}`);
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    private buildReasonsData(reasonIds: string[], otherContent: string | undefined, allowedReasons: ICancellationReasonConfig[]): any[] {
        const allowedReasonMap = new Map(allowedReasons.map(r => [r.key, r]));
        const reasonsData = [];

        for (const reasonId of reasonIds) {
            const allowedReason = allowedReasonMap.get(reasonId);
            if (allowedReason) {
                reasonsData.push({
                    id: reasonId,
                    name: allowedReason.label,
                    content: reasonId === 'other' ? otherContent || '' : '',
                });
            }
        }

        return reasonsData;
    }

    async verifyCskhToken(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        return this.patientMongoService.verifyCskhToken(cskhToken);
    }

    async getAccountType(): Promise<any> {
        try {
            const accountTypeJson = await this.globalSettingService.findByKeyAndRepoName('ACCOUNT_TYPE');
            if (!accountTypeJson) {
                throw new BadRequestException('Không tìm thấy loại tài khoản trong globalSettingService');
            }

            const accountType = JSON.parse(accountTypeJson);
            return accountType;
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Không thể lấy loại tài khoản! Vui lòng thử lại sau.');
        }
    }

    handleDateForFilter(fromDate: string, toDate: string) {
        const dateStart = fromDate ? moment(fromDate) : moment();
        const dateEnd = toDate ? moment(toDate) : moment();
        // Handle UTC time
        const timeStart = dateStart
            .set({
                hours: 0,
                minutes: 0,
                seconds: 0,
            })
            .subtract(7, 'hours')
            .toDate();
        const timeEnd = dateEnd
            .set({
                hours: 23,
                minutes: 59,
                seconds: 59,
            })
            .subtract(7, 'hours')
            .toDate();
        return { timeStart, timeEnd };
    }

    async getCancellations(body: BookingQueryDto): Promise<any> {
        try {
            const {
                partnerId,
                fromDate,
                toDate,
                fromBookingDate,
                toBookingDate,
                pageIndex = 0,
                pageSize = 10,
                searchText,
                appId,
                platform,
                cskhUserId,
                reasonIds,
                type,
            } = body;

            //
            const keysNotAllowQuery = ['pageIndex', 'pageSize'];
            const bodyKeys = Object.keys(body);

            if (bodyKeys.length <= keysNotAllowQuery.length) {
                return {
                    rows: [],
                    totalRows: 0,
                    pageIndex,
                    pageSize,
                };
            }

            let query: any = {};

            if (partnerId) {
                query.partnerId = partnerId;
            }

            if (appId) {
                query.appId = appId;
            }

            if (searchText) {
                query.$or = [{ transactionId: { $regex: searchText, $options: 'i' } }, { bookingCode: { $regex: searchText, $options: 'i' } }];
            }

            if (platform) {
                query.platform = platform;
            }

            if (cskhUserId) {
                query.cskhUserId = cskhUserId;
            }

            if (reasonIds) {
                query.reasonIds = { $in: reasonIds };
            }

            if (type !== undefined) {
                if (type === 1) {
                    query.hasBankAccount = true;
                } else if (type === 0) {
                    query.hasBankAccount = false;
                }
            }

            if (fromDate && toDate) {
                const { timeStart, timeEnd } = this.handleDateForFilter(fromDate, toDate);

                query.bookingCreateAt = { $gte: timeStart, $lte: timeEnd };
            }

            if (fromBookingDate && toBookingDate) {
                const { timeStart, timeEnd } = this.handleDateForFilter(fromBookingDate, toBookingDate);

                query.bookingDate = { $gte: timeStart, $lte: timeEnd };
            }

            const totalRows = await this.bookingCancellationModel.countDocuments(query);

            const cancellations = await this.bookingCancellationModel
                .find(query)
                .populate({
                    path: 'booking',
                    populate: [
                        { path: 'service', select: 'name' },
                        { path: 'partner', select: 'name' },
                        { path: 'user', select: 'username fullname email' },
                        { path: 'patient', select: 'name surname mobile code' },
                        { path: 'room', select: 'name' },
                        { path: 'subject', select: 'name' },
                        { path: 'doctor', select: 'name' },
                    ],
                })
                .sort({ cancelledAt: -1 })
                .skip(pageIndex * pageSize)
                .limit(pageSize)
                .lean();

            return {
                rows: cancellations,
                totalRows,
                pageIndex,
                pageSize,
            };
        } catch (error) {
            console.error('Error getting cancellations:', error);
            throw new InternalServerErrorException('Không thể lấy dữ liệu hủy booking. Vui lòng thử lại sau.');
        }
    }

    async getRequiredBankAccount(): Promise<any> {
        try {
            const requiredBankAccountJson = await this.globalSettingService.findByKeyAndRepoName('PAYMENT_METHOD_REQUIRED_REFUND_BANK');
            if (!requiredBankAccountJson) {
                throw new BadRequestException('Không tìm thấy thông tin yêu cầu tài khoản ngân hàng trong globalSettingService');
            }

            const requiredBankAccount = JSON.parse(requiredBankAccountJson);
            return requiredBankAccount;
        } catch (error) {
            const errorMsg = error?.message ? error.message : JSON.stringify(error);
            throw new InternalServerErrorException(errorMsg || 'Không thể lấy thông tin yêu cầu tài khoản ngân hàng! Vui lòng thử lại sau.');
        }
    }

    async exportCancellationsExcel(body: BookingQueryDto): Promise<Buffer> {
        try {
            // Get all data without pagination for export
            const exportBody = { ...body, pageIndex: 0, pageSize: 10000 };
            const { rows: cancellations } = await this.getCancellations(exportBody);

            // Get account type mapping
            const accountTypeObj = await this.globalSettingService.findByKeyAndRepoName('ACCOUNT_TYPE');
            const accountTypes = accountTypeObj ? JSON.parse(accountTypeObj) : {};

            // Create workbook
            const workbook = new Excel.Workbook();
            const worksheet = workbook.addWorksheet('Danh sách hủy booking');

            // Define columns
            worksheet.columns = [
                { header: 'bank_code', key: 'bank_code', width: 20 },
                { header: 'account_type', key: 'account_type', width: 20 },
                { header: 'account_no', key: 'account_no', width: 25 },
                { header: 'account_name', key: 'account_name', width: 30 },
                { header: 'amount', key: 'subTotal', width: 20 },
                { header: 'message', key: 'message', width: 50 },
            ];

            // Add data rows
            cancellations.forEach((cancellation, index) => {
                const accountTypeName = accountTypes[cancellation.accountType] || cancellation.accountType || '';

                worksheet.addRow({
                    bank_code: cancellation.bankCode || '',
                    account_type: accountTypeName || cancellation?.accountType,
                    account_no: cancellation.bankNumber || '',
                    account_name: cancellation.accountName || '',
                    subTotal: cancellation?.subTotal || 0,
                    message: cancellation.message || '',
                });
            });

            // Generate buffer
            const buffer = await workbook.xlsx.writeBuffer();
            return Buffer.from(buffer);
        } catch (error) {
            console.error('Error exporting cancellations to Excel:', error);
            throw new InternalServerErrorException('Không thể xuất file Excel. Vui lòng thử lại sau.');
        }
    }
}
