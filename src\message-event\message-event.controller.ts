import { Body, Controller, Get, HttpCode, Patch, Post, Query } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ApiOperation } from '@nestjs/swagger';
import { MESSAGE_EVENT } from './constant';
import { CreateMessageEventDto } from './dto/create-message-event.dto';
import { MessageEventDTO } from './dto/message-event.dto';
import { RollBackPatientProcessDto } from './dto/rollback-process-patient.dto';
import { MessageEventService } from './message-event.service';

@Controller('message-event')
export class MessageEventController {

    constructor(private service: MessageEventService) {}

    @Get('close-transaction')
    async closeTransaction(
        @Query('transactionId') transactionId: string,
        // @Query('partnerid') partnerId: string,
    ): Promise<any> {
        return this.service.closeTransaction(transactionId);
    }

    @Get('danh-sach')
    @ApiOperation({ summary: 'Lấy danh sách message event ' })
    async getMessageEvents(@Query() fromData: MessageEventDTO): Promise<any> {
        return this.service.getMessageEvents(fromData);
    }

    @Get('process')
    @ApiOperation({ summary: 'Lấy danh sách message event process ' })
    async getMessageEventProcess(@Query() fromData: MessageEventDTO): Promise<any> {
        return this.service.getMessageEventProcess(fromData);
    }

    @Get('process-failed')
    @ApiOperation({ summary: 'Lấy danh sách message event process failed ' })
    async getMessageEventProcessFailed(@Query() fromData: MessageEventDTO): Promise<any> {
        return this.service.getMessageEventProcessFailed(fromData);
    }

    @Get('success')
    @ApiOperation({ summary: 'Lấy danh sách message event success ' })
    async getMessageEventSuccess(@Query() fromData: MessageEventDTO): Promise<any> {
        return this.service.getMessageEventSuccess(fromData);
    }

    @Patch('rollback-process-failed')
    @HttpCode(204)
    @ApiOperation({ summary: 'Rollback message event process  fail' })
    async rollBackSyncBookingFail(
        @Body() fromData: RollBackPatientProcessDto,
    ): Promise<any> {
        return this.service.rollBackMessageEventFailService(fromData.processId);
    }

    // @Post('test')
    // async test(@Body() formData :CreateMessageEventDto): Promise<any> {
    //     this.emitter.emit(MESSAGE_EVENT,formData)
    //     return true;
    // }

    @Get('test')
    async testEvent(): Promise<any> {
        return this.service.testEvent();
    }

    @Get('check-third-party-logs')
    async thirdPartyErrorLogs(): Promise<any> {
        const payload = {
            "urlPaymentHubPay": "https://payment-gateway.medpro.com.vn:1234/payment/v1/payin/pay",
            "paymentHubData": {

                "description": "PKH - Thanh toan Phieu kham benh TT2308042HEHKENAPGXQ",
                "redirectUrl": "https://cs.medpro.com.vn/booking/T2308041F6EFC?mpTransaction=TT2308042HEHKENAPGXQ",
                "gatewayId": "payoo",
                "name": "NGUYEN DANG KHOI",
                "partnerId": "dalieuhcm",
                "paymentType": "atm",
                "transactionId": "TT2308042HEHKENAPGXQ",
                "userId": "64ccd47c7a821400190c5181",
                "bankCode": "SGB",
                "email": "",
                "country": "Việt Nam",
                "city": "Tỉnh Bình Phước",
                "phone": "**********",
                "address": "., Xã Đức Liễu, Huyện Bù Đăng",
                "platform": "pc",
                "systemId": "v2",
                "feeInfo": {
                    "name": "SAIGONBANK",
                    "methodName": "Thanh toán bằng Thẻ ATM nội địa/Internet Banking",
                    "code": "SGB",
                    "paymentIcon": {
                        "path": "https://api-v2.medpro.com.vn:5000/st/bank/sgb_logo.png",
                        "width": "",
                        "height": ""
                    },
                    "gatewayId": "payoo",
                    "paymentPartnerId": "payoo",
                    "templateId": "",

                    "type": "atm",
                    "highlight": false,
                    "imageURL": "",
                    "appIds": "",
                    "platforms": "",
                    "treeIds": "",
                    "countries": [],
                    "bannerURL": "",
                    "description": "",
                    "feeServiceCode": "DVTI",
                    "separateMedproFeeAtGateway": false,
                    "separateFeeMedproFeeAtGateway": false,
                    "separateGatewayFeeAtGateway": false,
                    "payMedproFeeAtHospital": false,
                    "subTotalNote": "",
                    "totalFeeNote": ""
                },

            },
            "errorBody": {
                "data": {
                    "timestamp": "2023-08-04T11:11:09.562+00:00",

                    "error": "Internal Server Error",
                    "path": "/payment/v1/payin/pay"
                },
                "config": {
                    "url": "",
                    "method": "",
                    "data": {
                        "timestamp": "2023-08-04T11:11:09.562+00:00",

                        "error": "Internal Server Error",
                        "path": "/payment/v1/payin/pay"
                    },
                    "headers": {
                        "server": "nginx/1.16.1",
                        "date": "Fri, 04 Aug 2023 11:11:09 GMT",
                        "content-type": "application/json",
                        "transfer-encoding": "chunked",
                        "connection": "close",
                        "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers"
                    }
                }
            },
            "message": "Request failed with status code 500",

        }

        return this.service.thirdPartyErrorLogs(payload);
    }
}



