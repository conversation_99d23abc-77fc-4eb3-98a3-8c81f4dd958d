import { Document } from 'mongoose';

export interface IBookingOrder extends Document {
    id?: string;
    readonly orderCode: string;
    readonly transactionId: string;
    readonly status: number;
    readonly paymentStatus: number;
    paymentMessage?: string;
    readonly patientId: string;
    patient?: string;
    patientVersionId: string;
    patientVersion: string;
    readonly userId: string;
    readonly partnerId: string;
    partner?: string;
    readonly appId: string;
    readonly platform: string;
    readonly invoiceId: string;
    readonly invoiceCode: string;
    visible: boolean;
    checkInRoom: object;
    syncStatus?: string;
    syncDate?: Date;
    note?: string;
    content: string;
    cskhUserId: string;
    isCashBack?:boolean
}
