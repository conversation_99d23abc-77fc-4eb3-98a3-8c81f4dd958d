import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SHARE_BOOKING } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const ShareBookingSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String, required: true },
    bookingId: { type: String, required: true },
    token: { type: String, required: true },
    link: { type: String },
    active: { type: Boolean, default: true },
}, {
    collection: SHARE_BOOKING,
    timestamps: true,
}).plugin(jsonMongo);
