import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Jo<PERSON> from 'joi';
import { KnexOptions } from './thuDucHospitalConnection/index';

@Injectable()
export class ThuDucHospitalConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            THU_DUC_HOSPITAL_HOST: {
                validate: Joi.string(),
                required: false,
                default: 'localhost',
            },
            THU_DUC_HOSPITAL_PORT: {
                validate: Joi.number(),
                required: false,
                default: 5432,
            },
            THU_DUC_HOSPITAL_USER: {
                validate: Joi.string(),
                required: true,
            },
            THU_DUC_HOSPITAL_DATABASE: {
                validate: Joi.string(),
                required: true,
            },
            /* Thông tin SMS brand name */
            THU_DUC_SMS_BRAND_NAME_API: {
                validate: Joi.string(),
                required: true,
            },
            THU_DUC_SMS_BRAND_NAME_USERNAME: {
                validate: Joi.string(),
                required: true,
            },
            THU_DUC_SMS_BRAND_NAME_PASSWORD: {
                validate: Joi.string(),
                required: true,
            },
            THU_DUC_SMS_BRAND_NAME_TITLE_FROM: {
                validate: Joi.string(),
                required: true,
            },
            THU_DUC_SMS_BRAND_NAME_BID: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createKnexOptions(): KnexOptions {
        return {
            client: 'mysql',
            debug: false,
            connection: {
                host: this.get<string>('THU_DUC_HOSPITAL_HOST'),
                user: this.get<string>('THU_DUC_HOSPITAL_USER'),
                password: this.get<string>('THU_DUC_HOSPITAL_PASSWORD'),
                database: this.get<string>('THU_DUC_HOSPITAL_DATABASE'),
                port: this.get<number>('THU_DUC_HOSPITAL_PORT'),
            },
        };
    }

    getSMSBrandNameInfo() {
        return {
            url: this.get<string>('THU_DUC_SMS_BRAND_NAME_API'),
            u: this.get<string>('THU_DUC_SMS_BRAND_NAME_USERNAME'),
            pwd: this.get<string>('THU_DUC_SMS_BRAND_NAME_PASSWORD'),
            from: this.get<string>('THU_DUC_SMS_BRAND_NAME_TITLE_FROM'),
            phone: '',
            sms: '',
            bid: this.get<string>('THU_DUC_SMS_BRAND_NAME_BID'),
        };
    }
}
