import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { CONVERT_USER_CSKH } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const ConvertUserCskhSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String, unique: true, required: true },
    username: { type: String },
    email: { type: String },
    phone: { type: String },
    fullname: { type: String },
    isConverted: { type: Boolean, default: false },
}, {
    collection: CONVERT_USER_CSKH,
    timestamps: true,
}).plugin(jsonMongo);
