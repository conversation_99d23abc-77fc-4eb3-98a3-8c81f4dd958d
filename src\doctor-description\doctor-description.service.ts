import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { IDoctorDescription } from './interface/doctor-description.inteface';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME } from './schema/constants';
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { IDoctor } from '../doctor-mongo/interfaces/doctor.interface';

@Injectable()
export class DoctorDescriptionService {
    private logger = new Logger(DoctorDescriptionService.name);

    constructor(
        @InjectModel(DOCTOR_DESCRIPTION_COLLECTION_NAME) private model: Model<IDoctorDescription>,
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
    ) {}

    async get(doctorId: string) {
        try {
            const data = await this.model
                .findOne({ doctorId })
                .lean()
                .exec();

            if (!data) {
                throw new HttpException(`Không tìm thấy thông tin`, HttpStatus.NOT_FOUND);
            }
            return data;
        } catch (e) {
            if (e.status == HttpStatus.NOT_FOUND) {
                throw e;
            }
            throw new HttpException(`Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getAvailableDoctor() {
        return this.model.find({ isDetailVisible: true }, { slug: true }).lean();
    }

    async seedDataDoctorDescription(formData: any): Promise<any> {
        try {
            const doctors = await this.doctorModel.find().exec();
            if (!doctors || doctors.length === 0) {
                return { isOk: false, message: 'Không có bác sĩ nào để seed dữ liệu' };
            }
            for await (const doctor of doctors) {
                try {
                    const findDoctorDes = await this.model.findOne({ doctorId: doctor._id }).exec();
                    if (!findDoctorDes) {
                        const newData = new this.model({
                            doctorId: doctor._id,
                        });
                        await newData.save();
                    }
                } catch (error) {
                    console.log('error0', JSON.stringify(error, null, 2))
                }
            }

            return { isOk: true };
        } catch (error) {
            console.log('error', JSON.stringify(error, null, 2))
            return {
                isOK: false,
                message: error.message || 'seedCountBookingSubjectPartner thất bại',
            };
        }
    }
}
