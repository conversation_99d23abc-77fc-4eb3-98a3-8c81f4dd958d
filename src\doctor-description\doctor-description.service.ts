import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { IDoctorDescription } from './interface/doctor-description.inteface';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME } from './schema/constants';

@Injectable()
export class DoctorDescriptionService {
    private logger = new Logger(DoctorDescriptionService.name);

    constructor(@InjectModel(DOCTOR_DESCRIPTION_COLLECTION_NAME) private model: Model<IDoctorDescription>) {}

    async get(doctorId: string) {
        try {
            const data = await this.model
                .findOne({ doctorId })
                .lean()
                .exec();

            if (!data) {
                throw new HttpException(`Không tìm thấy thông tin`, HttpStatus.NOT_FOUND);
            }
            return data;
        } catch (e) {
            if (e.status == HttpStatus.NOT_FOUND) {
                throw e;
            }
            throw new HttpException(`<PERSON><PERSON> thống chưa xử lý được thao tác n<PERSON>, vui lòng kiểm tra lại!`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getAvailableDoctor() {
        return this.model.find({ isDetailVisible: true }, { slug: true }).lean();
    }
}
