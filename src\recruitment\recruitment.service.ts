import { HttpException, HttpService, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { QueryRecruitmentDto } from './dto/query-recruitment.dto';
import { RecruitmentDto } from './dto/recruitment.dto';
import { IRecruitment } from './interfaces/recruitment.inteface';
import { APPLY_JOB_COLLECTION_NAME, RECRUITMENT_COLLECTION_NAME } from './schemas/constants';
import moment = require('moment');
import { IApplyJob } from './interfaces/apply-job.inteface';
import { ApplyJobDto } from './dto/apply-job.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PUSH_NOTIF_LARK_APPLY_RECRUITMENT } from 'src/booking-gateway/schemas/constants';
import { ConfigRecaptchaService } from 'src/config/config.recapcha.service';

@Injectable()
export class RecruitmentService {
    constructor(
        @InjectModel(RECRUITMENT_COLLECTION_NAME) private readonly RecruitmentModel: Model<IRecruitment>,
        @InjectModel(APPLY_JOB_COLLECTION_NAME) private readonly applyJobModel: Model<IApplyJob>,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        private globalSettingService: GlobalSettingService,
        private eventEmmiter: EventEmitter2,
        private readonly recaptchaConfig: ConfigRecaptchaService,
        private readonly httpService: HttpService,
    ) {}

    async createRecruitment(dto: RecruitmentDto): Promise<any> {
        try {
            // Lấy danh sách jobNature từ globalSettingService
            const [cityInfo, listJobResponseObj, workingFormObj] = await Promise.all([
                this.cityModel.findOne({ id: dto?.cityId }).exec(),
                this.globalSettingService.findByKeyAndRepoName('LIST_JOB_NATURE'),
                this.globalSettingService.findByKeyAndRepoName('WORKING_FORM_CONFIG'),
            ]);

            let jobNatureList = [];
            if (listJobResponseObj) {
                jobNatureList = JSON.parse(listJobResponseObj);
            }

            let workingFormList = [];
            if (workingFormObj) {
                workingFormList = JSON.parse(workingFormObj);
            }

            // Kiểm tra tính hợp lệ của jobNature
            const jobNature = jobNatureList.find(item => item.key === dto.jobNature);
            if (!jobNature) {
                throw new HttpException(`Tính chất công việc không hợp lệ!`, HttpStatus.BAD_REQUEST);
            }

            const workingForm = workingFormList.find(item => item.key === dto.workingForm);
            if (!workingForm) {
                throw new HttpException(`Hình thức làm việc không hợp lệ!`, HttpStatus.BAD_REQUEST);
            }

            if (!cityInfo) {
                throw new HttpException('Không tìm thấy thông tin tỉnh thành', HttpStatus.BAD_REQUEST);
            }

            // Kiểm tra trùng title
            const existingRecruitment = await this.RecruitmentModel.findOne({ title: dto.title }).exec();
            if (existingRecruitment) {
                throw new HttpException(`Công việc với tiêu đề ${dto.title} đã tồn tại!`, HttpStatus.BAD_REQUEST);
            }

            const newRecruitment = new this.RecruitmentModel({ ...dto, jobNature: jobNature, workingForm: workingForm, city: cityInfo?._id });
            return await newRecruitment.save();
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi tạo công việc!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async updateRecruitment(id: string, dto: RecruitmentDto): Promise<any> {
        try {
            const [Recruitment, cityInfo, listJobResponseObj, workingFormObj] = await Promise.all([
                this.RecruitmentModel.findById(id).exec(),
                this.cityModel.findOne({ id: dto?.cityId }).exec(),
                this.globalSettingService.findByKeyAndRepoName('LIST_JOB_NATURE'),
                this.globalSettingService.findByKeyAndRepoName('WORKING_FORM_CONFIG'),
            ]);
            if (!Recruitment) {
                throw new HttpException(`Công việc với ID ${id} không tồn tại!`, HttpStatus.NOT_FOUND);
            }
            // Lấy danh sách jobNature từ globalSettingService
            let jobNatureList = [];
            if (listJobResponseObj) {
                jobNatureList = JSON.parse(listJobResponseObj);
            }

            let workingFormList = [];
            if (workingFormObj) {
                workingFormList = JSON.parse(workingFormObj);
            }

            const workingForm = workingFormList.find(item => item.key === dto.workingForm);
            if (!workingForm) {
                throw new HttpException(`Hình thức làm việc không hợp lệ!`, HttpStatus.BAD_REQUEST);
            }

            // Kiểm tra tính hợp lệ của jobNature
            const jobNature = jobNatureList.find(item => item.key === dto.jobNature);
            if (!jobNature) {
                throw new HttpException(`Tính chất công việc không hợp lệ!`, HttpStatus.BAD_REQUEST);
            }

            if (!cityInfo) {
                throw new HttpException('Không tìm thấy thông tin tỉnh thành', HttpStatus.BAD_REQUEST);
            }

            // Kiểm tra trùng title nếu title được cập nhật
            if (dto.title && dto.title !== Recruitment.title) {
                const existingRecruitment = await this.RecruitmentModel.findOne({ title: dto.title }).exec();
                if (existingRecruitment) {
                    throw new HttpException(`Công việc với tiêu đề ${dto.title} đã tồn tại!`, HttpStatus.BAD_REQUEST);
                }
            }

            // Cập nhật các trường
            Object.assign(Recruitment, { ...dto, jobNature: jobNature, workingForm: workingForm, city: cityInfo?._id });
            return await Recruitment.save();
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi cập nhật công việc!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async deleteRecruitment(id: string): Promise<any> {
        try {
            const result = await this.RecruitmentModel.findByIdAndDelete(id).exec();
            if (!result) {
                throw new HttpException(`Công việc với ID ${id} không tồn tại!`, HttpStatus.NOT_FOUND);
            }
            return {
                message: 'Xóa công việc thành công!',
                statusCode: HttpStatus.OK,
            };
        } catch (error) {
            console.log('error', error);
            throw new HttpException(error.message || 'Lỗi xóa công việc!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getJobById(id: string): Promise<any> {
        try {
            const recruitment = await this.RecruitmentModel.findById(id)
                .populate({ path: 'city', select: { name: true, id: true, partnerId: true } })
                .exec();
            if (!recruitment) {
                throw new HttpException(`Công việc với ID ${id} không tồn tại!`, HttpStatus.NOT_FOUND);
            }
            return recruitment;
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi lấy công việc!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getAllRecruitments(query: QueryRecruitmentDto): Promise<{ rows: any[]; totalRow: number }> {
        try {
            const filter: any = {};

            // Lọc theo khu vực (location)
            if (query?.cityId) {
                filter.cityId = query.cityId;
            }

            if (query?.status) {
                filter.status = query.status === 'true';
            }

            // Lọc theo tính chất công việc (jobNature)
            if (query?.jobNature) {
                filter['jobNature.key'] = query.jobNature;
            }

            // Lọc theo tính chất công việc (jobNature)
            if (query?.workingForm) {
                filter['workingForm.key'] = query.workingForm;
            }

            if (query?.createdAtFrom && query?.createdAtTo) {
                const { timeStart, timeEnd } = this.handleDateForFilter(query?.createdAtFrom, query?.createdAtTo);
                filter.createdAt = { $gte: timeStart, $lte: timeEnd };
            }

            if (query?.deadlineFrom && query?.deadlineTo) {
                const { timeStart, timeEnd } = this.handleDateForFilter(query?.deadlineFrom, query?.deadlineTo);
                filter.deadline = { $gte: timeStart, $lte: timeEnd };
            }
            if (query?.title) {
                filter.title = { $regex: new RegExp(query.title, 'i') };
            }

            // Xử lý phân trang
            const page = parseInt(query.page as any) || 1;
            const limit = parseInt(query.limit as any) || 20;
            const skip = (page - 1) * limit;

            const [data, total] = await Promise.all([
                this.RecruitmentModel.find(filter)
                    .populate({ path: 'city', select: { name: true, id: true, partnerId: true } })
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.RecruitmentModel.countDocuments(filter).exec(),
            ]);

            return { rows: data, totalRow: total };
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi lấy danh sách công việc!', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    handleDateForFilter(fromDate: string, toDate: string) {
        const dateStart = fromDate ? moment(fromDate) : moment();
        const dateEnd = toDate ? moment(toDate) : moment();
        // Handle UTC time
        const timeStart = dateStart
            .set({
                hours: 0,
                minutes: 0,
                seconds: 0,
            })
            .subtract(7, 'hours')
            .toDate();
        const timeEnd = dateEnd
            .set({
                hours: 23,
                minutes: 59,
                seconds: 59,
            })
            .subtract(7, 'hours')
            .toDate();
        return { timeStart, timeEnd };
    }

    async getlistNature(): Promise<any[]> {
        try {
            const listJobResponseObj = await this.globalSettingService.findByKeyAndRepoName('LIST_JOB_NATURE');
            let jobNatureList = [];
            if (listJobResponseObj) {
                jobNatureList = JSON.parse(listJobResponseObj);
            }
            return jobNatureList;
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi lấy danh sách công việc!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getWorkingForm(): Promise<any[]> {
        try {
            const workingFormObj = await this.globalSettingService.findByKeyAndRepoName('WORKING_FORM_CONFIG');
            let workingForm = [];
            if (workingFormObj) {
                workingForm = JSON.parse(workingFormObj);
            }
            return workingForm;
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi lấy hình thức làm việc!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getlistPlatform(): Promise<any[]> {
        try {
            const platformObj = await this.globalSettingService.findByKeyAndRepoName('PLATFORM_RECRUITMENT_CONFIG');
            let platform = [];
            if (platformObj) {
                platform = JSON.parse(platformObj);
            }
            return platform;
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi lấy danh sách nền tảng!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async saveApplication(body: ApplyJobDto): Promise<any> {
        try {
            if (body?.captchaResponse) {
                await this.verifyGoogleRecaptcha({ captchaResponse: body.captchaResponse });
            } else {
                throw new HttpException('Thiếu thông tin xác thực, vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }

            const { fullname, phone, email, platform, coverLetter, cvFilePath, recruitmentId } = body;

            // 1. Kiểm tra tuyển dụng có tồn tại
            const recruitment = await this.RecruitmentModel.findById(recruitmentId).exec();
            if (!recruitment) {
                throw new HttpException(`Không tìm thấy thông tin tuyển dụng với ID: ${recruitmentId}`, HttpStatus.NOT_FOUND);
            }

            // 2. Kiểm tra deadline (nếu có)
            if (recruitment?.deadline && new Date(recruitment?.deadline) < new Date()) {
                throw new HttpException(`Tin tuyển dụng này đã hết hạn vào ${recruitment.deadline}`, HttpStatus.BAD_REQUEST);
            }

            // 3. Kiểm tra đơn đã tồn tại với cùng email + recruitmentId
            const existingApplication = await this.applyJobModel.findOne({ email, recruitmentId }).exec();
            if (existingApplication) {
                throw new HttpException(`Bạn đã ứng tuyển cho công việc này với email ${email} rồi. Vui lòng kiểm tra lại!`, HttpStatus.BAD_REQUEST);
            }

            // 4. Lưu đơn ứng tuyển mới
            const newApplication = await this.applyJobModel.create({
                fullname,
                phone,
                email,
                platform,
                coverLetter,
                cvFilePath,
                recruitmentId,
            });
            try {
                this.eventEmmiter.emit(PUSH_NOTIF_LARK_APPLY_RECRUITMENT, {
                    fullname: fullname,
                    email: email,
                    phone: phone,
                    platform: platform,
                    coverLetter: coverLetter,
                    cvFilePath: cvFilePath,
                    jobTitle: recruitment?.title,
                });
            } catch (err) {
                console.error(`logBookingData: ${err.message}`, err.stack);
            }
            return newApplication;
        } catch (error) {
            console.error('Lỗi khi lưu đơn ứng tuyển:', error);
            throw new HttpException(error.message || 'Lỗi lưu đơn ứng tuyển!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async verifyGoogleRecaptcha(formData: { captchaResponse: string }) {
        try {
            const { data } = await this.httpService
                .post(
                    'https://www.google.com/recaptcha/api/siteverify',
                    {},
                    {
                        params: {
                            secret: this.recaptchaConfig.getSecret(),
                            response: formData.captchaResponse,
                        },
                    },
                )
                .toPromise();

            if (data.success !== true) {
                throw new HttpException({ message: 'Lỗi xác thực reCAPTCHA, vui lòng thử lại!', recaptchaError: true }, HttpStatus.BAD_REQUEST);
            }
        } catch (err) {
            if (err instanceof HttpException) {
                throw err;
            }

            console.error('error verifyGoogleRecapcha: ', JSON.stringify(err, null, 2));

            throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
        }
    }
}
