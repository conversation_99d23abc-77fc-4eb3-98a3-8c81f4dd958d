import { GlobalSettingService } from './../global-setting/global-setting.service';
import { Injectable, HttpException, HttpStatus, Inject, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
    EVENT_COLLECTION_NAME, MESSAGE_SEND_RECORD_COLLECTION_NAME,
    MESSAGE_SEND_CONFIG_COLLECTION_NAME, SYNC_BOOKING_PROCESS, SYNC_BOOKING_SUCCESS,
    SYNC_BOOKING_PROCESS_FAILED, SYNC_PATIENT_PROCESS, SYNC_PATIENT_PROCESS_FAILED, SYNC_PATIENT_SUCCESS,
    SYNC_BOOKING_DATE_PROCESS, SYNC_BOOKING_DATE_PROCESS_FAILED, SYNC_BOOKING_DATE_SUCCESS,
    SYNC_SKIN_BOOKING_PROCESS, SYNC_SKIN_BOOKING_PROCESS_FAILED, SY<PERSON>_SKIN_BOOKING_SUCCESS,
    SY<PERSON>_SKIN_PATIENT_PROCESS, SYNC_USER_PROCESS, SY<PERSON>_USER_PROCESS_FAILED, SYNC_USER_SUCCESS,
    SYNC_V2_BOOKING, SYNC_DALIEU_PATIENT, SYNC_DALIEU_PATIENT_PROCESS, SYNC_DALIEU_PATIENT_PROCESS_FAILED, SYNC_DALIEU_PATIENT_SUCCESS,
    SYNC_DALIEU_BOOKING_PROCESS, SYNC_DALIEU_BOOKING_PROCESS_FAILED, SYNC_DALIEU_BOOKING_SUCCESS, SYNC_DALIEU_BOOKING, SYNC_ND1_BOOKING_PROCESS,
    SEND_MAIL_OR_SMS, ADDRESS_TRACKING_COLLECTION_NAME, SYNC_NHI_DONG_1_PATIENT_PROCESS, SYNC_NHI_DONG_1_PATIENT, SYNC_NHI_DONG_1_PATIENT_SUCCESS,
    SYNC_NHI_DONG_1_PATIENT_PROCESS_FAILED, SYNC_NHI_DONG_1_BOOKING, SYNC_NHI_DONG_1_BOOKING_PROCESS,
    SYNC_NHI_DONG_1_BOOKING_PROCESS_FAILED, SYNC_NHI_DONG_1_BOOKING_SUCCESS, SYNC_ND1_BOOKING_PROCESS_FAILED, SYNC_ND1_BOOKING_SUCCESS,
    SYNC_DHYD_PATIENT, SYNC_DHYD_PATIENT_PROCESS, SYNC_DHYD_PATIENT_SUCCESS, SYNC_DHYD_PATIENT_PROCESS_FAILED, SYNC_DHYD_BOOKING, SYNC_DHYD_BOOKING_SUCCESS,
    SYNC_DHYD_BOOKING_PROCESS_FAILED, SYNC_DHYD_BOOKING_PROCESS, NOTIFICATION_COLLECTION, MESSAGE_EVENT_COLLECTION, MESSAGE_EVENT_PROCESS_COLLECTION, MESSAGE_EVENT_PROCESS_FAILED_COLLECTION, MESSAGE_EVENT_SUCCESS_COLLECTION,
    PUSH_NOTIF_INFORM_COLLECTION_NAME,
} from './schemas/constants';
import {Model, Types} from 'mongoose';
import { IEvent } from './intefaces/event.inteface';
import * as uuid from 'uuid';
import * as moment from 'moment';
import * as OneSignal from 'onesignal-node';
import { CreateEventDTO } from './dto/create-event.dto';
import { groupBy, map, find, first, get, uniqBy, chunk, last, set } from 'lodash';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { IPushDevice } from 'src/push-device/intefaces/push-device.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { MailerService } from 'src/mailer/mailer.service';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME, BOOKING_DATE_CHANGE_EVENT_NAME } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { UrlConfigService } from 'src/config/config.url.service';
import { PkhHttpService } from 'src/config/config.http.service';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import { RECEIVER_VERSION_COLLECTION_NAME, RECEIVER_LIST_COLLECTION_NAME, MESSAGE_ITEM_COLLECTION_NAME } from 'src/message-config/schemas/constants';
import { IReceiverVersion } from 'src/message-config/intefaces/receiver-version.inteface';
import { IReceiverList } from 'src/message-config/intefaces/receiver-list.inteface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { IMessageItem } from 'src/message-config/intefaces/message-item.inteface';
import { IMessageSendRecord } from './intefaces/message-send-record.inteface';
import { IMessageSendConfig } from './intefaces/message-send-config.inteface';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { PATIENT_COLLECTION_NAME, PATIENT_CODE_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatientVersion } from 'src/patient-mongo/intefaces/patient-version.inteface';
import { IPatientCodes } from 'src/patient-mongo/intefaces/patient-codes.inteface';
import { EventChannel } from 'src/event-proccessor/dto/event-channel.dto';
import { isEmail, isMobilePhone } from 'class-validator';
import { SyncBookingService } from 'src/sync-booking/sync-booking.service';
import { ISyncBookingProcess } from './intefaces/sync-booking-process.inteface';
import { ISyncBookingSuccess } from './intefaces/sync-booking-success.inteface';
import { ISyncBookingProcessFailed } from './intefaces/sync-booking-process-failed.inteface';
import { SyncProcessStatus } from './dto/sync-status.dto';
import { SentryService, InjectSentry } from '@ntegral/nestjs-sentry';
import { ISyncPatientProcess } from './intefaces/sync-patient-process.inteface';
import { ISyncPatientProcessFailed } from './intefaces/sync-patient-process-failed.inteface';
import { ISyncPatientSuccess } from './intefaces/sync-patient-success.inteface';
import { ISyncBookingDateProcess } from './intefaces/sync-booking-date-process.inteface';
import { ISyncBookingDateProcessFailed } from './intefaces/sync-booking-date-process-failed.inteface';
import { ISyncBookingDateSuccess } from './intefaces/sync-booking-date-success.inteface';
import { IBookingDateChangeEvent } from 'src/booking-gateway/intefaces/booking-date-change-event.inteface';
import { EventProccessorService } from 'src/event-proccessor/event-proccessor.service';
import { ISyncSkinBookingProcess } from './intefaces/sync-skin-booking-process.inteface';
import { ISyncSkinBookingSuccess } from './intefaces/sync-skin-booking-success.inteface';
import { ISyncSkinBookingProcessFailed } from './intefaces/sync-skin-booking-process-failed.inteface';
import { ISyncSkinPatientProcess } from './intefaces/sync-skin-patient-process.inteface';
import { ISyncSkinPatientProcessFailed } from './intefaces/sync-skin-patient-process-failed.inteface';
import { ISyncSkinPatientSuccess } from './intefaces/sync-skin-patient-success.inteface';
import { ISyncUserProcess } from './intefaces/sync-user-process.inteface';
import { ISyncUserProcessFailed } from './intefaces/sync-user-process-failed.inteface';
import { ISyncUserSuccess } from './intefaces/sync-user-success.inteface';
import { SyncUserService } from 'src/sync-user/sync-user.service';
import {
    SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME, SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME,
    SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME,
    SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME,
} from 'src/sync-trung-vuong-medpro/schemas/constants';
import { ISyncUserTrungVuongProcess } from 'src/sync-trung-vuong-medpro/interfaces/sync-user-trungvuong-process.inteface';
import { ISyncUserTrungVuongSuccess } from 'src/sync-trung-vuong-medpro/interfaces/sync-user-trungvuong-success.inteface';
import { ISyncUserTrungVuongUpgrade } from 'src/sync-trung-vuong-medpro/interfaces/sync-user-trungvuong-upgrade.inteface';
import {
    SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME, SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME,
    SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME, SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME,
} from 'src/sync-da-lieu-medpro/schemas/constants';
import { ISyncUserDaLieuUpgrade } from 'src/sync-da-lieu-medpro/interfaces/sync-user-dalieu-upgrade.inteface';
import { ISyncUserDaLieuProcess } from 'src/sync-da-lieu-medpro/interfaces/sync-user-dalieu-process.inteface';
import { ISyncUserDaLieuProcessFailed } from 'src/sync-da-lieu-medpro/interfaces/sync-user-dalieu-process-failed.inteface';
import { ISyncUserDaLieuSuccess } from 'src/sync-da-lieu-medpro/interfaces/sync-user-dalieu-success.inteface';
import { ISyncV2Booking } from './intefaces/sync-v2-booking.inteface';
import { ISyncDaLieuPatient } from './intefaces/sync-dalieu-patient.inteface';
import { ISyncDaLieuPatientProcess } from './intefaces/sync-dalieu-patient-process.inteface';
import { ISyncDaLieuPatientProcessFailed } from './intefaces/sync-dalieu-patient-process-failed.inteface';
import { ISyncDaLieuPatientSuccess } from './intefaces/sync-dalieu-patient-success.inteface';
import { ISyncDaLieuBookingProcess } from './intefaces/sync-dalieu-booking-process.inteface';
import { ISyncDaLieuBookingProcessFailed } from './intefaces/sync-dalieu-booking-process-failed.inteface';
import { ISyncDaLieuBookingSuccess } from './intefaces/sync-dalieu-booking-success.inteface';
import { ISyncDaLieuBooking } from './intefaces/sync-dalieu-booking.inteface';
import { ISendMailOrSMS } from './intefaces/send-mail-or-sms.inteface';
import { ISyncNhiDong1PatientProcess } from './intefaces/sync-nhidong1-patient-process.inteface';
import { ISyncNhiDong1Patient } from './intefaces/sync-nhidong1-patient.inteface';
import { ISyncNhiDong1PatientSuccess } from './intefaces/sync-nhidong1-patient-success.inteface';
import { ISyncNhiDong1PatientProcessFailed } from './intefaces/sync-nhidong1-patient-process-failed.inteface';
import { ISyncNhiDong1Booking } from './intefaces/sync-nhidong1-booking.inteface';
import { ISyncNhiDong1BookingProcess } from './intefaces/sync-nhidong1-booking-process.inteface';
import { ISyncNhiDong1BookingProcessFailed } from './intefaces/sync-nhidong1-booking-process-failed.inteface';
import { ISyncNhiDong1BookingSuccess } from './intefaces/sync-nhidong1-booking-success.inteface';
import { ISyncNd1BookingSuccess } from './intefaces/sync-nd1-booking-success.inteface';
import { ISyncNd1BookingProcessFailed } from './intefaces/sync-nd1-booking-process-failed.inteface';
import { ISyncNd1BookingProcess } from './intefaces/sync-nd1-booking-process.inteface';
import { ISyncDHYDPatient } from './intefaces/sync-dhyd-patient.inteface';
import { ISyncDHYDPatientProcess } from './intefaces/sync-dhyd-patient-process.inteface';
import { ISyncDHYDPatientSuccess } from './intefaces/sync-dhyd-patient-success.inteface';
import { ISyncDHYDPatientProcessFailed } from './intefaces/sync-dhyd-patient-process-failed.inteface';
import { ISyncDHYDBooking } from './intefaces/sync-dhyd-booking.inteface';
import { ISyncDHYDBookingProcess } from './intefaces/sync-dhyd-booking-process.inteface';
import { ISyncDHYDBookingSuccess } from './intefaces/sync-dhyd-booking-success.inteface';
import { ISyncDHYDBookingProcessFailed } from './intefaces/sync-dhyd-booking-process-failed.inteface';
import { SearchEventByUserPhoneDTO } from './dto/search-event.dto';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { EventResponseDTO } from './dto/event-response.dto';
import { IEventResponse } from './intefaces/event-response.interface';
import { TransformDataToNotifDTO } from './dto/transform-data-to-notif.dto';
import { BookingStatus } from 'src/his-gateway/dto/bookingStatus.dto';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { UtilService } from 'src/config/util.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_REQUEST_EVENT, LOG_SERVICE_EVENT, SERVICE_LOG_NAME } from 'src/audit-log/constant';
import { IMessageEvent } from './intefaces/message-event.interface';
import { IMessageEventProcess } from './intefaces/message-event-process.interface';
import { IMessageEventProcessFailed } from './intefaces/message-event-process-failed.interface';
import { IMessageEventSuccess } from './intefaces/message-event-success.interface';
import { MESSAGE_EVENT, TransporterEvent } from 'src/message-event/constant';
import { SENDGRID_CONNECTION } from 'src/config/sendgridConnection';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { JwtService } from '@nestjs/jwt';
import * as querystring from 'querystring';
import { IPushNotifInfom } from './intefaces/push-notif-inform.inteface';
import { REPO_NAME_BETA } from '../common/constants';

@Injectable()
export class EventService {

    private logger = new Logger(EventService.name);
    private userTableName = 'user';
    private hospitalSubjectTableName = 'hospital_subject';
    private bookingTableName = 'booking';
    private scheduleTableName = 'schedule';
    private syncV2UserTableName = 'sync_v2_user';
    private pushDeviceTableName = 'push_device';
    private syncV2BookingTableName = 'sync_v2_booking';
    private syncV2PatientTableName = 'sync_v2_patient';
    private syncV2SkinBookingTableName = 'sync_v2_skin_booking_1';
    private syncV2Nd1BookingTableName = 'sync_v2_nd1_booking';
    private syncV2SkinPatientTableName = 'sync_v2_skin_patient';
    private umcUserPatient = 'user_patient';
    private stt: number = 61;
    private eventProcessorApi: string = '';
    private switchEventNotif: boolean;
    private trackingInfo: any = {};
    private isEnableBookingTransform: boolean;
    private readonly repoName: string;
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(EVENT_COLLECTION_NAME) private eventModel: Model<IEvent>,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(RECEIVER_VERSION_COLLECTION_NAME) private receiverVersionModel: Model<IReceiverVersion>,
        @InjectModel(RECEIVER_LIST_COLLECTION_NAME) private receiverListModel: Model<IReceiverList>,
        @InjectModel(MESSAGE_ITEM_COLLECTION_NAME) private messageItemModel: Model<IMessageItem>,
        @InjectModel(MESSAGE_SEND_RECORD_COLLECTION_NAME) private messageSendRecordModel: Model<IMessageSendRecord>,
        @InjectModel(MESSAGE_SEND_CONFIG_COLLECTION_NAME) private messageSendConfigModel: Model<IMessageSendConfig>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private patientVersionModel: Model<IPatientVersion>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(SYNC_V2_BOOKING) private syncV2BookingModel: Model<ISyncV2Booking>,
        @InjectModel(SYNC_BOOKING_PROCESS) private syncBookingProcessModel: Model<ISyncBookingProcess>,
        @InjectModel(SYNC_BOOKING_PROCESS_FAILED) private syncBookingProcessFailedModel: Model<ISyncBookingProcessFailed>,
        @InjectModel(SYNC_BOOKING_SUCCESS) private syncBookingSuccessModel: Model<ISyncBookingSuccess>,
        @InjectModel(SYNC_SKIN_BOOKING_PROCESS) private syncSkinBookingProcessModel: Model<ISyncSkinBookingProcess>,
        @InjectModel(SYNC_SKIN_BOOKING_PROCESS_FAILED) private syncSkinBookingProcessFailedModel: Model<ISyncSkinBookingProcessFailed>,
        @InjectModel(SYNC_SKIN_BOOKING_SUCCESS) private syncSkinBookingSuccessModel: Model<ISyncSkinBookingSuccess>,
        @InjectModel(SYNC_PATIENT_PROCESS) private syncPatientProcessModel: Model<ISyncPatientProcess>,
        @InjectModel(SYNC_PATIENT_PROCESS_FAILED) private syncPatientProcessFailedModel: Model<ISyncPatientProcessFailed>,
        @InjectModel(SYNC_PATIENT_SUCCESS) private syncPatientSuccessModel: Model<ISyncPatientSuccess>,
        @InjectModel(SYNC_BOOKING_DATE_PROCESS) private syncBookingDateProcessModel: Model<ISyncBookingDateProcess>,
        @InjectModel(SYNC_BOOKING_DATE_PROCESS_FAILED) private syncBookingDateProcessFailedModel: Model<ISyncBookingDateProcessFailed>,
        @InjectModel(SYNC_BOOKING_DATE_SUCCESS) private syncBookingDateSuccessModel: Model<ISyncBookingDateSuccess>,
        @InjectModel(BOOKING_DATE_CHANGE_EVENT_NAME) private bookingDateChangeEventModel: Model<IBookingDateChangeEvent>,
        @InjectModel(SYNC_SKIN_PATIENT_PROCESS) private syncSkinPatientProcessModel: Model<ISyncSkinPatientProcess>,
        @InjectModel(SYNC_PATIENT_PROCESS_FAILED) private syncSkinPatientProcessFailedModel: Model<ISyncSkinPatientProcessFailed>,
        @InjectModel(SYNC_PATIENT_SUCCESS) private syncSkinPatientSuccessModel: Model<ISyncSkinPatientSuccess>,
        @InjectModel(SYNC_USER_PROCESS) private syncUserProcessModel: Model<ISyncUserProcess>,
        @InjectModel(SYNC_USER_PROCESS_FAILED) private syncUserProcessFailedModel: Model<ISyncUserProcessFailed>,
        @InjectModel(SYNC_USER_SUCCESS) private syncUserSuccessModel: Model<ISyncUserSuccess>,
        @InjectModel(SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME) private syncUserTrungVuongProcessModel: Model<ISyncUserTrungVuongProcess>,
        @InjectModel(SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME) private syncUserTrungVuongProcessFailedModel: Model<ISyncUserProcessFailed>,
        @InjectModel(SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME) private syncUserTrungVuongSuccessModel: Model<ISyncUserTrungVuongSuccess>,
        @InjectModel(SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME) private syncUserTrungVuongUpgradeModel: Model<ISyncUserTrungVuongUpgrade>,
        @InjectModel(SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME) private syncUserDaLieuProcessModel: Model<ISyncUserDaLieuProcess>,
        @InjectModel(SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME) private syncUserDaLieuProcessFailedModel: Model<ISyncUserDaLieuProcessFailed>,
        @InjectModel(SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME) private syncUserDaLieuSuccessModel: Model<ISyncUserDaLieuSuccess>,
        @InjectModel(SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME) private syncUserDaLieuUpgradeModel: Model<ISyncUserDaLieuUpgrade>,
        @InjectModel(SYNC_DALIEU_PATIENT) private syncDaLieuPatientModel: Model<ISyncDaLieuPatient>,
        @InjectModel(SYNC_DALIEU_PATIENT_PROCESS) private syncDaLieuPatientProcessModel: Model<ISyncDaLieuPatientProcess>,
        @InjectModel(SYNC_DALIEU_PATIENT_PROCESS_FAILED) private syncDaLieuPatientProcessFailedModel: Model<ISyncDaLieuPatientProcessFailed>,
        @InjectModel(SYNC_DALIEU_PATIENT_SUCCESS) private syncDaLieuPatientSuccessModel: Model<ISyncDaLieuPatientSuccess>,
        @InjectModel(SYNC_DALIEU_BOOKING) private syncDaLieuBookingModel: Model<ISyncDaLieuBooking>,
        @InjectModel(SYNC_DALIEU_BOOKING_PROCESS) private syncDaLieuBookingProcessModel: Model<ISyncDaLieuBookingProcess>,
        @InjectModel(SYNC_DALIEU_BOOKING_PROCESS_FAILED) private syncDaLieuBookingtProcessFailedModel: Model<ISyncDaLieuBookingProcessFailed>,
        @InjectModel(SYNC_DALIEU_BOOKING_SUCCESS) private syncDaLieuBookingSuccessModel: Model<ISyncDaLieuBookingSuccess>,
        @InjectModel(SYNC_NHI_DONG_1_PATIENT) private syncNhiDong1PatientModel: Model<ISyncNhiDong1Patient>,
        @InjectModel(SYNC_NHI_DONG_1_PATIENT_PROCESS) private syncNhiDong1PatientProcessModel: Model<ISyncNhiDong1PatientProcess>,
        @InjectModel(SYNC_NHI_DONG_1_PATIENT_SUCCESS) private syncNhiDong1PatientSuccessModel: Model<ISyncNhiDong1PatientSuccess>,
        @InjectModel(SYNC_NHI_DONG_1_PATIENT_PROCESS_FAILED) private syncNhiDong1PatientProcessFailedModel: Model<ISyncNhiDong1PatientProcessFailed>,
        @InjectModel(SYNC_NHI_DONG_1_BOOKING) private syncNhiDong1BookingModel: Model<ISyncNhiDong1Booking>,
        @InjectModel(SYNC_NHI_DONG_1_BOOKING_PROCESS) private syncNhiDong1BookingProcessModel: Model<ISyncNhiDong1BookingProcess>,
        @InjectModel(SYNC_NHI_DONG_1_BOOKING_SUCCESS) private syncNhiDong1BookingSuccessModel: Model<ISyncNhiDong1BookingSuccess>,
        @InjectModel(SYNC_NHI_DONG_1_BOOKING_PROCESS_FAILED) private syncNhiDong1BookingProcessFailedModel: Model<ISyncNhiDong1BookingProcessFailed>,
        @InjectModel(SEND_MAIL_OR_SMS) private sendMailOrSmsModel: Model<ISendMailOrSMS>,
        @InjectModel(SYNC_ND1_BOOKING_PROCESS) private syncNd1BookingProcessModel: Model<ISyncNd1BookingProcess>,
        @InjectModel(SYNC_ND1_BOOKING_PROCESS_FAILED) private syncNd1BookingProcessFailedModel: Model<ISyncNd1BookingProcessFailed>,
        @InjectModel(SYNC_ND1_BOOKING_SUCCESS) private syncNd1BookingSuccessModel: Model<ISyncNd1BookingSuccess>,

        @InjectModel(SYNC_DHYD_PATIENT) private syncDHYDPatientModel: Model<ISyncDHYDPatient>,
        @InjectModel(SYNC_DHYD_PATIENT_PROCESS) private syncDHYDPatientProcessModel: Model<ISyncDHYDPatientProcess>,
        @InjectModel(SYNC_DHYD_PATIENT_SUCCESS) private syncDHYDPatientSuccessModel: Model<ISyncDHYDPatientSuccess>,
        @InjectModel(SYNC_DHYD_PATIENT_PROCESS_FAILED) private syncDHYDPatientProcessFailedModel: Model<ISyncDHYDPatientProcessFailed>,

        @InjectModel(SYNC_DHYD_BOOKING) private syncDHYDBookingModel: Model<ISyncDHYDBooking>,
        @InjectModel(SYNC_DHYD_BOOKING_PROCESS) private syncDHYDBookingProcessModel: Model<ISyncDHYDBookingProcess>,
        @InjectModel(SYNC_DHYD_BOOKING_SUCCESS) private syncDHYDBookingSuccessModel: Model<ISyncDHYDBookingSuccess>,
        @InjectModel(SYNC_DHYD_BOOKING_PROCESS_FAILED) private syncDHYDBookingProcessFailedModel: Model<ISyncDHYDBookingProcessFailed>,
        @InjectModel(NOTIFICATION_COLLECTION) private notificationModel: Model<IEvent>,

        @InjectModel(MESSAGE_EVENT_COLLECTION) private messageEventModel: Model<IMessageEvent>,
        @InjectModel(MESSAGE_EVENT_PROCESS_COLLECTION) private messageEventProcessModel: Model<IMessageEventProcess>,
        @InjectModel(MESSAGE_EVENT_PROCESS_FAILED_COLLECTION) private messageEventProcessFailModel: Model<IMessageEventProcessFailed>,
        @InjectModel(MESSAGE_EVENT_SUCCESS_COLLECTION) private messageEventSuccessModel: Model<IMessageEventSuccess>,

        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(EVENT_COLLECTION_NAME) private eventResponseModel: Model<IEventResponse>,
        @InjectModel(PUSH_NOTIF_INFORM_COLLECTION_NAME) private pushNotifInfomModel: Model<IPushNotifInfom>,
        @InjectSentry() private readonly clientSentry: SentryService,
        @Inject(SENDGRID_CONNECTION) private readonly sendgrid,
        private readonly mailerService: MailerService,
        private readonly syncBookingService: SyncBookingService,
        private readonly syncUserService: SyncUserService,
        private readonly eventProccessorService: EventProccessorService,
        private readonly urlConfigService: UrlConfigService,
        private readonly httpService: PkhHttpService,
        private scheduler: SchedulerRegistry,
        private configRepoService: ConfigRepoService,
        private readonly utilService: UtilService,
        private readonly eventEmitter: EventEmitter2,
        private readonly patientService: PatientMongoService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly cacheService: CacheManagerService,
        private readonly jwtService: JwtService,
    ) {
        this.eventProcessorApi = this.urlConfigService.getEventProcessorUrl();
        this.switchEventNotif = this.urlConfigService.getSwitchEventNotif();
        this.trackingInfo = { repoName: this.configRepoService.getRepoName() };
        this.isEnableBookingTransform = this.urlConfigService.getIsEnableBookingTransform();
        this.repoName = this.configRepoService.getRepoName();
    }

    async addEventCSKHEmailOrSMS(booking: IBooking, email: string, phone: string): Promise<any> {
        /* kiểm tra email  */
        const checkEmail = isEmail(email);
        if (checkEmail) {
            const newEvent = new this.sendMailOrSmsModel({
                id: booking._id,
                createTime: moment().toDate(),
                partnerId: booking.partnerId,
                title: `Yêu cầu gửi mail tới địa chỉ: ${email}`,
                type: 'mail',
                value: email,
                isSend: false,
            });
            try {
                return newEvent.save();
            } catch (error) {
                console.log(error);
            }
        }
        const isMobile = isMobilePhone(phone, 'vi-VN');
        if (isMobile) {
            //
        }
    }

    async testRunOneEnv(): Promise<any> {
        return this.urlConfigService.getRunOneEnv();
    }

    async addCron(): Promise<any> {
        const currentMoment = moment();
        // const seconds = 4;
        const add5 = currentMoment.clone().add(10, 'seconds').toDate();
        // return {
        //     current: currentMoment.toDate(),
        //     add5,
        //     format: add5
        // };
        const name = `test_add_cron2222${add5.valueOf()}`;
        const job = new CronJob(add5, () => {
            // console.log(`time (${add5}) for job ${name} to run!`);
            this.scheduler.deleteCronJob(name);
            // job.stop();
        }, () => {
            console.log(`on Complete`);
        });

        this.scheduler.addCronJob(name, job);
        job.start();

        // console.log(
        //     `job ${name} added for each minute at ${seconds} seconds!`,
        // );
    }

    async deleteCron(name: string): Promise<any> {
        return this.scheduler.deleteCronJob(name);
    }

    async allDynamicCron(): Promise<any> {
        const jobs = this.scheduler.getCronJobs();
        let dataCronJobs: any = [];
        jobs.forEach((value, key, map) => {
            let next;
            try {
                next = value.nextDates().toDate();
            } catch (e) {
                next = 'error: next fire date is in the past!';
            }
            // console.log(`job: ${key} -> next: ${next}`);
            dataCronJobs = [...dataCronJobs, `job: ${key} -> next: ${next}`];
        });
        return dataCronJobs;
    }

    async test(): Promise<any> {
        const id = uuid.v4().replace(/-/g, '');
        const event = new this.eventModel({
            id,
            createTime: moment().toDate(),
            topicId: 'bookings.reserve',
            eventData: {
                id: 'aaaaaa',
                name: 'hehehe',
            },
        });
        return event.save();
    }

    async handleProcessQuaNgayKham(): Promise<any> {
        // console.log('chay luc 11h');
        const list = await this.bookingModel
            .updateMany({
                date: { $gt: moment().subtract(2, 'days').toDate(), $lt: moment().toDate() },
                paymentStatus: { $ne: 2 },
            }, { visible: false })
            .exec();
        // console.log('list', list);
        return list;
    }

    /* Nhi Đồng 1 */
    async handleND1BeforeBookingPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncNd1BookingProcessModel.countDocuments();
        const tableSyncV1 = this.syncV2Nd1BookingTableName;
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const booking = await this.pkhPatientKnex(tableSyncV1)
                .first();
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncNd1BookingProcessModel({
                    processId,
                    id: booking.booking_id,
                    idAutoIncrement: booking.id,
                    date_create: moment(booking.date_create).add(7, 'hours').toDate(),
                    syncStatus: SyncProcessStatus.PENDING,
                });
                const resultProcess = await newProcess.save();
                // try {
                //     const resultEventProcessor =
                //         (await this.pushEventToEventHub(resultProcess)
                //             .toPromise()).data;
                // } catch (error) {
                //     this.clientSentry.instance().captureException(error);
                // }
                /* tiến hành xóa cái dòng trong sync_v2_skin_booking */
                await this.pkhPatientKnex(tableSyncV1)
                    .where({ id: booking.id }).del();
            }
        }
    }

    async handleNd1BeforeBookingSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncNd1BookingProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            // console.log('chạy vao handleSyncProcess');
            const aaaa = await this.syncNd1BookingProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneNd1Booking(syncProcess.id, 'nhidong1');
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncNd1BookingSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        idAutoIncrement: syncProcess.idAutoIncrement,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncNd1BookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncNd1BookingProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    idAutoIncrement: syncProcess.idAutoIncrement,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncNd1BookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    /* Da liễu */

    async handleDaLieuPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncSkinBookingProcessModel.countDocuments();
        const tableSyncV1 = this.syncV2SkinBookingTableName;
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const booking = await this.pkhPatientKnex(tableSyncV1)
                .first();
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncSkinBookingProcessModel({
                    processId,
                    id: booking.booking_id,
                    idAutoIncrement: booking.id,
                    date_create: moment(booking.date_create).add(7, 'hours').toDate(),
                    syncStatus: SyncProcessStatus.PENDING,
                });
                const resultProcess = await newProcess.save();
                // try {
                //     const resultEventProcessor =
                //         (await this.pushEventToEventHub(resultProcess)
                //             .toPromise()).data;
                // } catch (error) {
                //     this.clientSentry.instance().captureException(error);
                // }
                /* tiến hành xóa cái dòng trong sync_v2_skin_booking */
                await this.pkhPatientKnex(tableSyncV1)
                    .where({ id: booking.id }).del();
            }
        }
    }

    async handleDaLieuSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncSkinBookingProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            // console.log('chạy vao handleSyncProcess');
            const aaaa = await this.syncSkinBookingProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneSkinBooking(syncProcess.id, 'dalieuhcm');
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncSkinBookingSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        idAutoIncrement: syncProcess.idAutoIncrement,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncSkinBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                console.log(error);
                const newProcessFailed = new this.syncSkinBookingProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    idAutoIncrement: syncProcess.idAutoIncrement,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncSkinBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleUserPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncUserProcessModel.countDocuments();
        if (countProcessing === 0) {
            // console.log('chạy vao handlePushSyncProcess');
            // return countProcessing;
            /* lấy thông tin trong bảng */
            const booking = await this.pkhPatientKnex(this.syncV2UserTableName)
                // .where({ id: this.stt })
                .first();
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncUserProcessModel({
                    processId,
                    id: booking.user_id,
                    idAutoIncrement: booking.id,
                    date_create: moment(booking.date_create).add(7, 'hours').toDate(),
                    syncStatus: SyncProcessStatus.PENDING,
                });
                const resultProcess = await newProcess.save();
                /* Tiến hành gửi thông tin sang event processor */
                // await this.remindUserAutoPushEvent(resultProcess);
                // try {
                //     const resultEventProcessor =
                //         (await this.pushEventToEventHub(resultProcess, 'NEW_USER_V1')
                //             .toPromise()).data;
                // } catch (error) {
                //     this.clientSentry.instance().captureException(error);
                // }
                // this.stt += 1;
                /* tiến hành xóa cái dòng trong synv_v2_booking */
                await this.pkhPatientKnex(this.syncV2UserTableName)
                    .where({ id: booking.id }).del();
            }
        }
    }

    async handleUserDaLieuPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncUserDaLieuProcessModel.countDocuments();
        if (countProcessing === 0) {
            // console.log('chạy vao handlePushSyncProcess');
            // return countProcessing;
            /* lấy thông tin trong bảng */
            const usersDaLieu = await this.syncUserDaLieuUpgradeModel.find({}).limit(1).exec();
            const getUser = first(usersDaLieu);
            if (getUser) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncUserDaLieuProcessModel({
                    processId,
                    id: getUser.userId,
                    username: getUser.username,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                /* tiến hành xóa cái dòng trong synv_v2_booking */
                await this.syncUserDaLieuUpgradeModel.findByIdAndRemove({
                    _id: getUser._id,
                }).exec();
            } else {
                // đồng bộ xong.
                this.clientSentry.instance().captureMessage('Đồng bộ xong.');
            }
        }
    }

    async handleUserDaLieuSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncUserDaLieuProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            const aaaa = await this.syncUserDaLieuProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncUserService.syncOneUserDaLieu(syncProcess);
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncUserTrungVuongSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        username: syncProcess.username,
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncUserTrungVuongProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncUserTrungVuongProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    username: syncProcess.username,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncUserTrungVuongProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleTrungVuongPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncUserTrungVuongProcessModel.countDocuments();
        if (countProcessing === 0) {
            // console.log('chạy vao handlePushSyncProcess');
            // return countProcessing;
            /* lấy thông tin trong bảng */
            const usersTrungVuong = await this.syncUserTrungVuongUpgradeModel.find({}).limit(1).exec();
            const getUser = first(usersTrungVuong);
            if (getUser) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncUserTrungVuongProcessModel({
                    processId,
                    id: getUser.userId,
                    username: getUser.username,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                /* tiến hành xóa cái dòng trong synv_v2_booking */
                await this.syncUserTrungVuongUpgradeModel.findByIdAndRemove({
                    _id: getUser._id,
                }).exec();
            } else {
                // đồng bộ xong.
                this.clientSentry.instance().captureMessage('Đồng bộ xong.');
            }
        }
    }

    async handleTrungVuongSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncUserTrungVuongProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            const aaaa = await this.syncUserTrungVuongProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncUserService.upgradeUserTrungVuong(syncProcess);
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncUserTrungVuongSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        username: syncProcess.username,
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncUserTrungVuongProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncUserTrungVuongProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    username: syncProcess.username,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncUserTrungVuongProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async testOneEventDevices(): Promise<any> {
        const event = {
            id: uuid.v4().replace(/-/g, ''),
            topicId: 'messages.push-inform-notif',
            createTime: moment().toISOString(),
            userId: 'aaaaaaa',
            partnerId: 'trungvuong',
            appId: 'trungvuong',
            title: 'TEST NOIDUNG',
            content: 'CONETNT TSET CONETENT',
            isNotif: true,
            isPushNotif: true,
            type: 100,
            eventData: {
                clientId: '11111111',
                clientToken: '2222222',
            },
            devices: [
                {
                    id: uuid.v4().replace(/-/g, ''),
                    clientId: '11111111',
                    clientToken: '2222222',
                },
                {
                    id: uuid.v4().replace(/-/g, ''),
                    clientId: '333',
                    clientToken: '44444',
                },
            ],
        };
        return this.eventModel.create(event);
    }

    async checkSync(): Promise<any> {
        const bookings: any = await this.pkhPatientKnex('booking').where('id', '>', 1726246);
        const dataInsert = bookings.map((item: any) => {
            return {
                id: item.id,
                booking_id: item.id,
                date_create: item.date_create,
            };
        });
        if (dataInsert.length > 0) {
            await this.syncV2BookingModel.insertMany(dataInsert);
        }
        return dataInsert.length;
    }

    async getCountPatientInUserPatientUMC(userId: string): Promise<any> {
        return this.userModel.findById({ _id: userId }, { patients: true }).exec();
    }

    async getErrorInfo(): Promise<any> {

        // const users = [
        //     {
        //       "patients": [
        //         "5f9a1bc907782c001a5b1a31",
        //         "603821c47b166b0019251d25",
        //         "603d07e0b5190f001905e46a",
        //         "628aeb9ab25231001be0d6fd",
        //         "5ef71103771b4c0019751bb8"
        //       ],
        //       "prevPatients": [],
        //       "isCS": false,
        //       "userIdV1": 0,
        //       "partners": [],
        //       "username": "+84919811576",
        //       "password": "45d243a435528a8693c1278e6ade3b6c",
        //       "salt": "leMeOIaQklnXsENW",
        //       "email": "+84919811576",
        //       "fullname": "Võ Nhật Ngân Tuyền",
        //       "medproId": "mp+84919811576",
        //       "createdAt": "2020-10-29T01:26:01.859Z",
        //       "updatedAt": "2022-10-04T07:29:12.705Z",
        //       "id": "5f9a1a2907782c001a5b1a2b"
        //     },
        //     {
        //       "patients": [
        //         "5feaf26e7997c80019e2a6d5",
        //         "5ff257017997c80019e2c05c",
        //         "633c1f515a1e840019a42e72"
        //       ],
        //       "prevPatients": [],
        //       "isCS": false,
        //       "userIdV1": 0,
        //       "partners": [],
        //       "username": "+84902561428",
        //       "password": "27af0c787980a8c8bb18ad2cb47c8523",
        //       "salt": "wbMQEe20EFIg0ofH",
        //       "email": "+84902561428",
        //       "fullname": "Nguyễn Ngọc Hòa",
        //       "medproId": "mp+84902561428",
        //       "createdAt": "2020-12-29T09:09:33.101Z",
        //       "updatedAt": "2022-10-04T11:56:01.744Z",
        //       "id": "5feaf24d7997c80019e2a6d0"
        //     },
        //     {
        //       "patients": [
        //         "5ffac45eb6df43001a44c2dd",
        //         "5ffac4bab6df43001a44c2e0",
        //         "61670c7b2c76c90019887312"
        //       ],
        //       "prevPatients": [],
        //       "isCS": false,
        //       "userIdV1": 0,
        //       "partners": [],
        //       "username": "+84838301066",
        //       "password": "346c1a3c847e3a6c916f31125aaab589",
        //       "salt": "GbFqm7OjiHnSUcHp",
        //       "email": "+84838301066",
        //       "fullname": "Vũ thị thu hà",
        //       "medproId": "mp+84838301066",
        //       "createdAt": "2021-01-10T09:04:52.008Z",
        //       "updatedAt": "2022-10-04T15:40:16.800Z",
        //       "id": "5ffac334b6df43001a44c2d8"
        //     },
        //     {
        //       "patients": [
        //         "6012ca6f4959a400199a1010",
        //         "6091400cd679000019a6f409",
        //         "625d78844072c10019be8fc6",
        //         "625d86104072c10019be9c8b"
        //       ],
        //       "prevPatients": [],
        //       "isCS": false,
        //       "userIdV1": 0,
        //       "partners": [],
        //       "username": "+84907708083",
        //       "password": "ac834983358fc23ec514a51743b27005",
        //       "salt": "GDZqFYLZquwh5xEY",
        //       "email": "+84907708083",
        //       "fullname": "Nguyễn Thị Như Thùy",
        //       "medproId": "mp+84907708083",
        //       "createdAt": "2021-01-28T14:26:42.358Z",
        //       "updatedAt": "2022-10-04T08:31:45.768Z",
        //       "id": "6012c9a24959a400199a1006"
        //     }
        //   ];

        const users = await this.userModel.find({
            updatedAt: {
                $gte: moment().subtract(1, 'days').set({
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                }).toDate(),
                $lte: moment().subtract(1, 'days').set({
                    hours: 23,
                    minutes: 59,
                    seconds: 59,
                }).toDate(),
            },
        }).exec();

        const testPatient = '5ef71103771b4c0019751bb8';
        for await (const user of users) {
            const userId = user.id;
            const userMongo = await this.userModel.findById({ _id: userId }).exec();
            if (userMongo) {
                const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userId);
                const remainPatients = userInfoWithPatients.patients.filter(item => {
                    return `${item}` !== `${testPatient}`;
                });
                userMongo.patients = remainPatients;
                await userMongo.save();
            }
        }

        return 'update thanh cong';
    }

    async gettOneEventDevices(id: string): Promise<any> {
        return this.eventModel.findById({ _id: id }).exec();
    }

    async handleProcessUniqueBookingV1(): Promise<any> {
        const bookings: any = await this.pkhPatientKnex(this.syncV2BookingTableName).limit(100);
        const bookingsUnique = uniqBy(bookings, 'booking_id');
        const bookingIds = map(bookingsUnique, 'booking_id');
        const dataInsert = bookingsUnique.map((item: any) => {
            return {
                id: item.id,
                booking_id: item.booking_id,
                date_create: item.date_create,
            };
        });
        if (dataInsert.length > 0) {
            await this.syncV2BookingModel.insertMany(dataInsert);
            /* xóa 100 dòng đi */
            await this.pkhPatientKnex(this.syncV2BookingTableName).whereIn('booking_id', bookingIds).del();
        }
    }

    async handlePushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncBookingProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const booking = await this.pkhPatientKnex(this.syncV2BookingTableName)
                .first();
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                try {
                    const newProcess = new this.syncBookingProcessModel({
                        processId,
                        id: booking.booking_id,
                        idAutoIncrement: booking.id,
                        date_create: moment(booking.date_create).add(7, 'hours').toDate(),
                        syncStatus: SyncProcessStatus.PENDING,
                    });
                    await newProcess.save();
                    /* tiến hành xóa cái dòng trong synv_v2_booking */
                    await this.pkhPatientKnex(this.syncV2BookingTableName)
                        .where({ id: booking.id }).del();
                } catch (error) {
                    const newProcessFailed = new this.syncBookingProcessFailedModel({
                        processId,
                        id: booking.booking_id,
                        idAutoIncrement: booking.id,
                        date_create: moment(booking.date_create).add(7, 'hours').toDate(),
                        errorBody: this.utilService.errorHandler(error),
                    });
                    await newProcessFailed.save();
                }

            }
        }
    }

    async remindUserAutoPushEvent(resultProcess: ISyncUserProcess): Promise<any> {
        // console.log('chay vao remindUserAutoPushEvent , ', resultProcess.toJSON());
        const remindTime = moment().add(15, 'seconds').toDate();
        const name = `reminder_push_user_event_${resultProcess.processId}_${remindTime.valueOf()}`;
        const job = new CronJob(remindTime, async () => {
            /* Tìm lại thông tin booking */
            const syncProcess = await this.syncUserProcessModel.findOne({
                processId: resultProcess.processId,
                status: SyncProcessStatus.INITIAL,
            }).exec();

            if (!syncProcess) {
                this.scheduler.deleteCronJob(name);
            } else {
                await this.remindUserAutoPushEvent(resultProcess);
                try {
                    const resultEventProcessor =
                        (await this.pushEventToEventHub(resultProcess, 'NEW_USER_V1')
                            .toPromise()).data;
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                }
                this.scheduler.deleteCronJob(name);
            }
        }, () => {
            // console.log(`on Complete`);
        });

        try {
            this.scheduler.addCronJob(name, job);
            job.start();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
        }
    }

    async remindAutoPushEvent(resultProcess: ISyncBookingProcess): Promise<any> {
        // console.log('chay vao remindAutoPushEvent , ', resultProcess.toJSON());
        const remindTime = moment().add(15, 'seconds').toDate();
        const name = `reminder_push_event_${resultProcess.processId}_${remindTime.valueOf()}`;
        const job = new CronJob(remindTime, async () => {
            /* Tìm lại thông tin booking */
            const syncProcess = await this.syncBookingProcessModel.findOne({
                processId: resultProcess.processId,
                status: SyncProcessStatus.INITIAL,
            }).exec();

            if (!syncProcess) {
                this.scheduler.deleteCronJob(name);
            } else {
                await this.remindAutoPushEvent(resultProcess);
                try {
                    const resultEventProcessor =
                        (await this.pushEventToEventHub(resultProcess)
                            .toPromise()).data;
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                }
                this.scheduler.deleteCronJob(name);
            }
        }, () => {
            // console.log(`on Complete`);
        });

        try {
            this.scheduler.addCronJob(name, job);
            job.start();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
        }
    }

    async remindSkinAutoPushEvent(resultProcess: ISyncSkinBookingProcess): Promise<any> {
        // console.log('chay vao remindSkinAutoPushEvent , ', resultProcess.toJSON());
        const remindTime = moment().add(15, 'seconds').toDate();
        const name = `reminder_push_event_skin_${resultProcess.processId}_${remindTime.valueOf()}`;
        const job = new CronJob(remindTime, async () => {
            /* Tìm lại thông tin booking */
            const syncProcess = await this.syncSkinBookingProcessModel.findOne({
                processId: resultProcess.processId,
                status: SyncProcessStatus.INITIAL,
            }).exec();

            if (!syncProcess) {
                this.scheduler.deleteCronJob(name);
            } else {
                await this.remindSkinAutoPushEvent(resultProcess);
                try {
                    const resultEventProcessor =
                        (await this.pushEventToEventHub(resultProcess)
                            .toPromise()).data;
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                }
                this.scheduler.deleteCronJob(name);
            }
        }, () => {
            // console.log(`on Complete`);
        });

        try {
            this.scheduler.addCronJob(name, job);
            job.start();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
        }
    }

    async remindPatientAutoPushEvent(resultProcess: ISyncPatientProcess): Promise<any> {
        // console.log('chay vao remindPatientAutoPushEvent , ', resultProcess.toJSON());
        const remindTime = moment().add(15, 'seconds').toDate();
        const name = `reminder_push_event_patient_${resultProcess.processId}_${remindTime.valueOf()}`;
        const job = new CronJob(remindTime, async () => {
            /* Tìm lại thông tin booking */
            const syncProcess = await this.syncPatientProcessModel.findOne({
                processId: resultProcess.processId,
                status: SyncProcessStatus.INITIAL,
            }).exec();

            if (!syncProcess) {
                this.scheduler.deleteCronJob(name);
            } else {
                await this.remindPatientAutoPushEvent(resultProcess);
                try {
                    const resultEventProcessor =
                        (await this.pushEventToEventHub(resultProcess, 'NEW_PATIENT_V1')
                            .toPromise()).data;
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                }
                this.scheduler.deleteCronJob(name);
            }
        }, () => {
            // console.log(`on Complete`);
        });

        try {
            this.scheduler.addCronJob(name, job);
            job.start();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
        }
    }

    async remindSkinPatientAutoPushEvent(resultProcess: ISyncSkinPatientProcess): Promise<any> {
        // console.log('chay vao remindSkinPatientAutoPushEvent , ', resultProcess.toJSON());
        const remindTime = moment().add(15, 'seconds').toDate();
        const name = `reminder_push_event_skin_patient_${resultProcess.processId}_${remindTime.valueOf()}`;
        const job = new CronJob(remindTime, async () => {
            /* Tìm lại thông tin booking */
            const syncProcess = await this.syncSkinPatientProcessModel.findOne({
                processId: resultProcess.processId,
                status: SyncProcessStatus.INITIAL,
            }).exec();

            if (!syncProcess) {
                this.scheduler.deleteCronJob(name);
            } else {
                await this.remindSkinPatientAutoPushEvent(resultProcess);
                try {
                    const resultEventProcessor =
                        (await this.pushEventToEventHub(resultProcess, 'NEW_PATIENT_V1')
                            .toPromise()).data;
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                }
                this.scheduler.deleteCronJob(name);
            }
        }, () => {
            // console.log(`on Complete`);
        });

        try {
            this.scheduler.addCronJob(name, job);
            job.start();
        } catch (error) {
            this.clientSentry.instance().captureException(error);
        }
    }

    async handleSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncBookingProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            // console.log('chạy vao handleSyncProcess');
            await this.syncBookingProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneBooking(syncProcess.id);
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncBookingSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        idAutoIncrement: syncProcess.idAutoIncrement,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                let errorBody: any = {};
                if (error instanceof Error) {
                    errorBody = { ...errorBody, message: error.message };
                } else {
                    errorBody = this.utilService.errorHandler(error);
                }
                console.log(error);
                console.log(errorBody);
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncBookingProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    idAutoIncrement: syncProcess.idAutoIncrement,
                    date_create: syncProcess.date_create,
                    errorBody,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleUserSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncUserProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            const aaaa = await this.syncUserProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncUserService.syncOneUser(syncProcess.id);
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncUserSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        idAutoIncrement: syncProcess.idAutoIncrement,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncUserProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncUserProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    idAutoIncrement: syncProcess.idAutoIncrement,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncUserProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleDHYDPatientPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-patient-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncDHYDPatientProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const patients = await this.syncDHYDPatientModel.find({}).limit(1).exec();
            const patient = first(patients);
            if (patient) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncDHYDPatientProcessModel({
                    processId,
                    id: patient.id,
                    userId: patient.userId,
                    medproId: patient.medproId,
                    date_create: patient.date_create,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                await this.syncDHYDPatientModel.findByIdAndRemove({ _id: patient._id }).exec();
            }
        }
    }

    async handleDHYDPatientSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncDHYDPatientProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            const aaaa = await this.syncDHYDPatientProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncPatient(syncProcess.id, 'umc');
                if (result) {
                    /* add thông tin hồ sơ vào trong user */
                    await this.syncUserService.preAddPatientToUser(syncProcess.userId, syncProcess.medproId, result.idRef);
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncDHYDPatientSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        userId: syncProcess.userId,
                        medproId: syncProcess.medproId,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDHYDPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncDHYDPatientProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    userId: syncProcess.userId,
                    medproId: syncProcess.medproId,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDHYDPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleDHYDBookingPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncDHYDBookingProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const bookings = await this.syncDHYDBookingModel.find({}).limit(1).exec();
            const booking = first(bookings);
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncDHYDBookingProcessModel({
                    processId,
                    id: booking.id,
                    date_create: booking.date_create,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                /* tiến hành xóa cái dòng trong sync_v2_skin_booking */
                await this.syncDHYDBookingModel.findByIdAndRemove({ _id: booking._id }).exec();
            }
        }
    }

    async handleDHYDBookingSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncDHYDBookingProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            const aaaa = await this.syncDHYDBookingProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneBooking(syncProcess.id, 'umc');
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncDHYDBookingSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDHYDBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncDHYDBookingProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDHYDBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleNhiDong1PatientPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-patient-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncNhiDong1PatientProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const patients = await this.syncNhiDong1PatientModel.find({}).limit(1).exec();
            const patient = first(patients);
            if (patient) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncNhiDong1PatientProcessModel({
                    processId,
                    id: patient.id,
                    userId: patient.userId,
                    medproId: patient.medproId,
                    date_create: patient.date_create,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                await this.syncNhiDong1PatientModel.findByIdAndRemove({ _id: patient._id }).exec();
            }
        }
    }

    async handleNhiDong1PatientSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncNhiDong1PatientProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            const aaaa = await this.syncNhiDong1PatientProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncPatient(syncProcess.id, 'nhidong1');
                if (result) {
                    /* add thông tin hồ sơ vào trong user */
                    await this.syncUserService.preAddPatientToUser(syncProcess.userId, syncProcess.medproId, result.idRef);
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncNhiDong1PatientSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        userId: syncProcess.userId,
                        medproId: syncProcess.medproId,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncNhiDong1PatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncNhiDong1PatientProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    userId: syncProcess.userId,
                    medproId: syncProcess.medproId,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncNhiDong1PatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleDaLieuPatientPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-patient-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncDaLieuPatientProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const patients = await this.syncDaLieuPatientModel.find({}).limit(1).exec();
            const patient = first(patients);
            if (patient) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncDaLieuPatientProcessModel({
                    processId,
                    id: patient.id,
                    userId: patient.userId,
                    medproId: patient.medproId,
                    date_create: patient.date_create,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                /* tiến hành xóa cái dòng trong synv_v2_booking */
                await this.syncDaLieuPatientModel.findByIdAndRemove({ _id: patient._id }).exec();
            }
        }
    }

    async handleDaLieuPatientSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncDaLieuPatientProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            const aaaa = await this.syncDaLieuPatientProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncPatient(syncProcess.id, 'dalieuhcm');
                if (result) {
                    /* add thông tin hồ sơ vào trong user */
                    await this.syncUserService.preAddPatientToUser(syncProcess.userId, syncProcess.medproId, result.idRef);
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncDaLieuPatientSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        userId: syncProcess.userId,
                        medproId: syncProcess.medproId,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDaLieuPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncDaLieuPatientProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    userId: syncProcess.userId,
                    medproId: syncProcess.medproId,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDaLieuPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleNhiDong1BookingPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncNhiDong1BookingProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const bookings = await this.syncNhiDong1BookingModel.find({}).limit(1).exec();
            const booking = first(bookings);
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncNhiDong1BookingProcessModel({
                    processId,
                    id: booking.id,
                    date_create: booking.date_create,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                /* tiến hành xóa cái dòng trong sync_v2_skin_booking */
                await this.syncNhiDong1BookingModel.findByIdAndRemove({ _id: booking._id }).exec();
            }
        }
    }

    async handleNhiDong1BookingSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncNhiDong1BookingProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            const aaaa = await this.syncNhiDong1BookingProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneNd1Booking(syncProcess.id, 'nhidong1');
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncNhiDong1BookingSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncNhiDong1BookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncNhiDong1BookingProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncNhiDong1BookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleDaLieuBookingPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-booking-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncDaLieuBookingProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const bookings = await this.syncDaLieuBookingModel.find({}).limit(1).exec();
            const booking = first(bookings);
            if (booking) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncDaLieuBookingProcessModel({
                    processId,
                    id: booking.id,
                    date_create: booking.date_create,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await newProcess.save();
                /* tiến hành xóa cái dòng trong sync_v2_skin_booking */
                await this.syncDaLieuBookingModel.findByIdAndRemove({ _id: booking._id }).exec();
            }
        }
    }

    async handleDaLieuBookingSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncDaLieuBookingProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            const aaaa = await this.syncDaLieuBookingProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneSkinBooking(syncProcess.id, 'dalieuhcm');
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncDaLieuBookingSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDaLieuBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncDaLieuBookingtProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncDaLieuBookingProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleSkinPatientPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-patient-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncSkinPatientProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const patient = await this.pkhPatientKnex(this.syncV2SkinPatientTableName)
                // .where({ id: this.stt })
                .first();
            if (patient) {
                /* lưu thông vào trong sync-booking-process */
                const processId = uuid.v4().replace(/-/g, '');
                const newProcess = new this.syncSkinPatientProcessModel({
                    processId,
                    id: patient.patient_id,
                    idAutoIncrement: patient.id,
                    date_create: moment(patient.date_create).add(7, 'hours').toDate(),
                });
                const resultProcess = await newProcess.save();
                /* Tiến hành gửi thông tin sang event processor */
                await this.remindSkinPatientAutoPushEvent(resultProcess);
                try {
                    const resultEventProcessor =
                        (await this.pushEventToEventHub(resultProcess, 'NEW_PATIENT_V1')
                            .toPromise()).data;
                    // console.log('kết quả trả về của event processor hub sync patient: ', resultEventProcessor);
                    // this.clientSentry.instance().captureMessage('kết quả trả về của event processor hub -  sync patient')
                } catch (error) {
                    this.clientSentry.instance().captureException(error);
                }
                // this.stt += 1;
                /* tiến hành xóa cái dòng trong synv_v2_booking */
                await this.pkhPatientKnex(this.syncV2SkinPatientTableName)
                    .where({ id: patient.id }).del();
            }
        }
    }

    async handleSkinPatientSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncSkinPatientProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            // console.log('chạy vao handleSyncProcess');
            const aaaa = await this.syncSkinPatientProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncOneSkinBooking(syncProcess.id);
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncSkinPatientSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        idAutoIncrement: syncProcess.idAutoIncrement,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncSkinPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncSkinPatientProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    idAutoIncrement: syncProcess.idAutoIncrement,
                    date_create: syncProcess.date_create,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncSkinPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handlePatientPushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-patient-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncPatientProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const patient = await this.pkhPatientKnex(this.syncV2PatientTableName)
                // .where({ id: this.stt })
                .first();
            if (patient) {
                /* Tìm lại những thằng user có patient_id này */
                const usersList: [any] = await this.pkhPatientKnex(this.umcUserPatient).where({ patient_id: patient.patient_id });
                if (usersList.length > 0) {
                    for await (const user of usersList) {
                        /* Tìm lại userv1 theo userID */
                        const getUser: any = await this.pkhPatientKnex(this.userTableName)
                            // .select('username')
                            .where({ id: user.user_id })
                            .first();
                        if (getUser) {
                            let userV2Ob: any = {};
                            const isMobile = isMobilePhone(getUser.username, 'vi-VN');
                            if (isMobile) {
                                /* lấy thông tin userID, medproID trên v2 */
                                const getUserV2 = await this.userModel.findOne({
                                    username: getUser.username,
                                    medproId: `mp${getUser.username}`,
                                }).exec();
                                if (getUserV2) {
                                    userV2Ob = {
                                        ...userV2Ob,
                                        userId: getUser.id,
                                        medproId: getUserV2.medproId,
                                    };
                                }
                            } else {
                                const isZaloProvider = `${getUser.username}`.includes('zl_');
                                if (isZaloProvider) { /* Kiểm tra xem user là zalo hay ko */
                                    const dataZalo = await this.syncUserService.checkZaloIdV2(getUser.username);
                                    if (dataZalo) {
                                        userV2Ob = {
                                            ...userV2Ob,
                                            userId: getUser.id,
                                            medproId: dataZalo.medproId,
                                        };
                                    }
                                } else {
                                    const dataFirebase = await this.syncUserService.checkFirebaseIdV2(getUser.username);
                                    if (dataFirebase) {
                                        userV2Ob = {
                                            ...userV2Ob,
                                            userId: getUser.id,
                                            medproId: dataFirebase.medproId,
                                        };
                                    }
                                }
                            }

                            if (Object.keys(userV2Ob).length > 0) {
                                /* lưu thông vào trong sync-booking-process */
                                const processId = uuid.v4().replace(/-/g, '');
                                try {
                                    const newProcess = new this.syncPatientProcessModel({
                                        processId,
                                        id: patient.patient_id,
                                        ...userV2Ob,
                                        idAutoIncrement: patient.id,
                                        date_create: moment(patient.date_create).add(7, 'hours').toDate(),
                                        syncStatus: SyncProcessStatus.PENDING,
                                    });
                                    await newProcess.save(); // .catch(erorr => console.log(erorr));
                                } catch (error) {
                                    const newProcessFailed = new this.syncPatientProcessFailedModel({
                                        processId,
                                        id: patient.patient_id,
                                        ...userV2Ob,
                                        idAutoIncrement: patient.id,
                                        date_create: moment(patient.date_create).add(7, 'hours').toDate(),
                                        error: this.utilService.errorHandler(error),
                                    });
                                    await newProcessFailed.save();
                                }
                            }

                        }
                    }
                    /* tiến hành xóa cái dòng trong synv_v2_booking */
                    await this.pkhPatientKnex(this.syncV2PatientTableName)
                        .where({ id: patient.id }).del();
                } else {
                    /* TODO: service_logs */
                }
            }
        }

    }

    async handlePatientSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncPatientProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            // console.log('chạy vao handleSyncProcess');
            const aaaa = await this.syncPatientProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {
                const result = await this.syncBookingService.syncPatient(syncProcess.id);
                if (result) {
                    /* add thông tin hồ sơ vào trong user */
                    await this.syncUserService.preAddPatientToUser(syncProcess.userId, syncProcess.medproId, result.idRef);
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncPatientSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        userId: syncProcess.userId,
                        medproId: syncProcess.medproId,
                        idAutoIncrement: syncProcess.idAutoIncrement,
                        date_create: syncProcess.date_create,
                        /*  */
                        extra: {
                            ...result,
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncPatientProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                    userId: syncProcess.userId,
                    medproId: syncProcess.medproId,
                    idAutoIncrement: syncProcess.idAutoIncrement,
                    date_create: syncProcess.date_create,
                    error: this.utilService.errorHandler(error),
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncPatientProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async handleBookingDatePushSyncProcess(): Promise<any> {
        /* kiểm tra xem bảng sync-patient-process có thằng nào đang xử lý hay ko */
        const countProcessing = await this.syncBookingDateProcessModel.countDocuments();
        if (countProcessing === 0) {
            /* lấy thông tin trong bảng */
            const bookings = await this.bookingDateChangeEventModel
                .find({})
                .limit(1)
                .exec();
            if (bookings.length > 0) {
                const booking = first(bookings);
                if (booking) {
                    /* lưu thông vào trong sync-booking-process */
                    const processId = uuid.v4().replace(/-/g, '');
                    const newProcess = new this.syncBookingDateProcessModel({
                        processId,
                        id: booking.idBooking,
                    });
                    const resultProcess = await newProcess.save();
                    /* Tiến hành gửi thông tin sang event processor */
                    try {
                        const resultEventProcessor =
                            (await this.pushEventToEventHub(resultProcess, 'BOOKING_DATE_CHANGED')
                                .toPromise()).data;
                        // console.log('kết quả trả về của event processor hub sync patient: ', resultEventProcessor);
                        // this.clientSentry.instance().captureMessage('kết quả trả về của event processor hub -  sync patient')
                    } catch (error) {
                        this.clientSentry.instance().captureException(error);
                    }
                    // this.stt += 1;
                    /* tiến hành xóa cái dòng trong synv_v2_booking */
                    await this.bookingDateChangeEventModel.findByIdAndRemove({
                        _id: booking._id,
                    }).exec();
                }
            }
        }
    }

    async handleBookingDateSyncProcess(): Promise<any> {
        /* lấy thông tin sync process */
        const syncProcessList = await this.syncBookingDateProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).exec();
        const syncProcess = first(syncProcessList);
        if (syncProcess) {
            /* tiến hành sync */
            // console.log('chạy vao handleSyncProcess');
            const aaaa = await this.syncBookingDateProcessModel
                .findByIdAndUpdate({ _id: syncProcess._id }, { syncStatus: SyncProcessStatus.ACTIVE }, { new: true })
                .exec();
            try {

                const booking = await this.bookingModel.findOne({
                    id: syncProcess.id,
                }).exec();
                const result = await this.eventProccessorService.executeBookingDateChanged(booking);
                if (result) {
                    /* tiến hành lưu thông tin syncProcess này trong syncProcessSuccess */
                    const syncProcessSuccess = new this.syncBookingDateSuccessModel({
                        processId: syncProcess.processId,
                        id: syncProcess.id,
                        /*  */
                        extra: {
                            ...booking.toObject(),
                        },
                    });
                    await syncProcessSuccess.save();
                }
                /* tiến hành xóa dòng trong sync_process */
                await this.syncBookingDateProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            } catch (error) {
                console.log(error);
                /* Có lỗi xảy ra thì lưu dòng này qua chỗ khác */
                const newProcessFailed = new this.syncBookingDateProcessFailedModel({
                    processId: syncProcess.processId,
                    id: syncProcess.id,
                });
                await newProcessFailed.save();
                /* tiến hành xóa dòng trong sync_process */
                await this.syncBookingDateProcessModel.findByIdAndRemove({ _id: syncProcess._id }).exec();
            }
        }
    }

    async autoNotifBooking(): Promise<any> {
        const partnerId = 'umc';
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (partnerConfig) {
            if (partnerConfig.maxId > 0) {
                const booking = await this.pkhPatientKnex(this.bookingTableName)
                    .where('status', 1)
                    .where('id', '>', partnerConfig.maxId)
                    .first();
                if (booking) {
                    /* tiến hành cập nhật vào trong partnerConfig */
                    await this.partnerConfigModel.findByIdAndUpdate({ _id: partnerConfig._id }, { maxId: booking.id }).exec();
                    /* kiểm tra xem config auto */
                    const data = await this.pkhPatientKnex
                        .select(
                            `${this.bookingTableName}.user_id as userId`,
                            `${this.hospitalSubjectTableName}.subject_id as subjectId`,
                        )
                        .from(this.bookingTableName)
                        .innerJoin(this.scheduleTableName, `${this.scheduleTableName}.id`, `${this.bookingTableName}.schedule_id`)
                        .innerJoin(this.hospitalSubjectTableName, `${this.hospitalSubjectTableName}.id`, `${this.scheduleTableName}.hospital_subject_id`)
                        .where(`${this.bookingTableName}.id`, booking.id)
                        .first();
                    /* lấy thông tin auto cho partner , groupId, */
                    const messageSendConfig = await this.messageSendConfigModel.find(
                        { partnerId, groupId: `${data.subjectId}`, deleted: false, activeStatus: 'ACTIVATE' },
                    ).exec();
                    const mappIng = messageSendConfig.map(item => {
                        const obj = item.toObject();
                        const dateObj: any = {};
                        if (obj.fromDate) {
                            dateObj.fromDate = moment(obj.fromDate).isValid() ? moment(obj.fromDate).valueOf() : 0;
                        } else {
                            dateObj.fromDate = 0;
                        }
                        if (obj.toDate) {
                            dateObj.toDate = moment(obj.toDate).isValid() ? moment(obj.toDate).valueOf() : 0;
                        } else {
                            dateObj.toDate = 0;
                        }
                        return {
                            ...item.toObject(),
                            ...dateObj,
                        };
                    });
                    const valueOf = moment().valueOf();
                    const configList = mappIng.filter(item => {
                        const fromDate = item.fromDate;
                        const toDate = item.toDate;
                        if (fromDate > 0) {
                            if (valueOf >= fromDate) {
                                if (toDate > 0) {
                                    if (valueOf <= toDate) {
                                        return true;
                                    } else {
                                        return false;
                                    }
                                } else {
                                    return true;
                                }
                            } else {
                                return false; // không hợp lệ
                            }
                        } else {
                            if (toDate > 0) {
                                if (valueOf > toDate) {
                                    return false;
                                } else {
                                    return true;
                                }
                            } else {
                                return true; // hợp lệ
                            }
                        }
                    });
                    /* tìm thông tin trong push device */
                    const pushDevices = await this.pkhPatientKnex(this.pushDeviceTableName).where('user_id', data.userId);
                    if (pushDevices.length > 0) {
                        /* lấy thông tin message */
                        let reMapping = [];
                        for await (const conf of configList) {
                            const messageInfo = await await this.messageItemModel.findOne({ messageId: conf.messageId }).exec();
                            if (messageInfo) {
                                /* tiến hành insert vào trong notification */
                                const checkUrlNull: any = {};
                                if (!!messageInfo.url) {
                                    checkUrlNull.url = messageInfo.url;
                                }
                                const [idNotif] = await this.pkhPatientKnex('notification').insert({
                                    title: messageInfo.title,
                                    content: messageInfo.content,
                                    type: (!!messageInfo.url ? 3 : 4),
                                    user_id: data.userId,
                                    ...checkUrlNull,
                                });
                                /* tìm lại thông tin notif vừa insert */
                                const getResultData = await this.pkhPatientKnex('notification')
                                    .where({ id: idNotif }).first();

                                const details = pushDevices.map(item => {
                                    return {
                                        id: uuid.v4().replace(/-/g, ''),
                                        topicId: 'messages.push-notif',
                                        createTime: moment().toISOString(),
                                        userId: `${data.userId}`,
                                        partnerId,
                                        appId: partnerId,
                                        title: messageInfo.title,
                                        content: messageInfo.content,
                                        isNotif: true,
                                        isPushNotif: true,
                                        type: 3,
                                        eventData: {
                                            ...messageInfo.toObject(),
                                            ...item,
                                            clientId: item.onesignal_id,
                                            clientToken: item.onesignal_token,
                                            // type: 3, // hên là trùng với type = 3 trong bảng umc notification
                                            ...getResultData,
                                        },
                                    };
                                });
                                reMapping = [...reMapping, ...details];
                            }
                        }
                        /* Tiến hành tạo event */
                        await this.eventModel.insertMany(reMapping);
                        // return {
                        //     isOK: true,
                        // };
                    } else {
                        console.log('Không tìm thấy thông tin thiết bị nhận thông báo !');
                    }
                }
            }
        }
    }

    async messageSendConfigList(partnerId: string): Promise<any> {
        return this.messageSendConfigModel.find({ partnerId }).exec();
    }

    async markViewed(userMongoId: string, appId: string, id: string): Promise<any> {
        let noti = await this.eventModel
            .findOneAndUpdate({ id }, { isRead: true }, { new: true })
            .exec();

        if (!noti) {
            const informNotif = await this.pushNotifInfomModel.findOne({ _id: id }).exec();
            if (informNotif) {
                const alreadyAdd = await this.eventModel.findOne({ userId: userMongoId, appId, isNotif: true, informNotifId: informNotif._id }).exec();
                if (!alreadyAdd) {
                    const { _id, __v, id, ...rest} = informNotif.toObject();
                    noti = await this.eventModel.create({
                        ...rest,
                        id: uuid.v4().replace(/-/g, ''),
                        informNotifId: _id,
                        userId: userMongoId,
                        isNotif: true,
                        isRead: true,
                        visible: true,
                    })
                }
            }
        }

        this.cacheService.delByPattern(`event.*userMongoId=${userMongoId}`)

        return noti;

    }

    async markViewedAll(userMongoId: string, appId: string): Promise<any> {
        await this.eventModel
            .updateMany({ userId: userMongoId, appId, isNotif: true, isRead: false }, { isRead: true })
            .exec();

        await this.markViewSystemNotif(userMongoId, appId);
        this.cacheService.delByPattern(`event.*userMongoId=${userMongoId}`)

        return { isOk: true };
    }

    async markViewSystemNotif(userMongoId: string, appId: string) {

        const pushDeviceTime = await this.pushDeviceModel.find({ userId: userMongoId, appId }).limit(1).sort({_id: 1 }).exec();

        const events = await this.eventModel.find({ userId: userMongoId, appId, isNotif: true }, { informNotifId: true }).exec();
        const informNotifIds = events.filter(e => !!e.informNotifId).map(e => e.informNotifId);

        const registerDeviceMongoId = pushDeviceTime[0]?._id;

        const notifInforms = await this.pushNotifInfomModel
            .find({
                _id: {
                    ...(registerDeviceMongoId && { $gte: registerDeviceMongoId }),
                    $nin: informNotifIds
                },
                appId,
            })
            .limit(100)
            .sort({ _id: -1 })
            .exec();

        const insertData = notifInforms.map(notifInform => {
            const { _id, id, __v, ...rest} = notifInform.toObject();
            return {
                ...rest,
                id: uuid.v4().replace(/-/g, ''),
                informNotifId: _id,
                userId: userMongoId,
                isNotif: true,
                isRead: true,
                visible: true,
            }
        })

        this.eventModel.insertMany(insertData);
    }

    async handlePushNotifUMC_Schedule(): Promise<any> {
        const info = {
            functionName: 'handlePushNotifUMC_Schedule',
            ...this.trackingInfo,
        };
        /* lấy danh sách partner kèm theo appId, api key */
        const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const events = await this.eventModel
            .find({
                isPushNotif: true,
                topicId: 'messages.push-notif',
                type: 3,
            }, { title: true, id: true, userId: true, appId: true, topicId: true, eventData: true, content: true })
            .limit(80)
            .sort({ createdAt: 'asc' })
            .exec();
        const groupByAppId = groupBy(events, 'appId');
        /* tìm lại thông tin devices */
        /* tìm lại danh sách device cần push */
        for await (const [key, notifs] of Object.entries(groupByAppId)) {
            const findAppId = find(partners, { partnerId: key });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false }).exec();
                    /* lấy thông số config push notif */
                    const { eventData }: any = notif;
                    /* kiểm tra xem có insertNotifBeforePush */
                    if (typeof eventData.insertNotifBeforePush !== typeof undefined) {
                        /* Tiến hành insert vào trong notification */
                        const checkUrlNull: any = {};
                        if (!!eventData.url) {
                            checkUrlNull.url = eventData.url;
                        }
                        await this.pkhPatientKnex('notification').insert({
                            title: eventData.title,
                            content: eventData.content,
                            type: eventData.type,
                            user_id: eventData.userId,
                            ...checkUrlNull,
                        });
                    }
                    const resultData: any = { ...eventData };
                    const { url, ...restNotif } = resultData;
                    const objRes: any = {};
                    if (eventData.url) {
                        objRes.url = eventData.url;
                    }
                    const defaultNotif = {
                        contents: {
                            en: notif.content,
                            vi: notif.content,
                        },
                        headings: {
                            en: notif.title,
                            vi: notif.title,
                        },
                        data: { ...restNotif, ...objRes, ...info },
                        include_player_ids: [eventData.clientId],
                    };
                    const viewObj: any = { clientViewId: '', subscribed: false };
                    // console.log(`check device`, defaultNotif);
                    try {
                        const response = await client.createNotification({ ...defaultNotif });
                        const { statusCode, body } = response;
                        // console.log('data', response);
                        if (statusCode === 200) {
                            const { recipients, id } = body;
                            viewObj.status = -1;
                            if (recipients) {
                                viewObj.clientViewId = id;
                                viewObj.subscribed = true;
                                viewObj.status = 1;
                            }
                        }
                        /* cập nhật lại notif */
                        await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, ...viewObj }).exec();
                    } catch (error) {
                        await this.eventModel.findByIdAndUpdate({
                            _id: notif._id,
                        },
                            { isPushNotif: false, status: 0, error, ...viewObj },
                        ).exec();
                        if (error instanceof OneSignal.HTTPError) {
                            return error;
                        }
                    }
                }
            }
        }
    }

    async handlePushNotifNewHospital_Schedule(): Promise<any> {
        const info = {
            functionName: 'handlePushNotifNewHospital_Schedule',
            ...this.trackingInfo,
        };
        /* lấy danh sách partner kèm theo appId, api key */
        const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const events = await this.eventModel
            .find({
                isPushNotif: true,
                topicId: 'messages.push-inform-notif',
                type: 100,
            }, { title: true, id: true, userId: true, appId: true, topicId: true, eventData: true, content: true, devices: true })
            .limit(80)
            .sort({ createdAt: 'asc' })
            .exec();
        const groupByAppId = groupBy(events, 'appId');
        /* tìm lại thông tin devices */
        /* tìm lại danh sách device cần push */
        for await (const [key, notifs] of Object.entries(groupByAppId)) {
            const findAppId = find(partners, { partnerId: key });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false }).exec();
                    /* lấy thông số config push notif */
                    const { eventData }: any = notif;
                    const resultData: any = { ...eventData };
                    const { url, clientId, clientToken, ...restNotif } = resultData;
                    const objRes: any = {};
                    if (eventData.url) {
                        objRes.url = eventData.url;
                    }
                    /* kiểm tra thông tin devices trong event */
                    let playerIds: any = [];
                    const getDeviceEvent = get(notif, 'devices', []);
                    if (getDeviceEvent.length === 0) {
                        playerIds = [eventData.clientId];
                    } else {
                        playerIds = notif.devices.map(itemDe => itemDe.clientId);
                    }
                    const defaultNotif = {
                        contents: {
                            en: notif.content,
                            vi: notif.content,
                        },
                        headings: {
                            en: notif.title,
                            vi: notif.title,
                        },

                        data: { ...restNotif, ...objRes, topicId: notif.topicId, type: 100, ...info }, // 100 tin tức
                        include_player_ids: playerIds,
                    };
                    const viewObj: any = { clientViewId: '', subscribed: false };
                    // console.log(`check device`, defaultNotif);
                    try {
                        const response = await client.createNotification({ ...defaultNotif });
                        const { statusCode, body } = response;
                        // console.log('data', response);
                        if (statusCode === 200) {
                            const { recipients, id } = body;
                            viewObj.status = -1;
                            if (recipients) {
                                viewObj.clientViewId = id;
                                viewObj.subscribed = true;
                                viewObj.status = 1;
                            }
                        }
                        /* cập nhật lại notif */
                        await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, ...viewObj }).exec();
                    } catch (error) {
                        await this.eventModel.findByIdAndUpdate({
                            _id: notif._id,
                        },
                            { isPushNotif: false, status: 0, error, ...viewObj },
                        ).exec();
                        if (error instanceof OneSignal.HTTPError) {
                            return error;
                        }
                    }
                }
            }
        }
    }

    /* dành cho phần thay đổi số tt */
    async getEventsForPushNotifChangeSTT(): Promise<any> {
        const info = {
            functionName: 'getEventsForPushNotifChangeSTT',
            ...this.trackingInfo,
        };
        /* lấy danh sách partner kèm theo appId, api key */
        const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const events = await this.eventModel
            .find(
                {
                    isPushNotif: true,
                    topicId: 'his.confirm',
                    type: 5,
                },
                { title: true, id: true, userId: true, appId: true, topicId: true, eventData: true, content: true })
            .limit(50)
            .sort({ createdAt: 'asc' })
            .exec();
        const groupByAppId = groupBy(events, 'appId');
        /* tìm lại thông tin devices */
        const pluckUserId = map(events, 'userId');
        /* tìm lại danh sách device cần push */
        const listDevices = await this.pushDeviceModel.find({ userId: { $in: pluckUserId } }).exec();
        for await (const [key, notifs] of Object.entries(groupByAppId)) {
            const findAppId = find(partners, { partnerId: key });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false }).exec();
                    /* tìm lại device theo từng appId, userId */
                    const deviceInfos = listDevices
                        .filter(item => item.userId === notif.userId && item.appId === notif.appId);
                    if (deviceInfos.length > 0) {
                        /* lấy thông số config push notif */
                        const mapClients = map(deviceInfos, 'clientId');
                        const { eventData }: any = notif;
                        const resultData: any = { url: 'https://medpro.com.vn' };
                        resultData.transactionId = eventData.transactionId;
                        resultData.type = eventData.type;
                        const defaultNotif = {
                            contents: {
                                en: notif.title,
                                vi: notif.title,
                            },
                            headings: {
                                en: 'Thông báo!',
                                vi: 'Thông báo!',
                            },
                            data: { ...resultData, ...info },
                            include_player_ids: mapClients,
                        };
                        try {
                            await client.createNotification({ ...defaultNotif });
                            /* cập nhật lại notif */
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 1 }).exec();
                        } catch (error) {
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, error }).exec();
                            if (error instanceof OneSignal.HTTPError) {
                                return error;
                            }
                        }
                    } else {
                        await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, errorMessage: 'Không tìm thấy thông tin thiết bị.' }).exec();
                    }
                }
            }
        }
    }

    async getEventsForPushNotifRemindBooking(vdate?: string): Promise<any> {
        /* tìm lại những booking khám vào trong buổi sáng ngày hôm sau */
        let tomorrow = '';
        if (vdate) {
            tomorrow = vdate;
        } else {
            tomorrow = moment().add(1, 'days').format('YYYY-MM-DD');
        }
        // console.log(`tien hanh xu ly ngay ${tomorrow}`);

        const fromDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const display = fromDate.format('DD/MM/YYYY');
        const fromDateParam = fromDate.toDate();
        const toDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        }).toDate();

        /* tìm những partner có config reminder */
        const getBookingReminderByPartner = await this.partnerConfigModel.find({
            bookingReminder: true,
        }, { partnerId: true }).exec();

        const mapPartners = map(getBookingReminderByPartner, 'partnerId');
        // console.log('mapPartners', mapPartners);

        const bookings = await this.bookingModel.find({
            status: 1,
            date: {
                $gt: fromDateParam,
                $lt: toDate,
            },
            partnerId: {
                $in: mapPartners,
            },
            // bookingCode: 'T201020LHKTEG',
        }).exec();

        /* tiến hành insert vào trong event */
        const mapBookingsObject = bookings.map(item => item.toObject());
        for await (const booking of mapBookingsObject) {
            const isPushNotif = (booking.platform === 'android' || booking.platform === 'ios') ? true : false;
            // console.log(`tien hanh tạo event nhắc nhở lịch khám cho ngày ${tomorrow}`);
            /* tiến hành bắn event */
            await this.createEvent({
                topicId: 'bookings.reminder',
                createTime: moment().toISOString(),
                userId: booking.userId,
                appId: booking.appId,
                title: `Bạn có lịch khám vào ngày ${display}. Mã phiếu: ${booking.bookingCode}`,
                partnerId: booking.partnerId,
                eventData: {
                    ...booking,
                    type: 1,
                },
                type: 1,

            }, true, isPushNotif, false);
        }
        return bookings;
    }

    async handlePushRemindBookingUMC(vdate?: string): Promise<any> {
        /* tìm lại những booking khám vào trong buổi sáng ngày hôm sau */
        let tomorrow = '';
        if (vdate) {
            tomorrow = moment(vdate, 'YYYY-MM-DD').isValid() ? vdate : moment().add(1, 'days').format('YYYY-MM-DD');
        } else {
            tomorrow = moment().add(1, 'days').format('YYYY-MM-DD');
        }

        const fromDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const toDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        });

        const fromDateMysql = fromDate.format('YYYY-MM-DD HH:mm:ss');
        const toDateMysql = toDate.format('YYYY-MM-DD HH:mm:ss');

        const bookings = await this.pkhPatientKnex('booking')
            .select('user_id')
            .where('status', 1)
            .whereBetween(`date_create`, [fromDateMysql, toDateMysql]);

        const messageInfo = {
            url: '',
            title: '',
            content: '',
        };
        for await (const booking of bookings) {
            /* tìm lại thông tin userId */
            const userId = booking.user_id;
            const pushDevices = await this.pkhPatientKnex(this.pushDeviceTableName).where('user_id', userId);
            if (pushDevices.length > 0) {
                /* Tiến hành gửi message */
                /* lấy thông tin message config */
                // const messageInfo = await this.messageItemModel.findOne({ messageId: data.messageId }).exec();
                /* tiến hành insert vào trong bảng notifications */
                const checkUrlNull: any = {};
                if (!!messageInfo.url) {
                    checkUrlNull.url = messageInfo.url;
                }
                const [idNotif] = await this.pkhPatientKnex('notification').insert({
                    title: messageInfo.title,
                    content: messageInfo.content,
                    type: (!!messageInfo.url ? 3 : 4),
                    user_id: userId,
                    ...checkUrlNull,
                });
                /* tìm lại thông tin notif vừa insert */
                const getResultData = await this.pkhPatientKnex('notification')
                    .where({ id: idNotif }).first();
                const reMapping = pushDevices.map(item => {
                    return {
                        id: uuid.v4().replace(/-/g, ''),
                        topicId: 'messages.push-notif',
                        createTime: moment().toISOString(),
                        userId: `${userId}`,
                        partnerId: 'umc',
                        appId: 'umc',
                        title: messageInfo.title,
                        content: messageInfo.content,
                        isNotif: true,
                        isPushNotif: true,
                        type: 3,
                        eventData: {
                            ...messageInfo,
                            ...item,
                            clientId: item.onesignal_id,
                            clientToken: item.onesignal_token,
                            ...getResultData,
                        },
                    };
                });
                /* Tiến hành tạo event */
                await this.eventModel.insertMany(reMapping);
                return {
                    isOK: true,
                };
            } else {
                throw new HttpException('Không tìm thấy thông tin thiết bị nhận thông báo !', HttpStatus.NOT_FOUND);
            }
        }
        return bookings;
    }

    async getEventsForPushNotif(): Promise<any> {
        const info = {
            functionName: 'getEventsForPushNotif',
            ...this.trackingInfo,
        };
        /* lấy danh sách partner kèm theo appId, api key */
        const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const events = await this.eventModel
            .find(
                { isPushNotif: true, type: { $in: [0, 1, 2, 7] } },
                { title: true, id: true, userId: true, appId: true, topicId: true, eventData: true, content: true })
            .limit(50)
            .sort({ createdAt: 'asc' })
            .exec();
        const groupByAppId = groupBy(events, 'appId');
        /* tìm lại thông tin devices */
        const pluckUserId = map(events, 'userId');
        /* tìm lại danh sách device cần push */
        const listDevices = await this.pushDeviceModel.find({ userId: { $in: pluckUserId } }).exec();
        for await (const [key, notifs] of Object.entries(groupByAppId)) {
            const findAppId = find(partners, { partnerId: key });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false }).exec();
                    /* tìm lại device theo từng appId, userId */
                    const deviceInfos = listDevices
                        .filter(item => item.userId === notif.userId && item.appId === notif.appId);
                    if (deviceInfos.length > 0) {
                        /* lấy thông số config push notif */
                        const mapClients = map(deviceInfos, 'clientId');
                        const { eventData }: any = notif;
                        const resultData: any = { url: 'https://medpro.com.vn' };
                        if (
                            notif.topicId === 'bookings.confirm' ||
                            notif.topicId === 'bookings.cancel' ||
                            notif.topicId === 'bookings.reminder' ||
                            notif.topicId === 'bookings.not.payment.yet.reminder' ||
                            notif.topicId === 'booking.update'
                        ) {
                            resultData.transactionId = eventData.transactionId;
                            resultData.type = eventData.type;
                        }
                        if (notif.topicId === 'orders.confirm' || notif.topicId === 'orders.cancel') {
                            resultData.transactionId = eventData.transactionId;
                            resultData.type = eventData.type;
                        }
                        /* thêm phần xem thông tin hóa đơn */
                        if (notif.topicId === 'invoice.confirm') {
                            resultData.transactionId = eventData.transactionId;
                            resultData.type = eventData.type;
                            resultData.invoiceUrl = eventData.invoiceUrl;
                            resultData.topicId = notif.topicId;
                        }
                        if (notif.topicId === 'sync-booking.failed') {
                            resultData.transactionId = eventData.transactionId;
                            resultData.type = eventData.type;
                            resultData.content = notif.title;
                            resultData.title = notif.title;
                            resultData.url = '';
                        }
                        const defaultNotif = {
                            contents: {
                                en: notif.title,
                                vi: notif.title,
                            },
                            headings: {
                                en: 'Thông báo!',
                                vi: 'Thông báo!',
                            },
                            data: { ...resultData, ...info },
                            include_player_ids: mapClients,
                        };
                        try {
                            await client.createNotification({ ...defaultNotif });
                            /* cập nhật lại notif */
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 1 }).exec();
                        } catch (error) {
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, error }).exec();
                            if (error instanceof OneSignal.HTTPError) {
                                return error;
                            }
                        }
                    } else {
                        await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, errorMessage: 'Không tìm thấy thông tin thiết bị.' }).exec();
                    }
                }
            } else {
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, errorMessage: 'Chưa cấu hình thông tin push notif.' }).exec();
                }
            }
        }
    }

    async getEventsForPushNotifFeeders(): Promise<any> {
        const info = {
            functionName: 'getEventsForPushNotifFeeders',
            ...this.trackingInfo,
        };
        /* lấy danh sách partner kèm theo appId, api key */
        const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const events = await this.eventModel
            .find(
                { isPushNotif: true, channel: EventChannel.PUSH_NOTIF_APP },
                { title: true, id: true, userId: true, appId: true, topicId: true, eventData: true, content: true })
            .limit(50)
            .sort({ createdAt: 'asc' })
            .exec();
        const groupByAppId = groupBy(events, 'appId');
        /* tìm lại thông tin devices */
        const pluckUserId = map(events, 'userId');
        /* tìm lại danh sách device cần push */
        const listDevices = await this.pushDeviceModel.find({ userId: { $in: pluckUserId } }).exec();
        for await (const [key, notifs] of Object.entries(groupByAppId)) {
            const findAppId = find(partners, { partnerId: key });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false }).exec();
                    /* tìm lại device theo từng appId, userId */
                    const deviceInfos = listDevices
                        .filter(item => item.userId === notif.userId && item.appId === notif.appId);
                    if (deviceInfos.length > 0) {
                        /* lấy thông số config push notif */
                        const mapClients = map(deviceInfos, 'clientId');
                        const { eventData }: any = notif;
                        const resultData: any = { url: 'https://medpro.com.vn' };
                        resultData.transactionId = eventData.transactionId;
                        resultData.topicId = notif.topicId;
                        if (notif.topicId === 'deposit.payment.confirm') {
                            const { paymentHospitalFeeData = {} } = eventData;
                            if (Object.keys(paymentHospitalFeeData).length > 0) {
                                const getBillId = get(paymentHospitalFeeData, 'billId');
                                if (getBillId) {
                                    resultData.billId = getBillId;
                                }
                            }
                        }
                        const defaultNotif = {
                            contents: {
                                en: notif.title,
                                vi: notif.title,
                            },
                            headings: {
                                en: 'Thông báo!',
                                vi: 'Thông báo!',
                            },
                            data: { ...resultData, ...info },
                            include_player_ids: mapClients,
                        };
                        // console.log(defaultNotif);
                        try {
                            const response = await client.createNotification({ ...defaultNotif });
                            const { statusCode, body } = response;
                            // console.log('body', body);
                            /* cập nhật lại notif */
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 1 }).exec();
                        } catch (error) {
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, error }).exec();
                            if (error instanceof OneSignal.HTTPError) {
                                return error;
                            }
                        }
                    } else {
                        await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, errorMessage: 'Không tìm thấy thông tin thiết bị.' }).exec();
                    }
                }
            }
        }
    }

    async testViewNotif(id: string): Promise<any> {
        const client = new OneSignal.Client('************************************', 'YjkwMmViNDctZmU2NC00NTU1LWE5NmUtODFhMDc5YTgyOTgx', { apiRoot: 'https://onesignal.com/api/v1' });
        return client.viewNotification(id);
    }

    async getEventsForSendMailReExam(): Promise<any> {
        const events = await this.eventModel
            .find(
                { isSendMail: true, topicId: 'bookings.reExam', type: 8 }, // event.type = 8
                { title: true, appId: true, eventData: true },
            )
            .limit(1)
            .sort({ createdAt: 'asc' })
            .exec();
        if (events.length > 0) {
            const data = first(events);
            const dataObj = data.toObject();
            const { eventData }: any = dataObj;
            if (eventData) {
                const response =
                    await this.mailerService.sendMailReExam(eventData);
                const firstResponse = first(response);
                const getStatusCode = get(firstResponse, 'statusCode', 0);
                if (getStatusCode >= 200 && getStatusCode < 400) {
                    /* Cập nhật lại event */
                    await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                } else {
                    await this.eventModel.findByIdAndUpdate(
                        { _id: dataObj._id },
                        {
                            isSendMail: false,
                            errorMessage: 'Gửi mail không thành công.',
                        },
                    ).exec();
                }
            }
        }
    }

    async getEventsForSendMailInvoice(): Promise<any> {
        const events = await this.eventModel
            .find(
                { isSendMail: true, topicId: 'invoice.confirm' }, // event.type = 4
                { title: true, userId: true, appId: true, eventData: true },
            )
            .limit(1)
            .sort({ createdAt: 'asc' })
            .exec();
        if (events.length > 0) {
            /* Có thông tin event */
            /* Lấy thông tin bệnh viện */
            const data = first(events);
            const dataObj = data.toObject();
            const { eventData }: any = dataObj;
            if (eventData) {
                /* lấy thông tin booking */
                const email = eventData.emailPatient;
                if (!!email) {
                    /* lấy thông tin partnerId */
                    const hospitalInfo = await this.hospitalModel.findOne({ partnerId: eventData.partnerId }, { name: true }).exec();
                    if (!hospitalInfo) {
                        throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.BAD_REQUEST);
                    }
                    /* populate thong tin trong booking */
                    const booking: IBooking = await (await this.bookingModel
                        .findById({ _id: eventData._id },
                            {
                                service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                                room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                                transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                                insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true, bookingCodeV1: true,
                                syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true, addonServices: true, id: true,
                            }))
                        .populate('service')
                        .populate({ path: 'subject', select: 'name' })
                        .populate({ path: 'room', select: 'name' })
                        .populate({ path: 'doctor', select: 'name' })
                        .populate({ path: 'section', select: 'name' })
                        .populate({
                            path: 'patient', select: {
                                name: true, surname: true, sex: true,
                                birthdate: true, birthyear: true, code: true,
                                address: true,
                            },
                        })
                        .execPopulate();
                    /* lấy thông tin payment */
                    const payment = await this.paymentModel
                        .findOne({ transactionId: booking.transactionId }, { subTotal: true, chargeFeeInfo: true, amount: true })
                        .exec();
                    let resultRoomCheckIn = {};
                    if (typeof booking.checkInRoom === typeof undefined || Object.keys(booking.checkInRoom).length === 0) {
                        try {
                            const baseUrl = await this.getBookingTreeRestApi(booking.partnerId);

                            // add addonservice to getCheckinRoomInfo
                            const { addonServices = [] } = booking;
                            const addonServiceIds = addonServices.length ? addonServices.map(service => service.id) : [];

                            resultRoomCheckIn =
                                (await this.getCheckinRoomInfo(
                                    baseUrl,
                                    booking.partnerId,
                                    booking.serviceId,
                                    booking.subjectId,
                                    booking.roomId,
                                    booking.insuranceType,
                                    addonServiceIds,
                                )
                                    .toPromise()).data;
                        } catch (error) {
                            console.log('Hệ thống không tìm thấy thông tin hướng dẫn đến phòng khám.', error);
                            // throw new HttpException('Kết nối đến checkIn Room bị lỗi', HttpStatus.BAD_REQUEST);
                        }
                    } else {
                        resultRoomCheckIn = booking?.checkInRoom || {};
                    }
                    /* lấy thông tin patientCode */
                    let patients = [];
                    if (typeof booking.patientVersionId !== typeof undefined) {
                        patients = await this.patientVersionModel
                            .find({ id: booking.patientVersionId })
                            .populate('profession')
                            .populate('country')
                            .populate('nation')
                            .populate('city')
                            .populate('district')
                            .populate('ward')
                            .exec();
                    } else {
                        patients = await this.patientModel
                            .find({ id: booking.patientId })
                            .populate('profession')
                            .populate('country')
                            .populate('nation')
                            .populate('city')
                            .populate('district')
                            .populate('ward')
                            .exec();
                    }
                    const firstPatient = first(patients);
                    const patientObj = firstPatient.toObject();
                    /* tìm lại thông tin patient codes */
                    let patientCode = patientObj.code;
                    const appId = get(booking, 'appId', 'medpro');
                    // if (appId !== 'medpro') {
                        const patientCurId = typeof patientObj.patientId !== typeof undefined ? patientObj.patientId : patientObj.id;
                        // const findPatientCode = await this.patientCodeModel.findOne({ appId, patientId: patientCurId }).exec();
                        const findPatientCode = await this.getPatientCodeByPatientId(patientCurId, booking.partnerId);
                        if (findPatientCode) {
                            const getpatientCode = get(findPatientCode.toObject(), 'patientCode', null);
                            if (getpatientCode) {
                                patientCode = findPatientCode.patientCode;
                            }
                        }
                    // }
                    const bookingObj = booking.toObject();
                    bookingObj.patientCode = patientCode;
                    const birthdate = !!patientObj.birthdate && moment(patientObj.birthdate).isValid() ?
                                            moment(patientObj.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
                    bookingObj.patient = {
                        ...firstPatient.toObject(),
                        birthdate,
                    };

                    const bookingData = await this.handleDataForSendMailBooking(bookingObj);
                    const response =
                        await this.mailerService.sendMailBookingSuccess(hospitalInfo, eventData, bookingData, resultRoomCheckIn, payment, true);
                    const firstResponse = first(response);
                    const getStatusCode = get(firstResponse, 'statusCode', 0);
                    if (getStatusCode >= 200 && getStatusCode < 400) {
                        /* Cập nhật lại event */
                        await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                    } else {
                        await this.eventModel.findByIdAndUpdate(
                            { _id: dataObj._id },
                            {
                                isSendMail: false,
                                errorMessage: 'Gửi mail không thành công.',
                            },
                        ).exec();
                    }
                    // return response;
                } else {
                    await this.eventModel.findByIdAndUpdate(
                        { _id: dataObj._id },
                        {
                            isSendMail: false,
                            errorMessage: `Gửi mail không thành công.Kiểm tra lại thông tin format email: ${email}`,
                        },
                    ).exec();
                }
            }
        }
    }

    async getEventsForSendMailCSKH(): Promise<any> {
        const events = await this.sendMailOrSmsModel
            .find(
                { type: 'mail', isSend: false },
            )
            .limit(1)
            .sort({ createdAt: 'asc' })
            .exec();
        if (events.length > 0) {
            /* lấy thông tin booking */
            const event = first(events);
            /* lấy thông tin partnerId */
            const hospitalInfo = await this.hospitalModel.findOne({ partnerId: event.partnerId }, { name: true }).exec();
            if (!hospitalInfo) {
                throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.BAD_REQUEST);
            }
            /* populate thong tin trong booking */
            const booking: any = await (await this.bookingModel
                .findById({ _id: event.id },
                    {
                        service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                        room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                        transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                        insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true, bookingCodeV1: true,
                        syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true, addonServices: true, id: true,
                    }))
                .populate('service')
                .populate({ path: 'subject', select: 'name' })
                .populate({ path: 'room', select: 'name' })
                .populate({ path: 'doctor', select: 'name' })
                .populate({ path: 'section', select: 'name' })
                .populate({
                    path: 'patient', select: {
                        name: true, surname: true, sex: true,
                        birthdate: true, birthyear: true, code: true,
                        address: true,
                    },
                })
                .execPopulate();
            /* lấy thông tin payment */
            const payment = await this.paymentModel
                .findOne({ transactionId: booking.transactionId }, { subTotal: true, chargeFeeInfo: true, amount: true })
                .exec();
            let resultRoomCheckIn = {};
            if (typeof booking.checkInRoom === typeof undefined || Object.keys(booking.checkInRoom).length === 0) {
                try {
                    const baseUrl = await this.getBookingTreeRestApi(booking.partnerId);

                    // add addonservice to getCheckinRoomInfo
                    const { addonServices = [] } = booking;
                    const addonServiceIds = addonServices.length ? addonServices.map(service => service.id) : [];

                    resultRoomCheckIn =
                        (await this.getCheckinRoomInfo(
                            baseUrl,
                            booking.partnerId,
                            booking.serviceId,
                            booking.subjectId,
                            booking.roomId,
                            booking.insuranceType,
                            addonServiceIds,
                        )
                            .toPromise()).data;
                } catch (error) {
                    console.log('Hệ thống không tìm thấy thông tin hướng dẫn đến phòng khám.', error);
                }
            } else {
                resultRoomCheckIn = booking?.checkInRoom || {};
            }

            /* lấy thông tin patientCode */
            let patients = [];
            if (typeof booking.patientVersionId !== typeof undefined) {
                patients = await this.patientVersionModel
                    .find({ id: booking.patientVersionId })
                    .populate('profession')
                    .populate('country')
                    .populate('nation')
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec();
            } else {
                patients = await this.patientModel
                    .find({ id: booking.patientId })
                    .populate('profession')
                    .populate('country')
                    .populate('nation')
                    .populate('city')
                    .populate('district')
                    .populate('ward')
                    .exec();
            }
            const firstPatient = first(patients);
            const patientObj = firstPatient.toObject();
            /* tìm lại thông tin patient codes */
            let patientCode = patientObj.code;
            const appId = get(booking, 'appId', 'medpro');
            // if (appId !== 'medpro') {
                const patientCurId = typeof patientObj.patientId !== typeof undefined ? patientObj.patientId : patientObj.id;
                // const findPatientCode = await this.patientCodeModel.findOne({ appId, patientId: patientCurId }).exec();
                const findPatientCode = await this.getPatientCodeByPatientId(patientCurId, booking.partnerId);
                if (findPatientCode) {
                    const getpatientCode = get(findPatientCode.toObject(), 'patientCode', null);
                    if (getpatientCode) {
                        patientCode = findPatientCode.patientCode;
                    }
                }
            // }

            const birthdate = !!patientObj.birthdate && moment(patientObj.birthdate).isValid() ?
                                            moment(patientObj.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
            const bbb = {
                ...booking.toObject(),
                ...firstPatient.toObject(),
                subTotal: payment.subTotal,
                birthdate,
            };

            const bookingObj = await this.handleDataForSendMailBooking(bbb);
            bookingObj.patientCode = patientCode;
            const eventData: any = {
                bookingCode: bookingObj.bookingCode,
                invoiceUrl: bookingObj.invoiceUrl,
                emailPatient: event.value,
            };

            const response =
                await this.mailerService.sendMailBookingSuccess(hospitalInfo, eventData, bookingObj, resultRoomCheckIn, payment);
            const firstResponse = first(response);
            const getStatusCode = get(firstResponse, 'statusCode', 0);
            if (getStatusCode >= 200 && getStatusCode < 400) {
                /* Cập nhật lại event */
                await this.sendMailOrSmsModel.findByIdAndUpdate({ _id: event._id }, {
                    isSend: true,
                    message: 'Gửi thành công.',
                }).exec();
            } else {
                await this.sendMailOrSmsModel.findByIdAndUpdate(
                    { _id: event._id },
                    {
                        isSend: true,
                        message: 'Gửi mail không thành công.',
                    },
                ).exec();
            }

        }
    }

    async getEventsForSendMail(id?: string): Promise<any> {
        let filter = {};
        if (id) {
            filter = { _id: id };
        } else {
            filter = { isSendMail: true, topicId: { $in: ['bookings.confirm', 'booking.update']} , type: { $in: [1, 2] } };
        }
        // console.log('job gui mail');
        const events = await this.eventModel
            .find(
                // { isSendMail: true, topicId: { $in: ['bookings.confirm', 'booking.update']} , type: { $in: [1, 2] } },
                filter,
                { title: true, userId: true, appId: true, eventData: true },
            )
            .limit(1)
            .sort({ createdAt: 'asc' })
            .exec();
        if (events.length > 0) {
            /* Có thông tin event */
            /* Lấy thông tin bệnh viện */
            const data = first(events);
            const dataObj = data.toObject();
            const { eventData }: any = dataObj;
            if (eventData) {
                if (typeof eventData.type !== typeof undefined && eventData.type === 1) { /* Đặt khám online */
                    /* lấy thông tin booking */
                    const email = eventData.emailPatient;
                    if (!!email) {
                        /* lấy thông tin partnerId */
                        const hospitalInfo = await this.hospitalModel.findOne({ partnerId: eventData.partnerId }, { name: true, partnerId: true }).exec();
                        if (!hospitalInfo) {
                            await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                            throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.BAD_REQUEST);
                        }
                        /* populate thong tin trong booking */
                        const booking: any = await (await this.bookingModel
                            .findById({ _id: eventData._id },
                                {
                                    service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                                    room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                                    transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                                    insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                                    bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
                                    addonServices: true, id: true,
                                }))
                            .populate('service')
                            .populate({ path: 'subject', select: 'name' })
                            .populate({ path: 'room', select: 'name' })
                            .populate({ path: 'doctor', select: 'name' })
                            .populate({ path: 'section', select: 'name' })
                            .populate({
                                path: 'patient', select: {
                                    name: true, surname: true, sex: true,
                                    birthdate: true, birthyear: true, code: true,
                                    address: true,
                                },
                            })
                            .execPopulate();
                        if (!booking) {
                            const message  = `Không tìm thấy phiếu khám với _id: ${eventData._id}!`;
                            await this.eventModel.findByIdAndUpdate(
                                { _id: dataObj._id },
                                {
                                    isSendMail: false,
                                    errorMessage: message,
                                },
                            ).exec();
                            throw new HttpException(message, HttpStatus.BAD_REQUEST);
                        }
                        /* lấy thông tin payment */
                        const payment = await this.paymentModel
                            .findOne({ transactionId: booking.transactionId }, { subTotal: true, chargeFeeInfo: true, amount: true })
                            .exec();
                        let resultRoomCheckIn = {};
                        if (typeof booking.checkInRoom === typeof undefined || Object.keys(booking.checkInRoom).length === 0) {
                            try {
                                const baseUrl = await this.getBookingTreeRestApi(booking.partnerId);

                                // add addonservice to getCheckinRoomInfo
                                const { addonServices = [] } = booking;
                                const addonServiceIds = addonServices.length ? addonServices.map(service => service.id) : [];

                                resultRoomCheckIn =
                                    (await this.getCheckinRoomInfo(
                                        baseUrl,
                                        booking.partnerId,
                                        booking.serviceId,
                                        booking.subjectId,
                                        booking.roomId,
                                        booking.insuranceType,
                                        addonServiceIds,
                                    )
                                        .toPromise()).data;
                            } catch (error) {
                                // console.log('Hệ thống không tìm thấy thông tin hướng dẫn đến phòng khám.', error);
                                // throw new HttpException('Kết nối đến checkIn Room bị lỗi', HttpStatus.BAD_REQUEST);
                                throw error;
                            }
                        } else {
                            resultRoomCheckIn = booking?.checkInRoom || {};
                        }

                        /* lấy thông tin patientCode */
                        let patients = [];
                        if (typeof booking.patientVersionId !== typeof undefined) {
                            patients = await this.patientVersionModel
                                .find({ id: booking.patientVersionId })
                                // .populate('profession')
                                // .populate('country')
                                // .populate('nation')
                                // .populate('city')
                                // .populate('district')
                                // .populate('ward')
                                .exec();
                        } else {
                            patients = await this.patientModel
                                .find({ id: booking.patientId })
                                // .populate('profession')
                                // .populate('country')
                                // .populate('nation')
                                // .populate('city')
                                // .populate('district')
                                // .populate('ward')
                                .exec();
                        }
                        if (patients.length === 0) {
                            const message  = `Không tồn tại bệnh nhân trong user!`;
                            await this.eventModel.findByIdAndUpdate(
                                { _id: dataObj._id },
                                {
                                    isSendMail: false,
                                    errorMessage: message,
                                },
                            ).exec();
                            throw new HttpException(message, HttpStatus.BAD_REQUEST);
                        }
                        const firstPatient = first(patients);
                        const patientObj = firstPatient.toObject();
                        /* tìm lại thông tin patient codes */
                        let patientCode = patientObj.code;
                        const appId = get(booking, 'appId', 'medpro');
                        const patientCurId = typeof patientObj.patientId !== typeof undefined ? patientObj.patientId : patientObj.id;
                        // const findPatientCode = await this.patientCodeModel.findOne({ appId, patientId: patientCurId }).exec();
                        const findPatientCode = await this.getPatientCodeByPatientId(patientCurId, booking.partnerId);
                        if (findPatientCode) {
                            const getpatientCode = get(findPatientCode.toObject(), 'patientCode', null);
                            if (getpatientCode) {
                                patientCode = findPatientCode.patientCode;
                            }
                        }
                        // Bổ sung cho trường hợp đồng bộ với his v2
                        const bookingObj = booking.toObject();
                        bookingObj.patientCode = patientCode;
                        bookingObj.subTotal = payment.subTotal;
                        const birthdate = !!patientObj.birthdate && moment(patientObj.birthdate).isValid() ?
                                         moment(patientObj.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
                        bookingObj.patient = {
                            ...firstPatient.toObject(),
                            birthdate,
                        };
                        const bookingData = await this.handleDataForSendMailBooking(bookingObj);
                        const awaitMessage = get(bookingData, 'awaitMessage', null);
                        if (awaitMessage) {
                            await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                        } else {
                            const response =
                                await this.mailerService.sendMailBookingSuccess(
                                    hospitalInfo, eventData, {
                                    ...bookingData,
                                    ...(!!birthdate && { birthDayPT: birthdate }),
                                    birthYearPT: patientObj.birthyear,
                                },
                                    resultRoomCheckIn, payment);
                            const firstResponse = first(response);
                            const getStatusCode = get(firstResponse, 'statusCode', 0);
                            if (getStatusCode >= 200 && getStatusCode < 400) {
                                /* Cập nhật lại event */
                                await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                            } else {
                                await this.eventModel.findByIdAndUpdate(
                                    { _id: dataObj._id },
                                    {
                                        isSendMail: false,
                                        errorMessage: 'Gửi mail không thành công.',
                                    },
                                ).exec();
                            }
                        }
                        // return response;
                    } else {
                        await this.eventModel.findByIdAndUpdate(
                            { _id: dataObj._id },
                            {
                                isSendMail: false,
                                errorMessage: `Gửi mail không thành công.Kiểm tra lại thông tin format email: ${email}`,
                            },
                        ).exec();
                    }
                } else if (typeof eventData.type !== typeof undefined && eventData.type === 2) { /* Thanh toán phiếu tạm ứng */
                    /* ghi chú */
                    const email = eventData.emailPatient;
                    if (!!email) {
                        /* lấy thông tin partnerId */
                        const hospitalInfo = await this.hospitalModel.findOne({ partnerId: eventData.partnerId }, { name: true, partnerId: true }).exec();
                        if (!hospitalInfo) {
                            throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.BAD_REQUEST);
                        }
                        /* tiến hành gửi mail */
                        const response =
                            await this.mailerService.sendMailPaymentFeeSuccess(hospitalInfo, eventData);
                        const firstResponse = first(response);
                        const getStatusCode = get(firstResponse, 'statusCode', 0);
                        if (getStatusCode >= 200 && getStatusCode < 400) {
                            /* Cập nhật lại event */
                            await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                        } else {
                            await this.eventModel.findByIdAndUpdate(
                                { _id: dataObj._id },
                                {
                                    isSendMail: false,
                                    errorMessage: 'Gửi mail không thành công.',
                                },
                            ).exec();
                        }
                    } else {
                        await this.eventModel.findByIdAndUpdate(
                            { _id: dataObj._id },
                            {
                                isSendMail: false,
                                errorMessage: `Gửi mail Phiếu tạm ứng không thành công.Kiểm tra lại thông tin format email: ${email}`,
                            },
                        ).exec();
                    }
                }
            }
        }
        // return true;
    }

    async sendMailByEventIds(id: string, senderTo: string = ''): Promise<any> {
        // console.log('job gui mail');
        const getEvent: IEvent = await this.eventModel
            .findById(id)
            .exec();
        if (getEvent) {
            /* Lấy thông tin bệnh viện */
            const dataObj = getEvent.toObject();
            const { eventData }: any = dataObj;
            if (eventData) {
                if (typeof eventData.type !== typeof undefined && eventData.type === 1) { /* Đặt khám online */
                    /* lấy thông tin booking */
                    const email = eventData.emailPatient;
                    if (!!email) {
                        /* lấy thông tin partnerId */
                        const hospitalInfo = await this.hospitalModel.findOne({ partnerId: eventData.partnerId }, { name: true }).exec();
                        if (!hospitalInfo) {
                            await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                            throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.BAD_REQUEST);
                        }
                        /* populate thong tin trong booking */
                        const booking: any = await (await this.bookingModel
                            .findById({ _id: eventData._id },
                                {
                                    service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
                                    room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
                                    transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
                                    insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
                                    bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
                                    addonServices: true, id: true,
                                }))
                            .populate('service')
                            .populate({ path: 'subject', select: 'name' })
                            .populate({ path: 'room', select: 'name' })
                            .populate({ path: 'doctor', select: 'name' })
                            .populate({ path: 'section', select: 'name' })
                            .populate({
                                path: 'patient', select: {
                                    name: true, surname: true, sex: true,
                                    birthdate: true, birthyear: true, code: true,
                                    address: true,
                                },
                            })
                            .execPopulate();
                        /* lấy thông tin payment */
                        const payment = await this.paymentModel
                            .findOne({ transactionId: booking.transactionId }, { subTotal: true, chargeFeeInfo: true, amount: true })
                            .exec();
                        let resultRoomCheckIn = {};
                        if (typeof booking.checkInRoom === typeof undefined || Object.keys(booking.checkInRoom).length === 0) {
                            try {
                                const baseUrl = await this.getBookingTreeRestApi(booking.partnerId);

                                // add addonservice to getCheckinRoomInfo
                                const { addonServices = [] } = booking;
                                const addonServiceIds = addonServices.length ? addonServices.map(service => service.id) : [];

                                resultRoomCheckIn =
                                    (await this.getCheckinRoomInfo(
                                        baseUrl,
                                        booking.partnerId,
                                        booking.serviceId,
                                        booking.subjectId,
                                        booking.roomId,
                                        booking.insuranceType,
                                        addonServiceIds
                                    )
                                        .toPromise()).data;
                            } catch (error) {
                                // console.log('Hệ thống không tìm thấy thông tin hướng dẫn đến phòng khám.', error);
                                // throw new HttpException('Kết nối đến checkIn Room bị lỗi', HttpStatus.BAD_REQUEST);
                                throw error;
                            }
                        } else {
                            resultRoomCheckIn = booking?.checkInRoom || {};
                        }

                        /* lấy thông tin patientCode */
                        let patients = [];
                        if (typeof booking.patientVersionId !== typeof undefined) {
                            patients = await this.patientVersionModel
                                .find({ id: booking.patientVersionId })
                                .populate('profession')
                                .populate('country')
                                .populate('nation')
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .exec();
                        } else {
                            patients = await this.patientModel
                                .find({ id: booking.patientId })
                                .populate('profession')
                                .populate('country')
                                .populate('nation')
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .exec();
                        }
                        const firstPatient = first(patients);
                        const patientObj = firstPatient.toObject();
                        /* tìm lại thông tin patient codes */
                        let patientCode = patientObj.code;
                        const appId = get(booking, 'appId', 'medpro');
                        const patientCurId = typeof patientObj.patientId !== typeof undefined ? patientObj.patientId : patientObj.id;
                        // const findPatientCode = await this.patientCodeModel.findOne({ appId, patientId: patientCurId }).exec();
                        const findPatientCode = await this.getPatientCodeByPatientId(patientCurId, booking.partnerId);
                        if (findPatientCode) {
                            const getpatientCode = get(findPatientCode.toObject(), 'patientCode', null);
                            if (getpatientCode) {
                                patientCode = findPatientCode.patientCode;
                            }
                        }
                        // Bổ sung cho trường hợp đồng bộ với his v2
                        const bookingObj = booking.toObject();
                        bookingObj.patientCode = patientCode;
                        bookingObj.subTotal = payment.subTotal;

                        const birthdate = !!patientObj.birthdate && moment(patientObj.birthdate).isValid() ?
                                                moment(patientObj.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
                        bookingObj.patient = {
                            ...firstPatient.toObject(),
                            birthdate,
                        };
                        const bookingData = await this.handleDataForSendMailBooking(bookingObj);
                        const awaitMessage = get(bookingData, 'awaitMessage', null);
                        if (awaitMessage) {
                            await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                        } else {
                            const response =
                                await this.mailerService.sendMailBookingSuccess(hospitalInfo, {
                                    ...eventData,
                                    emailPatient: senderTo ? senderTo : email,
                                }, bookingData, resultRoomCheckIn, payment);
                            const firstResponse = first(response);
                            const getStatusCode = get(firstResponse, 'statusCode', 0);
                            if (getStatusCode >= 200 && getStatusCode < 400) {
                                /* Cập nhật lại event */
                                await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                            } else {
                                await this.eventModel.findByIdAndUpdate(
                                    { _id: dataObj._id },
                                    {
                                        isSendMail: false,
                                        errorMessage: 'Gửi mail không thành công.',
                                    },
                                ).exec();
                            }
                        }
                        // return response;
                    } else {
                        await this.eventModel.findByIdAndUpdate(
                            { _id: dataObj._id },
                            {
                                isSendMail: false,
                                errorMessage: `Gửi mail không thành công.Kiểm tra lại thông tin format email: ${email}`,
                            },
                        ).exec();
                    }
                }
            }
        }
    }

    async handleSyncUserDaLieuUpgrade(): Promise<any> {
        const getRows = await this.syncUserDaLieuUpgradeModel.find({ isOk: false }, { userId: true }).limit(1).exec();
        const firstRow = first(getRows);
        if (firstRow) {
            /* lấy thông tin bên user */
            const getUser: any = await this.pkhPatientKnex(this.userTableName)
                .select('username')
                .where({ id: firstRow.userId })
                .first();
            if (getUser) {
                /* Kiểm tra lại xem username có phải của medproid sync xuống hay ko. */
                const checkMPV2 = `${getUser.username}`.includes('mp');
                if (checkMPV2) {
                    await this.syncUserDaLieuUpgradeModel.findByIdAndRemove({
                        _id: firstRow._id,
                    })
                        .exec();
                } else {
                    /* cập nhật lại thông tin userUpgrade */
                    await this.syncUserDaLieuUpgradeModel.findByIdAndUpdate({
                        _id: firstRow._id,
                    }, {
                        username: getUser.username,
                        fullname: getUser.fullname,
                        isOk: true,
                    })
                        .exec();
                }

            } else {
                await this.syncUserDaLieuUpgradeModel.findByIdAndRemove({
                    _id: firstRow._id,
                })
                    .exec();
            }
        } else {
            this.scheduler.deleteCronJob('sync_user_da_lieu_upgrade');
        }
    }

    async getEventsForSendMailFeeders(): Promise<any> {
        const events = await this.eventModel
            .find(
                { isSendMail: true, channel: EventChannel.MAIL },
                { title: true, userId: true, appId: true, eventData: true },
            )
            .limit(1)
            .sort({ createdAt: 'asc' })
            .exec();
        if (events.length > 0) {
            /* Có thông tin event */
            /* Lấy thông tin bệnh viện */
            const data = first(events);
            const dataObj = data.toObject();
            const { eventData, topicId }: any = dataObj;
            if (eventData) {
                const { email = '' } = eventData;
                if (email && isEmail(email)) {
                    /* lấy thông tin partnerId */
                    const hospitalInfo = await this.hospitalModel.findOne({ partnerId: eventData.partnerId }, { name: true }).exec();
                    if (!hospitalInfo) {
                        throw new HttpException('Không tìm thấy thông tin bệnh viện', HttpStatus.BAD_REQUEST);
                    }
                    let response;
                    switch (topicId) {
                        case 'deposit.payment.confirm': // thanh toan bang thẻ
                            response = await this.mailerService.sendMailPaymentFeeByCard(data, hospitalInfo);
                            break;
                        case 'deposit.confirm': // nạp tiền vào thẻ
                            response = await this.mailerService.sendMailCreateDeposit(data, hospitalInfo);
                            break;
                        default:
                            break;
                    }
                    const firstResponse = first(response);
                    const getStatusCode = get(firstResponse, 'statusCode', 0);
                    if (getStatusCode >= 200 && getStatusCode < 400) {
                        /* Cập nhật lại event */
                        await this.eventModel.findByIdAndUpdate({ _id: dataObj._id }, { isSendMail: false }).exec();
                    } else {
                        await this.eventModel.findByIdAndUpdate(
                            { _id: dataObj._id },
                            {
                                isSendMail: false,
                                errorMessage: 'Gửi mail không thành công.',
                            },
                        ).exec();
                    }
                } else {
                    await this.eventModel.findByIdAndUpdate(
                        { _id: dataObj._id },
                        {
                            isSendMail: false,
                            errorMessage: `Gửi mail không thành công.Kiểm tra lại thông tin format email: ${email}`,
                        },
                    ).exec();
                }
            }
        }
    }

    async syncPushDevicesUMC(): Promise<any> {
        /* sync thông tin user bên UMC */
        const list = await this.receiverListModel
            .find({
                isSync: true,
            })
            .limit(1)
            .exec();
        if (list.length > 0) {
            /* cập nhật lại iSync: false */
            const firstRecord = first(list);
            /* lấy thông tin từ bên UMC MySQL */
            if (firstRecord.partnerId === 'umc') {
                const getDevices = await this.pkhPatientKnex('push_device')
                    .select('onesignal_token as clientToken', 'onesignal_id as clientId')
                    .where('user_id', firstRecord.userId);
                await this.receiverListModel.findByIdAndUpdate({ _id: firstRecord._id }, { isSync: false, pushDevices: getDevices }).exec();
            } else {
                const getDevices = await this.pushDeviceModel.find({ userId: firstRecord.userMongo }).exec();
                await this.receiverListModel.findByIdAndUpdate({ _id: firstRecord._id }, { isSync: false, pushDevices: getDevices }).exec();
            }

        }
    }

    async summaryEventForPushNotifUMC(): Promise<any> {
        const messageSendRecord = await this.messageSendRecordModel.find({ send_status: 'SENDING' })
            .sort({ createTime: 'desc' })
            .limit(1)
            .exec();
        if (messageSendRecord.length > 0) {
            /* query lại trong cái events */
            const firstRecord = first(messageSendRecord);
            const events = await this.eventModel.find(
                { requestId: firstRecord.id },
                { id: true, requestId: true, status: true, clientViewId: true, subscribed: true },
            ).exec();
            const countEvents = events.length;
            if (countEvents >= firstRecord.send_quantity) {
                /* cập nhật lại */
                const filter = events.filter(item => item.toObject().status === 1);
                await this.messageSendRecordModel.findByIdAndUpdate(
                    { _id: firstRecord._id },
                    { send_status: 'COMPLETED', completed_quantity: filter.length },
                ).exec();
            }
        }
    }

    async getEventForPushNotifUMC(): Promise<any> {
        const receiverVersions = await this.receiverVersionModel
            .find({
                active: true,
                partnerId: 'umc',
            })
            .sort({ createdAt: 'asc' })
            .limit(1)
            .exec();
        if (receiverVersions.length > 0) {
            /* tiến hành add vào trong event */
            const firstRecord = first(receiverVersions);
            /* Tiến hành xử lý */
            const check = await this.receiverListModel
                .find({
                    groupId: firstRecord.groupId,
                    partnerId: firstRecord.partnerId,
                    isSync: true,
                })
                .limit(1)
                .exec();
            if (check.length === 0) {
                /* lấy thông tin message config */
                const messageInfo = await this.messageItemModel.findOne({ messageId: firstRecord.messageId }).exec();
                /* cập nhật lại active */
                await this.receiverVersionModel.findByIdAndUpdate({ _id: firstRecord._id }, { active: false }).exec();
                /* Tiến hành cập nhật lại Message send record */
                await this.messageSendRecordModel.findOneAndUpdate({ id: firstRecord.requestId }, { send_status: 'SENDING' }).exec();
                /* tiến hành lấy dữ liệu bỏ vào trong events */
                const getList = await this.receiverListModel.find({
                    groupId: firstRecord.groupId,
                    partnerId: firstRecord.partnerId,
                }).exec();
                /* filter lại những pushDevices empty */
                const filterlist = getList.filter((item: any) => item.pushDevices.length > 0);
                /* unique user */
                const uniqueValue = uniqBy(filterlist, 'userId');
                /* tiến hành insert thông tin vào trong events */
                let eventsData = [];
                for (const detailItem of uniqueValue) {
                    const { pushDevices, ...rest } = detailItem.toObject();
                    const pushDeviceUser = pushDevices.map(item => {
                        return {
                            ...item,
                            ...rest,
                        };
                    });
                    eventsData = [...eventsData, ...pushDeviceUser];
                }
                const objCheckInsertNOtifUmc: any = {};
                if (firstRecord.partnerId === 'umc') {
                    objCheckInsertNOtifUmc.insertNotifBeforePush = true;
                }
                const reMapping = eventsData.map(item => {
                    return {
                        id: uuid.v4().replace(/-/g, ''),
                        topicId: 'messages.push-notif',
                        createTime: moment().toISOString(),
                        userId: `${item.userId}`,
                        partnerId: item.partnerId,
                        appId: item.partnerId,
                        title: messageInfo.title,
                        content: messageInfo.content,
                        requestId: firstRecord.requestId,
                        isNotif: true,
                        isPushNotif: true,
                        type: 3,
                        eventData: {
                            ...messageInfo.toObject(),
                            ...item,
                            type: (!!messageInfo.url ? 3 : 4),
                            ...objCheckInsertNOtifUmc,
                        },
                    };
                });
                /* cập nhật lại thông tin tổng số lương notif */
                await this.messageSendRecordModel.findOneAndUpdate({ id: firstRecord.requestId }, { send_quantity: reMapping.length }).exec();
                const chunkData = chunk(reMapping, 10000);
                for await (const chunkDetail of chunkData) {
                    await this.eventModel.insertMany(chunkDetail);
                }
            }
        }
    }

    async getEventForPushNotifNewHospital(): Promise<any> {
        const receiverVersions = await this.receiverVersionModel
            .find({
                active: true,
                partnerId: {
                    $ne: 'umc',
                },
            })
            .sort({ createdAt: 'asc' })
            .limit(1)
            .exec();
        if (receiverVersions.length > 0) {
            /* tiến hành add vào trong event */
            const firstRecord = first(receiverVersions);
            /* Tiến hành xử lý */
            const check = await this.receiverListModel
                .find({
                    groupId: firstRecord.groupId,
                    partnerId: firstRecord.partnerId,
                    isSync: true,
                })
                .limit(1)
                .exec();
            if (check.length === 0) {
                /* lấy thông tin message config */
                const messageInfo = await this.messageItemModel.findOne({ messageId: firstRecord.messageId }).exec();
                /* cập nhật lại active */
                await this.receiverVersionModel.findByIdAndUpdate({ _id: firstRecord._id }, { active: false }).exec();
                /* Tiến hành cập nhật lại Message send record */
                await this.messageSendRecordModel.findOneAndUpdate({ id: firstRecord.requestId }, { send_status: 'SENDING' }).exec();
                /* tiến hành lấy dữ liệu bỏ vào trong events */
                const getList = await this.receiverListModel.find({
                    groupId: firstRecord.groupId,
                    partnerId: firstRecord.partnerId,
                }).exec();
                /* filter lại những pushDevices empty */
                const filterlist = getList.filter((item: any) => item.pushDevices.length > 0);
                /* unique user */
                const uniqueValue = uniqBy(filterlist, 'userMongo');
                /* tiến hành insert thông tin vào trong events */
                let eventsData = [];
                for (const detailItem of uniqueValue) {
                    const { pushDevices, ...rest } = detailItem.toObject();
                    const pushDeviceUser = pushDevices.map(item => {
                        return {
                            ...item,
                            ...rest,
                        };
                    });
                    eventsData = [...eventsData, ...pushDeviceUser];
                }
                const reMapping = eventsData.map(item => {
                    return {
                        id: uuid.v4().replace(/-/g, ''),
                        topicId: 'messages.push-inform-notif',
                        createTime: moment().toISOString(),
                        userId: `${item.userMongo}`,
                        partnerId: item.partnerId,
                        appId: item.partnerId,
                        title: messageInfo.title,
                        content: messageInfo.content,
                        requestId: firstRecord.requestId,
                        isNotif: true,
                        isPushNotif: true,
                        type: 100,
                        eventData: {
                            ...messageInfo.toObject(),
                            ...item,
                        },
                    };
                });
                /* cập nhật lại thông tin tổng số lương notif */
                await this.messageSendRecordModel.findOneAndUpdate({ id: firstRecord.requestId }, { send_quantity: reMapping.length }).exec();
                const chunkData = chunk(reMapping, 10000);
                for await (const chunkDetail of chunkData) {
                    await this.eventModel.insertMany(chunkDetail);
                }
            }
        }
    }

    pushEventToEventHub(syncProcess: any, eventType: string = 'NEW_BOOKING_V1'): Observable<AxiosResponse<any>> {
        const url = `${this.eventProcessorApi}/eventprocessor/rest/feeder/event`;
        const objValue: any = {};
        switch (eventType) {
            case 'NEW_BOOKING_V1':
                objValue.bookingId = syncProcess.id;
                break;
            case 'NEW_PATIENT_V1':
                objValue.patientId = syncProcess.id;
                break;
            case 'NEW_USER_V1':
                objValue.userId = syncProcess.id;
                break;
            case 'BOOKING_DATE_CHANGED':
                objValue.idBooking = syncProcess.id;
                break;
            default:
                break;
        }
        const params = {
            eventId: syncProcess.processId,
            eventType,
            sourceId: syncProcess.sourceId,
            eventTime: moment(syncProcess.createdAt).format('DDMMYYYYHHmmss'),
            eventData: {
                ...objValue,
            },
        };
        // console.log(url, params);
        return this.httpService.postHttpRequest(url, params);
    }

    async getBookingTreeRestApi(partnerId: string): Promise<any> {
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        const { bookingTreeRestApi = '' } = partnerConfig;

        if (REPO_NAME_BETA.includes(this.repoName)) {
            return this.urlConfigService.getBookingTreeUrl();
        }
        
        if (bookingTreeRestApi) {
            return bookingTreeRestApi;
        } else {
            return this.urlConfigService.getBookingTreeUrl();
        }
    }

    getCheckinRoomInfo(
        baseUrl: string,
        partnerId: string,
        serviceId: string,
        subjectId: string,
        roomId: string,
        insuranceType: string,
        addonServiceIds: string[] = [],
    )
        : Observable<AxiosResponse<any>> {
        // http://103.48.193.51:8080/schedule/v1/booking/room
        return this.httpService.postHttpRequest(`${baseUrl}/schedule/v1/booking/room`, {
            partnerId,
            serviceId,
            subjectId,
            roomId,
            insuranceType,
            addonServiceIds,
        });
    }

    async eventsUnReadByUser(userMongoId: string, appId: string): Promise<any> {
        let events = await this.eventModel
            .find({
                isNotif: true,
                userId: userMongoId,
                appId,
            }, { isRead: true, visible: true, topicId: true, type: true })
            .sort({ createdAt: 'desc' })
            .limit(100)
            .exec();

        // lấy luôn cái visible = false, và isRead = false để bỏ ra.
        const notifInformIds = await this.eventModel.distinct('informNotifId', { appId, userId: userMongoId, isNotif: true}).exec();

        const ignoreGroup100 = events.filter(it => it.topicId === 'messages.push-inform-notif').map(i => i.toObject());
        const uniqByGroup = uniqBy(ignoreGroup100, 'title')

        events = events.filter(it => it.topicId !== 'messages.push-inform-notif')
        events = [...events, ...uniqByGroup];

        const countNotifInform = await this.pushNotifInfomModel.countDocuments({ appId, _id: { $nin: [...notifInformIds] }}).exec();
        const unreadEvent = events.filter(e => e.visible !== false && e.isRead !== true && e.type !== 103);

        return {
            unReadCount: unreadEvent.length + countNotifInform,
            events: unreadEvent.length,
            countNotifInform,
            informNotifIds: [...notifInformIds],
        };
    }

    async eventsByUser(userMongoId: string, appId: string): Promise<any> {
        let eventData = []
        if (this.switchEventNotif) {
            eventData = await this.notificationModel
                .find({
                    appId,
                    userId: userMongoId,
                    isNotif: true,
                })
                .sort({ createdAt: 'desc' })
                .limit(100)
                .exec();
        } else {
            eventData = await this.eventModel
                .find({
                    userId: userMongoId,
                    appId,
                    isNotif: true,
                })
                .sort({ createdAt: 'desc' })
                .limit(100)
                .exec();
        }

        const notifInformIds = await this.eventModel.distinct('informNotifId', { appId, userId: userMongoId, isNotif: true}).exec();

        const ignoreGroup100 = eventData.filter(it => it.topicId === 'messages.push-inform-notif').map(i => i.toObject());
        const uniqByGroup = uniqBy(ignoreGroup100, 'title')

        eventData = eventData.filter(it => it.topicId !== 'messages.push-inform-notif').map(i => i.toObject());
        eventData = [...eventData, ...uniqByGroup];


        eventData = eventData.filter(event => event.visible !== false && event.type !== 103);

        eventData = await Promise.all(eventData.map(async (eObj: any ) => {
            // const eObj = event.toObject();

            const diffDay = moment().utc().add(7, 'hours').diff(moment(eObj.createdAt).utc().add(7, 'hours'), 'day');
            if (eObj.topicId === 'remider.exam.umc' && diffDay >= 7) {
                eObj.type = 1;
                eObj.eventData.type = 1;

                if (!eObj.transactionId) {
                    const query = querystring.parse(eObj.eventData.url.split('?')[1]);
                    const btoken = query['btoken'] as string
                    const payload : any = this.jwtService.decode(btoken);
                    const booking = await this.bookingModel.findOne({ bookingId: payload.bookingId }, { transactionId: true }).exec();

                    if (!booking) {
                        eObj.isNotif = false;
                        // eObj.set({
                        //     isNotif: false,
                        // });
                        await  this.eventModel.findByIdAndUpdate(eObj._id, { isNotif: false, }).exec();
                        // event.save();
                    } else {
                        // event.set({
                        //     eventData: {
                        //         ...eObj.eventData,
                        //         transactionId: booking.transactionId,
                        //     }
                        // });
                        // await event.save();
                        await this.eventModel.findByIdAndUpdate(eObj._id, {
                            eventData: {
                                ...eObj.eventData,
                                transactionId: booking.transactionId,
                            }
                        }).exec();
                        eObj.eventData.transactionId = booking.transactionId;
                    }
                }
            }

            return eObj;
        }))

        // filter bỏ chổ umc update isNotif thành false
        eventData = eventData.filter(e => e.isNotif === true);

        let informNotifs: any[] = await this.pushNotifInfomModel
            .find({
                appId,
                _id: { $nin: [...notifInformIds] }
            })
            .limit(100)
            .sort({ _id: -1 });

        informNotifs = informNotifs
            .map(item => {
                const obj = item.toObject();
                return {
                    ...obj,
                    id: obj._id,
                    isNotif: true,
                    isRead: false,
                    userId: userMongoId,
                    visible: true,
                };
            });

        const allEvent = [...eventData, ...informNotifs];
        allEvent.sort((a,b) => {
            const momentA = moment(a.createdAt)
            const momentB = moment(b.createdAt)
            if (momentA.isAfter(momentB)) {
                return -1;
            } else if (momentA.isBefore(momentB)) {
                return 1;
            } else {
                return 0;
            }
        })

        return allEvent;
    }

    async eventsByUserGroup(userMongoId: string, appId: string): Promise<any> {
        let eventData: IEvent[] = [];
        if (userMongoId) {
            if (this.switchEventNotif) {
                eventData = await this.notificationModel
                    .find({
                        appId,
                        userId: userMongoId,
                        isNotif: true,
                    })
                    .sort({ createdAt: 'desc' })
                    .limit(100)
                    .exec();
            } else {
                eventData = await this.eventModel
                    .find({
                        userId: userMongoId,
                        appId,
                        isNotif: true,
                    })
                    .sort({ createdAt: 'desc' })
                    .limit(100)
                    .exec();
            }
        }

        // lấy luôn cái visible = false để bỏ ra.
        const notifInformIds = await this.eventModel.distinct('informNotifId', { appId, userId: userMongoId, isNotif: true}).exec();

        /* filter bỏ cái topic messages.push-inform-notif */
        const ignoreGroup100 = eventData.filter(it => it.topicId === 'messages.push-inform-notif').map(i => i.toObject());
        const uniqByGroup = uniqBy(ignoreGroup100, 'title')

        eventData = eventData.filter(it => it.topicId !== 'messages.push-inform-notif').map(i => i.toObject());
        eventData = [...eventData, ...uniqByGroup];


        eventData = eventData.filter(event => event.visible !== false && event.type !== 103);

        eventData = await Promise.all(eventData.map(async (eObj: any ) => {
            // const eObj = event.toObject();

            const diffDay = moment().utc().add(7, 'hours').diff(moment(eObj.createdAt).utc().add(7, 'hours'), 'day');
            if (eObj.topicId === 'remider.exam.umc' && diffDay >= 7) {
                eObj.type = 1;
                eObj.eventData.type = 1;

                if (!eObj.transactionId) {
                    const query = querystring.parse(eObj.eventData.url.split('?')[1]);
                    const btoken = query['btoken'] as string
                    const payload : any = this.jwtService.decode(btoken);
                    const booking = await this.bookingModel.findOne({ bookingId: payload.bookingId }, { transactionId: true }).exec();

                    if (!booking) {
                        eObj.isNotif = false;
                        // eObj.set({
                        //     isNotif: false,
                        // });
                        await  this.eventModel.findByIdAndUpdate(eObj._id, { isNotif: false, }).exec();
                        // event.save();
                    } else {
                        // event.set({
                        //     eventData: {
                        //         ...eObj.eventData,
                        //         transactionId: booking.transactionId,
                        //     }
                        // });
                        // await event.save();
                        await this.eventModel.findByIdAndUpdate(eObj._id, {
                            eventData: {
                                ...eObj.eventData,
                                transactionId: booking.transactionId,
                            }
                        }).exec();
                        eObj.eventData.transactionId = booking.transactionId;
                    }
                }
            }

            return eObj;
        }))

        eventData = eventData.filter(e => e.isNotif === true);

        let informNotifs: any[] = await this.pushNotifInfomModel
            .find({
                appId,
                _id: { $nin: [...notifInformIds] }
            })
            .limit(100)
            .sort({ _id: -1 });

        informNotifs = informNotifs
            .map(item => {
                const obj = item.toObject();
                return {
                    ...obj,
                    id: obj._id,
                    isNotif: true,
                    isRead: false,
                    userId: userMongoId,
                    visible: true,
                };
            });

        const allEvent = [...eventData, ...informNotifs].sort((a,b) => {
            const momentA = moment(a.createdAt)
            const momentB = moment(b.createdAt)
            if (momentA.isAfter(momentB)) {
                return -1;
            } else if (momentA.isBefore(momentB)) {
                return 1;
            } else {
                return 0;
            }
        })

        const cfString = await this.globalSettingService.findByKeyAndRepoName('EVENT_GROUP_TOPIC_CONFIG');
        let groupsCf = JSON.parse(cfString);

        const topicConfig = groupsCf.reduce((r, group) => {
            group.topics.forEach(topic => {
                r[topic] = group.key;
            });
            return r;
        }, {});

        groupsCf.forEach(gr => {
            gr.data = [];
            delete gr.topics;
        });

        groupsCf = allEvent.reduce((r, e) => {
            const groupKey = topicConfig[e.topicId];

            if (groupKey) {
                const group = r.find(gr => gr.key === groupKey);
                group.data.push(e);
            } else {
                const group = r.find(gr => gr.key === 'notif');
                group.data.push(e);
            }

            return r;
        }, groupsCf);

        return groupsCf.map(gr => {
            gr.unReadCount = gr.data.filter(d => d.isRead !== true).length;
            return gr;
        });
    }

    async createEvent(data: CreateEventDTO, isNotif?: boolean, isPushNotif?: boolean, isSendMail?: boolean): Promise<any> {
        const id = uuid.v4().replace(/-/g, '');
        const obj: any = {};
        if (data.userId) {
            obj.userId = data.userId;
        }
        const { type = 0 } = data;
        /* lấy thông tin bookingDate */
        const { eventData }: any = data;
        const objRecommend: any = {};
        if (eventData) {
            if (typeof eventData.type !== typeof undefined && eventData.type === 1) {
                objRecommend.bookingDate = eventData.date;
                objRecommend.type = eventData.type;
                objRecommend.isRecommended = true;
            }
        }
        const eventInfo = {
            id,
            createTime: moment(data.createTime).toDate(),
            topicId: data.topicId,
            ...obj,
            ...objRecommend,
            appId: data.appId,
            partnerId: data.appId,
            title: data.title,
            isNotif: isNotif ? true : false,
            isPushNotif: isPushNotif ? true : false,
            isSendMail: isSendMail ? true : false,
            eventData: {
                ...data.eventData,
            },
            type,
        };
        const event = new this.eventModel(eventInfo);
        // For notification
        const notification = new this.notificationModel(eventInfo);
        event.save().catch(error => Logger.log(error));
        const listTopicId = new Set([
            'invoice.confirm',
            'his.confirm',
            'bookings.confirm',
            'bookings.cancel',
            'deposit.confirm',
            'orders.confirm',
            'orders.cancel',
            'booking.update',
        ]);
        const isTopicIdNotif = listTopicId.has(eventInfo.topicId);
        if (isTopicIdNotif) {
            // đẩy sang chỗ MESAGE EVENT xử lý riêng
            this.eventEmitter.emit(MESSAGE_EVENT, {
                appId: data.appId,
                topic: data.topicId,
                partnerId: data.partnerId,
                type,
                title: data.title,
                transactionId: eventInfo.eventData?.transactionId,
                userId: data.userId,
                isPushNotif: isPushNotif ? true : false,
                isSendMail: isSendMail ? true : false,
                eventData: eventInfo.eventData,
            });
            notification.save().catch(error => Logger.error(error));
        }
        return true;
    }

    async isExistUserId(userId: string): Promise<boolean> {
        try {
            const userInfo = await this.eventModel.findOne({ userId }).exec();
            if (userInfo) {
                return true;
            }
        } catch (err) {
            Logger.log(`Error: ${err.message}`);
            throw new HttpException(`Error: ${err.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return false;
    }

    async isExistPartnerId(partnerId: string): Promise<boolean> {
        try {
            const partnerInfo = await this.eventModel.findOne({ partnerId }).exec();
            if (partnerInfo) {
                return true;
            }
        } catch (err) {
            Logger.log(`Error: ${err.message}`);
            throw new HttpException(`Error: ${err.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return false;
    }

    async filterOptionEvent(fromData: SearchEventByUserPhoneDTO): Promise<any> {
        let filter = {};
        const { topicId, type } = fromData;
        if (topicId && type) {
            filter = { topicId, type };
        } else if (topicId && !type) {
            filter = { topicId };
        } else if (!topicId && type) {
            filter = { type };
        }
        return filter;
    }

    async getEventByUserId(userId: string, fromData: SearchEventByUserPhoneDTO): Promise<IEventResponse[]> {
        const { pageIndex, pageSize } = fromData;
        const isExistUserId = await this.isExistUserId(userId);
        if (!isExistUserId) {
            throw new HttpException(`UserId: ${userId} does not exist on eventsModel`, HttpStatus.BAD_REQUEST);
        }
        const filter = await this.filterOptionEvent(fromData);
        try {
            const eventData = await this.eventResponseModel
                .find(
                    { userId },
                    {
                        title: true,
                        requestId: true,
                        clientViewId: true,
                        subscribed: true,
                        isNotif: true,
                        isRead: true,
                        isPushNotif: true,
                        isSendMail: true,
                        type: true,
                        topicId: true,
                        appId: true,
                        partnerId: true,
                    },
                )
                .skip(pageIndex)
                .limit(pageSize)
                .sort({ createdAt: 'desc' })
                .where(filter)
                .exec();
            return eventData;
        } catch (err) {
            Logger.error(`Error: ${err.message}`);
            throw err.message;
        }
    }

    async handlePrams(fromData: SearchEventByUserPhoneDTO): Promise<any> {
        const userId = await this.findUserIdByUserPhone(fromData.userPhone);
        const { partnerId = '' } = fromData;
        let params: any = { userId };
        if (partnerId) {
            params = { ...params, partnerId };
        }
        return params;
    }

    async getTotalRows(fromData: SearchEventByUserPhoneDTO): Promise<number> {
        const params = await this.handlePrams(fromData);
        try {
            return this.eventModel.find({ ...params }).countDocuments();
        } catch (err) {
            Logger.error(`Error when exec getTotalRows() with userPhone: ${params.userPhone}\nError: ${err.message}`);
            throw err;
        }
    }

    async getEventByUserAndPartnerId(userId: string, fromData: SearchEventByUserPhoneDTO): Promise<IEventResponse[]> {
        const { partnerId, pageIndex, pageSize } = fromData;
        const isExistPartner = await this.isExistPartnerId(partnerId);
        if (!isExistPartner) {
            throw new HttpException(`PartnerId: ${partnerId} does not exist on eventsModel`, HttpStatus.BAD_REQUEST);
        }
        const filter = await this.filterOptionEvent(fromData);
        try {
            const eventData = await this.eventResponseModel
                .find(
                    { userId, appId: partnerId },
                    {
                        title: true,
                        requestId: true,
                        clientViewId: true,
                        subscribed: true,
                        isNotif: true,
                        isRead: true,
                        isPushNotif: true,
                        isSendMail: true,
                        type: true,
                        topicId: true,
                        appId: true,
                        partnerId: true,
                    },
                )
                .skip(pageIndex)
                .limit(pageSize)
                .sort({ createdAt: 'desc' })
                .where(filter)
                .exec();
            return eventData;
        } catch (err) {
            Logger.error(`Error: ${err.message}`);
            throw err.message;
        }
    }

    async findUserIdByUserPhone(userPhone: string): Promise<any> {
        const userPhoneInfo = userPhone
            .replace(/^[+]84|^0/, '+84')
            .replace(/^84/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        try {
            const userIdInfo = await this.userModel.findOne({ username: userPhoneInfo }, { username: true }).exec();
            return userIdInfo._id;
        } catch (err) {
            Logger.error(`Error findUserIdByUserPhone() with userPhone: ${userPhone}\nError: ${err.message}`);
            throw new HttpException(`Can't find userId with userPhone: ${userPhone}`, HttpStatus.BAD_REQUEST);
        }
    }

    // Transfer data for response getEventByUserPhone()
    async transferEventResponse(fromData: SearchEventByUserPhoneDTO, events: IEventResponse[]): Promise<any> {
        const { pageSize, pageIndex } = fromData;
        const totalRows = await this.getTotalRows(fromData);
        const eventResponse = {
            totalRows,
            pageSize,
            pageIndex,
            rows: events,
        };
        return eventResponse;
    }

    async getEventByUserPhone(fromData: SearchEventByUserPhoneDTO): Promise<EventResponseDTO> {
        const userId = await this.findUserIdByUserPhone(fromData.userPhone);
        const isExistUserId = await this.isExistUserId(userId);
        if (!isExistUserId) {
            throw new HttpException(`UserId: ${userId} does not exist on eventsModel`, HttpStatus.BAD_REQUEST);
        }

        if (fromData.partnerId) {
            const isExistPartnerId = await this.isExistPartnerId(fromData.partnerId);
            if (!isExistPartnerId) {
                throw new HttpException(
                    `PartnerId: ${fromData.partnerId} does not exist on eventsModel`,
                    HttpStatus.BAD_REQUEST,
                );
            }
            try {
                const eventData = await this.getEventByUserAndPartnerId(userId, fromData);
                const response = await this.transferEventResponse(fromData, eventData);
                return response;
            } catch (err) {
                Logger.error(`Error getEventByUserPhone() with userPhone: ${fromData.userPhone}.\nError:${err.message}`);
                throw err;
            }
        } else {
            try {
                const eventData = await this.getEventByUserId(userId, fromData);
                const response = await this.transferEventResponse(fromData, eventData);
                return response;
            } catch (err) {
                Logger.error(`Error getEventByUserPhone() with userPhone: ${fromData.userPhone}.\nError:${err.message}`);
                throw err;
            }
        }
    }

    async getPartnerIdAndClientViewIdByIdEvent(idEvent: string): Promise<any> {
        try {
            const partnerEvent = await this.eventModel.findById(idEvent).exec();
            if (partnerEvent) {
                const { appId: partnerId, clientViewId } = partnerEvent;
                return {
                    partnerId,
                    clientViewId,
                };
            }
        } catch (error) {
            throw new HttpException(`Lỗi server : ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getPartnerIdFromPartnerConfig(partnerId: string): Promise<string> {
        try {
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            if (partnerConfig) {
                return partnerConfig.partnerId;
            }
        } catch (error) {
            throw new HttpException(`Lỗi server  : ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getNotifyIdAndNotifyKeyByPartnerId(partnerId: string): Promise<any> {
        try {
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            if (partnerConfig) {
                const { notifAppId, notifApiKey } = partnerConfig;
                return {
                    notifAppId,
                    notifApiKey,
                };
            }
        } catch (error) {
            throw new HttpException(`Lỗi server  : ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getNotificationFromOneSigNalByClientViewID(clientViewId: string, notifAppId: string, notifApiKey: string): Promise<any> {
        try {
            const apiRoot = 'https://onesignal.com/api/v1';
            const client = new OneSignal.Client(notifAppId, notifApiKey, { apiRoot });
            return await client.viewNotification(clientViewId);
        } catch (error) {
            throw new HttpException(`Lỗi : ${error.message}`, HttpStatus.NOT_FOUND);
        }
    }

    async getDetailOneSignalePushByIdEvent(idEvent: string): Promise<any> {
        try {
            const { clientViewId, partnerId } = await this.getPartnerIdAndClientViewIdByIdEvent(idEvent);
            if (!partnerId || !clientViewId) {
                throw new HttpException(`Không tồn tại partnerId và clientViewId theo event trong table events!`, HttpStatus.NOT_FOUND);
            }
            const existPartnerConfig = await this.getPartnerIdFromPartnerConfig(partnerId);
            if (!existPartnerConfig) {
                throw new HttpException(`Không tồn tại partnerId trong partner-configs`, HttpStatus.NOT_FOUND);
            }
            const { notifAppId, notifApiKey } = await this.getNotifyIdAndNotifyKeyByPartnerId(existPartnerConfig);
            if (!!notifAppId === false || !!notifApiKey === false) {
                throw new HttpException(`Không tồn tại notify id và notify app id`, HttpStatus.NOT_FOUND);
            }
            const res = await this.getNotificationFromOneSigNalByClientViewID(clientViewId, notifAppId, notifApiKey);
            return res.body;
        } catch (error) {
            throw error;
        }
    }

    async handleDateForTransfrom(date?): Promise<any> {
        const current = date ? moment(date) : moment();
        // Handle UTC time
        const fromDate = current
            .add(7, 'hours')
            .set({ hours: 0, minutes: 0, seconds: 0 })
            .toDate();
        const toDate = current.set({ hours: 23, minutes: 59, seconds: 59 }).toDate();
        return { fromDate, toDate };
    }

    async transformDataToNotification(fromData: TransformDataToNotifDTO): Promise<any> {
        const { date = '' } = fromData;
        const { fromDate, toDate } = await this.handleDateForTransfrom(date);
        try {
            // Get event by date | if date null -> default is moment()
            const listTopicId = ['invoice.confirm', 'his.confirm', 'bookings.confirm', 'deposit.confirm', 'orders.confirm'];
            const event = await this.eventModel.find({ topicId: { $in: listTopicId }, createdAt: { $gte: fromDate, $lt: toDate } }).exec();
            const chunkData = chunk(event, 10000);
            // Transform data from event model to notif model
            for await (const chunkDetail of chunkData) {
                await this.notificationModel.insertMany(chunkDetail);
            }
            return { isOk: true };
        } catch (err) {
            Logger.error(`Error exec transformDataToNotification()\nError: ${err.message}`);
            throw err;
        }
    }

    async handleDataForSendMailBooking(bookingObj): Promise<any> {
        // Bổ sung cho trường hợp đồng bộ với his v2
        if (bookingObj.status === BookingStatus.RESERVE || bookingObj.status === BookingStatus.SHARETOPAY) {
            bookingObj.sequenceNumber = null;
        }
        // Override bookingCode
        const rewriteBookingCode =
            typeof bookingObj.bookingCodeV1 !== typeof undefined && bookingObj.bookingCodeV1 ? bookingObj.bookingCodeV1 : bookingObj.bookingCode;
        bookingObj.bookingCode = rewriteBookingCode;
        /* tiếp tục kiểm tra tình trạng syncBooking */
        const partnerConfig = await this.partnerConfigModel
            .findOne({ partnerId: bookingObj.partnerId }, { syncBooking: true, reserveAndSync: true, displayCodeBooking: true })
            .exec();
        const { syncBooking = false, reserveAndSync = false } = partnerConfig;
        if ((syncBooking || reserveAndSync) && bookingObj.date && bookingObj?.numberConfirmed === false) {
            /* trường hợp trong ngày */
            const checkTrongNgay = moment(bookingObj.date).isSame(moment(), 'day');
            if ((checkTrongNgay || reserveAndSync) && bookingObj.syncStatus !== 'success' && bookingObj.status === 1) {
                // console.log('chay vao toi day')
                bookingObj.sequenceNumber = null;
                bookingObj.date = moment(bookingObj.date)
                    .set({
                        hour: 0,
                        minute: 0,
                        second: 0,
                    })
                    .toDate();
                bookingObj.awaitMessage = 'Hệ thống đang yêu cầu cấp số khám. Vui lòng chờ giây lát.';
            }
        }

        // const displayCodeBooking = partnerConfig.get('displayCodeBooking', {});
        // if (displayCodeBooking && Object.keys(displayCodeBooking).length > 0) {
        //     const template = await this.handlerDisplayCodeBookingForPartner({...bookingObj});
        //     if (!!template) {
        //         displayCodeBooking.value = template;
        //     } else {
        //         displayCodeBooking.value = bookingObj[displayCodeBooking.value] || '';
        //     }
        //     // switch (bookingObj.partnerId) {
        //     //     case 'dkkvangiang':
        //     //         displayCodeBooking.value = `${bookingObj.id}|${rewriteBookingCode}|${bookingObj.partnerId}|$`;
        //     //         break;
        //     //     default:
        //     //         displayCodeBooking.value = bookingObj[displayCodeBooking.value] || '';
        //     //         break;
        //     // }
        // }

        // const setBookingStatus = new Set([0, 6]);
        // if (setBookingStatus.has(bookingObj?.status)) {
        //     displayCodeBooking.value = '';
        // }

        const partnerConfigObj = partnerConfig.toObject();
        const displayCodeBooking = await this.switchDisplayCodeBookingVersion(partnerConfigObj, {...bookingObj});
        const bookingStyleStatus = await this.getStyleBookingStatus(this.utilService.getBookingText(bookingObj), bookingObj?.partnerId);

        return { ...bookingObj, displayCodeBooking, bookingStyleStatus, ...this.getBookingInfoPrice(bookingObj) };
    }

    async getEventsForPushNotifyTelemed(): Promise<any> {
        const info = {
            functionName: 'getEventsForPushNotifyTelemed',
            ...this.trackingInfo,
        };
        /* lấy danh sách partner kèm theo appId, api key */
        const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
        const events = await this.eventModel
            .find(
                { isPushNotif: true, type: 99 },
                { title: true, id: true, userId: true, appId: true, topicId: true, eventData: true, content: true })
            .limit(50)
            .sort({ createdAt: 'asc' })
            .exec();
        const groupByAppId = groupBy(events, 'appId');
        /* tìm lại thông tin devices */
        const pluckUserId = map(events, 'userId');
        /* tìm lại danh sách device cần push */
        const listDevices = await this.pushDeviceModel.find({ userId: { $in: pluckUserId } }).exec();
        for await (const [key, notifs] of Object.entries(groupByAppId)) {
            const findAppId = find(partners, { partnerId: key });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                for await (const notif of notifs) {
                    await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false }).exec();
                    /* tìm lại device theo từng appId, userId */
                    const deviceInfos = listDevices
                        .filter(item => item.userId === notif.userId && item.appId === notif.appId);
                    if (deviceInfos.length > 0) {
                        /* lấy thông số config push notif */
                        const mapClients = map(deviceInfos, 'clientId');
                        const { eventData }: any = notif;
                        const resultData: any = { url: 'https://medpro.com.vn' };
                        if (notif.topicId === 'bookings.telemed.reminder') {
                            resultData.transactionId = eventData.transactionId;
                            resultData.type = 1;
                        }

                        const defaultNotif = {
                            contents: {
                                en: notif.title,
                                vi: notif.title,
                            },
                            headings: {
                                en: 'Thông báo!',
                                vi: 'Thông báo!',
                            },
                            data: { ...resultData, ...info },
                            include_player_ids: mapClients,
                        };
                        try {
                            await client.createNotification({ ...defaultNotif });
                            /* cập nhật lại notif */
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 1 }).exec();
                            this.logger.debug(`Onesignal is pushed for eventId: ${notif._id}, userId: ${notif.userId}`);
                        } catch (error) {
                            await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, error }).exec();
                            if (error instanceof OneSignal.HTTPError) {
                                return error;
                            }
                        }
                    } else {
                        await this.eventModel.findByIdAndUpdate({ _id: notif._id }, { isPushNotif: false, status: 0, errorMessage: 'Không tìm thấy thông tin thiết bị.' }).exec();
                    }
                }
            }
        }
    }

    async handleMessageEvent(): Promise<any> {
        const [countProcess, countMessageEvent] = await Promise.all([
            this.messageEventProcessModel.countDocuments(),
            this.messageEventModel.countDocuments({ transporter: 'mail' }),
        ]);
        if (countProcess === 0 && countMessageEvent > 0) {
            try {
                const messageEvents = await this.messageEventModel.find({ transporter: 'mail' }).limit(1).lean();
                const messageEvent = first(messageEvents);
                await this.messageEventProcessModel.create({
                    ...messageEvent,
                    syncStatus: SyncProcessStatus.PENDING,
                });
                await this.messageEventModel.findByIdAndRemove(messageEvent._id);
            } catch (error) {
                this.logger.error(error);
            }
        }
    }

    // tiến hành dẩy message-event sang message-event-process để push notif
    async handleMessageEventProcess(): Promise<any> {
        const processList = await this.messageEventProcessModel.find({
            syncStatus: SyncProcessStatus.PENDING,
        }).limit(1).lean();
        const messageEventProcess = first(processList);
        if (messageEventProcess) {
            const process = await this.messageEventProcessModel
                .findByIdAndUpdate(messageEventProcess._id, { syncStatus: SyncProcessStatus.ACTIVE })
                .lean();
            const { syncStatus, ...data } = process;
            try {
                const result = await this.pushOneSignalMessageEvent(process);
                if (result) {
                    await this.messageEventSuccessModel.create({
                        ...data,
                        syncStatus: SyncProcessStatus.SUCCESS,
                    });
                }
                await this.messageEventProcessModel.findByIdAndRemove(messageEventProcess._id).exec();
            } catch (error) {
                const newProcessFailed = new this.messageEventProcessFailModel({
                    ...data,
                    syncStatus: SyncProcessStatus.ERRORED,
                    errorBody: this.utilService.errorHandler(error),
                });
                await newProcessFailed.save();
                await this.messageEventProcessModel.findByIdAndRemove(messageEventProcess._id).exec();
                this.logger.error(`Error Handle MessageEvent Process : ${process._id}`);
            }
        }
        return;
    }

    // tiến hành push notif theo process message event
    async pushOneSignalMessageEvent(process: IMessageEventProcess): Promise<boolean> {
        const info = {
            functionName: 'pushOneSignalMessageEvent',
            ...this.trackingInfo,
        };
        const { title, userId, clientIds, notifApiKey, notifAppId, eventData, transporter } = process;
        this.logger.debug(`Data process: ${{ title, userId, clientIds, notifApiKey, notifAppId, transporter }}`);
        const defaultNotif = {
            contents: {
                en: title,
                vi: title,
            },
            headings: {
                en: 'Thông báo!',
                vi: 'Thông báo!',
            },
            data: {
                ...info,
                ...eventData,
            },
            include_player_ids: clientIds,
        };
        try {
            switch (transporter) {
                case TransporterEvent.PUSH:
                    const client = new OneSignal.Client(notifAppId, notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                    const response = await client.createNotification({ ...defaultNotif });
                    this.logger.debug(`Onesignal is pushed for message Event: ${process._id}, userId: ${userId}`);
                    return response.statusCode === 200 ? true : false;
                case TransporterEvent.MAIL:
                    // luồng mail
                    const res = this.sendgrid.send({
                        ...eventData,
                    });
                    this.logger.debug(`Running sengrid mail for message Event: ${process._id}, userId: ${userId}`);
                    return (res.statusCode >= 200 && res.statusCode < 400) ? true : false;
            }
        } catch (error) {
            if (error instanceof OneSignal.HTTPError) {
                this.logger.error(`Onesignal is pushed for message Event : ${error}`);
                throw error;
            }
        }
    }

    getBookingInfoPrice(bookingInfo: any): object {
        const currencyCode = ' VND';
        if (this.isEnableBookingTransform) {
            // trường hợp có serviceInfo
            if (bookingInfo.serviceInfo) {
                // if (bookingInfo?.subTotal || false) {
                //     const { priceDescription: priceDesc = '' } = bookingInfo.serviceInfo;
                //     const getPriceText = this.getPriceText(bookingInfo.subTotal, priceDesc);
                //     bookingInfo.service.priceText = getPriceText;
                //     bookingInfo.service.price = bookingInfo.subTotal;
                // } else {
                const { price: priceInfo, priceText } = bookingInfo.serviceInfo;
                bookingInfo.service.priceText = priceText;
                bookingInfo.service.price = priceInfo;
                // }
                return { service: bookingInfo.service };
            }
            const { price = 0, priceDescription = '' } = bookingInfo.service;
            const overridePrice = price; // bookingInfo?.subTotal || price;
            if (overridePrice > 0) {
                bookingInfo.service.priceText = `${overridePrice}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.') + currencyCode;
            }
            if (overridePrice === 0) {
                // bookingInfo.service.price = price + currencyCode;
                if (bookingInfo.service?.priceDescription || '') {
                    bookingInfo.service.priceText = priceDescription;
                } else {
                    bookingInfo.service.priceText = overridePrice + currencyCode;
                }
            }
            return { service: bookingInfo.service };
        }
        return {};
    }

    getPriceText(price: number, priceDescription: string): string {
        const currencyCode = ' VND';
        let priceText = '';
        if (price > 0) {
            priceText = `${price}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.') + currencyCode;
        }
        if (price === 0) {
            if (priceDescription || '') {
                priceText = priceDescription;
            } else {
                priceText = price + currencyCode;
            }
        }
        return priceText;
    }

    async handlerDisplayCodeBookingForPartner(booking: any): Promise<string> {
        const bookingObj = booking;

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: bookingObj.partnerId }, {
            templateQrCodeContent: 1,
            charSeparateQrcodeContent: 1,
        }).exec();
        let templateQrCodeContent = get(partnerConfig, 'templateQrCodeContent', '');

        if (!!templateQrCodeContent) {
            const charSeparateQrcodeContent = get(partnerConfig, 'charSeparateQrcodeContent', '|');
            let { patient } = bookingObj;
            patient = {...patient, fullname: `${patient.surname} ${patient.name}`};
            patient.address = `${this.patientService.getFullAddress(patient)}`;
            patient.birthdate = `${!!patient.birthdate ? patient.birthdate : patient.birthyear}`;
            bookingObj.insuranceCode = !!bookingObj.insuranceCode ? `${bookingObj.insuranceCode}` : 'null';

            // qui định giới tính từng bệnh viện
            switch (bookingObj.partnerId) {
                case 'hoanmyvp1':
                    patient.sex = `${patient.sex === 0 ? 2 : 1 }`;
                    break;
                default:
                    break;
            }
            bookingObj.patient = patient;

            const arrTemplates = templateQrCodeContent.split(charSeparateQrcodeContent);
            for (const itemTemplate of arrTemplates) {
                const arrItemTemplate = itemTemplate.split(':');
                const checkEncode = last(arrItemTemplate);
                const keyString = first(arrItemTemplate);
                const prop = get(bookingObj, keyString);

                if (checkEncode === 'hex') {
                    set(bookingObj, keyString, Buffer.from(prop).toString('hex'));
                    const newProp = get(bookingObj, keyString);
                    templateQrCodeContent = templateQrCodeContent.replace(':hex', '').replace(keyString, newProp);
                } else {
                    templateQrCodeContent = templateQrCodeContent.replace(keyString, prop || keyString);
                }
            }
        }
        return templateQrCodeContent;
    }

    async switchDisplayCodeBookingVersion(partnerConfig: any, bookingObj: any): Promise<any> {
        const displayCodeBookingV2 = get(partnerConfig, 'displayCodeBookingV2');
        let displayCodeBooking = get(partnerConfig, 'displayCodeBooking', {});
        const setBookingStatus = new Set([-2, 0, 6]);
        if (setBookingStatus.has(bookingObj.status) || displayCodeBooking?.visible === false) {
            return {
                ...displayCodeBooking,
                type: displayCodeBooking?.type || '',
                value: '',
                title: displayCodeBooking?.title || '',
            };
        }
        let template: string;
        if (displayCodeBookingV2 && displayCodeBookingV2.length) {
            const { treeId } = bookingObj;
            const findDisplayCodeBookingV2 = displayCodeBookingV2.find(codeDisplay => codeDisplay.treeId === treeId);
            if (treeId && findDisplayCodeBookingV2) {
                bookingObj.displayCodeBooking = findDisplayCodeBookingV2;
                displayCodeBooking = findDisplayCodeBookingV2;
                template = await this.handlerDisplayCodeBookingForPartner({ ...bookingObj });
            } else {
                template = await this.handlerDisplayCodeBookingForPartner({ ...bookingObj });
            }
        } else if (displayCodeBooking && Object.keys(displayCodeBooking).length > 0) {
            template = await this.handlerDisplayCodeBookingForPartner({ ...bookingObj });
        }

        if (template) {
            displayCodeBooking.value = template;
        } else {
            displayCodeBooking.value = bookingObj[displayCodeBooking.value] || '';
        }

        return displayCodeBooking;
    }

    async getPatientCodeByPatientId(patientId: string, partnerId: string, treeId?: string): Promise<any> {
        const patientCodes = await this.patientCodeModel.find({
            partnerId,
            patientId,
            patientCode: { $nin: [null, ''] },
        }).exec();

        if (treeId && treeId === 'CLS') {
            const patientCodeCls = await this.patientCodeModel.find({
                partnerId,
                patientId,
            }).exec();

            return patientCodeCls.length !== 0 ? first(patientCodeCls) : first(patientCodes);
        }

        return first(patientCodes);
    }

    async getStyleBookingStatus(bookingStatusText: string, partnerId: string): Promise<any> {
        const slug = this.utilService.slugVietnameseName(bookingStatusText, '_').toLowerCase();
        const bookingStyleStatus = await this.globalSettingService.findByKeyAndRepoName('STYLE_BOOKING_STATUS');
        const bookingStyleStatusObj = JSON.parse(bookingStyleStatus);
        return get(bookingStyleStatusObj, `${partnerId}|${slug}`, get(bookingStyleStatusObj, `${slug}`));
    }

    async updateNotifNe(): Promise<any> {
        return this.pkhPatientKnex('notification').where('title', 'BẠN CÓ LỊCH HẸN KHÁM BỆNH').update({
            user_id: 0
        });
    }

    async pushBookingNotif({ vdate, bookingCode }: any = {}): Promise<any> {
        const partnerId = 'umc';

        let tomorrow = '';
        if (vdate) {
            tomorrow = moment(vdate, 'YYYY-MM-DD').isValid() ? vdate : moment().add(1, 'days').format('YYYY-MM-DD');
        } else {
            tomorrow = moment().add(7, 'hours').add(1, 'days').format('YYYY-MM-DD');
        }

        const fromDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 0,
            minutes: 0,
            seconds: 0,
        });

        const toDate = moment(tomorrow, 'YYYY-MM-DD').set({
            hours: 23,
            minutes: 59,
            seconds: 59,
        });

        const fromDateMysql = fromDate.format('YYYY-MM-DD HH:mm:ss');
        const toDateMysql = toDate.format('YYYY-MM-DD HH:mm:ss');

        // console.log('fromDateMysql: ', fromDateMysql)
        // console.log('toDateMysql: ', toDateMysql)


        const query = this.pkhPatientKnex('booking')
            .where('status', 1)
            .whereBetween(`booking_date`, [fromDateMysql, toDateMysql]);

        if (bookingCode) {
            query.where('transaction_code_gd', bookingCode)
        }

        const bookings = await query;
        this.logger.log(`Tien hanh chay job  ${fromDateMysql} ~ ${toDateMysql}: bookings: ${bookings.length} `)
        for await (const booking of bookings) {
            const messageInfo = {
                id: uuid.v4().replace(/-/g, ''),
                title: 'BẠN CÓ LỊCH HẸN KHÁM BỆNH',
                content: `Quý NB có lịch hẹn khám bệnh tại BV Đại học Y Dược TP.HCM Cơ sở 1 vào lúc {{time}}, ngày {{date}}. NB Vui lòng có mặt BV trước giờ hẹn 30 phút để xác nhận thông tin. Phiếu khám có giá trị trong ngày khám từ 6h30-16h30. Riêng thứ bảy chỉ có giá trị từ 6h30-11h30.`,
            };

            if (booking.app === 'medpro') {
                const v2Booking = await this.bookingModel.findOne({ bookingCodeV1: booking.transaction_code_gd, partnerId }).exec();
                if (!v2Booking) {
                    continue;
                }
                let overrideContent = messageInfo.content;
                overrideContent = overrideContent.replace(
                    '{{time}}',
                    moment(v2Booking.date)
                        .utc()
                        .add(7, 'hours')
                        .format('HH:mm'),
                );
                overrideContent = overrideContent.replace(
                    '{{date}}',
                    moment(v2Booking.date)
                        .utc()
                        .add(7, 'hours')
                        .format('DD/MM/YYYY'),
                );
                messageInfo.content = overrideContent;
                await this.createEvent(
                    {
                        topicId: 'bookings.reminder',
                        createTime: moment().toISOString(),
                        userId: v2Booking.userId,
                        appId: v2Booking.appId,
                        title: messageInfo.content,
                        partnerId: v2Booking.partnerId,
                        eventData: {
                            ...v2Booking.toObject(),
                            type: 1,
                        },
                        type: 1,
                    },
                    true,
                    true,
                    false,
                );
            } else {
                const v2Booking = await this.bookingModel.findOne({ bookingCode: booking.transaction_code_gd, partnerId }).exec();
                if (!v2Booking) {
                    continue;
                }
                /* tìm thông tin trong push device */
                const pushDevices = await this.pkhPatientKnex(this.pushDeviceTableName).where('user_id', booking.user_id);

                let overrideContent = messageInfo.content;
                overrideContent = overrideContent.replace(
                    '{{time}}',
                    moment(v2Booking.date)
                        .utc()
                        .add(7, 'hours')
                        .format('HH:mm'),
                );
                overrideContent = overrideContent.replace(
                    '{{date}}',
                    moment(v2Booking.date)
                        .utc()
                        .add(7, 'hours')
                        .format('DD/MM/YYYY'),
                );
                messageInfo.content = overrideContent;

                // console.log('messageInfo: ', messageInfo);
                // console.log('pushDevices: ', pushDevices.length);

                if (pushDevices.length > 0) {
                    const [idNotif] = await this.pkhPatientKnex('notification').insert({
                        title: messageInfo.title,
                        content: messageInfo.content,
                        type: 4,
                        // type: (!!messageInfo.url ? 3 : 4),
                        user_id: booking.user_id,
                    });
                    /* tìm lại thông tin notif vừa insert */
                    const getResultData = await this.pkhPatientKnex('notification')
                        .where({ id: idNotif })
                        .first();
                    /* lấy thông tin message */
                    const reMapping = pushDevices.map(item => {
                        return {
                            id: uuid.v4().replace(/-/g, ''),
                            topicId: 'messages.push-notif',
                            createTime: moment().toISOString(),
                            userId: `${booking.user_id}`,
                            partnerId,
                            appId: partnerId,
                            title: messageInfo.title,
                            content: messageInfo.content,
                            isNotif: true,
                            isPushNotif: true,
                            type: 3,
                            eventData: {
                                ...messageInfo,
                                ...item,
                                clientId: item.onesignal_id,
                                clientToken: item.onesignal_token,
                                // type: 3, // hên là trùng với type = 3 trong bảng umc notification
                                ...getResultData,
                            },
                        };
                    });
                    /* Tiến hành tạo event */
                    await this.eventModel.insertMany(reMapping);
                }
            }
        }
        return {
            isOK: true,
        };
    }

    async removeAllNotiByUser(userMongoId: string, appId: string): Promise<any> {
        if (this.switchEventNotif) {
            await this.notificationModel
                .updateMany({ userId: userMongoId, appId }, { visible: false, isRead: true }, { new: true })
                .exec();
        } else {
            await this.eventModel
                .updateMany({ userId: userMongoId, appId }, { visible: false, isRead: true }, { new: true })
                .exec();
        }

        await this.removeSystemNotifUser(userMongoId, appId);
        this.cacheService.delByPattern(`event.*userMongoId=${userMongoId}`);

        return { isOk: true };
    }


    async removeSystemNotifUser(userMongoId: string, appId: string) {

        const pushDeviceTime = await this.pushDeviceModel.find({ userId: userMongoId, appId }).limit(1).sort({_id: 1 }).exec();

        const events = await this.eventModel.find({ userId: userMongoId, appId, isNotif: true }, { informNotifId: true }).exec();
        const informNotifIds = events.filter(e => !!e.informNotifId).map(e => e.informNotifId);

        const registerDeviceMongoId = pushDeviceTime[0]?._id;


        const notifInforms = await this.pushNotifInfomModel
            .find({
                _id: {
                    ...(registerDeviceMongoId && { $gte: registerDeviceMongoId }),
                    $nin: informNotifIds,
                },
                appId,
            })
            .limit(100)
            .sort({ _id: -1 })
            .exec();

        const newEvents = notifInforms.map(notifInform => {
            const { _id, id, __v, ...rest} = notifInform.toObject()
            return {
                ...rest,
                id: uuid.v4().replace(/-/g, ''),
                informNotifId: _id,
                userId: userMongoId,
                isNotif: true,
                isRead: true,
                visible: false,
            }
        });

        await this.eventModel.insertMany(newEvents);

        return { isOk: true };
    }

    async removeNotiById(userMongoId: string, id: string): Promise<any> {
        let noti;
        if (this.switchEventNotif) {
            noti = await this.notificationModel
                .findOneAndUpdate({ userId: userMongoId, id }, { visible: false, isRead: true })
                .exec();
        } else {
            noti = await this.eventModel
                .findOneAndUpdate({ userId: userMongoId, id }, { visible: false, isRead: true })
                .exec();
        }

        // check trong bảng tin tức hệ thống
        if (!noti) {
            const notifInform = await this.pushNotifInfomModel.findOne({ _id: id }).exec();

            if (notifInform) {
                const { _id, __v, id, ...rest} = notifInform.toObject();
                await this.eventModel.create({
                    ...rest,
                    id: uuid.v4().replace(/-/g, ''),
                    informNotifId: _id,
                    userId: userMongoId,
                    isNotif: true,
                    isRead: true,
                    visible: false,
                })
            }
        }

        this.cacheService.delByPattern(`event.*userMongoId=${userMongoId}`)

        return { isOk: true };
    }
}
