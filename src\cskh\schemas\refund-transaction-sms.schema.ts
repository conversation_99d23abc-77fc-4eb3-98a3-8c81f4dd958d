import * as mongoose from 'mongoose';
import * as json<PERSON><PERSON>o from '@meanie/mongoose-to-json';
import { REFUND_TRANSACTION_SMS } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';
import { PATIENT_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const RefundTransactionSmsSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String, required: true },
    medproId: { type: String },
    fullname: { type: String },
    transactionId: { type: String, index: true, required: true },
    payment: { type: Schema.Types.ObjectId, ref: PAYMENT_COLLECTION_NAME },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    type: { type: Number },
    bookingCode: { type: String },
    partnerId: { type: String, index: true },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    subTotal: { type: Number, required: true },
    smsStatus: { type: Number, default: 0 }, //0: khởi tạo, 1: đã gửi, -2: lỗi
    refundBankUser: { type: Schema.Types.Mixed },
    smsTimeSent: { type: Date },
    userAction: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    smsContent: { type: String },
    refundStatus: { type: Boolean, default: false },
    refundDate: { type: Date },
    note: { type: String }
}, {
    collection: REFUND_TRANSACTION_SMS,
    timestamps: true,
}).plugin(jsonMongo);
