import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { CANCEL_BOOKING_LOG_NAME } from '../constant';
import * as uuid from 'uuid';

const Schema = mongoose.Schema;

const id = () => {
    const uuidv4 = uuid.v4();
    const idv4 = uuidv4.replace(/-/g, '');
    return idv4;
};

export const CancelBookingLogSchema = new Schema({
    uuid: {type: String , default: id},
    name: { type: String },
    summary: { type: String },
    nameParent: { type: String },
    params: { type: Schema.Types.Mixed },
    response: { type: Schema.Types.Mixed},
    errorBody: { type: Schema.Types.Mixed },
    message: { type: String },
    nameRepo: { type: String },
    bookingCode: { type: String },
    bookingDate: { type: Date },
    status: { type: Number },
    partnerId: { type: String },
}, {
    collection: CANCEL_BOOKING_LOG_NAME,
    timestamps: true,
    versionKey: false,
}).plugin(jsonMongo);
