import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsDateString, IsMobilePhone, IsEmail, MaxLength, Validate, IsNumberString } from 'class-validator';
import * as moment from 'moment';
import { CheckValidVietNamCambodia } from '../../user/dto/valid-phone-VN-KMdto';

export class PatientFormMongoUpdateDTO {

    @ApiProperty({
        description: 'Họ và tên lót',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @MaxLength(100, {
        message: 'Vui lòng không vượt quá 100 ký tự.',
    })
    @Transform(value => `${value}`.trim().toUpperCase())
    readonly surname: string;

    @ApiProperty({
        description: 'Ten',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: '<PERSON><PERSON> lòng bổ sung thông tin',
    })
    @MaxLength(50, {
        message: 'Vui lòng không vượt quá 50 ký tự.',
    })
    @Transform(value => `${value}`.trim().toUpperCase())
    readonly name: string;

    @ApiProperty({
        description: 'Mobile',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @Validate(CheckValidVietNamCambodia, ['mobileLocaleIso'], {
        message: 'Vui lòng nhập đúng định dạng mobile phone',
    })
    @Transform(value => `${value}`.trim())
    mobile: string;
    mobileLocaleIso?: string;

    @ApiProperty({
        description: 'Năm sinh',
        required: true,
        type: Number,
    })
    readonly birthyear: number;

    @ApiProperty({
        description: 'Ngày tháng năm sinh',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, {
        message: 'Ngày sinh theo ISOString',
    })
    @ValidateIf(o => (moment().year() - o.birthyear <= 3))
    readonly birthdate: string;

    @ApiProperty({
        description: 'Giới tính',
        required: true,
        type: Number,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @Transform(value => Number(value))
    readonly sex: number;

    @ApiProperty({
        description: 'insuranceId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    insuranceId?: string;

    @ApiProperty({
        description: 'CMND',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @MaxLength(100, {
        message: 'Vui lòng nhập đúng số CMND',
    })
    // @IsNumberString({}, {
    //     message: 'Vui lòng nhập đúng CMND',
    // })
    @ValidateIf(o => o.cmnd)
    @Transform(value => `${value}`.trim())
    readonly cmnd: string;

    @ApiProperty({
        description: 'Email',
        required: false,
        type: String,
    })
    @ValidateIf(o => o.email && o.email !== 'null')
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @Transform(value => `${value}`.trim())
    readonly email: string;

    @ApiProperty({
        description: 'Nghề nghiệp',
        required: false,
        type: String,
    })
    // tslint:disable-next-line: variable-name
    readonly profession_id: string;

    @ApiProperty({
        description: 'Quốc gia',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    // tslint:disable-next-line: variable-name
    readonly country_code: string;

    @ApiProperty({
        description: 'Dân tộc',
        required: true,
        type: String,
    })
    // tslint:disable-next-line: variable-name
    dantoc_id: string;


    @ValidateIf(o => o.country_code === 'VIE')
    @ApiProperty({
        description: 'Tỉnh/Thành phố',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    // tslint:disable-next-line: variable-name
    city_id: string;

    @ApiProperty({
        description: 'Quận/Huyện',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.country_code === 'VIE')
    // tslint:disable-next-line: variable-name
    readonly district_id: string;

    @ApiProperty({
        description: 'Phường/Xã',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.country_code === 'VIE')
    // tslint:disable-next-line: variable-name
    readonly ward_id: string;

    @ApiProperty({
        description: 'Địa chỉ ( số nhà + tên đường)',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @MaxLength(100, {
        message: 'Vui lòng không vượt quá 100 ký tự.',
    })
    @Transform(value => `${value}`.trim())
    @ValidateIf(o => o.country_code === 'VIE')
    readonly address: string;

    /* Thông tin thân nhân */
    @ApiProperty({
        description: 'Họ và tên người thân',
        required: false,
        type: String,
    })
    @MaxLength(100, {
        message: 'Vui lòng không vượt quá 100 ký tự.',
    })
    @ValidateIf(o => o.relative_name)
    @Transform(value => `${value}`.trim())
    // tslint:disable-next-line: variable-name
    readonly relative_name: string;

    @ApiProperty({
        description: 'Số điện thoại người thân',
        required: false,
        type: String,
    })
    @Validate(CheckValidVietNamCambodia, ['relativeMobileLocaleIso'], {
        message: 'Vui lòng nhập đúng định dạng mobile phone',
    })
    @ValidateIf(o => o.relative_mobile)
    @Transform(value => `${value}`.trim())
    // tslint:disable-next-line: variable-name
    relative_mobile: string;
    relativeMobileLocaleIso?: string;

    @ApiProperty({
        description: 'Mối quan hệ',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.relative_name)
    @Transform(value => `${value}`.trim())
    // tslint:disable-next-line: variable-name
    readonly relative_type_id: string;

    @ApiProperty({
        description: 'Email người thân',
        required: false,
        type: String,
    })
    @ValidateIf(o => o.relative_email && o.relative_email !== 'null')
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @Transform(value => `${value}`.trim())
    // tslint:disable-next-line: variable-name
    readonly relative_email: string;

    @ApiProperty({
        description: 'id relation type',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    relationType?: string;


    @Expose()
    profession: string;
    nation: string;
    country: string;
    city: string;
    district: string;
    ward: string;
    // tslint:disable-next-line: variable-name
    country_id: string;
    id: string;

    // id của booking khi update hồ sow thiếu thông tin
    bookingId?: string
}
