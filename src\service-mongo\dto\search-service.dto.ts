import { IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from "class-transformer";

export class SearchServiceDto {
    @ApiProperty()
    category: string;

    @ApiProperty()
    tags?: string;

    @ApiProperty()
    search_key?: string;

    @IsOptional()
    @ApiProperty()
    city_id?: string;

    @ApiProperty()
    treeIds?: string;

    @ApiProperty()
    @Transform(value => Number(value))
    limit: number;

    @ApiProperty()
    @Transform(value => Number(value))
    offset: number;

    @ApiProperty()
    subject_ids?: string;

    @ApiProperty()
    role_ids?: string;

    @ApiProperty()
    tag_ids?: string;

    @ApiProperty()
    excludes?: string;

    @ApiProperty()
    includes?: string;

    @ApiProperty()
    gender_ids?: string;
    
    @ApiProperty()
    slug?: string;
}
