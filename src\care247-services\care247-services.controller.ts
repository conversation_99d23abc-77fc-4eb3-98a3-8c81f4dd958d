import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
// import { CskhGuard } from '../common/guards/cskh.guard';
import { CskhGuard } from 'src/common/guards/cskh.guard';
import { AppCskhActivitiesInterceptor } from 'src/middleware/app-cskh-activities.interceptor';
import { Care247Service } from './care247-services.service';
import { ICare247HospitalDto } from './dto/create-care247-partner.dto';
import { ICare247ServiceDto } from './dto/create-care247-service.dto';
import { IUpdateCare247HospitalDto } from './dto/update-care247-partner.dto';
import { IUpdateTimeBeforeBookingCare247Dto } from './dto/update-time-before-booking.dto';
import { ActionData } from 'src/cskh/decorator/cskh-action.decorator';
import { ActionEnum, CategoryEnum } from 'src/cskh/decorator/action-data.enum';
import { Care247Guard } from 'src/common/guards/care247.guard';

@Controller('care247-service')
@ApiTags('Care247 Services')
// @UseGuards(CskhGuard)
@UseInterceptors(AppCskhActivitiesInterceptor)
export class Care247SeviceController {
    constructor(private readonly service: Care247Service) {}

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.CARE247_SERVICES })
    @Get()
    async getCare247Services(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247Service(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.CREATE_CARE247_SERVICE })
    @Post()
    async createCare247Service(@Req() req: any, @Body() body: ICare247ServiceDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.createCare247Service(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.UPDATE_CARE247_SERVICE })
    @Patch('/:id')
    async updateCare247Service(@Req() req: any, @Param('id') id: string, @Body() body: ICare247ServiceDto) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateCare247Service(userMongoId, id, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.DICH_VU_DAT_THEM, action: ActionEnum.UPDATE_CARE247_SERVICE })
    @Delete('/:id')
    async deleteCare247Service(@Req() req: any, @Param('id') id: string) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.deleteCare247Service(userMongoId, id);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @Get('care247-service-changes')
    async getCare247ServiceChanges(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getCare247ServiceChanges(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.CARE247_SERVICES_PARTNER })
    @Get('/partners')
    async getListHospitalCare247(@Req() req: any) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.getListHospitalCare247(userMongoId);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.DETAIL_PARTNER_CARE247_SERVICE })
    @Get('/partners/:id')
    async getPartnerCare247ById(@Req() req: any,  @Param('id') id: string) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.getPartnerCare247ById(userMongoId,id);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.CREATE_PARTNER_CARE247_SERVICE })
    @Post('/partner')
    async createListHospitalCare247(@Req() req: any, @Body() body: ICare247HospitalDto) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.createListHospitalCare247(userMongoId, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.UPDATE_PARTNER_CARE247_SERVICE })
    @Patch('/partner/:id')
    async updateListHospitalCare247(@Req() req: any, @Param('id') id: string, @Body() body: IUpdateCare247HospitalDto) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.updateCare247HospitalById(userMongoId, id, body);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.DELETE_PARTNER_CARE247_SERVICE })
    @Delete('/partner/:id')
    async deleteCare247HospitalById(@Req() req: any, @Param('id') id: string) {
        const user = req.user;
        const { userMongoId } = user;

        return this.service.deleteCare247HospitalById(userMongoId, id);
    }

    @UseGuards(CskhGuard, Care247Guard)
    @Patch('update-time-before-booking/:id')
    @ActionData({ category: CategoryEnum.CSYT_TRIEN_KHAI_CARE247, action: ActionEnum.UPDATE_TIME_BEFORE_BOOKING })
    async updateTimeBeforeBooking(
        @Param('id') id: string,
        @Body() body: IUpdateTimeBeforeBookingCare247Dto
    ) {
        return await this.service.updateTimeBeforeBooking(id, body.timeBeforeBookingCare247);
    }
}
