import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
// import { CskhGuard } from '../common/guards/cskh.guard';
import { CskhGuard } from 'src/common/guards/cskh.guard';
import { Care247Service } from './care247-services.service';
import { ICare247ConsultationRegistrationDto } from './dto/create-care247-consultation-registration.dto';

@Controller('care247-service')
@ApiTags('Care247 Services')
export class Care247SeviceController {
    constructor(private readonly service: Care247Service) {}

    @Post('consultation-registration')
    async createConsultationRegistration(@Body() body: ICare247ConsultationRegistrationDto) {
        return this.service.createConsultationRegistration(body);
    }
}
