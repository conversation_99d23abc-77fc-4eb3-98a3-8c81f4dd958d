import { HttpModule, Module } from '@nestjs/common';
import { DanhMucBangGiaController } from './danh-muc-bang-gia.controller';
import { DanhMucBangGiaService } from './danh-muc-bang-gia.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DanhMucBangGiaSchema } from './schemas/danh-muc-bang-gia.schema';
import { DANH_MUC_BANG_GIA_COLLECTION_NAME } from './schemas/constants';

@Module({
    imports: [HttpModule, MongooseModule.forFeature([{ name: DANH_MUC_BANG_GIA_COLLECTION_NAME, schema: DanhMucBangGiaSchema }])],
    controllers: [DanhMucBangGiaController],
    providers: [DanhMucBangGiaService],
})
export class DanhMucBangGiaModule {}
