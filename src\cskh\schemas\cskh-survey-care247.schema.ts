import * as mongoose from 'mongoose';
import { CSKH_SURVEY_CARE247_COLLECTION } from './constants';
import { BOOKING_CARE_247 } from 'src/booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';

const Schema = mongoose.Schema;

export const CskhSurveyCare247Schema = new Schema(
    {
        userId: { type: String, required: true },                // ID người dùng
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        surveyDate: { type: Date, required: true },             // Ngày khảo sát
        callOutcome: { type: String, required: true },          // Kết quả gọi
        isServiceUsed: { type: Boolean },                       // Người dùng có sử dụng dịch vụ thành công
        supportDuration: { type: String },                      // Thời gian hỗ trợ bao lâu
        serviceEase: { type: String },                          // <PERSON><PERSON>h giá quy trình đặt dịch vụ
        scheduleConfirmation: { type: Boolean },                // Nhân viên có gọi xác nhận lịch trình không
        notes: { type: String },                                // Ghi chú thêm
        bookingCare247: { type: String, required: true },
        bookingCare247Info: { type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 },
        staffAttitudeRating: { type: Number, required: true },  // Mức độ hài lòng với dịch vụ
    },
    {
        collection: CSKH_SURVEY_CARE247_COLLECTION,                         // Tên collection trong MongoDB
        timestamps: true,                                      // Tự động thêm `createdAt` và `updatedAt`
    },
);