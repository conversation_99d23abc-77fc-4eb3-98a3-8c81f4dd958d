import { IsOptional, IsString, IsDateString } from 'class-validator';

export class AnKhangReportQueryDto {
    @IsOptional()
    @IsDateString()
    fromDate?: string;

    @IsOptional()
    @IsDateString()
    toDate?: string;

    @IsOptional()
    @IsString()
    referralCode?: string;

    @IsOptional()
    @IsString()
    partnerId?: string;

    @IsOptional()
    @IsString()
    searchText?: string;

    @IsOptional()
    @IsDateString()
    fromCreate?: string;

    @IsOptional()
    @IsDateString()
    toCreate?: string;
}
