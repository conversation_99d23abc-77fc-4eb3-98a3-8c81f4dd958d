import { ApiProperty } from '@nestjs/swagger';
import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsBoolean, IsNotEmpty } from 'class-validator';

export class CreateRatingHospitalDto {
    @ApiProperty({ description: 'Tên khách hàng', example: 'Nguyễn Văn A' })
    @IsString()
    @IsNotEmpty()
    customerName: string;

    @ApiProperty({ description: 'Đánh giá từ 1 đến 5', example: 5 })
    @IsNumber()
    @Min(1)
    @Max(5)
    @IsNotEmpty()
    rating: number;

    @ApiProperty({ description: 'Bình luận', example: 'Bệnh viện rất tốt!' })
    @IsString()
    @IsNotEmpty()
    comment: string;

    @ApiProperty({ description: 'Mã số vé', example: 'TICKET12345' })
    @IsString()
    @IsNotEmpty()
    bookingCode: string;

    @ApiProperty({ description: 'slug cơ sở y tế', example: 'TICKET12345' })
    @IsString()
    @IsNotEmpty()
    slug: string;

    userId?: string;
}
