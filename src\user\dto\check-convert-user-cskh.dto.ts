import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsMobilePhone, IsNotEmpty, IsOptional, ValidateIf } from 'class-validator';
import { PhoneRegisterDTO } from './phone-register.dto';
import { ApplyNewPasswordDTO } from './apply-new-password.dto';

export class ConvertUserOtpDTO {
    @ApiProperty({
        description: 'Số điện thoại',
        required: false,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    @ValidateIf(o => o.phone)
    readonly phone: string;

    @IsOptional()
    userId?: string;
}

export class CheckConvertUserCskhDTO {
    @ApiProperty({
        description: 'Số điện thoại',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON>ui lòng nhập số điện thoại',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    readonly phone: string;

    @ApiProperty({
        description: 'Device Id',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên deviceId',
    })
    readonly deviceId: string;

    @ApiProperty({
        description: 'Code từ SMS',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên code',
    })
    readonly code: string;

    userId: string;
}

export class convertUserApplyPassword extends ApplyNewPasswordDTO {
    userId: string;   
}