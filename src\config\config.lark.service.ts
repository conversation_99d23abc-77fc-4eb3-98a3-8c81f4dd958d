import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ConfigLarkService extends ConfigService {
    get larkEnv(): string {
        return this.get<string>('LARK_ENV');
    }

    get larkReceiveSystemInfoUrl(): string {
        return this.get<string>('LARK_RECEIVE_SYSTEM_INFO_URL');
    }

    get trackingPatientActionInfo(): string {
        return this.get<string>('LARK_RECEIVE_TRACKING_PATIENT_URL');
    }

    get larkReceiveContactUsUrl(): string {
        return this.get<string>('LARK_RECEIVE_CONTACT_US_URL');
    }
}
