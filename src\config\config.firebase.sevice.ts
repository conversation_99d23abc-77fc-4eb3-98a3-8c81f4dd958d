import { Injectable } from '@nestjs/common';
import { ConfigManager } from '@nestjsplus/config';
import * as Joi from 'joi';
import * as admin from 'firebase-admin';
import { FirebaseOptions } from './firebaseConnection';

@Injectable()
export class FirebaseConfigService extends ConfigManager {
    provideConfigSpec() {
        return {
            FIREBASE_PROJECT_ID: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_PRIVATE_KEY: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_CLIENT_EMAIL: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_DATABASE_URL: {
                validate: Joi.string(),
                required: true,
            },
            FIREBASE_DYNAMIC_LINK_WEB_API_KEY: {
                validate: Joi.string(),
                required: true,
            },
        };
    }

    createServiceAccount(): FirebaseOptions {
        return {
            credential: admin.credential.cert({
                projectId: this.get<string>('FIREBASE_PROJECT_ID'),
                privateKey: this.get<string>('FIREBASE_PRIVATE_KEY').replace(/\\n/g, '\n'), /* _______________ */
                clientEmail: this.get<string>('FIREBASE_CLIENT_EMAIL'),
            }),
            databaseURL: this.get<string>('FIREBASE_DATABASE_URL'),
        };
    }

    getWebApiKey() {
        return this.get<string>('FIREBASE_DYNAMIC_LINK_WEB_API_KEY');
    }
}
