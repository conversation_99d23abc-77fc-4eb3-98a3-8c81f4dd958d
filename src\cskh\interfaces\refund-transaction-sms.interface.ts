import { Document } from 'mongoose';

export interface IRefundTransactionSms extends Document {
    user: string;
    userId: string;
    medproId: string;
    fullname: string;
    transactionId: string;
    payment: string;
    booking: string;
    patient: string;
    type: number;
    bookingCode: string;
    partnerId: string;
    partner: string;
    subTotal: number;
    smsStatus: number;
    refundBankUser: any;
    smsTimeSent: Date;
    userAction: string;
    smsContent: string;
    refundStatus: boolean;
    refundDate: Date;
    note: string;
}
