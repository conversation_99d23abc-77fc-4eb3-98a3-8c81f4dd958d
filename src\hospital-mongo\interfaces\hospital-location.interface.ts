import type { Document } from 'mongoose';
import { ISpecialty } from 'src/specialty/interface/specialty.interface';

export interface IHospitalLocation extends Document {
    geofenceId: string,
    live: boolean,
    description: string,
    tag: string,
    externalId: string,
    partnerId: string,
    partner: string,
    type: string,
    geometryRadius: number,
    geometryCenter: any,
    geometry: any,
    ip: any,
    enabled: boolean,
    address: string
    city_id: string,
    city: string,
    district_id: string,
    district: string,
    isContractSigned: boolean,
    listingPackagePaid: boolean,
    isCashBack: boolean,
    circleLogo: string,
    slug: string,
}
