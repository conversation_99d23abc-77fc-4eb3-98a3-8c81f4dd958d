import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { RESET_OTP_LOGS_COLLECTION_NAME } from './constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const ResetOtpLogSchema = new Schema(
    {
        phone: String,
        userId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    },
    {
        collection: RESET_OTP_LOGS_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
