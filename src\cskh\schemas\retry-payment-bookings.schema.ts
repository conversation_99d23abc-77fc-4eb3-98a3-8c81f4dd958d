import * as mongoose from 'mongoose';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from '../../patient-mongo/schemas/constants';
import { BOOKING_CARE_247, BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME, SECTION_COLLECTION_NAME } from '../../booking-gateway/schemas/constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from '../../hospital-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from '../../service-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from '../../doctor-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from '../../room-mongo/schemas/constants';
import { SUBJECT_COLLECTION_NAME } from '../../subject-mongo/schemas/constants';
import { RETRY_PAYMENT_BOOKING } from './constants';

const Schema = mongoose.Schema;

export const RetryPaymentBookingSchema = new Schema(
    {
        bookingId: { type: String },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
        bookingCode: { type: String },
        userId: { type: String },
        user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
        patientId: { type: String },
        patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
        patientVersionId: { type: String },
        partnerId: { type: String },
        appId: { type: String },
        platform: { type: String },
        status: { type: Number },
        transactionId: { type: String },
        parentId: { type: Schema.Types.ObjectId, ref: BOOKING_CARE_247 },
        date: { type: Date },
        subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
        room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        section: { type: Schema.Types.ObjectId, ref: SECTION_COLLECTION_NAME },
        cskhUserId: { type: String },
        cskh: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        bookingsRelation: [{
            _id: { type: String },
            bookingCode: { type: String },
            bookingCodeV1: { type: String },
        }],
        userAction: {
            date: { type: Date },
            user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        },
    },
    {
        collection: RETRY_PAYMENT_BOOKING,
        timestamps: true,
    },
);
