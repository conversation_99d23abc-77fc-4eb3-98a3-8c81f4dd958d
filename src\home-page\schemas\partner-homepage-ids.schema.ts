import * as mongoose from 'mongoose';
import { PARTNER_HOME_IDS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PartnerHomeIdsSchema = new Schema(
    {
        repo: { type: String },
        id: { type: String },
        cta: { type: Object, default: {} },
        appid: {
            type: String,
            default: 'medpro',
        },
        order: {
            type: Number,
            default: 0,
        },
        fromDate: {
            type: Date,
            default: null,
        },
        toDate: {
            type: Date,
            default: null,
        },
        status: {
            type: Boolean,
            default: false,
        },
        display: {
            type: Boolean,
            default: false,
        },
    },
    {
        collection: PARTNER_HOME_IDS_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
);
