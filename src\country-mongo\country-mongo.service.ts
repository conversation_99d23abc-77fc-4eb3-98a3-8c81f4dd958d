import { Injectable, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ICountry } from './interfaces/country.interface';
import { COUNTRY_COLLECTION_NAME } from './schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { Logger } from '@nestjs/common';

@Injectable()
export class CountryMongoService {
    private readonly logger: Logger = new Logger(CountryMongoService.name);
    private readonly COUNTRY_SUPPORT_XNC: string = 'COUNTRY_SUPPORT_XNC';

    constructor(
        @InjectModel(COUNTRY_COLLECTION_NAME) private countryModel: Model<ICountry>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitaltModel: Model<IHospital>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly globalSettingService: GlobalSettingService,
    ) { }

    async find(): Promise<any> {
        const countries = await this.countryModel
            .find({ partnerId: 'medpro' }, { code: 1, name: 1 })
            .exec();
        return countries.map(item => {
            const toObject = item.toObject();
            return { name: toObject.name, id: toObject.code };
        });
    }

    async seed(partnerId: string): Promise<any> {
        const hospital = await this.hospitaltModel.findOne({ partnerId }).exec();
        const countries = await this.pkhPatientKnex('dm_country');
        for await (const country of countries) {
            const countryM = new this.countryModel({
                code: country.code,
                name: country.name,
                partnerId,
                hospitalId: hospital._id,
            });
            await countryM.save();
        }
        return true;
    }

    async filterByBookingXnc(): Promise<any> {
        try {
            // fetch countries from global setting
            const countriesSupport = await this.getCountriesSupportXnc() || [];
            if (!countriesSupport?.length) {
                return [];
            }
            return this.countryModel.find({ partnerId: 'medpro', code: { $in: countriesSupport } }, { code: true, name: true }).exec();
        } catch (error) {
            throw error;
        }
    }

    async getCountriesSupportXnc(): Promise<any> {
        const data = await this.globalSettingService.findByKeyAndRepoName(this.COUNTRY_SUPPORT_XNC);
        if (!data) {
            return [];
        }
        return data.split(',');
    }
}
