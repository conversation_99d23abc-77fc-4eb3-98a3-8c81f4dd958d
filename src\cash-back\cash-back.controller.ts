import { BadRequestException, Body, Controller, Get, Post, UploadedFiles, UseInterceptors } from '@nestjs/common';
import { CashBackServiceInject } from './persistences/cash-back.service';
import { CashBackService } from './cash-back.service';
import { StorageUserBankingInfoOnSubmitDto } from './dto/storage-user-banking-info-on-submit.dto';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';


@ApiTags('cash-back')
@Controller('cash-back')
export class CashBackController {
    constructor(
        @CashBackServiceInject()
        private readonly cashBackService: CashBackService
    ) {}

    @UseInterceptors(FilesInterceptor("files"))
    @Post("form/submit")
    @ApiOperation({ summary: 'Lưu thông tin thanh toán của người dùng khi người dùng nhập form khảo sát hoàn tiền' })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'array', 
                    items: {
                        type: 'string',
                        format: 'binary',
                    },
                },
            },
        },
    })
    async storageUserBankingInfoOnSubmit(
        @Body() storageUserBankingInfoOnSubmit: StorageUserBankingInfoOnSubmitDto,
        @UploadedFiles() files: Express.Multer.File[],
    ) {
        if(!files) {
            throw new BadRequestException('Bill thánh toán không được để trống');
        }

        if(files.length === 0) {
            throw new BadRequestException('Bill thánh toán không được để trống');
        }

        if(files.length > 10) {
            throw new BadRequestException('Số lượng file upload không được vượt quá 10 file');
        }

        files.forEach(file => {
            if(file.size > 1024 * 1024 * 10) {
                throw new BadRequestException('Kích thước file không được vượt quá 10MB');
            }

            const isImage = file.mimetype.includes('image');
            if(!isImage) {
                throw new BadRequestException('File upload phải là file ảnh');
            }
            
            const isValidExtension = ['jpg', 'jpeg', 'png'].includes(file.mimetype.split('/')[1]);
            if(!isValidExtension) {
                throw new BadRequestException(`File upload phải là file ảnh: ${['jpg', 'jpeg', 'png']}`);
            }
        });

        const fileUrls = await Promise.all(
            files.map(async file => {
                const { fileUrl } = await this.cashBackService.storageFileToS3(file);
                return fileUrl;
            })
        )

        return this.cashBackService.storageUserBankingInfoOnSubmit({
            ...storageUserBankingInfoOnSubmit,
            invoiceImages: fileUrls,
        });
    }
    
    @Get("send-cash-back-notification")
    sendCashBackNotificationOnSuccessfulExamination() {
        return this.cashBackService.sendCashBackNotificationOnSuccessfulBooking();
    }

    @Post("push-cashback")
    async pushCashBack(): Promise<any> {
        return this.cashBackService.pushCashBack();
    }
}
