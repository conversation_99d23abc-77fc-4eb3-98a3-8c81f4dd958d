
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, ValidateIf, IsEmail } from 'class-validator';

export class PaymentFeeFormDTO {

    // @ApiProperty({
    //     description: 'Id tự tăng',
    //     required: true,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // id: number;

    @ApiProperty({
        description: 'bill_id',
        required: true,
        type: Number,
        default: 200000,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    // tslint:disable-next-line: variable-name
    bill_id: number;

    @ApiProperty({
        description: 'fee_code',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    // tslint:disable-next-line: variable-name
    fee_code: string;

    @ApiProperty({
        description: 'Email',
        required: false,
        type: String,
    })
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @ValidateIf(o => o.email)
    @Transform(value => `${value}`.trim())
    readonly email: string;

    // @ApiProperty({
    //     description: 'bv_id',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // // tslint:disable-next-line: variable-name
    // bv_id: string;

    // @ApiProperty({
    //     description: 'patientEMRNo',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // patientEMRNo: string;

    // @ApiProperty({
    //     description: 'fullname',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // fullname: string;

    // @ApiProperty({
    //     description: 'sex',
    //     required: true,
    //     type: Number,
    //     default: 200000,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // sex: number;

    // @ApiProperty({
    //     description: 'birthdate',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // birthdate: string;

    // @ApiProperty({
    //     description: 'subject_id',
    //     required: true,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // // tslint:disable-next-line: variable-name
    // subject_id: number;

    // @ApiProperty({
    //     description: 'subject_name',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // // tslint:disable-next-line: variable-name
    // subject_name: string;

    // @ApiProperty({
    //     description: 'typeId',
    //     required: true,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // typeId: number;

    // @ApiProperty({
    //     description: 'mobile',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // mobile: string;

    // @ApiProperty({
    //     description: 'email',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // email: string;

    // @ApiProperty({
    //     description: 'content',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // content: string;

    // @ApiProperty({
    //     description: 'place',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // place: string;

    // @ApiProperty({
    //     description: 'number',
    //     required: true,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // number: number;

    // @ApiProperty({
    //     description: 'status',
    //     required: true,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // status: number;

    // @ApiProperty({
    //     description: 'is_sent',
    //     required: true,
    //     type: Number,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // // tslint:disable-next-line: variable-name
    // is_sent: number;

    // @ApiProperty({
    //     description: 'place',
    //     required: true,
    //     type: String,
    // })
    // @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // message: string;

    @ApiProperty({
        description: 'Platform',
        required: false,
        type: String,
        enum: [
            'ios', 'android', 'pc', 'web',
        ],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.platform)
    platform: string;

    // @ApiProperty({
    //     description: 'Số tiền dịch vụ',
    //     required: true,
    //     type: Number,
    //     default: 200000,
    // })
    // @Transform(value => Number(value))
    // @IsNotEmpty({
    //     message: 'vui lòng bổ sung thông tin',
    // })
    // amount: number;

    @ApiProperty({
        description: 'Phương thức thanh toán',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    methodId: string;

    @ApiProperty({
        description: 'Thông tin payment type detail',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    paymentTypeDetail: string;

    @ApiProperty({
        description: 'Redirect URL',
        required: true,
        type: String,
    })
    redirectUrl: string;
}
