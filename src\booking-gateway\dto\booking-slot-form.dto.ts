
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsDateString, ValidateIf, Validate, IsMobilePhone, ValidateNested, IsOptional, IsArray, ArrayMaxSize } from 'class-validator';
import * as uuid from 'uuid';
import { InsuranceChoiceValue } from './insurance-choice.dt';
import { CheckInsuranceChoice } from './check-insurance-choice';
import { PatientXcDto } from 'src/patient-mongo/dto/patient-xc.dto';

export class BookingSlotFormDTO {

    @ApiProperty({
        description: 'Platform',
        required: false,
        type: String,
        enum: [
            'ios', 'android', 'pc', 'web',
        ],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.platform)
    platform: string;

    @ApiProperty({
        description: 'Tiền gốc',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    subTotal: number;

    @ApiProperty({
        description: 'Tiền phí',
        required: true,
        type: Number,
        default: 200000,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    totalFee: number;

    @ApiProperty({
        description: 'Số tiền dịch vụ',
        required: true,
        type: Number,
        default: 200000,
    })
    @Transform(value => Number(value))
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    amount: number;

    @ApiProperty({
        description: 'Phương thức thanh toán',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    methodId: string;

    @ApiProperty({
        description: 'Thông tin payment type detail',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    paymentTypeDetail: string;

    @ApiProperty({
        description: 'Id của bệnh nhân',
        required: true,
        type: String,
        default: '5ed076b8fc0f320019712ae0',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    patientId: string;

    @ApiProperty({
        description: 'Id Dịch vụ',
        required: false,
        type: String,
        default: 'service0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.serviceId)
    serviceId: string;

    @ApiProperty({
        description: 'Id chuyên khoa',
        required: false,
        type: String,
        default: 'subject0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.subjectId)
    subjectId: string;

    @ApiProperty({
        description: 'Id phòng khám',
        required: false,
        type: String,
        default: 'room001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.roomId)
    roomId: string;

    @ApiProperty({
        description: 'Id Bác sĩ',
        required: false,
        type: String,
        default: 'doctor0001',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.doctorId)
    doctorId: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
        // default: '2020-06-03T02:12:07.381Z',
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.startTimeString)
    startTimeString?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
        // default: '2020-06-03T02:12:07.381Z',
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.startTime)
    startTime?: string;

    @ApiProperty({
        description: 'Format kiểu ISO String',
        required: false,
        type: String,
        // default: '2020-06-03T02:12:07.381Z',
    })
    @IsDateString({ strict: true }, {
        message: 'Thông tin ngày đặt khám. ISOString',
    })
    @ValidateIf(o => o.endTime)
    endTime?: string;

    @ApiProperty({
        description: 'Booking Slot Id',
        required: false,
        type: String,
        // default: uuid.v4(),
    })
    @Transform(value => `${value}`.trim())
    // @ValidateIf(o => o.bookingSlotId)
    bookingSlotId?: string;

    @ApiProperty({
        description: 'Redirect URL',
        required: true,
        type: String,
    })
    redirectUrl: string;

    @ApiProperty({
        description: 'maxSlot',
        required: true,
        type: Number,
    })
    @Transform(value => Number(value))
    maxSlot: number;

    @ApiProperty({
        description: 'hasInsuranceCode',
        required: true,
        type: Boolean,
        default: false,
    })
    @Transform(value => Boolean(value))
    hasInsuranceCode: boolean;


    @ApiProperty({
        description: 'isCashBack',
        required: false,
        type: Boolean,
        default: false,
    })
    @Transform(value => Boolean(value))
    isCashBack?: boolean;

    @ApiProperty({
        description: 'insuranceCode',
        required: true,
        type: String,
        default: '',
    })
    @Transform(value => `${value}`.trim())
    insuranceCode: string;

    @ApiProperty({
        description: 'insuranceTransferCode',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    insuranceTransferCode?: string;

    @ApiProperty({
        description: 'patientProfileId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    patientProfileId?: string;

    @ApiProperty({
        description: 'referralCode',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    referralCode?: string;

    @ApiProperty({
        description: 'insuranceChoice',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @Validate(CheckInsuranceChoice, [InsuranceChoiceValue.DUNG_TUYEN, InsuranceChoiceValue.CHUYEN_TUYEN, InsuranceChoiceValue.TAI_KHAM], {
        message: 'Giá trị không hợp lệ.',
    })
    @ValidateIf(o => o.insuranceChoice)
    insuranceChoice?: string;

    @ApiProperty({
        description: 'idReExam',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @ValidateIf(o => o.idReExam)
    idReExam?: string;

    @ApiProperty({
        description: 'cbWebView',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.cbWebView)
    cbWebView?: number;

    @ApiProperty({
        description: 'groupId',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.groupId)
    groupId?: number;

    @ApiProperty({
        description: 'treeId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    treeId?: string;

    @Expose()
    @ApiProperty({
        description: 'availableSlot',
        required: false,
        type: Number,
    })
    availableSlot: number;

    @Expose()
    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId: string;

    @ApiProperty({
        description: 'phone: dùng cho thanh toán 1900',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    @ValidateIf(o => o.phone)
    phone?: string;

    @Expose()
    patient: string;

    @ApiProperty({
        description: 'insuranceFileUrl',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    insuranceFileUrl?: string;

    @ApiProperty({
        description: 'filterCheckData',
        required: false,
    })
    filterCheckData?: string[];
 
    @ApiProperty({
        description: 'addonServices',
        required: false,
    })
    addonServices?: string[];

    @ApiProperty({ description: 'xcInfo', required: false })
    @IsNotEmpty()
    @ValidateIf((o) => o.xcInfo && `${o?.treeId}`.toUpperCase() === 'XNC')
    @ValidateNested()
    xcInfo?: PatientXcDto;

    @ApiProperty({
        description: 'optionBHYT',
        type: Number,
    })
    @Transform(value => Number(value))
    optionBHYT?: number;
    
    @ApiProperty({ description: 'medproCare', required: false })
    @IsOptional()
    @IsArray()
    @ArrayMaxSize(1)
    medproCareServiceIds?: string[];

    customerIpAddress?: string
    browserScreenHeight?: number
    browserScreenWidth?: number
}
