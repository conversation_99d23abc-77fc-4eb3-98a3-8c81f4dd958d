import { ApiProperty } from '@nestjs/swagger';

export class UpdateMultipleKey {
    static readonly VALID_KEYS = [
        'MEDPRO_CARE_PARTNER_LIST',
        'MEDPRO_CARE_PARTNER_LIST_LATER',
        'CARE247_ADDON_DISCOUNT',
        'BOOKING_CARE247_BASIC_DISCOUNT',
        'BOOKING_CARE247_ADVANCE_ENGLISH_DISCOUNT',
        'BOOKING_CARE247_ADVANCE_KHMER_DISCOUNT',
        'CARE247_PUSH_ZNS_REMINDER',
        'CARE247_PUSH_NOTIFICATION_REMINDER',
        'CONFIG_NOTIF_CARE247_24HOURS_REMINDER',
        'SMS_TEMPLATE_CARE247',
        'COMMISSION_CARE247',
    ];

    @ApiProperty({
        description: 'Danh sách CSYT hỗ trợ đặt dịch vụ Care247 (Luồng booking)',
        required: false,
        type: String,
    })
    MEDPRO_CARE_PARTNER_LIST?: string;

    @ApiProperty({
        description: 'Danh sách CSYT hỗ trợ đặt dịch vụ Care247 (Đặt sau)',
        required: false,
        type: String,
    })
    MEDPRO_CARE_PARTNER_LIST_LATER?: string;

    @ApiProperty({
        description: 'Giảm giá khi đặt thêm giờ',
        required: false,
        type: Number,
    })
    CARE247_ADDON_DISCOUNT?: number;

    @ApiProperty({
        description: 'Giảm giá khi đặt lại dịch vụ Care247 (Gói cơ bản)',
        required: false,
        type: Number,
    })
    BOOKING_CARE247_BASIC_DISCOUNT?: number;

    @ApiProperty({
        description: 'Giảm giá khi đặt lại dịch vụ Care247 (Gói tiếng Anh/Hoa)',
        required: false,
        type: Number,
    })
    BOOKING_CARE247_ADVANCE_ENGLISH_DISCOUNT?: number;

    @ApiProperty({
        description: 'Giảm giá khi đặt lại dịch vụ Care247 (Gói tiếng Khmer)',
        required: false,
        type: Number,
    })
    BOOKING_CARE247_ADVANCE_KHMER_DISCOUNT?: number;

    @ApiProperty({
        description: 'Push ZNS nhắc đặt sau dịch vụ Care247',
        required: false,
        type: String,
    })
    CARE247_PUSH_ZNS_REMINDER?: string;

    @ApiProperty({
        description: 'Push Notification (OneSignal) nhắc đặt sau dịch vụ Care247',
        required: false,
        type: String,
    })
    CARE247_PUSH_NOTIFICATION_REMINDER?: string;

    @ApiProperty({
        description: 'Template Push Notifications (OneSignal)',
        required: false,
        type: Object,
    })
    CONFIG_NOTIF_CARE247_24HOURS_REMINDER?: Record<string, any>;

    @ApiProperty({
        description: 'Template SMS Đặt thành công Care247',
        required: false,
        type: Object,
    })
    SMS_TEMPLATE_CARE247?: Record<string, any>;

    @ApiProperty({
        description: 'Hoa hồng DV Care247',
        required: false,
        type: Number,
    })
    COMMISSION_CARE247?: number;
}
