import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ICare247ConsultationRegistrationDto {
    @ApiProperty({ description: 'Số điện thoại', example: '0123456789' })
    @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
    @IsString({ message: 'Số điện thoại phải là chuỗi' })
    phone: string;

    @ApiProperty({ description: 'Họ và tên', example: 'Nguyễn Văn A' })
    @IsNotEmpty({ message: 'Họ và tên không được để trống' })
    @IsString({ message: 'Họ và tên phải là chuỗi' })
    fullname: string;

    @ApiProperty({ description: 'Email', example: '<EMAIL>' })
    @IsOptional()
    @IsEmail({}, { message: '<PERSON><PERSON> không đúng định dạng' })
    email: string;

    @ApiProperty({ description: 'Google reCAPTCHA response token', required: false })
    @IsNotEmpty()
    @IsString({ message: 'captchaResponse phải là chuỗi' })
    captchaResponse: string;
}