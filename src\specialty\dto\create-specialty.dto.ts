import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsUrl, IsNumber, ValidateNested, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

class CtaDto {
    @ApiProperty({ example: 'https://example.com', description: 'URL của CTA' })
    @IsUrl()
    url?: string;

    @ApiProperty({ example: '_blank', description: 'Mở tab mới hay không', required: false })
    @IsOptional()
    @IsString()
    target?: string;

    @ApiProperty({ example: 'click', description: 'Loại hành động của CTA', required: false })
    @IsOptional()
    @IsString()
    action?: string;

    @ApiProperty({ example: true, description: 'Mở trong trình duyệt hay không', required: false })
    @IsOptional()
    @IsBoolean()
    browser?: boolean;

    @ApiProperty({ example: 1, description: 'Chỉ mục dữ liệu', required: false })
    @IsOptional()
    @IsNumber()
    dataIndex?: number;

    @ApiProperty({ example: 'CTA Name', description: 'Tên của CTA' })
    @IsString()
    name?: string;

    @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'ID đối tác' })
    partnerId?: string;

    @ApiProperty({ example: '456e7890-e89b-12d3-a456-426614174111', description: 'ID cây dữ liệu' })
    treeId?: string;

    @ApiProperty({ example: null, description: 'ID chủ đề, có thể là null', required: false })
    @IsOptional()
    subjectId?: string;

    @ApiProperty({ example: null, description: 'ID dịch vụ, có thể là null', required: false })
    @IsOptional()
    serviceId?: string;

    @ApiProperty({ example: '789e0123-e89b-12d3-a456-426614174222', description: 'ID bác sĩ' })
    doctorId?: string;

    @ApiProperty({ example: null, description: 'ID phòng, có thể là null', required: false })
    @IsOptional()
    roomId?: string;
}


export class CreateSpecialtyDto {
    @ApiProperty({ example: 'Tim Mạch', description: 'Tên chuyên khoa' })
    @IsNotEmpty()
    name: string;

    @ApiProperty({ example: 'https://example.com', description: 'link hình ảnh', required: true })
    @IsNotEmpty()
    @IsString()
    icon?: string;

    @ApiProperty({ example: true, description: 'Trạng thái hoạt động', required: true })
    @IsNotEmpty()
    @IsBoolean()
    status?: boolean;

    @ApiProperty({ description: 'Thông tin CTA', required: false, type: () => CtaDto })
    @IsNotEmpty()
    @ValidateNested()
    @Type(() => CtaDto)
    cta?: CtaDto;
}
