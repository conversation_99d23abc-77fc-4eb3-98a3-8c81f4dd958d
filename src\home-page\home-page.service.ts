import { HttpService, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HomePageHeaderDto } from './dto/get-home-page-data.dto';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME, SEARCH_KEYWORD_COLLECTION_NAME, SERVICE_COLLECTION_NAME, SERVICE_DESCRIPTION_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { IServiceDescription } from 'src/service-mongo/interfaces/service-description.interface';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { SearchObject } from './interface/get-doctor-and-hospital.interface';
import { UrlConfigService } from 'src/config/config.url.service';
import { IHospitalDescription } from 'src/hospital-description/interface/hospital-description.inteface';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from 'src/hospital-description/schema/constants';
import { ISearchKeywords } from 'src/service-mongo/interfaces/search-keywords.interface';
import { CacheManagerService } from 'src/cache-manager/cache-manager.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { HomePageBlockConfig } from './interface/home-page-block-config.interface';
import { IDoctorDescription } from 'src/doctor-description/interface/doctor-description.inteface';
import { flattenDeep, get } from 'lodash';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IFeeRefundConfigs } from 'src/cash-back/interfaces/fee-refund-configs.interface';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import { HOME_PAGE } from './schemas/constants';

export interface HomePageService {
    getHomePageWithGlobalSettingConfig({
        headers,
        config
    }: {
        headers: HomePageHeaderDto;
        config: HomePageBlockConfig;
    }): Promise<{
        partner: object;
        hospitals: object;
        telemed_doctor_in_month: object;
        service_in_month: object;
        testimonials: object;
        subjects: object;
        banners: object;
        bannersMulti: object;
        bannerServices: object;
        ads: object
    }>
    readCacheIfExist(key: string): Promise<unknown | undefined>
    setCacheIfMiss(key: string, data: unknown, ttl: number): Promise<void>
    getHomePageGlobalSettingConfig (appid:string): Promise<HomePageBlockConfig> 
    getPartnerCashBackPopupList(): Promise<{ [x: string]: { title?: string; content?: string; html?: string; } }>
    deleteCache(): Promise<{
        message: string
    }>
    createHomePageData(body: any): Promise<any>
    getHomePageWithDataCreated(): Promise<any>
}

@Injectable()
export class HomePageServiceImpl implements HomePageService {
    private readonly logger = new Logger(HomePageServiceImpl.name);

    constructor(
        @InjectModel(HOSPITAL_COLLECTION_NAME)
        private readonly hospitalModel: Model<IHospital>,
        @InjectModel(HOSPITAL_DESCRIPTION_COLLECTION_NAME) 
        private hospitalDescriptionModel: Model<IHospitalDescription>,
        @InjectModel(SERVICE_DESCRIPTION_COLLECTION_NAME)
        private readonly serviceDescriptionModel: Model<IServiceDescription>,
        @InjectModel(SERVICE_COLLECTION_NAME)
        private readonly serviceModel: Model<IService>,
        @InjectModel(SEARCH_KEYWORD_COLLECTION_NAME)
        private readonly searchKeywordsModel: Model<ISearchKeywords>,
        @InjectModel(DOCTOR_DESCRIPTION_COLLECTION_NAME) 
        private readonly doctorDescriptionModel: Model<IDoctorDescription>,
        @InjectModel(FEE_REFUND_CONFIGS_COLLECTION_NAME) 
        private readonly feeRefundConfigs: Model<IFeeRefundConfigs>,
        @InjectModel(HOME_PAGE) private readonly homePageModel: Model<any>,
        private readonly httpService: HttpService,
        private readonly urlConfigService: UrlConfigService,
        private cacheService: CacheManagerService,
        private globalSettingService: GlobalSettingService,
        private repoConfigService: ConfigRepoService,
    ) {
        this.doctorTelemedMapper = this.doctorTelemedMapper.bind(this);
        this.mapHospitalToHospitalDescription = this.mapHospitalToHospitalDescription.bind(this);
    }

    private genConfigHomePage(appid:string) {
        const getAppId = appid === 'zalopay' || appid === 'viettelpay' ? appid + '_' : ''
        return `HOME_PAGE_BLOCK_CONFIG_${getAppId}api-v2`
    }

    public async deleteCache () {
        try {
            await this.cacheService.delByPattern(`get-hospital-info-map_web_app`);
            await this.cacheService.delByPattern(`get-cash-back-popup_web`);
            
            return {
                message: 'Xóa cache thành công'
            };
        } catch (error) {
            throw new InternalServerErrorException(`Xóa cache thất bại`);
        }
    }

    public async getHomePageGlobalSettingConfig (appid:string): Promise<HomePageBlockConfig> {
        try {
            const config = await this.globalSettingService.findByKeyAndRepoName(this.genConfigHomePage(appid))
            try {
                const parsedValue = JSON.parse(config);
                return parsedValue
              } catch (error) {
                console.error("JSON parse error: ",`${this.genConfigHomePage(appid)}: ${error}`);
                return
              }
        } catch (error) {
            this.logger.error(`Error when getting global setting config: ${error.message}`);
            throw new InternalServerErrorException('Error when getting global setting config');
        }
    }

    // ? ==================== DATA MAPPER ====================
    private doctorTelemedSubjectMapper(subjects?: SearchObject['subjects']) {
        if(!subjects) {
            return '';
        }
        
        return subjects.map(subject => subject.name).join(' - ');
    }

    private doctorTelemedHospitalMapper(hospital?: SearchObject['hospitals'][0]) {
        return {
            id: hospital.id || null,
            type: hospital.type || null,
            name: hospital.name || null,
            address: hospital.address || null,
            image: hospital.image || null,
            circleLogo: hospital.circleLogo || null,
            ctas: hospital.ctas || null
        };
    }

    // ? ==================== PARTNER ====================
    private async getMedproPartner({ ids }: { ids: string[] }) {
        const listHospital = await this.hospitalModel.find({ _id: { $in: ids } }, { _id: true, circleLogo: true, name: true, image: true, partnerId: true }).exec();
        const sortedData = this.sortByIds(ids, listHospital, '_id')
        return sortedData
    }

    // ? ==================== SERVICE ====================
    private async mapServiceDescriptionToService(mapServiceToServiceDesc: IServiceDescription[]) {
        const serviceMapped = mapServiceToServiceDesc.map(async serviceDesc => {
            const service = await this.serviceModel.findOne({ id: serviceDesc.serviceId }).exec();
            const hospital = await this.hospitalModel.findOne(
                { partnerId: service?.partnerId }, 
                {
                    name: true,
                    partnerId: true,
                    image: true,
                    circleLogo: true,
                    address: true,
                    city_id: true,
                    deliveryStatus: true,
                    message: true,
                    sponsored: true,
                    listingPackagePaid:true,
                    newHospitalTypes: true,
                    slug: true,
                    rating: true
                }).exec();

            const hospitalObject = hospital?.toObject();

            return {
                _id: service?._id || null,
                serviceName: service?.name || null,
                hospital: hospitalObject || null,
                description: serviceDesc.description || null,
                shortDescription: serviceDesc.shortDescription || null,
                price: service?.price || null,
                originalPrice: service?.originalPrice || null,
            };
        });
    
        return Promise.all(serviceMapped);
    }

    private async getHospitalInfoMap(partnerIds?: string[]): Promise<any> {

        const projection = {
            partnerId: 1,
            city_id: 1,
            name: 1,
            address: 1,
            packageImgDefault: 1,
            circleLogo: 1,
            slug: 1,
            newHospitalTypes: 1,
            isCashBack: 1
        };

        const cacheKey = `get-hospital-info-map_web_app`;
        const cachedData = await this.cacheService.get(cacheKey);

        if (cachedData) {
            return cachedData;
        }

        let allPartners = await this.hospitalModel.find({}, projection).exec();
        if (allPartners.length) {
            allPartners = allPartners.map(item => item.toObject())
        }

        const feesWithPartner = await this.getPartnerCashBackPopupList();
        if (partnerIds?.length) {
            allPartners = allPartners.filter(it => partnerIds.includes(it.partnerId));
        }

        try {
            const result = allPartners.reduce((res, item) => {
                res[`${item.partnerId}`] = {
                    ...item,
                    ...(feesWithPartner.hasOwnProperty(item.partnerId) ? { popup: feesWithPartner[item.partnerId] } : {})
                };
                return res;
            }, {});

            this.cacheService.set(cacheKey, result, { ttl: 60 * 60 });

            return result;
        } catch (error) {
            console.log(error)
            return {}
        }

        
    }

    private async getServiceInMonth({ ids, headers }: { ids: HomePageBlockConfig["service_in_month"]["ids"], headers: HomePageHeaderDto }) {
        const allServiceDescription = await this.getServiceDescriptionMapByServiceId();
        const allPartner = await this.getHospitalInfoMap();
        const [
            healthServiceDescription,
            testServiceDescription,
            vaccineServiceDescription,
            
        ] = await Promise.all([
            this.getServiceFromSearchApi({ ids: ids['suc-khoe'], headers }),
            this.getServiceFromSearchApi({ ids: ids['xet-nghiem'], headers }),
            this.getServiceFromSearchApi({ ids: ids['tiem-chung'], headers }),
        ])

        return [
            {
                key: 'suc-khoe',
                type: 'Sức khỏe',
                // services: healthService,
                services: healthServiceDescription.map(service => ({
                    ...service,
                    partner: allPartner[service?.partnerId],
                    description: {
                        ...service?.description,
                        ...allServiceDescription[service?.cta?.serviceId]    
                    }
                })),
            },
            {
                key: 'xet-nghiem',
                type: 'Xét nghiệm',
                // services: testService,
                services: testServiceDescription.map(service => ({
                    ...service,
                    partner: allPartner[service?.partnerId],
                    description: {
                        ...service?.description,
                        ...allServiceDescription[service?.cta?.serviceId]
                    }
                })),
            },
            {
                key: 'tiem-chung',
                type: 'Tiêm chủng',
                // services: vaccineService,
                services: vaccineServiceDescription.map(service => ({
                    ...service,
                    partner: allPartner[service?.partnerId],
                    description: {
                        ...service?.description,
                        ...allServiceDescription[service?.cta?.serviceId]
                    }
                })),
            },
        ];
    }
    
    // ? ==================== DOCTOR TELEMED ====================
    private async doctorTelemedMapper(doctorTelemed?: SearchObject['results'][0]) {
        const doctorTelemedDescription = await this.doctorDescriptionModel.findOne(
            { doctorId: doctorTelemed?.id },
            {
                isDetailVisible: true,
                disabled: true,
                massage: true,
                rating: true,
                _id: true,
                bookingTotal: true,
                refHospital: true,
                slug: true,
            }
            ).exec();
        
        // return {
        //     id: doctorTelemed.id || null,
        //     partnerId: doctorTelemed?.partner?.partnerId || null,
        //     title: doctorTelemed.title || '',
        //     role: doctorTelemed.role || '',
        //     gender: doctorTelemed.gender || null,
        //     price: doctorTelemed.price || null,
        //     imageUrl: doctorTelemed.imageUrl || null,
        //     days: doctorTelemed.days || null,
        //     hospitalAddress: doctorTelemed.hospitalAddress || null,
        //     hospitals: doctorTelemed.hospitals.map(this.doctorTelemedHospitalMapper),
        //     ctas: doctorTelemed.hospitals.map((h) => h.ctas),
        //     rating: doctorTelemedDescription?.rating || null,
        //     bookingTotal: doctorTelemedDescription?.bookingTotal || null,
        //     slug: doctorTelemedDescription?.slug || '',
        //     subjects: this.doctorTelemedSubjectMapper(doctorTelemed?.subjects),
        // };

        return {
            ...doctorTelemed,
            description: {
                ...doctorTelemed?.description,
                ...doctorTelemedDescription?.toObject(),
                rating: {
                    ...doctorTelemedDescription?.toObject()?.rating,
                    count: doctorTelemedDescription?.toObject()?.bookingTotal || 100
                }
            }
        }
    }

    private async callApiGetDoctorTelemedFromSearch({ ids }: { ids: string[] }) {
        let response: AxiosResponse<SearchObject[]>;
        try {
            let config: AxiosRequestConfig = {
                method: 'get',
                url: `${this.urlConfigService.getUrlApiSearchService}/services/services?category=doctor&offset=0&limit=${ids.length}&includes=results&treeId=TELEMEDNOW&doctor_ids=${ids.join(",")}`,
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            response = await this.httpService.request<SearchObject[]>(config).toPromise();
            const doctorByIds = response.data[0].results;
            const sortedData = this.sortByIds(ids, doctorByIds, 'id')
            return Promise.all(sortedData.map(this.doctorTelemedMapper));
        } catch (error) {
            this.logger.error(`Error when calling API: ${error.message}`);
            return {
                doctorTelemed: []
            };
        }
    }

    // ? ==================== HOSPITALS ====================
    private async mapHospitalDescriptionToHospital({ mapHospitalDescToHospital, ctas }: { mapHospitalDescToHospital: IHospitalDescription[],  ctas: HomePageBlockConfig["hospitals"]["ctas"] }) {
        const hospitalDescriptionMapToHospital = mapHospitalDescToHospital.map(async hospitalDesc => {
            const hospital = await this.hospitalModel.findOne(
                { slug: hospitalDesc.hospitalId },
                {
                    name: true,
                    partnerId: true,
                    image: true,
                    circleLogo: true,
                    address: true,
                    city_id: true,
                    deliveryStatus: true,
                    message: true,
                    sponsored: true,
                    listingPackagePaid:true,
                    newHospitalTypes: true,
                    slug: true,
                    googleMap: true,
                    packageImgDefault: true,
                }).populate({
                    path: "city",
                    select : {
                        name: 1,
                        shortName: 1
                    }
                }).populate({
                    path: "district",
                    select: {
                        name: 1
                    }
                })
                .exec();
                
            return {
                ...hospital?.toObject(),
                ...( ctas?.hasOwnProperty(hospital.partnerId) ? { cta: ctas[hospital.partnerId] } : {} ),
                description: hospitalDesc?.toObject() ?? {},
            }
        })

        return Promise.all(hospitalDescriptionMapToHospital);
    }

    private async getHospitalsWithDescription ({ ids, ctas } : { ids: string[] , ctas: HomePageBlockConfig["hospitals"]["ctas"] }) {
        const hospitalDescription = await this.hospitalDescriptionModel.find(
            { _id: { $in: ids } }, 
        ).select({
            hospitalId: true,
            description: true,
            shortDescription: true,
            rating: true,
            bookingTotal: true,
            slug: true,
            googleMap: true,
            packageImgDefault: true
        }).exec();
        return this.mapHospitalDescriptionToHospital({ mapHospitalDescToHospital: hospitalDescription, ctas: ctas });
    }

    private async mapHospitalToHospitalDescription(hospital: IHospital) {
        const hospitalDescription = await this.hospitalDescriptionModel.findOne(
            { hospitalId: hospital?.slug },
            {
                hospitalId: true,
                description: true,
                shortDescription: true,
                rating: true,
                bookingTotal: true,
                slug: true,
                googleMap: true,
                packageImgDefault: true
            }
            ).exec();
        return {
            ...hospital,
            description: hospitalDescription?.toObject() ?? {},
        }
    }

    private async getCashBackContentPopup (): Promise<{ content: string[] }> {
        try {
            const cashBackPopupContent = await this.globalSettingService.findByKeyAndRepoName(`HOME_PAGE_CASH_BACK_POPUP_${this.repoConfigService.getRepoName()}`);
            return JSON.parse(cashBackPopupContent);
        } catch (error) {
            this.logger.error(`Error when getting cash back popup content: ${error.message}`);
            return {
                content: []
            };
        }
    } 

    public async getPartnerCashBackPopupList(): Promise<{
        [x: string]: {
            title?: string;
            content?: string;
            html?: string;
        }
    }> {
        const cacheKey = `get-cash-back-popup_web`;
        const value = await this.cacheService.get(cacheKey);

        if(value) {
            return value;
        }

        const fees = await this.feeRefundConfigs.find().exec();
        let { content } = await this.getCashBackContentPopup();

        if(fees.length === 0 || content.length === 0) {
            return {};
        }

        const feesMapped = fees.map(fee => ({
            [fee.feeRefundPartnerId]: {
                title: content[0],
                content: content[1].replace('[x]', fee.feeRate?.toString()).replace('[CSYT]', fee.hospitalName?.toString()).replace('[TẠI ĐÂY]', "https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien"),
                html: content[2].replace('[x]', fee.feeRate?.toString()).replace('[CSYT]', fee.hospitalName?.toString()).replace('[TẠI ĐÂY]', "https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien")
            }
        }));

        const response = feesMapped.reduce((acc, item) => ({ ...acc, ...item }), {});               
    
        await this.cacheService.set(cacheKey, response, { ttl: 60 * 60 });
        
        return response;
    }

    private async getHospitalByIds({ partnerIds , ctas }:{ partnerIds: string[]; ctas: HomePageBlockConfig["hospitals"]["ctas"] }) {
        const hospitals = await this.hospitalModel.find(
            { partnerId : { $in: partnerIds } },
            {
                name: true,
                partnerId: true,
                image: true,
                circleLogo: true,
                address: true,
                city_id: true,
                deliveryStatus: true,
                message: true,
                sponsored: true,
                listingPackagePaid:true,
                newHospitalTypes: true,
                slug: true,
                googleMap: true,
                packageImgDefault: true,
                isCashBack: true
            }
        ).populate({
            path: "city",
            select : {
                name: 1,
                shortName: 1
            }
        }).populate({
            path: "district",
            select: {
                name: 1
            }
        })
        .exec();

        const fees = await this.getPartnerCashBackPopupList()
        
        const hospitalsObjectWithCta = hospitals.map((hospital) => ({
            ...hospital.toObject(),
            ...( ctas?.hasOwnProperty(hospital.partnerId) ? { cta: ctas[hospital.partnerId] } : {} ),
            ...( fees?.hasOwnProperty(hospital.partnerId) ? { popup: fees[hospital.partnerId] } : {} )
        }));

        const sortedData = this.sortByIds(partnerIds, hospitalsObjectWithCta, 'partnerId')
        return Promise.all(sortedData.map(this.mapHospitalToHospitalDescription));
    }


    private async getSubjects() {
        const keywords = await this.searchKeywordsModel
        .find({ enable: true, type: "departments" })
        .exec();

        // return keywords.map(keyword => keyword.value).map(value => JSON.parse(value)).flat()
        const subjects = keywords.map(keyword => keyword.value).map(value => JSON.parse(value));
        return flattenDeep(subjects);
    }

    public async readCacheIfExist(key: string): Promise<unknown | undefined> {
        return this.cacheService.get(key);
    }

    public async setCacheIfMiss(key: string, data: unknown, ttl = 3600) {
        return this.cacheService.set(key, data, { ttl });
    }

    public async getHomePageWithGlobalSettingConfig ({ headers, config } : { config: HomePageBlockConfig, headers: HomePageHeaderDto }) {
        const { partner, hospitals, telemed_doctor_in_month, service_in_month, testimonials, subjects, ads } = config
        const data = await this.getHomePage({ config, headers });

        return {
            ads: ads.isAds ? ads : null,
            partner: partner.hide ? null : data.partner,
            hospitals: hospitals.hide ? null : data.hospitals,  
            telemed_doctor_in_month: telemed_doctor_in_month.hide ? null : data.telemed_doctor_in_month,
            service_in_month: service_in_month.hide ? null : data.service_in_month,
            testimonials: testimonials.hide ? null : data.testimonials,
            subjects: subjects.hide ? null : data.subjects,
            banners: partner.hide ? null : data.banners,
            bannersMulti: partner.hide ? null : data.bannersMulti,
            bannerServices: partner.hide ? null : data.bannerServices
        }
    }

    async getServiceDescriptionMapByServiceId(): Promise<any> {
        const cacheKey = `get-service-description_web_app`;
        const cachedData = await this.cacheService.get(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        let allServiceDescriptions: any = await this.serviceDescriptionModel.find({}).select('-description -shortDescription').exec();

        if (allServiceDescriptions.length) {
            allServiceDescriptions = allServiceDescriptions.map(item => item.toObject());
        }

        const result = allServiceDescriptions.reduce((res, item) => {
            res[`${item.serviceId}`] = item;
            return res;
        }, {});

        this.cacheService.set(cacheKey, result, { ttl: 60 * 60 });

        return result;
    }

    private sortByIds(ids, serviceData, id) {
        const dataMap = serviceData.reduce((acc, item) => {
            acc[item[id]] = item;
            return acc;
        }, {});

        return ids.map(id => dataMap[id]);
    }
      
    private async getServiceFromSearchApi ({ ids, headers }: { ids: string[], headers: HomePageHeaderDto }) {
        try {
            let data = JSON.stringify({
                "category": "package",
                "includes": "results",
                "service_ids": ids.join(','),
                "limit": ids?.length,
                "offset": 0,
            });
        
            let config: AxiosRequestConfig = {
              method: 'post',
              url: `${this.urlConfigService.getAppMedproApi()}/mongo/service/home-page/package/list`,
              headers: { 
                'Content-Type': 'application/json',
                appid: 'medpro',
                locale: 'vi'
              },
              data : data
            };
            
            const response = await this.httpService.request(config).toPromise();
            const serviceByIds = response?.data?.results ?? []
            const sortedData = this.sortByIds(ids, serviceByIds, 'id')
            return sortedData;
        } catch (error) {
            this.logger.error(`Error when calling API: ${error.message}`);
            return [];
        }
    }

    private async getHomePage({ headers , config } : { config: HomePageBlockConfig, headers: HomePageHeaderDto }): Promise<{
        partner: object;
        hospitals: object;
        telemed_doctor_in_month: object;
        service_in_month: object;
        testimonials: object;
        subjects: object;
        banners: object;
        bannersMulti: object;
        bannerServices: object;
    }>  {
        const [
            doctorTelemed, 
            partner, 
            service_in_month, 
            hospitals,
            subject
        ] = await Promise.all([
            this.callApiGetDoctorTelemedFromSearch({ ids: config.telemed_doctor_in_month.ids }),
            this.getMedproPartner({ ids: config.partner.ids }),
            this.getServiceInMonth({ ids: config.service_in_month.ids, headers }),
            this.getHospitalByIds({ partnerIds: config.hospitals.ids, ctas: config.hospitals.ctas }),
            this.getSubjects()
        ]);

        return {
            partner: {
                data: partner,
            },
            hospitals: {
                data: hospitals,
            },
            telemed_doctor_in_month: {
                data: doctorTelemed,
            },
            service_in_month: {
                data: service_in_month,
            },
            testimonials: {
                data: config.testimonials.data,
            },
            subjects: {
                data: subject,
            },
            banners: {
                data: config.banners.data
            },
            bannersMulti: {
                data: config?.bannersMulti?.data
            },
            bannerServices: {
                data: config?.bannerServices?.data
            },
        };
    }

    async createHomePageData(body: any) {
        try {
            const { headers, config } = body;
            const data = await this.getHomePageWithGlobalSettingConfig({ config , headers });
            return await this.homePageModel.create({ ...data });
        } catch (error) {
            console.log('error', error);
        }
    }

    public async getHomePageWithDataCreated() {
        try {
            const data = await this.homePageModel.find().sort({ updatedAt: -1 }).exec();
            if (data.length > 0) {
                return data[0]
            }
        } catch (error) {
            console.log('error', error);
        }
    }
}
