import { HttpService, Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HomePageHeaderDto } from './dto/get-home-page-data.dto';
import {
    DOCTOR_DESCRIPTION_COLLECTION_NAME,
    SEARCH_KEYWORD_COLLECTION_NAME,
    SERVICE_COLLECTION_NAME,
    SERVICE_DESCRIPTION_COLLECTION_NAME,
} from 'src/service-mongo/schemas/constants';
import { IServiceDescription } from 'src/service-mongo/interfaces/service-description.interface';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { SearchObject } from './interface/get-doctor-and-hospital.interface';
import { UrlConfigService } from 'src/config/config.url.service';
import { IHospitalDescription } from 'src/hospital-description/interface/hospital-description.inteface';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from 'src/hospital-description/schema/constants';
import { ISearchKeywords } from 'src/service-mongo/interfaces/search-keywords.interface';
import { CacheManagerService } from 'src/cache-manager/cache-manager.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { HomePageBlockConfig } from './interface/home-page-block-config.interface';
import { IDoctorDescription } from 'src/doctor-description/interface/doctor-description.inteface';
import { flattenDeep } from 'lodash';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IFeeRefundConfigs } from 'src/cash-back/interfaces/fee-refund-configs.interface';
import { FEE_REFUND_CONFIGS_COLLECTION_NAME } from 'src/cash-back/constants';
import {
    ADS_COLLECTION_NAME,
    BANNER_SERVICES_COLLECTION_NAME,
    BANNERS_HOME_COLLECTION_NAME,
    BANNERS_MULTI_COLLECTION_NAME,
    CATEGORY_SERVICE_IN_MONTH_COLLECTION_NAME,
    HOSPITALS_HOME_IDS_COLLECTION_NAME,
    PARTNER_HOME_IDS_COLLECTION_NAME,
    SUBJECT_HOME_PAGE_IDS_COLLECTION_NAME,
    TELEMED_DOCTOR_IN_MONTH_IDS_COLLECTION_NAME,
    TESTIMONIALS_COLLECTION_NAME,
} from './schemas/constants';
import { REPO_NAME_BETA } from 'src/common/constants';
import { IAds } from './interface/ads.interface';
import { IBannerServices } from './interface/banner-services.interface';
import { IBannersMulti } from './interface/banners-multi.interface';
import { BannersHomeDto } from './interface/home-banners.interface';
import { IHospitalsHomeIdDto } from './interface/hospitals-home-page-ids.interface';
import { IPartnerHomeIdDto } from './interface/partners-home-page-ids.interface';
import { ICategoryServiceInMonth } from './interface/service-in-month-category.interface';
import { ISubjectsHomePageIds } from './interface/subject-homepage-ids.interface';
import { ITelemedDoctorInMonthIds } from './interface/telemed-doctor-in-month-ids.interface';
import { ITestimonials } from './interface/testimonials.interface';
import moment = require('moment');

export interface HomePageService {
    getHomePageWithGlobalSettingConfig({
        headers,
        config,
    }: {
        headers: HomePageHeaderDto;
        config: HomePageBlockConfig;
    }): Promise<{
        partner: object;
        hospitals: object;
        telemed_doctor_in_month: object;
        service_in_month: object;
        testimonials: object;
        subjects: object;
        banners: object;
        bannersMulti: object;
        bannerServices: object;
        ads: object
    }>
    readCacheIfExist(key: string): Promise<unknown | undefined>
    setCacheIfMiss(key: string, data: unknown, ttl: number): Promise<void>
    getHomePageGlobalSettingConfig (appid?:string): Promise<HomePageBlockConfig> 
    getPartnerCashBackPopupList(): Promise<{ [x: string]: { title?: string; content?: string; html?: string; } }>
    deleteCache(): Promise<{
        message: string
    }>
    getSubjects(): Promise<any>,
    getHomePageDataV2(headers: HomePageHeaderDto): Promise<any>
    cloneBetaToLive(): Promise<any>;
    cloneMedproToMomo(): Promise<any>;
}

@Injectable()
export class HomePageServiceImpl implements HomePageService {
    private readonly repoName: string;
    private readonly logger = new Logger(HomePageServiceImpl.name);
    private readonly globalSettingConfigKey: string

    constructor(
        @InjectModel(HOSPITAL_COLLECTION_NAME)
        private readonly hospitalModel: Model<IHospital>,
        @InjectModel(HOSPITAL_DESCRIPTION_COLLECTION_NAME)
        private hospitalDescriptionModel: Model<IHospitalDescription>,
        @InjectModel(SERVICE_DESCRIPTION_COLLECTION_NAME)
        private readonly serviceDescriptionModel: Model<IServiceDescription>,
        @InjectModel(SERVICE_COLLECTION_NAME)
        private readonly serviceModel: Model<IService>,
        @InjectModel(SEARCH_KEYWORD_COLLECTION_NAME)
        private readonly searchKeywordsModel: Model<ISearchKeywords>,
        @InjectModel(DOCTOR_DESCRIPTION_COLLECTION_NAME)
        private readonly doctorDescriptionModel: Model<IDoctorDescription>,
        @InjectModel(FEE_REFUND_CONFIGS_COLLECTION_NAME)
        private readonly feeRefundConfigs: Model<IFeeRefundConfigs>,
        @InjectModel(ADS_COLLECTION_NAME) private adsModel: Model<IAds>,
        @InjectModel(BANNER_SERVICES_COLLECTION_NAME) private bannerServicesModel: Model<IBannerServices>,
        @InjectModel(BANNERS_MULTI_COLLECTION_NAME) private bannersMultiModel: Model<IBannersMulti>,
        @InjectModel(BANNERS_HOME_COLLECTION_NAME) private bannersHomeModel: Model<BannersHomeDto>,
        @InjectModel(TESTIMONIALS_COLLECTION_NAME) private testimonialsModel: Model<ITestimonials>,
        @InjectModel(CATEGORY_SERVICE_IN_MONTH_COLLECTION_NAME) private categoryServiceInMonthModel: Model<ICategoryServiceInMonth>,
        @InjectModel(TELEMED_DOCTOR_IN_MONTH_IDS_COLLECTION_NAME) private telemedDoctorInMonthIdModel: Model<ITelemedDoctorInMonthIds>,
        @InjectModel(SUBJECT_HOME_PAGE_IDS_COLLECTION_NAME) private subjectHomePageIdModel: Model<ISubjectsHomePageIds>,
        @InjectModel(HOSPITALS_HOME_IDS_COLLECTION_NAME) private hospitalsHomeIdsModel: Model<IHospitalsHomeIdDto>,
        @InjectModel(PARTNER_HOME_IDS_COLLECTION_NAME) private partnerHomeIdsModel: Model<IPartnerHomeIdDto>,
        private readonly httpService: HttpService,
        private readonly urlConfigService: UrlConfigService,
        private cacheService: CacheManagerService,
        private globalSettingService: GlobalSettingService,
        private repoConfigService: ConfigRepoService,
    ) {
        this.doctorTelemedMapper = this.doctorTelemedMapper.bind(this);
        this.mapHospitalToHospitalDescription = this.mapHospitalToHospitalDescription.bind(this);
        this.repoName = this.repoConfigService.getRepoName();
    }

    private genConfigHomePage(appid: string) {
        if (REPO_NAME_BETA.includes(this.repoName)) {
            const getAppId = appid === 'zalopay' || appid === 'viettelpay' ? appid + '_' : '';
            return `HOME_PAGE_BLOCK_CONFIG_${getAppId}api-v2-beta`;
        }
        const getAppId = appid === 'zalopay' || appid === 'viettelpay' ? appid + '_' : '';
        return `HOME_PAGE_BLOCK_CONFIG_${getAppId}api-v2`;
    }

    public async deleteCache () {
        try {
            await this.cacheService.delByPattern(`get-hospital-info-map_web_app`);
            await this.cacheService.delByPattern(`get-cash-back-popup_web`);
            
            return {
                message: 'Xóa cache thành công'
            };
        } catch (error) {
            throw new InternalServerErrorException(`Xóa cache thất bại`);
        }
    }

    public async getHomePageGlobalSettingConfig (appid?:string): Promise<HomePageBlockConfig> {
        try {
            const config = await this.globalSettingService.findByKeyAndRepoName(this.genConfigHomePage(appid))
            return JSON.parse(config)
        } catch (error) {
            this.logger.error(`Error when getting global setting config: ${error.message}`);
            throw new InternalServerErrorException('Error when getting global setting config');
        }
    }

    // ? ==================== DATA MAPPER ====================
    private doctorTelemedSubjectMapper(subjects?: SearchObject['subjects']) {
        if(!subjects) {
            return '';
        }
        
        return subjects.map(subject => subject.name).join(' - ');
    }

    private doctorTelemedHospitalMapper(hospital?: SearchObject['hospitals'][0]) {
        return {
            id: hospital.id || null,
            type: hospital.type || null,
            name: hospital.name || null,
            address: hospital.address || null,
            image: hospital.image || null,
            circleLogo: hospital.circleLogo || null,
            ctas: hospital.ctas || null
        };
    }

    // ? ==================== PARTNER ====================
    private async getMedproPartner({ ids, ctas }: { ids: string[],  ctas: HomePageBlockConfig["partner"]["ctas"] }) {
        const listHospital = await this.hospitalModel.find({ partnerId: { $in: ids } }, { _id: true, circleLogo: true, name: true, image: true, partnerId: true, listingPackagePaid: true , slug:true}).exec();
        const hospitalsObjectWithCta = listHospital.map((hospital) => ({
            ...hospital.toObject(),
            ...( ctas?.hasOwnProperty(hospital.partnerId) ? { cta: ctas[hospital.partnerId] } : {} )
        }));
        const sortedData = this.sortByIds(ids, hospitalsObjectWithCta, 'partnerId')
        let mapData = await Promise.all(sortedData.map(this.mapHospitalToHospitalDescription));
        mapData = mapData.map(h => {
            return { ...h, cta: { ...h.cta, layout: h.layout }}
        })
        return mapData
    }

    // ? ==================== SERVICE ====================
    private async mapServiceDescriptionToService(mapServiceToServiceDesc: IServiceDescription[]) {
        const serviceMapped = mapServiceToServiceDesc.map(async serviceDesc => {
            const service = await this.serviceModel.findOne({ id: serviceDesc.serviceId }).exec();
            const hospital = await this.hospitalModel.findOne(
                { partnerId: service?.partnerId }, 
                {
                    name: true,
                    partnerId: true,
                    image: true,
                    circleLogo: true,
                    address: true,
                    city_id: true,
                    deliveryStatus: true,
                    message: true,
                    sponsored: true,
                    listingPackagePaid:true,
                    newHospitalTypes: true,
                    slug: true,
                    rating: true
                }).exec();

            const hospitalObject = hospital?.toObject();

            return {
                _id: service?._id || null,
                serviceName: service?.name || null,
                hospital: hospitalObject || null,
                description: serviceDesc.description || null,
                shortDescription: serviceDesc.shortDescription || null,
                price: service?.price || null,
                originalPrice: service?.originalPrice || null,
            };
        });
    
        return Promise.all(serviceMapped);
    }

    private async getHospitalInfoMap(partnerIds?: string[]): Promise<any> {

        const projection = {
            partnerId: 1,
            city_id: 1,
            name: 1,
            address: 1,
            packageImgDefault: 1,
            circleLogo: 1,
            slug: 1,
            newHospitalTypes: 1,
            isCashBack: 1,
            listingPackagePaid: 1
        };

        const cacheKey = `get-hospital-info-map_web_app`;
        const cachedData = await this.cacheService.get(cacheKey);

        if (cachedData) {
            return cachedData;
        }

        let allPartners = await this.hospitalModel.find({}, projection).exec();
        if (allPartners.length) {
            allPartners = allPartners.map(item => item.toObject())
        }

        const feesWithPartner = await this.getPartnerCashBackPopupList();
        if (partnerIds?.length) {
            allPartners = allPartners.filter(it => partnerIds.includes(it.partnerId));
        }

        try {
            const result = allPartners.reduce((res, item) => {
                res[`${item.partnerId}`] = {
                    ...item,
                    ...(feesWithPartner.hasOwnProperty(item.partnerId) ? { popup: feesWithPartner[item.partnerId] } : {})
                };
                return res;
            }, {});

            this.cacheService.set(cacheKey, result, { ttl: 60 * 60 });

            return result;
        } catch (error) {
            console.log(error)
            return {}
        }

        
    }

    private async getServiceInMonth({ ids, headers }: { ids: HomePageBlockConfig["service_in_month"]["ids"], headers: HomePageHeaderDto }) {
        const allServiceDescription = await this.getServiceDescriptionMapByServiceId();
        const allPartner = await this.getHospitalInfoMap();
        const [
            healthServiceDescription,
            testServiceDescription,
            vaccineServiceDescription,
            
        ] = await Promise.all([
            this.getServiceFromSearchApi({ ids: ids['suc-khoe'], headers }),
            this.getServiceFromSearchApi({ ids: ids['xet-nghiem'], headers }),
            this.getServiceFromSearchApi({ ids: ids['tiem-chung'], headers }),
        ])
        return [
            {
                key: 'suc-khoe',
                type: 'Sức khỏe',
                services: healthServiceDescription?.length > 0 
                    ? healthServiceDescription.map(service => ({
                        ...service,
                        partner: allPartner[service?.partnerId],
                        description: {
                            ...service?.description,
                            ...allServiceDescription[service?.cta?.serviceId]
                        }
                    })) 
                    : []
            },
            {
                key: 'xet-nghiem',
                type: 'Xét nghiệm',
                services: testServiceDescription?.length > 0 
                    ? testServiceDescription.map(service => ({
                        ...service,
                        partner: allPartner[service?.partnerId],
                        description: {
                            ...service?.description,
                            ...allServiceDescription[service?.cta?.serviceId]
                        }
                    })) 
                    : []
            },
            {
                key: 'tiem-chung',
                type: 'Tiêm chủng',
                services: vaccineServiceDescription?.length > 0 
                    ? vaccineServiceDescription.map(service => ({
                        ...service,
                        partner: allPartner[service?.partnerId],
                        description: {
                            ...service?.description,
                            ...allServiceDescription[service?.cta?.serviceId]
                        }
                    })) 
                    : []
            },
        ];
    }
    
    // ? ==================== DOCTOR TELEMED ====================
    private async doctorTelemedMapper(doctorTelemed?: SearchObject['results'][0]) {
        const doctorTelemedDescription = await this.doctorDescriptionModel.findOne(
            { doctorId: doctorTelemed?.id },
            {
                isDetailVisible: true,
                disabled: true,
                massage: true,
                rating: true,
                _id: true,
                bookingTotal: true,
                refHospital: true,
                slug: true,
            }
            ).exec();
        
        // return {
        //     id: doctorTelemed.id || null,
        //     partnerId: doctorTelemed?.partner?.partnerId || null,
        //     title: doctorTelemed.title || '',
        //     role: doctorTelemed.role || '',
        //     gender: doctorTelemed.gender || null,
        //     price: doctorTelemed.price || null,
        //     imageUrl: doctorTelemed.imageUrl || null,
        //     days: doctorTelemed.days || null,
        //     hospitalAddress: doctorTelemed.hospitalAddress || null,
        //     hospitals: doctorTelemed.hospitals.map(this.doctorTelemedHospitalMapper),
        //     ctas: doctorTelemed.hospitals.map((h) => h.ctas),
        //     rating: doctorTelemedDescription?.rating || null,
        //     bookingTotal: doctorTelemedDescription?.bookingTotal || null,
        //     slug: doctorTelemedDescription?.slug || '',
        //     subjects: this.doctorTelemedSubjectMapper(doctorTelemed?.subjects),
        // };

        return {
            ...doctorTelemed,
            description: {
                ...doctorTelemed?.description,
                ...doctorTelemedDescription?.toObject(),
                rating: {
                    ...doctorTelemedDescription?.toObject()?.rating,
                    count: doctorTelemedDescription?.toObject()?.bookingTotal || 100
                }
            }
        }
    }

    private async callApiGetDoctorTelemedFromSearch({ ids }: { ids: string[] }) {
        let response: AxiosResponse<SearchObject[]>;
        try {
            let config: AxiosRequestConfig = {
                method: 'get',
                url: `${this.urlConfigService.getUrlApiSearchService}/services/services?category=doctor&offset=0&limit=${ids.length}&includes=results&treeId=TELEMEDNOW&doctor_ids=${ids.join(",")}`,
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            response = await this.httpService.request<SearchObject[]>(config).toPromise();
            const doctorByIds = response.data[0].results;
            const sortedData = this.sortByIds(ids, doctorByIds, 'id')
            return Promise.all(sortedData.map(this.doctorTelemedMapper));
        } catch (error) {
            this.logger.error(`Error when calling API: ${error.message}`);
            return {
                doctorTelemed: []
            };
        }
    }

    // ? ==================== HOSPITALS ====================
    private async mapHospitalDescriptionToHospital({ mapHospitalDescToHospital, ctas }: { mapHospitalDescToHospital: IHospitalDescription[],  ctas: HomePageBlockConfig["hospitals"]["ctas"] }) {
        const hospitalDescriptionMapToHospital = mapHospitalDescToHospital.map(async hospitalDesc => {
            const hospital = await this.hospitalModel.findOne(
                { slug: hospitalDesc.hospitalId },
                {
                    name: true,
                    partnerId: true,
                    image: true,
                    circleLogo: true,
                    address: true,
                    city_id: true,
                    deliveryStatus: true,
                    message: true,
                    sponsored: true,
                    listingPackagePaid:true,
                    newHospitalTypes: true,
                    slug: true,
                    googleMap: true,
                    packageImgDefault: true,
                }).populate({
                    path: "city",
                    select : {
                        name: 1,
                        shortName: 1
                    }
                }).populate({
                    path: "district",
                    select: {
                        name: 1
                    }
                })
                .exec();
                
            return {
                ...hospital?.toObject(),
                ...( ctas?.hasOwnProperty(hospital.partnerId) ? { cta: ctas[hospital.partnerId] } : {} ),
                description: hospitalDesc?.toObject() ?? {},
            }
        })

        return Promise.all(hospitalDescriptionMapToHospital);
    }

    private async getHospitalsWithDescription ({ ids, ctas } : { ids: string[] , ctas: HomePageBlockConfig["hospitals"]["ctas"] }) {
        const hospitalDescription = await this.hospitalDescriptionModel.find(
            { _id: { $in: ids } }, 
        ).select({
            hospitalId: true,
            description: true,
            shortDescription: true,
            rating: true,
            bookingTotal: true,
            slug: true,
            googleMap: true,
            packageImgDefault: true
        }).exec();
        return this.mapHospitalDescriptionToHospital({ mapHospitalDescToHospital: hospitalDescription, ctas: ctas });
    }

    private async mapHospitalToHospitalDescription(hospital: IHospital) {
        const hospitalDescription = await this.hospitalDescriptionModel.findOne(
            { hospitalId: hospital?.slug },
            {
                hospitalId: true,
                description: true,
                shortDescription: true,
                rating: true,
                bookingTotal: true,
                slug: true,
                googleMap: true,
                packageImgDefault: true,
                layout: true
            }
            ).exec();
        return {
            ...hospital,
            description: hospitalDescription?.toObject() ?? {},
            layout: hospitalDescription?.layout || 'NORMAL'
        }
    }

    private async getCashBackContentPopup (): Promise<{ content: string[] }> {
        try {
            const cashBackPopupContent = await this.globalSettingService.findByKeyAndRepoName(`HOME_PAGE_CASH_BACK_POPUP_${this.repoConfigService.getRepoName()}`);
            return JSON.parse(cashBackPopupContent);
        } catch (error) {
            this.logger.error(`Error when getting cash back popup content: ${error.message}`);
            return {
                content: []
            };
        }
    } 

    public async getPartnerCashBackPopupList(): Promise<{
        [x: string]: {
            title?: string;
            content?: string;
            html?: string;
        }
    }> {
        const cacheKey = `get-cash-back-popup_web`;
        const value = await this.cacheService.get(cacheKey);

        if(value) {
            return value;
        }

        const fees = await this.feeRefundConfigs.find().exec();
        let { content } = await this.getCashBackContentPopup();

        if(fees.length === 0 || content.length === 0) {
            return {};
        }

        const feesMapped = fees.map(fee => ({
            [fee.feeRefundPartnerId]: {
                title: content[0],
                content: content[1].replace('[x]', fee.feeRate?.toString()).replace('[CSYT]', fee.hospitalName?.toString()).replace('[TẠI ĐÂY]', "https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien"),
                html: content[2].replace('[x]', fee.feeRate?.toString()).replace('[CSYT]', fee.hospitalName?.toString()).replace('[TẠI ĐÂY]', "https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien")
            }
        }));

        const response = feesMapped.reduce((acc, item) => ({ ...acc, ...item }), {});               
    
        await this.cacheService.set(cacheKey, response, { ttl: 60 * 60 });
        
        return response;
    }

    private async getHospitalByIds({ partnerIds , ctas }:{ partnerIds: string[]; ctas: HomePageBlockConfig["hospitals"]["ctas"] }) {
        const hospitals = await this.hospitalModel.find(
            { partnerId : { $in: partnerIds } },
            {
                name: true,
                partnerId: true,
                image: true,
                circleLogo: true,
                address: true,
                city_id: true,
                deliveryStatus: true,
                message: true,
                sponsored: true,
                listingPackagePaid: true,
                newHospitalTypes: true,
                slug: true,
                googleMap: true,
                packageImgDefault: true,
                isCashBack: true
            }
        ).populate({
            path: "city",
            select : {
                name: 1,
                shortName: 1
            }
        }).populate({
            path: "district",
            select: {
                name: 1
            }
        })
        .exec();

        const fees = await this.getPartnerCashBackPopupList()
        
        const hospitalsObjectWithCta = hospitals.map((hospital) => ({
            ...hospital.toObject(),
            ...( ctas?.hasOwnProperty(hospital.partnerId) ? { cta: ctas[hospital.partnerId] } : {} ),
            ...( fees?.hasOwnProperty(hospital.partnerId) ? { popup: fees[hospital.partnerId] } : {} )
        }));

        const sortedData = this.sortByIds(partnerIds, hospitalsObjectWithCta, 'partnerId')
        let mapData = await Promise.all(sortedData.map(this.mapHospitalToHospitalDescription));
        mapData = mapData.map(h => {
            return { ...h, cta: { ...h.cta, layout: h.layout }}
        })
        return mapData
    }


    public async getSubjects() {
        const keywords = await this.searchKeywordsModel
        .find({ enable: true, type: "departments" })
        .exec();

        // return keywords.map(keyword => keyword.value).map(value => JSON.parse(value)).flat()
        const subjects = keywords.map(keyword => keyword.value).map(value => JSON.parse(value));
        return flattenDeep(subjects);
    }

    public async readCacheIfExist(key: string): Promise<unknown | undefined> {
        return this.cacheService.get(key);
    }

    public async setCacheIfMiss(key: string, data: unknown, ttl = 3600) {
        return this.cacheService.set(key, data, { ttl });
    }

    public async getHomePageWithGlobalSettingConfig ({ headers, config } : { config: HomePageBlockConfig, headers: HomePageHeaderDto }) {
        const { partner, hospitals, telemed_doctor_in_month, service_in_month, testimonials, subjects, ads } = config
        const data = await this.getHomePage({ config, headers });

        return {
            ads: ads.isAds ? ads : null,
            partner: partner.hide ? null : data.partner,
            hospitals: hospitals.hide ? null : data.hospitals,  
            telemed_doctor_in_month: telemed_doctor_in_month.hide ? null : data.telemed_doctor_in_month,
            service_in_month: service_in_month.hide ? null : data.service_in_month,
            testimonials: testimonials.hide ? null : data.testimonials,
            subjects: subjects.hide ? null : data.subjects,
            banners: partner.hide ? null : data.banners,
            bannersMulti: partner.hide ? null : data.bannersMulti,
            bannerServices: partner.hide ? null : data.bannerServices
        }
    }

    async getServiceDescriptionMapByServiceId(): Promise<any> {
        const cacheKey = `get-service-description_web_app`;
        const cachedData = await this.cacheService.get(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        let allServiceDescriptions: any = await this.serviceDescriptionModel.find({}).select('-description -shortDescription').exec();

        if (allServiceDescriptions.length) {
            allServiceDescriptions = allServiceDescriptions.map(item => item.toObject());
        }

        const result = allServiceDescriptions.reduce((res, item) => {
            res[`${item.serviceId}`] = item;
            return res;
        }, {});

        this.cacheService.set(cacheKey, result, { ttl: 60 * 60 });

        return result;
    }

    private sortByIds(ids, serviceData, id) {
        const dataMap = serviceData.reduce((acc, item) => {
            acc[item[id]] = item;
            return acc;
        }, {});
    
        return ids.map(id => dataMap[id])?.filter(Boolean);
    }
    
    private async getServiceFromSearchApi ({ ids, headers }: { ids: string[], headers: HomePageHeaderDto }) {
        try {
            let data = JSON.stringify({
                "category": "package",
                "includes": "results",
                "service_ids": ids.join(','),
                "limit": ids?.length,
                "offset": 0,
            });
            let config: AxiosRequestConfig = {
              method: 'post',
              url: `${this.urlConfigService.getAppMedproApi()}/mongo/service/home-page/package/list`,
              headers: { 
                'Content-Type': 'application/json',
                appid: 'medpro',
                locale: 'vi'
              },
              data : data
            };
            
            const response = await this.httpService.request(config).toPromise();
            const serviceByIds = response?.data?.results ?? []
            const sortedData = this.sortByIds(ids, serviceByIds, 'id')
            return sortedData;
        } catch (error) {
            this.logger.error(`Error when calling API: ${error.message}`);
            return [];
        }
    }

    private async getHomePage({ headers , config } : { config: HomePageBlockConfig, headers: HomePageHeaderDto }): Promise<{
        partner: object;
        hospitals: object;
        telemed_doctor_in_month: object;
        service_in_month: object;
        testimonials: object;
        subjects: object;
        banners: object;
        bannersMulti: object;
        bannerServices: object;
    }>  {
        const [
            doctorTelemed, 
            partner, 
            service_in_month, 
            hospitals,
            subject
        ] = await Promise.all([
            this.callApiGetDoctorTelemedFromSearch({ ids: config.telemed_doctor_in_month.ids }),
            this.getMedproPartner({ ids: config.partner.ids, ctas: config.partner.ctas }),
            this.getServiceInMonth({ ids: config.service_in_month.ids, headers }),
            this.getHospitalByIds({ partnerIds: config.hospitals.ids, ctas: config.hospitals.ctas }),
            this.getSubjects()
        ]);

        return {
            partner: {
                data: partner?.map(pObj => {
                    if (['android', 'ios'].includes(headers?.platform) && pObj?.partnerId === 'drcheck') {
                        return { ...pObj, name: 'Trung Tâm\nNội Soi Tiêu Hoá\nDoctor Check' }
                    }
                    return { ...pObj }
                }),
            },
            hospitals: {
                data: hospitals,
            },
            telemed_doctor_in_month: {
                data: doctorTelemed,
            },
            service_in_month: {
                data: service_in_month,
            },
            testimonials: {
                data: config.testimonials.data,
            },
            subjects: {
                data: subject,
            },
            banners: {
                data: config.banners.data
            },
            bannersMulti: {
                data: config?.bannersMulti?.data
            },
            bannerServices: {
                data: config?.bannerServices?.data
            },
        };
    }

    /////HOME PAGE VER 2.0

    private getDateFilter(headers: any) {
        const currentDate = moment().toDate();
        let repo = 'beta'
        if (!REPO_NAME_BETA.includes(this.repoName)) {
            repo = 'live';
        }
        return {
            repo,
            status: true,
            appid: headers?.appid || 'medpro',
            $or: [
                { display: true },
                {
                    display: false,
                    $expr: {
                        $and: [
                            { $lte: [{ $toDate: '$fromDate' }, currentDate] }, // fromDate <= currentDate
                            { $gte: [{ $toDate: '$toDate' }, currentDate] }, // toDate >= currentDate
                        ],
                    },
                },
            ],
        };
    }

    async getHomePageDataV2(headers: any): Promise<any> {
        const dateFilter = this.getDateFilter(headers);
        if (headers?.appid === 'canthozone') {
            const banners = await this.bannersHomeModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec();
            return {
                banners: {
                    data: {
                        desktop: (banners || []).filter(banner => banner.platform === 'desktop'),
                        mobile: (banners || []).filter(banner => banner.platform === 'mobile'),
                    },
                },
            };
        }

        const [
            ads,
            bannerServices,
            bannersMulti,
            bannersHome,
            testimonials,
            categoryServiceInMonth,
            telemedDoctorInMonthIds,
            subjectHomePageIds,
            hospitalsHomeIds,
            partnerHomeIds,
        ] = await Promise.all([
            this.adsModel
                .findOne(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.bannerServicesModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.bannersMultiModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.bannersHomeModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.testimonialsModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.categoryServiceInMonthModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.telemedDoctorInMonthIdModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.subjectHomePageIdModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.hospitalsHomeIdsModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
            this.partnerHomeIdsModel
                .find(dateFilter)
                .sort({ order: 1 })
                .exec(),
        ]);

        // Fetch data from external APIs
        const [doctorTelemed, partner, service_in_month, hospitals, subject] = await Promise.all([
            this.callApiGetDoctorTelemedFromSearchV2(telemedDoctorInMonthIds).catch(error => {
                console.error('Error fetching telemed doctors:', error);
                return [];
            }),
            this.getMedproPartnerV2(partnerHomeIds).catch(error => {
                console.error('Error fetching partners:', error);
                return [];
            }),
            this.getServiceInMonthV2(categoryServiceInMonth, headers).catch(error => {
                console.error('Error fetching services:', error);
                return [];
            }),
            this.getMedproPartnerV2(hospitalsHomeIds).catch(error => {
                console.error('Error fetching hospitals:', error);
                return [];
            }),
            this.getSubjects().catch(error => {
                console.error('Error fetching subjects:', error);
                return [];
            }),
        ]);

        return {
            partner: {
                data: partner,
            },
            hospitals: {
                data: hospitals,
            },
            telemed_doctor_in_month: {
                data: doctorTelemed,
            },
            service_in_month: {
                data: service_in_month,
            },
            testimonials: {
                data: testimonials,
            },
            subjects: {
                data: subject,
            },
            banners: {
                data: {
                    desktop: (bannersHome || []).filter(banner => banner.platform === 'desktop'),
                    mobile: (bannersHome || []).filter(banner => banner.platform === 'mobile'),
                },
            },
            bannersMulti: {
                data: {
                    desktop: (bannersMulti || []).filter(banner => banner.platform === 'desktop'),
                    mobile: (bannersMulti || []).filter(banner => banner.platform === 'mobile'),
                },
            },
            bannerServices: {
                data: {
                    desktop: (bannerServices || []).filter(banner => banner.platform === 'desktop'),
                    mobile: (bannerServices || []).filter(banner => banner.platform === 'mobile'),
                },
            },
            ads: ads.toObject() || {},
        };
    }

    // ? ==================== PARTNER ====================
    private async getMedproPartnerV2(partners: IPartnerHomeIdDto[]) {
        const ids = partners.map(partner => partner.id);
        const hospitals = await this.hospitalModel
            .find(
                { partnerId: { $in: ids } },
                {
                    name: true,
                    partnerId: true,
                    image: true,
                    circleLogo: true,
                    address: true,
                    city_id: true,
                    deliveryStatus: true,
                    message: true,
                    sponsored: true,
                    listingPackagePaid: true,
                    newHospitalTypes: true,
                    slug: true,
                    googleMap: true,
                    packageImgDefault: true,
                    isCashBack: true,
                },
            )
            .populate({
                path: 'city',
                select: {
                    name: 1,
                    shortName: 1,
                },
            })
            .populate({
                path: 'district',
                select: {
                    name: 1,
                },
            })
            .exec();

        const fees = await this.getPartnerCashBackPopupList();

        const hospitalsObjectWithCta = hospitals.map(hospital => ({
            ...hospital.toObject(),
            ...(fees?.hasOwnProperty(hospital.partnerId) ? { popup: fees[hospital.partnerId] } : {}),
            cta: partners.find(partner => partner.id === hospital.partnerId)?.cta || {},
        }));
        const sortedData = this.sortByIds(ids, hospitalsObjectWithCta, 'partnerId');
        return await Promise.all(sortedData.map(h => this.mapHospitalToHospitalDescriptionV2(h)));
    }
    //  ? ==================== HOSPITALS DESCRIPTION ====================
    private async mapHospitalToHospitalDescriptionV2(hospital: IHospital) {
        const hospitalDescription = await this.hospitalDescriptionModel
            .findOne(
                { partnerId: hospital?.partnerId },
                {
                    hospitalId: true,
                    description: true,
                    shortDescription: true,
                    rating: true,
                    bookingTotal: true,
                    slug: true,
                    googleMap: true,
                    packageImgDefault: true,
                    layout: true,
                },
            )
            .exec();
        return {
            ...hospital,
            description: hospitalDescription?.toObject() ?? {},
            // layout: hospitalDescription?.layout || 'NORMAL',
        };
    }

    // ? ==================== CALL API SEARCH V2 ====================
    async callApiGetDoctorTelemedFromSearchV2(telemeds: ITelemedDoctorInMonthIds[]): Promise<any> {
        if(!telemeds || telemeds.length === 0) {
            return [];
        }
        let response: AxiosResponse<SearchObject[]>;
        const ids = telemeds.map(telemed => telemed.doctorId);
        try {
            let config: AxiosRequestConfig = {
                method: 'get',
                url: `${this.urlConfigService.getUrlApiSearchService}/services/services?category=doctor&offset=0&limit=${
                    ids.length
                }&includes=results&doctor_ids=${ids.join(',')}`,
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            response = await this.httpService.request<SearchObject[]>(config).toPromise();
            const doctors = response.data[0]?.results || [];
            const sortedData = this.sortByIds(ids, doctors, 'id')
            return Promise.all(sortedData.map(doctor => this.doctorTelemedMapperV2(doctor)));
        } catch (error) {
            this.logger.error(`Error when calling API: ${error.message}`);
            return [];
        }
    }

    //// ? ==================== DOCTOR TELEMED V2 ====================
    private async doctorTelemedMapperV2(doctorTelemed?: SearchObject['results'][0]) {
        const doctorTelemedDescription = await this.doctorDescriptionModel
            .findOne(
                { doctorId: doctorTelemed?.id },
                {
                    isDetailVisible: true,
                    disabled: true,
                    massage: true,
                    rating: true,
                    _id: true,
                    bookingTotal: true,
                    refHospital: true,
                    slug: true,
                },
            )
            .exec();

        return {
            ...doctorTelemed,
            description: {
                ...doctorTelemed?.description,
                ...doctorTelemedDescription?.toObject(),
                rating: {
                    ...doctorTelemedDescription?.toObject()?.rating,
                    count: doctorTelemedDescription?.toObject()?.bookingTotal || 100,
                },
            },
        };
    }

    // ? ==================== SERVICE IN MONTH V2 ====================
    private async getServiceInMonthV2(categorysService: ICategoryServiceInMonth[], headers: HomePageHeaderDto) {
        const serviceIdsByCategory = categorysService.reduce(
            (acc, doc) => {
                if (doc.packageId && ['suc-khoe', 'xet-nghiem', 'tiem-chung'].includes(doc.category)) {
                    acc[doc.category] = acc[doc.category] || [];
                    acc[doc.category].push(doc.packageId);
                }
                return acc;
            },
            { 'suc-khoe': [], 'xet-nghiem': [], 'tiem-chung': [] },
        );
        const serviceInMonthIds = {
            'suc-khoe': serviceIdsByCategory['suc-khoe'],
            'xet-nghiem': serviceIdsByCategory['xet-nghiem'],
            'tiem-chung': serviceIdsByCategory['tiem-chung'],
        };
        const allServiceDescription = await this.getServiceDescriptionMapByServiceId();
        const allPartner = await this.getHospitalInfoMap();
        const [healthServiceDescription, testServiceDescription, vaccineServiceDescription] = await Promise.all([
            this.getServiceFromSearchApiV2({ ids: serviceInMonthIds['suc-khoe'], headers }),
            this.getServiceFromSearchApiV2({ ids: serviceInMonthIds['xet-nghiem'], headers }),
            this.getServiceFromSearchApiV2({ ids: serviceInMonthIds['tiem-chung'], headers }),
        ]);

        return [
            {
                key: 'suc-khoe',
                type: 'Sức khỏe',
                services: healthServiceDescription.map(service => ({
                    ...service,
                    partner: allPartner[service?.partnerId],
                    description: {
                        ...service?.description,
                        ...allServiceDescription[service?.cta?.serviceId],
                    },
                })),
            },
            {
                key: 'xet-nghiem',
                type: 'Xét nghiệm',
                services: testServiceDescription.map(service => ({
                    ...service,
                    partner: allPartner[service?.partnerId],
                    description: {
                        ...service?.description,
                        ...allServiceDescription[service?.cta?.serviceId],
                    },
                })),
            },
            {
                key: 'tiem-chung',
                type: 'Tiêm chủng',
                services: vaccineServiceDescription.map(service => ({
                    ...service,
                    partner: allPartner[service?.partnerId],
                    description: {
                        ...service?.description,
                        ...allServiceDescription[service?.cta?.serviceId],
                    },
                })),
            },
        ];
    }

    private async getServiceFromSearchApiV2({ ids, headers }: { ids: string[]; headers: HomePageHeaderDto }) {
        try {
            if (!ids || ids.length === 0) {
                return [];
            }
            let config: AxiosRequestConfig = {
                method: 'get',
                url: `${this.urlConfigService.getUrlApiSearchService}/services/services?category=package&offset=0&limit=${
                    ids?.length
                }&includes=results&service_ids=${ids.join(',')}`,
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            const response = await this.httpService.request(config).toPromise();

            const serviceByIds = response?.data[0]?.results ?? [];
             const sortedData = this.sortByIds(ids, serviceByIds, 'id')
            return sortedData;
        } catch (error) {
            this.logger.error(`Error when calling API: ${error.message}`);
            return [];
        }
    }

    // Clone data from beta to live
    async cloneBetaToLive(): Promise<{ success: boolean; message: string; results?: any; error?: string }> {
        try {
            const results: any = {};

            // Clone Ads data
            const betaAds = await this.adsModel.find({ repo: 'beta' }).lean();
            if (betaAds.length > 0) {
                await this.adsModel.deleteMany({ repo: 'live' });

                const liveAdsData = betaAds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdAds = await this.adsModel.insertMany(liveAdsData);
                results.ads = { count: createdAds.length };
            }

            // Clone Banner Services data (if has repo field)
            const betaBannerServices = await this.bannerServicesModel.find({ repo: 'beta' }).lean();
            if (betaBannerServices.length > 0) {
                await this.bannerServicesModel.deleteMany({ repo: 'live' });

                const liveBannerServicesData = betaBannerServices.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdBannerServices = await this.bannerServicesModel.insertMany(liveBannerServicesData);
                results.bannerServices = { count: createdBannerServices.length };
            }

            // Clone Banners Multi data (if has repo field)
            const betaBannersMulti = await this.bannersMultiModel.find({ repo: 'beta' }).lean();
            if (betaBannersMulti.length > 0) {
                await this.bannersMultiModel.deleteMany({ repo: 'live' });

                const liveBannersMultiData = betaBannersMulti.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdBannersMulti = await this.bannersMultiModel.insertMany(liveBannersMultiData);
                results.bannersMulti = { count: createdBannersMulti.length };
            }

            // Clone Banners Home data (if has repo field)
            const betaBannersHome = await this.bannersHomeModel.find({ repo: 'beta' }).lean();
            if (betaBannersHome.length > 0) {
                await this.bannersHomeModel.deleteMany({ repo: 'live' });

                const liveBannersHomeData = betaBannersHome.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdBannersHome = await this.bannersHomeModel.insertMany(liveBannersHomeData);
                results.bannersHome = { count: createdBannersHome.length };
            }

            // Clone Testimonials data (if has repo field)
            const betaTestimonials = await this.testimonialsModel.find({ repo: 'beta' }).lean();
            if (betaTestimonials.length > 0) {
                await this.testimonialsModel.deleteMany({ repo: 'live' });

                const liveTestimonialsData = betaTestimonials.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdTestimonials = await this.testimonialsModel.insertMany(liveTestimonialsData);
                results.testimonials = { count: createdTestimonials.length };
            }

            // Clone Hospitals Home IDs data (if has repo field)
            const betaHospitalsHomeIds = await this.hospitalsHomeIdsModel.find({ repo: 'beta' }).lean();
            if (betaHospitalsHomeIds.length > 0) {
                await this.hospitalsHomeIdsModel.deleteMany({ repo: 'live' });

                const liveHospitalsHomeIdsData = betaHospitalsHomeIds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdHospitalsHomeIds = await this.hospitalsHomeIdsModel.insertMany(liveHospitalsHomeIdsData);
                results.hospitalsHomeIds = { count: createdHospitalsHomeIds.length };
            }

            // Clone Partner Home IDs data (if has repo field)
            const betaPartnerHomeIds = await this.partnerHomeIdsModel.find({ repo: 'beta' }).lean();
            if (betaPartnerHomeIds.length > 0) {
                await this.partnerHomeIdsModel.deleteMany({ repo: 'live' });

                const livePartnerHomeIdsData = betaPartnerHomeIds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdPartnerHomeIds = await this.partnerHomeIdsModel.insertMany(livePartnerHomeIdsData);
                results.partnerHomeIds = { count: createdPartnerHomeIds.length };
            }

            // Clone Category Service In Month data (if has repo field)
            const betaCategoryServiceInMonth = await this.categoryServiceInMonthModel.find({ repo: 'beta' }).lean();
            if (betaCategoryServiceInMonth.length > 0) {
                await this.categoryServiceInMonthModel.deleteMany({ repo: 'live' });

                const liveCategoryServiceInMonthData = betaCategoryServiceInMonth.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdCategoryServiceInMonth = await this.categoryServiceInMonthModel.insertMany(liveCategoryServiceInMonthData);
                results.categoryServiceInMonth = { count: createdCategoryServiceInMonth.length };
            }

            // Clone Telemed Doctor In Month IDs data (if has repo field)
            const betaTelemedDoctorInMonthIds = await this.telemedDoctorInMonthIdModel.find({ repo: 'beta' }).lean();
            if (betaTelemedDoctorInMonthIds.length > 0) {
                await this.telemedDoctorInMonthIdModel.deleteMany({ repo: 'live' });

                const liveTelemedDoctorInMonthIdsData = betaTelemedDoctorInMonthIds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, repo: 'live' };
                });

                const createdTelemedDoctorInMonthIds = await this.telemedDoctorInMonthIdModel.insertMany(liveTelemedDoctorInMonthIdsData);
                results.telemedDoctorInMonthIds = { count: createdTelemedDoctorInMonthIds.length };
            }

            return {
                success: true,
                message: 'Successfully cloned beta data to live',
                results,
            };
        } catch (error) {
            console.error('Error cloning beta to live:', error);
            return {
                success: false,
                message: 'Failed to clone beta data to live',
                error: error.message,
            };
        }
    }

    async cloneMedproToMomo(): Promise<{ success: boolean; message: string; results?: any; error?: string }> {
        try {
            const results: any = {};

            const medproAds = await this.adsModel.find({ appid: 'medpro' }).lean();
            if (medproAds.length > 0) {
                await this.adsModel.deleteMany({ appid: 'momo' });

                const momoAdsData = medproAds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdAds = await this.adsModel.insertMany(momoAdsData);
                results.ads = { count: createdAds.length };
            }
            const medproBannerServices = await this.bannerServicesModel.find({ appid: 'medpro' }).lean();
            if (medproBannerServices.length > 0) {
                await this.bannerServicesModel.deleteMany({ appid: 'momo' });

                const momoBannerServicesData = medproBannerServices.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdBannerServices = await this.bannerServicesModel.insertMany(momoBannerServicesData);
                results.bannerServices = { count: createdBannerServices.length };
            }

            const medproBannersMulti = await this.bannersMultiModel.find({ appid: 'medpro' }).lean();
            if (medproBannersMulti.length > 0) {
                await this.bannersMultiModel.deleteMany({ appid: 'momo' });

                const momoBannersMultiData = medproBannersMulti.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdBannersMulti = await this.bannersMultiModel.insertMany(momoBannersMultiData);
                results.bannersMulti = { count: createdBannersMulti.length };
            }
            const medproBannersHome = await this.bannersHomeModel.find({ appid: 'medpro' }).lean();
            if (medproBannersHome.length > 0) {
                await this.bannersHomeModel.deleteMany({ appid: 'momo' });

                const momoBannersHomeData = medproBannersHome.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdBannersHome = await this.bannersHomeModel.insertMany(momoBannersHomeData);
                results.bannersHome = { count: createdBannersHome.length };
            }

            const medproTestimonials = await this.testimonialsModel.find({ appid: 'medpro' }).lean();
            if (medproTestimonials.length > 0) {
                await this.testimonialsModel.deleteMany({ appid: 'momo' });

                const momoTestimonialsData = medproTestimonials.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdTestimonials = await this.testimonialsModel.insertMany(momoTestimonialsData);
                results.testimonials = { count: createdTestimonials.length };
            }

            const medproHospitalsHomeIds = await this.hospitalsHomeIdsModel.find({ appid: 'medpro' }).lean();
            if (medproHospitalsHomeIds.length > 0) {
                await this.hospitalsHomeIdsModel.deleteMany({ appid: 'momo' });

                const momoHospitalsHomeIdsData = medproHospitalsHomeIds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdHospitalsHomeIds = await this.hospitalsHomeIdsModel.insertMany(momoHospitalsHomeIdsData);
                results.hospitalsHomeIds = { count: createdHospitalsHomeIds.length };
            }

            const medproPartnerHomeIds = await this.partnerHomeIdsModel.find({ appid: 'medpro' }).lean();
            if (medproPartnerHomeIds.length > 0) {
                await this.partnerHomeIdsModel.deleteMany({ appid: 'momo' });

                const momoPartnerHomeIdsData = medproPartnerHomeIds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdPartnerHomeIds = await this.partnerHomeIdsModel.insertMany(momoPartnerHomeIdsData);
                results.partnerHomeIds = { count: createdPartnerHomeIds.length };
            }

            // Clone Category Service In Month data from medpro to momo
            const medproCategoryServiceInMonth = await this.categoryServiceInMonthModel.find({ appid: 'medpro' }).lean();
            if (medproCategoryServiceInMonth.length > 0) {
                await this.categoryServiceInMonthModel.deleteMany({ appid: 'momo' });

                const momoCategoryServiceInMonthData = medproCategoryServiceInMonth.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdCategoryServiceInMonth = await this.categoryServiceInMonthModel.insertMany(momoCategoryServiceInMonthData);
                results.categoryServiceInMonth = { count: createdCategoryServiceInMonth.length };
            }

            // Clone Telemed Doctor In Month IDs data from medpro to momo
            const medproTelemedDoctorInMonthIds = await this.telemedDoctorInMonthIdModel.find({ appid: 'medpro' }).lean();
            if (medproTelemedDoctorInMonthIds.length > 0) {
                await this.telemedDoctorInMonthIdModel.deleteMany({ appid: 'momo' });

                const momoTelemedDoctorInMonthIdsData = medproTelemedDoctorInMonthIds.map(item => {
                    const { _id, createdAt, updatedAt, ...rest } = item;
                    return { ...rest, appid: 'momo' };
                });

                const createdTelemedDoctorInMonthIds = await this.telemedDoctorInMonthIdModel.insertMany(momoTelemedDoctorInMonthIdsData);
                results.telemedDoctorInMonthIds = { count: createdTelemedDoctorInMonthIds.length };
            }

            return {
                success: true,
                message: 'Successfully cloned medpro data to momo',
                results,
            };
        } catch (error) {
            console.error('Error cloning medpro to momo:', error);
            return {
                success: false,
                message: 'Failed to clone medpro data to momo',
                error: error.message,
            };
        }
    }
}
