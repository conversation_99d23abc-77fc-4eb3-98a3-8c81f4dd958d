import { IsBoolean, IsDateString, IsOptional, IsString } from "class-validator";

export class QueryRecruitmentDto {
  @IsString()
  @IsOptional()
  cityId?: string;

  @IsString()
  @IsOptional()
  jobNature?: string;

  @IsDateString()
  @IsOptional()
  createdAtFrom?: string;

  @IsDateString()
  @IsOptional()
  createdAtTo?: string;

  @IsDateString()
  @IsOptional()
  deadlineFrom?: string;

  @IsDateString()
  @IsOptional()
  deadlineTo?: string;

  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  page?: string;

  @IsString()
  @IsOptional()
  limit?: string;

  @IsString()
  @IsOptional()
  workingForm?: string;

  @IsOptional()
  status?:string
}
