import { GL<PERSON><PERSON>L_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { Injectable, HttpException, HttpStatus, Logger, HttpService } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
    BOOKING_GUIDE_COLLECTION_NAME,
    CAU_HOI_COLLECTION_NAME,
    DANH_MUC_COLLECTION_NAME,
    HOSPITAL_COLLECTION_NAME,
    HOSPITAL_EXTRA_INFO_COLLECTION_NAME,
    MENU_COLLECTION_NAME,
    SEO_PAGE_COLLECTION_NAME,
} from './schemas/constants';
import { Model } from 'mongoose';
import * as moment from 'moment';
import { first, get, groupBy, has, filter } from 'lodash';
import { IHospital } from './interfaces/hospital.interface';
import { CreateHospitalDTO } from './dto/create-hospital.dto';
import { UpdateHospitalDTO } from './dto/update-hospital.dto';
import { UpdateNotifPartnerDTO } from './dto/update-notif-partner.dto';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { IFeature } from 'src/feature-mongo/interfaces/feature.interface';
import { UpdateMessagePartnerDTO } from './dto/update-message-partner.dto';
import { UpdateBannerDTO } from './dto/update-banner.dto';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { UpdateStaticResourceDTO } from './dto/update-static-resource.dto';
import { APP_ID_COLLECTION_NAME } from 'src/app-id/schemas/constants';
import { IAppId } from 'src/app-id/interfaces/app-id.inteface';
import { UrlConfigService } from 'src/config/config.url.service';
import { PartnerConfigService } from 'src/partner-config/partner-config.service';
import { CreatePartnerConfigDTO } from 'src/partner-config/dto/create-partner-config.dto';
import * as uuid from 'uuid';
import { DeliveryStatusEnum } from './enums/delivery-status.enums';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { find } from 'lodash';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { IGlobalSetting } from 'src/global-setting/interfaces/global-setting.interface';
import { UtilService } from 'src/config/util.service';
import { CreateClinicDto } from './dto/create-clinic.dto';
import { PatientMongoService } from '../patient-mongo/patient-mongo.service';
import { CSKHTokenVerifyDTO } from '../patient-mongo/dto/cskh-token-verify.dto';
import { USER_COLLECTION_NAME } from './../user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { AxiosRequestConfig } from 'axios';
import { GetContentPageDto } from './dto/get-content-page.dto';
import { ClientUtilService } from '../config/client-util.service';
import { HeadersDto } from 'src/common/base/headers.dto';
import { DanhSachV5QueryDto } from './dto/danh-sach-v5-query.dto';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../hospital-description/schema/constants';
import { IHospitalDescription } from '../hospital-description/interface/hospital-description.inteface';
import { CacheManagerService } from '../cache-manager/cache-manager.service';
import { DAILY_TRANSACTION_REPORT_COLLECTION_NAME } from '../report/schemas/constants';
import { IReportTransactionDaily } from '../report/interfaces/report-transaction-daily.interface';
import { IBookingGuide } from './interfaces/booking-guide.interface';
import { IMenu } from './interfaces/menu.interface';
import { IDanhMuc } from './interfaces/danh-muc.interface';
import { IHospitalExtraInfo } from './interfaces/hospital-extra.interface';
import { ICauHoi } from './interfaces/cau-hoi.interface';
import { ISeoPage } from './interfaces/seo-page.interface';
import { SeoPageQuery } from './dto/seo-page.query';
import { REPO_NAME_BETA } from 'src/common/constants';
import { HomePageService } from 'src/home-page/home-page.service';
import { HomePageServiceInject } from 'src/home-page/persistence/home-page.service';

@Injectable()
export class HospitalMongoService {
    private ignoreMessage: string;
    private readonly repoName: string;
    private readonly IGNORE_MESSAGE: string = 'BETA_IGNORE_MESSAGE';
    private logger = new Logger(HospitalMongoService.name);
    private IGNORE_DOCTOR_FEATURE = 'IGNORE_DOCTOR_FEATURE';
    private BOOKING_DOCTOR_TYPE = 'booking.doctor';
    private BOOKING_DATE_TYPE = 'booking.date';
    private CIRCLE_LOGO_URL: string = 'https://resource-testing.medpro.com.vn/static/images';
    private readonly CHO_RAY_DAT_KHAM: string = 'CHO_RAY_DAT_KHAM';
    private boUrl: string;

    constructor(
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(FEATURE_COLLECTION_NAME) private featureModel: Model<IFeature>,
        @HomePageServiceInject() private readonly homePageService: HomePageService,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(APP_ID_COLLECTION_NAME) private appIdModel: Model<IAppId>,
        @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private globalSetingModel: Model<IGlobalSetting>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(HOSPITAL_DESCRIPTION_COLLECTION_NAME) private hospitalDescriptionModel: Model<IHospitalDescription>,
        @InjectModel(DAILY_TRANSACTION_REPORT_COLLECTION_NAME) private transactionDailyModel: Model<IReportTransactionDaily>,
        @InjectModel(BOOKING_GUIDE_COLLECTION_NAME) private readonly bookingGuideModel: Model<IBookingGuide>,
        @InjectModel(MENU_COLLECTION_NAME) private readonly menuModel: Model<IMenu>,
        @InjectModel(DANH_MUC_COLLECTION_NAME) private readonly danhMucModel: Model<IDanhMuc>,
        @InjectModel(CAU_HOI_COLLECTION_NAME) private readonly cauHoiModel: Model<ICauHoi>,
        @InjectModel(HOSPITAL_EXTRA_INFO_COLLECTION_NAME) private readonly hospitalExtraInfoModel: Model<IHospitalExtraInfo>,
        @InjectModel(SEO_PAGE_COLLECTION_NAME) private seoPageModel: Model<ISeoPage>,
        private readonly partnerConfigService: PartnerConfigService,
        private readonly urlConfigService: UrlConfigService,
        private readonly globalSettingService: GlobalSettingService,
        private readonly repoConfigService: ConfigRepoService,
        private readonly utilsService: UtilService,
        private readonly httpService: HttpService,
        private readonly utilService: UtilService,
        private readonly patientMongoService: PatientMongoService,
        private readonly clientUtilService: ClientUtilService,
        private cacheService: CacheManagerService,
    ) {
        this.bookingGuideModel = bookingGuideModel;
        this.repoName = this.repoConfigService.getRepoName();
        this.boUrl = this.urlConfigService.getBoUrl();
    }

    async getBookingGuide(appid: string, locale = 'vi'): Promise<any> {
        let condition: any = { partnerId: appid, type: 'guide-booking', isDeleted: false };
        if (locale && locale !== 'vi') {
            condition = {
                ...condition,
                locale,
            };
        } else {
            condition = {
                ...condition,
                locale: { $in: [null, '', 'vi'] },
            };
        }

        return this.bookingGuideModel.find(condition).exec();
    }

    async getContentPageByKeyAndPartner(partnerId: string, key: string, locale = 'vi') {
        try {
            let result: any;
            switch (key) {
                case 'thac-mac':
                    result = await this.getAllDanhAndCauHoiByPartner(partnerId, locale);
                    break;
                case 'quy-trinh':
                    const conditionQuyTrinh = this.getCondtionLocaleVi({ partnerId, type: 'flow-booking', isDeleted: false }, locale);
                    // console.log('conditionQuyTrinh', conditionQuyTrinh);
                    result = await this.bookingGuideModel
                        .find(conditionQuyTrinh)
                        .sort({ sortOrder: 'asc' })
                        .lean();
                    break;
                default:
                    const conditionMenu = this.getCondtionLocaleVi({ key, partnerId }, locale);
                    result = await this.menuModel.findOne(conditionMenu).exec();
                    break;
            }
            return result;
        } catch (error) {
            this.logger.error(`Error when exec getContentPageByKeyAndRepo()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    getCondtionLocaleVi(condition: any, locale = 'vi') {
        if (locale && locale !== 'vi') {
            return {
                ...condition,
                locale,
            };
        } else {
            return {
                ...condition,
                locale: { $in: [null, '', 'vi'] },
            };
        }
    }

    async getAllDanhAndCauHoiByPartner(partnerId: string, locale = 'vi'): Promise<any> {
        try {
            const conditionDanhMuc = this.getCondtionLocaleVi({ partnerId }, locale);
            const danhmucs = await this.danhMucModel.find(conditionDanhMuc).exec();

            const conditionCauHoi = this.getCondtionLocaleVi(
                {
                    category_id: {
                        $in: [...danhmucs.map(danhmuc => danhmuc.id)],
                    },
                    isDeleted: false,
                },
                locale,
            );

            const cauhois = await this.cauHoiModel
                .find(conditionCauHoi)
                .sort({ sortOrder: 'asc' })
                .exec();

            const groupCauHois = groupBy(cauhois, 'category_id');

            const datas = danhmucs.map(danhmuc => {
                const listCauHoi = groupCauHois[danhmuc.id];
                return {
                    ...danhmuc.toObject(),
                    questions: listCauHoi,
                };
            });
            return datas;
        } catch (error) {
            throw error;
        }
    }

    async partnerConfigData(partnerId: string): Promise<CreatePartnerConfigDTO> {
        const id = uuid.v4().replace(/-/g, '');
        const prefixValuePatientCode = '';
        const notifAppId = '';
        const notifApiKey = '';
        const notifAppIdVOIP = '';
        const notifApiKeyVOIP = '';
        const isVerifiedByPhone = true;
        const beforeTimeCancelBookingAccepted = '16:00:00';
        const syncBooking = false;
        const reserveAndSync = false;
        const bookingReminder = false;
        const bookingBHYT = false;
        const bookingBeforeTest = false;
        return {
            id,
            partnerId,
            prefixValuePatientCode,
            notifAppId,
            notifApiKey,
            notifAppIdVOIP,
            notifApiKeyVOIP,
            isVerifiedByPhone,
            beforeTimeCancelBookingAccepted,
            syncBooking,
            reserveAndSync,
            bookingReminder,
            bookingBHYT,
            bookingBeforeTest,
        };
    }

    async isExistsPartner(partnerId: string): Promise<boolean> {
        return this.hospitalModel.exists({ partnerId });
    }

    async create(formData: CreateHospitalDTO | CreateClinicDto): Promise<IHospital> {
        try {
            const isExistsPartner = await this.isExistsPartner(formData.partnerId);
            if (isExistsPartner) {
                throw new HttpException(`partnerId: ${formData.partnerId} already exists`, HttpStatus.BAD_REQUEST);
            }
            /* tìm lại thông tin cityId */
            const findCity = await this.cityModel.findOne({ id: formData.city_id }).exec();
            if (findCity) {
                formData.city = findCity._id;
            }
            const idv4 = uuid.v4().replace(/-/g, '');
            const hospital = new this.hospitalModel({ ...formData, id: idv4 });
            const res = await hospital.save();
            if (res) {
                const data = await this.partnerConfigData(res.partnerId);
                await this.partnerConfigService.create(data);
            }
            return res.toJSON();
        } catch (error) {
            const errorMessage = error?.response || `Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            this.logger.error(`Error when exec addFeatureIntoPartner with partnerId: ${formData.partnerId}\nError: ${error.message}`);
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async danhSach(appId: string, type: number = 1): Promise<any> {
        this.ignoreMessage = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_MESSAGE, this.repoName);
        const ignoreMessageConfig = new Set(this.ignoreMessage.split(','));
        const trackingDeliveryStatus = new Set([DeliveryStatusEnum.IN_PROGRESS, DeliveryStatusEnum.HOLD]);
        const globalSetting = await this.globalSettingService.findAll();
        const partnersOnlyShowBeta = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA', this.repoName);

        switch (appId) {
            case 'medpro':
            case 'zalopay':
            case 'momo':
            case '1900':
            case 'cskh':
                const filterCskhSql: any = ['api-v2-beta', 'api-v2-111'].includes(this.repoName)
                    ? { partnerId: { $nin: ['medpro', 'umcmono', 'digimed', 'canthozone'] }, hospitalType: type }
                    : { partnerId: { $nin: ['medpro', 'umcmono', 'digimed', 'canthozone'] }, status: 1, hospitalType: type };

                let dataOrigin = await this.hospitalModel
                    .find(filterCskhSql)
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (appId === 'cskh') {
                    const getND1CS = await this.hospitalModel
                        .find({
                            partnerId: { $in: ['nhidong1cs', 'digimed'] },
                        })
                        .populate('city')
                        .exec();
                    dataOrigin = [...dataOrigin, ...getND1CS];
                }
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    dataOrigin = dataOrigin.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                dataOrigin = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), dataOrigin);
                let resultData: any = [];
                for (const hospital of dataOrigin) {
                    const hosObj = hospital.toObject();
                    const features = hosObj.features;
                    if (hospital.partnerId === 'trungvuong') {
                        const findBookingDate = (features || []).filter(item => item.type === this.BOOKING_DATE_TYPE);
                        const resultFeatures = findBookingDate.length === 1 ? findBookingDate : features;
                        hosObj.features = resultFeatures;
                        resultData = [...resultData, hosObj];
                    } else {
                        let resultFeatures: any = [];
                        for (const feature of features) {
                            const getChildren = get(feature, 'children', []);
                            if (getChildren.length > 0) {
                                resultFeatures = [...resultFeatures, ...getChildren];
                            } else {
                                resultFeatures = [...resultFeatures, feature];
                            }
                        }
                        hosObj.features = resultFeatures;
                        resultData = [...resultData, hosObj];
                    }
                }
                const resultInfo = resultData
                    .map((item: any) => {
                        if (ignoreMessageConfig.has(item.partnerId)) {
                            return {
                                ...item,
                                message: '',
                            };
                        }
                        return item;
                    })
                    .map((element: any) => {
                        const { circleLogo } = element;
                        if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                            return {
                                ...element,
                                deliveryMessage: this.deliveryMessage(element.deliveryStatus, globalSetting),
                                ...(circleLogo && { image: circleLogo }),
                            };
                        }
                        return { ...element, ...(circleLogo && { image: circleLogo }) };
                    })
                    .map(itemcs => {
                        if (itemcs.partnerId === 'nhidong1cs') {
                            return {
                                ...itemcs,
                                status: 1,
                            };
                        } else {
                            if (['api-v2-beta', 'api-v2-111'].includes(this.repoName)) {
                                return {
                                    ...itemcs,
                                    status: 1,
                                };
                            } else {
                                return itemcs;
                            }
                        }
                    });
                if (appId === 'momo') {
                    return resultInfo.filter(item => item.partnerId !== 'umc')
                }
                return resultInfo;
            case 'medprodanang':
                const filterDanangSql: any = ['api-v2-beta', 'api-v2-111'].includes(this.repoName)
                    ? {
                          partnerId: {
                              $in: [
                                  'bvphoidanang',
                                  'bvcdanang',
                                  'bvphcndanang',
                                  'bvpsndanang',
                                  'bvdndanang',
                                  'bvmdanang',
                                  'bvlpdanang',
                                  'bvyhctdanang',
                                  'bvttdanang',
                                  'bvdldanang',
                                  'bvdkbddanang',
                                  'bvubdanang',
                                  'ttksbtdanang',
                                  'ttpydanang',
                                  'ttytqhcdanang',
                                  'ttkdytqtdanang',
                                  'ttkndanang',
                                  'ttdtbdcbdanang',
                                  'ccdskhhgddanang',
                                  'ttrhmdanang',
                                  'ttpchivdanang',
                                  'ttcsskssdanang',
                                  'ttttgddskdnang',
                                  'ttgdykdanang',
                                  'ttytqcldanang',
                                  'ttytqlcdanang',
                                  'ttythhvdanang',
                                  'ttytnhsdnang',
                                  'ttytqstdanang',
                                  'ttytqtkdanang',
                                  'ttytdpdanang',
                                  'ttccdanang',
                                  'ccatvstpdanang',
                              ],
                          },
                          hospitalType: type,
                      }
                    : {
                          partnerId: {
                              $in: [
                                  'bvphoidanang',
                                  'bvcdanang',
                                  'bvphcndanang',
                                  'bvpsndanang',
                                  'bvdndanang',
                                  'bvmdanang',
                                  'bvlpdanang',
                                  'bvyhctdanang',
                                  'bvttdanang',
                                  'bvdldanang',
                                  'bvdkbddanang',
                                  'bvubdanang',
                                  'ttksbtdanang',
                                  'ttpydanang',
                                  'ttytqhcdanang',
                                  'ttkdytqtdanang',
                                  'ttkndanang',
                                  'ttdtbdcbdanang',
                                  'ccdskhhgddanang',
                                  'ttrhmdanang',
                                  'ttpchivdanang',
                                  'ttcsskssdanang',
                                  'ttttgddskdnang',
                                  'ttgdykdanang',
                                  'ttytqcldanang',
                                  'ttytqlcdanang',
                                  'ttythhvdanang',
                                  'ttytnhsdnang',
                                  'ttytqstdanang',
                                  'ttytqtkdanang',
                                  'ttytdpdanang',
                                  'ttccdanang',
                                  'ccatvstpdanang',
                              ],
                          },
                          hospitalType: type,
                          status: 1,
                      };
                let dataOriginDN = await this.hospitalModel
                    .find(filterDanangSql)
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    dataOriginDN = dataOriginDN.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                let resultDataDN: any = [];
                for (const hospital of dataOriginDN) {
                    const hosObj = hospital.toObject();
                    const features = hosObj.features;
                    if (hospital.partnerId === 'trungvuong') {
                        const findBookingDate = (features || []).filter(item => item.type === this.BOOKING_DATE_TYPE);
                        const resultFeatures = findBookingDate.length === 1 ? findBookingDate : features;
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    } else {
                        let resultFeatures: any = [];
                        for (const feature of features) {
                            const getChildren = get(feature, 'children', []);
                            if (getChildren.length > 0) {
                                resultFeatures = [...resultFeatures, ...getChildren];
                            } else {
                                resultFeatures = [...resultFeatures, feature];
                            }
                        }
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    }
                }
                return resultDataDN.map(element => {
                    const { circleLogo } = element;
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                });
            case 'umcmono':
                const dataUmcMono = await this.getPartnersForUmcMono(
                    globalSetting,
                    type,
                    partnersOnlyShowBeta,
                    trackingDeliveryStatus,
                    ignoreMessageConfig,
                );
                return dataUmcMono.map(item => {
                    return {
                        ...item,
                        message: '',
                    };
                });
            default:
                let data = await this.hospitalModel
                    .find({ hospitalType: type })
                    .populate('subjects')
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    data = data.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                return data.map((item: any) => {
                    const oo = item.toObject();
                    const { circleLogo } = item;
                    if (ignoreMessageConfig.has(item.partnerId)) {
                        return {
                            ...oo,
                            message: '',
                            ...(circleLogo && { image: circleLogo }),
                        };
                    }
                    return { ...oo, ...(circleLogo && { image: circleLogo }) };
                });
        }
    }

    // ? **********************************************************************************
    // ? START - CACHE CHO DANH SÁCH BỆNH VIỆN MONGO
    // ? **********************************************************************************
    private getHospitalListCacheKey(appId: string, type: number, version: number, platform: string): string {
        return `HOSPITAL_${appId}_${type}_${version}_${platform}_${this.repoName}`;
    }    

    public async handleGetHospitalCacheOrSetNewIfNotExistAndGetData(payload: {
        appId: string, 
        type: number, 
        version: number, 
        cskhInfo?: CSKHTokenVerifyDTO, 
        platform?: string
    }) {
        const key = this.getHospitalListCacheKey(payload.appId, payload.type, payload.version, payload.platform);

        console.log(`danh sach - key: ${key}`)
        
        let data = await this.cacheService.get(key);

        if (data) {
            // console.log("Cache còn hiệu lực -> Trả dữ liệu:::" )
            return data;
        }
        
        // console.log("Cache không còn hiệu lực hoặc chưa được cache - Tiến hành query vào db lấy dữ liệu trả về và cache cho sau -->" )
        return this.handleCacheForHospitalListAndGetNewData({
            appId: payload.appId,
            type: payload.type,
            version: payload.version,
            cskhInfo: payload.cskhInfo,
            platform: payload.platform
        });
    }

    private async handleCacheForHospitalListAndGetNewData(payload: {
        appId: string, 
        type: number, 
        version: number, 
        cskhInfo?: CSKHTokenVerifyDTO, 
        platform?: string
    }) {
       
        const key = this.getHospitalListCacheKey(payload.appId, payload.type, payload.version, payload.platform);
        const TIME_TO_LIVE = 7200;
        const data = await this.danhSachByVersion(payload.appId, payload.type, payload.version, payload.cskhInfo, payload.platform);
        this.cacheService.set(key, data, { ttl: TIME_TO_LIVE });

        return data;
    }    
    // ? **********************************************************************************
    // ? END - CACHE CHO DANH SÁCH BỆNH VIỆN MONGO
    // ? **********************************************************************************

    async danhSachByVersion(appId: string, type: number, version: number = 1, cskhInfo?: CSKHTokenVerifyDTO, platform?: string): Promise<any> {
        const ignoreDoctorFeatureKey = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_DOCTOR_FEATURE);
        const ignoreDoctorFeature = new Set(ignoreDoctorFeatureKey.split(','));
        let data: any = [];
        switch (+version) {
            case 1:
                data = await this.danhSach(appId, type);
                data = await this.overrideFeaturesModeBeta(data);
                break;
            // return data.map(item => {
            //     item.features = item.features.filter(element => element.status !== false && element.disabled !== true);
            //     return item;
            // });
            case 2:
                data = await this.danhSach(appId, type);
                data = await this.overrideFeaturesModeBeta(data);
                break;
            default:
                break;
        }
        // if (ignoreDoctorFeature.has(appId)) {
        //     return data.map(element => {
        //         // for parent feature
        //         const features = element.features.filter(item => item.type.toUpperCase() !== this.BOOKING_DOCTOR_TYPE.toUpperCase());
        //         // for children
        //         const featureTransfer = features.map(item => {
        //             if (item.children.length > 0) {
        //                 item.children = item.children.filter(children => children.type.toUpperCase() !== this.BOOKING_DOCTOR_TYPE.toUpperCase());
        //             }
        //             return item;
        //         });
        //         element.features = featureTransfer;
        //         return element;
        //     });
        // }

        // lấy danh sách partner chỉ định khám cskh
        if (cskhInfo) {
            data = await this.handlePartnersForCskh(data, cskhInfo);
        }
        data = await this.handleFeatureChoRay(data, appId, platform);
        return data;
    }

    async danhSachV2(appId: string, type: number = 1): Promise<any> {
        this.ignoreMessage = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_MESSAGE, this.repoName);
        const ignoreMessageConfig = new Set(this.ignoreMessage.split(','));
        const trackingDeliveryStatus = new Set([DeliveryStatusEnum.IN_PROGRESS, DeliveryStatusEnum.HOLD]);
        const globalSetting = await this.globalSettingService.findAll();
        const partnersOnlyShowBeta = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA', this.repoName);

        switch (appId) {
            case 'medpro':
            case 'momo':
            case 'zalopay':
            case '1900':
            case 'cskh': {
                let list = await this.hospitalModel
                    .find({
                        partnerId: { $nin: ['medpro', 'umcmono', 'digimed', 'canthozone'] },
                        status: 1,
                        hospitalType: type,
                    })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    list = list.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                list = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), list);
                return list
                    .map(item => {
                        if (ignoreMessageConfig.has(item.partnerId)) {
                            const objBv = item.toObject();
                            return {
                                ...objBv,
                                message: '',
                            };
                        }
                        return {
                            ...item.toObject(),
                        };
                    })
                    .map(element => {
                        const { circleLogo } = element;
                        if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                            return {
                                ...element,
                                deliveryMessage: this.deliveryMessage(element.deliveryStatus, globalSetting),
                                ...(circleLogo && { image: circleLogo }),
                            };
                        }
                        return { ...element, ...(circleLogo && { image: circleLogo }) };
                    });
            }
            case 'medprodanang':
                let dataOriginDN = await this.hospitalModel
                    .find({
                        partnerId: {
                            $in: [
                                'bvphoidanang',
                                'bvcdanang',
                                'bvphcndanang',
                                'bvpsndanang',
                                'bvdndanang',
                                'bvmdanang',
                                'bvlpdanang',
                                'bvyhctdanang',
                                'bvttdanang',
                                'bvdldanang',
                                'bvdkbddanang',
                                'bvubdanang',
                                'ttksbtdanang',
                                'ttpydanang',
                                'ttytqhcdanang',
                                'ttkdytqtdanang',
                                'ttkndanang',
                                'ttdtbdcbdanang',
                                'ccdskhhgddanang',
                                'ttrhmdanang',
                                'ttpchivdanang',
                                'ttcsskssdanang',
                                'ttttgddskdnang',
                                'ttgdykdanang',
                                'ttytqcldanang',
                                'ttytqlcdanang',
                                'ttythhvdanang',
                                'ttytnhsdnang',
                                'ttytqstdanang',
                                'ttytqtkdanang',
                                'ttytdpdanang',
                                'ttccdanang',
                                'ccatvstpdanang',
                            ],
                        },
                        hospitalType: type,
                        status: 1,
                    })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    dataOriginDN = dataOriginDN.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                let resultDataDN: any = [];
                for (const hospital of dataOriginDN) {
                    const hosObj = hospital.toObject();
                    const features = hosObj.features;
                    if (hospital.partnerId === 'trungvuong') {
                        const findBookingDate = (features || []).filter(item => item.type === this.BOOKING_DATE_TYPE);
                        const resultFeatures = findBookingDate.length === 1 ? findBookingDate : features;
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    } else {
                        let resultFeatures: any = [];
                        for (const feature of features) {
                            const getChildren = get(feature, 'children', []);
                            if (getChildren.length > 0) {
                                resultFeatures = [...resultFeatures, ...getChildren];
                            } else {
                                resultFeatures = [...resultFeatures, feature];
                            }
                        }
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    }
                }
                return resultDataDN.map(element => {
                    const { circleLogo } = element;
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                });
            case 'umcmono':
                return this.getPartnersForUmcMono(globalSetting, type, partnersOnlyShowBeta, trackingDeliveryStatus, ignoreMessageConfig);
            default: {
                let list = await this.hospitalModel
                    .find({
                        partnerId: appId,
                        status: 1,
                        hospitalType: type,
                    })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    list = list.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                // check case choray datkham
                const isDisabledBookingChoRay = await this.globalSettingService.findByKeyAndRepoName(this.CHO_RAY_DAT_KHAM);
                list = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), list);
                return list
                    .map(item => {
                        if (isDisabledBookingChoRay === 'ON' && item.get('partnerId') === 'choray') {
                            const features = item.get('features').filter(fe => {
                                return fe.type !== this.BOOKING_DATE_TYPE;
                            });
                            return {
                                ...item.toObject(),
                                features,
                            };
                        }
                        if (item.get('partnerId') === 'nhidonghcm') {
                            const features = item.get('features').filter(fe => {
                                return fe.type !== this.BOOKING_DATE_TYPE;
                            });
                            return {
                                ...item.toObject(),
                                features,
                            };
                        }
                        return {
                            ...item.toObject(),
                            // features,
                        };
                    })
                    .map(element => {
                        const { circleLogo } = element;
                        if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                            return {
                                ...element,
                                deliveryMessage: this.deliveryMessage(element.deliveryStatus, globalSetting),
                                ...(circleLogo && { image: circleLogo }),
                            };
                        }
                        return { ...element, ...(circleLogo && { image: circleLogo }) };
                    });
            }
        }
    }

    async danhSachV2ByVersion(appId: string, version: number = 1, type: number, platform?: string): Promise<any> {
        let data: any;
        switch (+version) {
            case 1:
                data = await this.danhSachV2(appId, type);
                data = await this.overrideFeaturesModeBeta(data);
                data = await this.handleFeatureChoRay(data, appId, platform);
                data = await this.excludeFeatureHospital(data, appId, platform);
                data = await this.includeFeatureHospital(data, appId, platform);
                data = await this.appendFeatureToPartner(data, appId, platform);
                return data;
            // return data.map(item => {
            //     item.features = item.features.filter(element => element.status !== false && element.disabled !== true);
            //     return item;
            // });
            case 2:
                data = await this.danhSachV2(appId, type);
                data = await this.overrideFeaturesModeBeta(data);
                data = await this.handleFeatureChoRay(data, appId, platform);
                data = await this.excludeFeatureHospital(data, appId, platform);
                data = await this.includeFeatureHospital(data, appId, platform);
                data = await this.appendFeatureToPartner(data, appId, platform);
                return data;
            default:
                throw new HttpException(`Version phải có giá trị là 1 hoặc 2. Vui lòng kiểm tra lại!`, HttpStatus.BAD_REQUEST);
        }
    }

    async danhSachV3(appId: string, type: number = 1, homeBannerArray = 'false'): Promise<any> {
        this.ignoreMessage = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_MESSAGE, this.repoName);
        const ignoreMessageConfig = new Set(this.ignoreMessage.split(','));
        const trackingDeliveryStatus = new Set([DeliveryStatusEnum.IN_PROGRESS, DeliveryStatusEnum.HOLD]);
        const globalSetting = await this.globalSettingService.findAll();
        const partnersOnlyShowBeta = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA', this.repoName);

        switch (appId) {
            case 'medpro':
            case 'momo':
            case 'zalopay':
            case '1900':
            case 'cskh':
                let list = await this.hospitalModel
                    .find({
                        status: 1,
                        hospitalType: type,
                        partnerId: { $nin: ['umcmono', 'canthozone'] },
                    })
                    // .select({
                    //     'message': true, 'sortOrder': true, 'deliveryStatus': true, '_id': true, 'partnerId': true, 'name': true, 'status': true,
                    //     'image': true, 'features.message': true, 'features.disabled': true, 'features.children': true,
                    //     'features._id': true, 'features.name': true, 'features.image': true, 'features.type': true, 'features.status': true,
                    // })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    list = list.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                list = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), list);
                const mappingResult = list
                    .map(item => {
                        if (ignoreMessageConfig.has(item.partnerId)) {
                            return {
                                ...item.toObject(),
                                message: '',
                            };
                        }
                        return {
                            ...item.toObject(),
                        };
                    })
                    .map(element => {
                        const { circleLogo } = element;
                        if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                            return {
                                ...element,
                                deliveryMessage: this.deliveryMessage(element.deliveryStatus, globalSetting),
                                ...(circleLogo && { image: circleLogo }),
                            };
                        }
                        return { ...element, ...(circleLogo && { image: circleLogo }) };
                    });

                if (this.urlConfigService.setFullFeature118()) {
                    return mappingResult.map(item => {
                        if (item.partnerId === 'medpro') {
                            if (homeBannerArray === 'true') {
                                return {
                                    ...item,
                                    homeBannerAction: (item?.homeBannerAction || []).map(banner => {
                                        return {
                                            ...banner,
                                            imageUrl: banner?.mobileUrl || banner.imageUrl,
                                        };
                                    }),
                                };
                            } else {
                                const fistBanner = first(item?.homeBannerAction || []);
                                return {
                                    ...item,
                                    homeBannerAction: fistBanner,
                                };
                            }
                        }
                        if (this.repoName === 'api-v2-111') {
                            if (item.partnerId === 'umc') {
                                return {
                                    ...item,
                                    features: [
                                        ...item.features,
                                        {
                                            message: '',
                                            disabled: false,
                                            children: [],
                                            _id: '5f80021760d7f743f8006ec2',
                                            name: 'Tư vấn khám bệnh từ xa',
                                            image: 'https://bo-api.medpro.com.vn:5000/static/images/medpro/app/image/tu_van_truc_tuyen_big.png',
                                            priority: 12,
                                            type: 'booking.telemed',
                                            mobileStatus: true,
                                            updatedAt: '2021-08-11T03:41:17.183Z',
                                            createdAt: '2021-08-11T03:41:17.183Z',
                                        },
                                    ],
                                };
                            }
                            if (item.partnerId === 'leloi') {
                                return {
                                    ...item,
                                    features: [
                                        ...item.features,
                                        {
                                            _id: '60ea6cd637a2390220004e63',
                                            type: 'booking.covid',
                                            name: 'Xét nghiệm COVID-19',
                                            image: 'https://api-111.medpro.com.vn:5000/st/feature/covid.svg',
                                            priority: 1,
                                            status: false,
                                            mobileStatus: true,
                                            children: [],
                                            createdAt: '2021-07-08T04:10:53.067Z',
                                            updatedAt: '2021-07-08T04:10:53.067Z',
                                        },
                                    ],
                                };
                            }
                        }
                        return item;
                    });
                }
                return mappingResult;
            case 'medprodanang':
                let dataOriginDN = await this.hospitalModel
                    .find({
                        partnerId: {
                            $in: [
                                'bvphoidanang',
                                'bvcdanang',
                                'bvphcndanang',
                                'bvpsndanang',
                                'bvdndanang',
                                'bvmdanang',
                                'bvlpdanang',
                                'bvyhctdanang',
                                'bvttdanang',
                                'bvdldanang',
                                'bvdkbddanang',
                                'bvubdanang',
                                'ttksbtdanang',
                                'ttpydanang',
                                'ttytqhcdanang',
                                'ttkdytqtdanang',
                                'ttkndanang',
                                'ttdtbdcbdanang',
                                'ccdskhhgddanang',
                                'ttrhmdanang',
                                'ttpchivdanang',
                                'ttcsskssdanang',
                                'ttttgddskdnang',
                                'ttgdykdanang',
                                'ttytqcldanang',
                                'ttytqlcdanang',
                                'ttythhvdanang',
                                'ttytnhsdnang',
                                'ttytqstdanang',
                                'ttytqtkdanang',
                                'ttytdpdanang',
                                'ttccdanang',
                                'ccatvstpdanang',
                            ],
                        },
                        hospitalType: type,
                        status: 1,
                    })
                    // .select({
                    //     'message': true, 'sortOrder': true, 'deliveryStatus': true, '_id': true, 'partnerId': true, 'name': true, 'status': true,
                    //     'image': true, 'features.message': true, 'features.disabled': true, 'features.children': true,
                    //     'features._id': true, 'features.name': true, 'features.image': true, 'features.type': true, 'features.status': true,
                    // })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    dataOriginDN = dataOriginDN.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                let resultDataDN: any = [];
                for (const hospital of dataOriginDN) {
                    const hosObj = hospital.toObject();
                    const features = hosObj.features;
                    if (hospital.partnerId === 'trungvuong') {
                        const findBookingDate = (features || []).filter(item => item.type === this.BOOKING_DATE_TYPE);
                        const resultFeatures = findBookingDate.length === 1 ? findBookingDate : features;
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    } else {
                        let resultFeatures: any = [];
                        for (const feature of features) {
                            const getChildren = get(feature, 'children', []);
                            if (getChildren.length > 0) {
                                resultFeatures = [...resultFeatures, ...getChildren];
                            } else {
                                resultFeatures = [...resultFeatures, feature];
                            }
                        }
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    }
                }
                return resultDataDN.map(element => {
                    const { circleLogo } = element;
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                });
            case 'umcmono':
                return this.getPartnersForUmcMono(globalSetting, type, partnersOnlyShowBeta, trackingDeliveryStatus, ignoreMessageConfig);
            default:
                let listDefault = await this.hospitalModel
                    .find({
                        partnerId: appId,
                        status: 1,
                        hospitalType: type,
                    })
                    // .select({
                    //     'message': true, 'sortOrder': true, 'deliveryStatus': true, '_id': true, 'partnerId': true, 'name': true, 'status': true,
                    //     'image': true, 'features.message': true, 'features.disabled': true, 'features.children': true, 'features._id': true,
                    //     'features.name': true, 'features.image': true, 'features.type': true, 'features.status': true,
                    // })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    listDefault = listDefault.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                const partnerPriority1 = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                return this.utilsService
                    .sortPartnersPriorityFolowGlobalConfig(partnerPriority1.split(','), listDefault)
                    .map(element => {
                        const { circleLogo } = element;
                        return { ...element.toObject(), ...(circleLogo && { image: circleLogo }) };
                    })
                    .map(item => {
                        if (ignoreMessageConfig.has(item.partnerId)) {
                            return {
                                ...item,
                                message: '',
                                disabled: false,
                            };
                        }
                        return {
                            ...item,
                        };
                    });
        }
    }

    async danhSachV3ByVersion(appId: string, version: number = 1, type: number, platform?: string, homeBannerArray?: string): Promise<any> {
        const ignoreDoctorFeatureKey = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_DOCTOR_FEATURE);
        const ignoreDoctorFeature = new Set(ignoreDoctorFeatureKey.split(','));
        let data: any = [];
        switch (+version) {
            case 1:
                data = await this.danhSachV3(appId, type, homeBannerArray);
                data = await this.overrideFeaturesModeBeta(data);
                data = await this.handleFeatureChoRay(data, appId, platform);
                data = await this.overideFeatureModeBetaConfig(data, appId, platform);
                break;
            case 2:
                data = await this.danhSachV3(appId, type, homeBannerArray);
                data = await this.overrideFeaturesModeBeta(data);
                data = await this.handleFeatureChoRay(data, appId, platform);
                data = await this.overideFeatureModeBetaConfig(data, appId, platform);
                break;
            default:
                break;
        }
        // if (ignoreDoctorFeature.has(appId)) {
        //     return data.map(element => {
        //         // for parent feature
        //         const features = element.features.filter(item => item.type.toUpperCase() !== this.BOOKING_DOCTOR_TYPE.toUpperCase());
        //         // for children
        //         const featureTransfer = features.map(item => {
        //             if (item.children.length > 0) {
        //                 item.children = item.children.filter(children => children.type.toUpperCase() !== this.BOOKING_DOCTOR_TYPE.toUpperCase());
        //             }
        //             return item;
        //         });
        //         element.features = featureTransfer;
        //         return element;
        //     });
        // }
        return data;
    }

    async danhSachV4(appId: string, type: number = 1, methodId: string = '', version: number = 1, platform?: string): Promise<any> {
        this.ignoreMessage = await this.globalSettingService.findByKeyAndRepoName(this.IGNORE_MESSAGE, this.repoName);
        const ignoreMessageConfig = new Set(this.ignoreMessage.split(','));
        let listDefault = await this.hospitalModel
            .find({
                partnerId: appId,
                status: 1,
                hospitalType: type,
            })
            .populate('city')
            .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
            .exec();
        const partnersOnlyShowBeta = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA', this.repoName);
        if (!partnersOnlyShowBeta) {
            const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
            const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
            listDefault = listDefault.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
        }
        let data = listDefault
            .map(element => {
                const { circleLogo } = element;
                return { ...element.toObject(), ...(circleLogo && { image: circleLogo }) };
            })
            .map(item => {
                if (ignoreMessageConfig.has(item.partnerId)) {
                    return {
                        ...item,
                        message: '',
                    };
                }
                return item;
            });
        data = await this.overrideFeaturesModeBeta(data);
        data = await this.handleFeatureChoRay(data, appId, platform);
        return data;
    }

    async danhSachV5(query: DanhSachV5QueryDto, headers: HeadersDto): Promise<any> {
        const { appid: appId, locale, platform } = headers;
        const { featureType, methodId = '', type, homeBannerArray } = query;

        const [inProgess, hold, ignoreMessage] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_IN_PROGRESS'),
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_HOLD'),
            this.globalSettingService.findByKeyAndRepoName(this.IGNORE_MESSAGE, this.repoName),
        ]);

        const deliveryListStatus = {
            hold,
            inProgess,
        };

        this.ignoreMessage = ignoreMessage;
        const ignoreMessageConfig = new Set(this.ignoreMessage.split(','));
        const trackingDeliveryStatus = new Set([1, 2]);
        const partnersOnlyShowBeta = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA', this.repoName);
        let partners: string[];

        // if (methodId.toLocaleLowerCase() === 'momo') {
        //     partners = await this.getPartnersPaymentMethodMomo(appId);
        // }

        const queryFeatureByPlatform = this.utilService.getPlatformType(platform) === 'mobile' ? { mobileStatus: true } : { status: true };

        switch (appId) {
            case 'medpro':
            case 'momo':
            case 'zalopay':
            case '1900':
            case 'cskh': {
                const query: any = {
                    status: 1,
                    // hospitalType: type,
                    ...(partners?.length > 0 && { partnerId: { $in: partners } }),
                    partnerId: { $nin: ['medpro', 'umcmono', 'canthozone'] },
                };

                let projection: any = {};

                if (type || type === 0) {
                    query.hospitalType = type;
                }

                if (featureType) {
                    query.features = { $elemMatch: { type: featureType === 'booking.date' ? /^booking./ : featureType, ...queryFeatureByPlatform } };
                    projection.features = { $elemMatch: { type: featureType, ...queryFeatureByPlatform } };
                }

                // console.log('query: ', query);

                let list = await this.hospitalModel
                    .find(query)
                    .select({
                        name: true,
                        image: true,
                        circleLogo: true,
                        address: true,
                        partnerId: true,
                        city_id: true,
                        features: true,
                        deliveryStatus: true,
                        ...projection,
                    })
                    .populate('city', { id: true, name: true })
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();

                // console.log('list.length: ', list.length);

                this.logger.log(`query danh sách : ${moment().toDate()}`);
                // list = list.filter(item => item.partnerId !== 'umcmono' && item.status === 1 && item.hospitalType === type);
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    list = list.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }

                const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                list = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), list);
                let mappingResult = list.map(item => {
                    if (ignoreMessageConfig.has(item.partnerId)) {
                        return {
                            ...item.toObject(),
                            message: '',
                            disabled: false,
                        };
                    }
                    return {
                        ...item.toObject(),
                    };
                });

                mappingResult = mappingResult.map(element => {
                    const { circleLogo } = element;
                    if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                        const deliveryMessage = this.deliveryMessageOptimize(element.deliveryStatus, deliveryListStatus);
                        return {
                            ...element,
                            deliveryMessage,
                            ...(circleLogo && { image: circleLogo }),
                        };
                    }
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                });

                return mappingResult.map(item => {
                    if (item.partnerId === 'medpro') {
                        if (homeBannerArray) {
                            const infoTransform = {
                                ...item,
                                homeBannerAction: (item?.homeBannerAction || []).map(banner => {
                                    return {
                                        ...banner,
                                        imageUrl: banner?.mobileUrl || banner.imageUrl,
                                    };
                                }),
                            };

                            const checkModile = new Set(['android', 'ios']);
                            if (checkModile.has(platform)) {
                                const homeBannerActionFilter = infoTransform?.homeBannerAction || [];
                                const findBannerWebb = homeBannerActionFilter.filter(item2 => (item2?.platform || '') === 'mobile');
                                return {
                                    ...infoTransform,
                                    homeBannerAction: findBannerWebb,
                                };
                            }
                            return infoTransform;
                        } else {
                            const fistBanner = first(item?.homeBannerAction || []);
                            return {
                                ...item,
                                homeBannerAction: fistBanner,
                            };
                        }
                    }
                    return item;
                });
            }
            case 'medprodanang':
                this.logger.log(` medprodanang : ${moment().toDate()}`);
                let dataOriginDN = await this.hospitalModel
                    .find({
                        partnerId: {
                            $in: [
                                'bvphoidanang',
                                'bvcdanang',
                                'bvphcndanang',
                                'bvpsndanang',
                                'bvdndanang',
                                'bvmdanang',
                                'bvlpdanang',
                                'bvyhctdanang',
                                'bvttdanang',
                                'bvdldanang',
                                'bvdkbddanang',
                                'bvubdanang',
                                'ttksbtdanang',
                                'ttpydanang',
                                'ttytqhcdanang',
                                'ttkdytqtdanang',
                                'ttkndanang',
                                'ttdtbdcbdanang',
                                'ccdskhhgddanang',
                                'ttrhmdanang',
                                'ttpchivdanang',
                                'ttcsskssdanang',
                                'ttttgddskdnang',
                                'ttgdykdanang',
                                'ttytqcldanang',
                                'ttytqlcdanang',
                                'ttythhvdanang',
                                'ttytnhsdnang',
                                'ttytqstdanang',
                                'ttytqtkdanang',
                                'ttytdpdanang',
                                'ttccdanang',
                                'ccatvstpdanang',
                            ],
                        },
                        hospitalType: type,
                        status: 1,
                    })
                    // .select({
                    //     'message': true, 'sortOrder': true, 'deliveryStatus': true, '_id': true, 'partnerId': true, 'name': true, 'status': true,
                    //     'image': true, 'features.message': true, 'features.disabled': true, 'features.children': true,
                    //     'features._id': true, 'features.name': true, 'features.image': true, 'features.type': true, 'features.status': true,
                    // })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    dataOriginDN = dataOriginDN.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                let resultDataDN: any = [];
                for (const hospital of dataOriginDN) {
                    const hosObj = hospital.toObject();
                    const features = hosObj.features;
                    if (hospital.partnerId === 'trungvuong') {
                        const findBookingDate = (features || []).filter(item => item.type === this.BOOKING_DATE_TYPE);
                        const resultFeatures = findBookingDate.length === 1 ? findBookingDate : features;
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    } else {
                        let resultFeatures: any = [];
                        for (const feature of features) {
                            const getChildren = get(feature, 'children', []);
                            if (getChildren.length > 0) {
                                resultFeatures = [...resultFeatures, ...getChildren];
                            } else {
                                resultFeatures = [...resultFeatures, feature];
                            }
                        }
                        hosObj.features = resultFeatures;
                        resultDataDN = [...resultDataDN, hosObj];
                    }
                }
                return resultDataDN.map(element => {
                    const { circleLogo } = element;
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                });
            case 'umcmono':
                return this.getPartnersForUmcMonoOptimize(
                    type,
                    partnersOnlyShowBeta,
                    trackingDeliveryStatus,
                    ignoreMessageConfig,
                    deliveryListStatus,
                );
            default:
                this.logger.log(`default 1 : ${moment().toDate()}`);
                let listDefault = await this.hospitalModel
                    .find({
                        partnerId: appId,
                        status: 1,
                        hospitalType: type,
                        ...(partners?.length > 0 && { partnerId: { $in: partners } }),
                    })
                    .populate('city')
                    .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                    .exec();
                if (!partnersOnlyShowBeta) {
                    const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
                    const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
                    listDefault = listDefault.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
                }
                const partnerPriority1 = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
                return this.utilsService
                    .sortPartnersPriorityFolowGlobalConfig(partnerPriority1.split(','), listDefault)
                    .map(element => {
                        const { circleLogo } = element;
                        return { ...element.toObject(), ...(circleLogo && { image: circleLogo }) };
                    })
                    .map(item => {
                        if (ignoreMessageConfig.has(item.partnerId)) {
                            return {
                                ...item,
                                message: '',
                                disabled: false,
                            };
                        }
                        return {
                            ...item,
                        };
                    });
        }
    }

    deliveryMessageOptimize(deliveryStatus: number, deliveryListStatus: { hold: string; inProgess: string }) {
        switch (deliveryStatus) {
            case DeliveryStatusEnum.IN_PROGRESS:
                return deliveryListStatus.inProgess;
            case DeliveryStatusEnum.HOLD:
                return deliveryListStatus.hold;
            default:
                return '';
        }
    }

    async getPartnersForUmcMonoOptimize(
        type?: number,
        partnersOnlyShowBeta?: string,
        trackingDeliveryStatus?: Set<DeliveryStatusEnum>,
        ignoreMessageConfig?: Set<string>,
        deliveryMessageList?: any,
    ): Promise<any> {
        let dataOrigin = await this.hospitalModel
            .find({
                partnerId: { $in: ['umc', 'umc2'] },
                status: 1,
                hospitalType: type,
            })
            .populate('city')
            .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
            .exec();
        if (!partnersOnlyShowBeta) {
            const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
            const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
            dataOrigin = dataOrigin.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
        }
        const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
        dataOrigin = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), dataOrigin);
        let hospitalFeatures: any = [];
        for (const hospital of dataOrigin) {
            const hosObj = hospital.toObject();
            const features = hosObj.features;
            let resultFeatures: any = [];
            for (const feature of features) {
                const getChildren = get(feature, 'children', []);
                if (getChildren.length > 0) {
                    resultFeatures = [...resultFeatures, ...getChildren];
                } else {
                    resultFeatures = [...resultFeatures, feature];
                }
            }
            hosObj.features = resultFeatures;
            hospitalFeatures = [...hospitalFeatures, hosObj];
        }
        if (ignoreMessageConfig) {
            hospitalFeatures = hospitalFeatures.map((item: any) => {
                if (ignoreMessageConfig.has(item.partnerId)) {
                    return {
                        ...item,
                        message: '',
                        disabled: false,
                    };
                }
                return item;
            });
        }
        if (trackingDeliveryStatus) {
            hospitalFeatures = await Promise.all(
                hospitalFeatures.map(async (element: any) => {
                    const { circleLogo } = element;
                    const deliveryMessage = this.deliveryMessageOptimize(element.deliveryStatus, deliveryMessageList);
                    if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                        return {
                            ...element,
                            deliveryMessage,
                            ...(circleLogo && { image: circleLogo }),
                        };
                    }
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                }),
            );
        }
        return hospitalFeatures;
    }

    async list(): Promise<any> {
        const trackingDeliveryStatus = new Set([DeliveryStatusEnum.IN_PROGRESS, DeliveryStatusEnum.HOLD]);
        const globalSetting = await this.globalSettingService.findAll();
        try {
            const res = await this.hospitalModel
                .find({})
                .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                .exec();
            return res.map(element => {
                if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                    return {
                        ...element.toObject(),
                        deliveryMessage: this.deliveryMessage(element.deliveryStatus, globalSetting),
                    };
                }
                return element.toObject();
            });
        } catch (error) {
            throw error;
        }
    }

    async findOne(id: string): Promise<any> {
        const hospitals = await this.hospitalModel
            .find({ _id: id })
            .limit(1)
            .populate('subjects')
            .exec();
        return first(hospitals);
    }

    async getPartnerInfo(partnerId: string, type: number = 1, slug: string = ''): Promise<any> {
        const trackingDeliveryStatus = new Set([DeliveryStatusEnum.IN_PROGRESS, DeliveryStatusEnum.HOLD]);
        const globalSetting = await this.globalSettingService.findAll();

        let condition = {};
        if (partnerId) {
            condition = { partnerId, hospitalType: type };
        } else {
            condition = { slug };
        }

        try {
            let info: any = {};
            const res = await this.hospitalModel.findOne(condition).exec();

            if (!res) {
                throw new HttpException({ message: 'Không tìm thấy thông tin cơ sở ý tế!' }, 404);
            }

            if (trackingDeliveryStatus.has(res.deliveryStatus)) {
                info = { ...res.toObject(), deliveryMessage: this.deliveryMessage(res.deliveryStatus, globalSetting) };
            } else {
                info = res.toObject();
            }

            if (partnerId === 'digimed') {
                const digimedNameOverride = await this.globalSettingService.findByKeyAndRepoName('DIGIMED_OVERRIDE_NAME');
                info.extra = {
                    doctor: {
                        name: digimedNameOverride,
                    },
                };
            }

            if (partnerId === 'medpro') {
                /* Tiến hành xử lý theo platform */
                const homeBannerAction = info?.homeBannerAction || [];
                const findBannerWebb = homeBannerAction.filter(item => (item?.platform || '') === 'web');
                return {
                    ...info,
                    homeBannerAction: findBannerWebb,
                };
            }
            let objImpl: any = {};
            let popup = null;
            if (REPO_NAME_BETA.includes(this.cacheService.repoName)) {

                if (['medlatecbp', 'binhthanhhcm'].includes(res.partnerId)) {
                    
                    if(res.partnerId === 'medlatecbp'){
                        popup =  {
                            "title": "Ưu đãi hoàn tiền",
                            "content": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Melatec Bình Phước. khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>",
                            "html": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Melatec Bình Phước. khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>"
                        };
                    }

                    if(res.partnerId === 'binhthanhhcm'){
                        popup = {
                            "title": "Ưu đãi hoàn tiền",
                            "content": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Quận Bình Thạnh khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>",
                            "html": "<div style='font-size:16px;line-height:20px;color:#003553'><p>Hoàn tiền <b>3%</b> trên tổng hoá đơn thanh toán (không bao gồm chi phí thuốc hoặc / và vật tư y tế) tại Bệnh viện Quận Bình Thạnh khi đặt lịch qua Medpro.</p><p>Quý khách chỉ cần bổ sung thông tin cơ bản và tài khoản nhận ưu đãi hoàn tiền sau khi hoàn tất dịch vụ y tế. Medpro giúp quý khách đặt lịch nhanh chóng, chủ động thời gian mà không cần chờ đợi xếp hàng.</p><p>➡️ Chi tiết, xin liên hệ 19002115 hoặc xem <a href='https://medpro.vn/tin-tuc/dieu-kien-va-dieu-khoan-hoan-tien' target='_self' style='color:#003553'><b>Tại đây</b></a>.</p></div>"
                        };
                    }
                    objImpl = {
                        ...objImpl,
                        isCashBack: true,
                        popup
                    }
                }
            }


            return {
                ...info,
                circleLogo: info.circleLogo || info.image,
                ...objImpl
            };
        } catch (error) {
            throw error;
        }
    }

    async updateNotifPartner(data: UpdateNotifPartnerDTO): Promise<any> {
        return this.hospitalModel
            .findOneAndUpdate({ partnerId: data.partnerId }, { notifAppId: data.notifAppId, notifApiKey: data.notifApiKey }, { new: true })
            .exec();
    }

    async resetNotifPartner(): Promise<any> {
        return this.hospitalModel.updateMany({}, { notifApiKey: '', notifAppId: '' }).exec();
    }

    async updateFeatures(partnerId: string): Promise<any> {
        const hospital = await this.hospitalModel.findOne({ partnerId }, { features: true }).exec();
        if (hospital.features.length > 0) {
            const features: IFeature[] = hospital.features;
            const listFeature: IFeature[] = [];
            for await (const feature of features) {
                const featureInfo = await this.featureModel.findById({ _id: feature._id }).exec();
                listFeature.push(featureInfo);
            }
            hospital.features = listFeature;
            await hospital.save();
        }
        return this.hospitalModel.findOne({ partnerId }, { features: true }).exec();
    }

    async updateMessage(data: UpdateMessagePartnerDTO): Promise<any> {
        return await this.hospitalModel.findOneAndUpdate({ partnerId: data.partnerId }, { message: data.message }, { new: true }).exec();
    }

    async updateBanner(data: UpdateBannerDTO): Promise<any> {
        return await this.hospitalModel.findOneAndUpdate({ partnerId: data.partnerId }, { banner: data.banner }, { new: true }).exec();
    }

    async seedPushNotif(): Promise<any> {
        const partnersConfig = [
            {
                appId: 'trungvuong',
                notifAppId: '7cbfd5ec-108a-42c7-8c65-35eff524de38',
                notifApiKey: 'ZjJjNDhhNjAtOTllZi00Y2U3LTljZGMtNDIwN2E4OTA5MGEz',
            },
            {
                appId: 'dalieuhcm',
                notifAppId: '2a5be6bb-8a5b-4920-8379-e0e96f533cb8',
                notifApiKey: 'OTI5ZGI0OGMtZjkxMy00YTk1LTgxZjItN2JkNzJhNDVkMTg3',
            },
            {
                appId: 'ctchhcm',
                notifAppId: '7fb108e8-8c11-4621-beb7-1e772fdb1b3c',
                notifApiKey: 'NmE1MTAyOGEtN2MyMS00ZDViLThiMTctMGE3ODMwNWNlMzEz',
            },
            {
                appId: 'umc',
                notifAppId: '8f39ea58-cd31-4262-9f01-87052405e573',
                notifApiKey: 'YjkwMmViNDctZmU2NC00NTU1LWE5NmUtODFhMDc5YTgyOTgx',
            },
            {
                appId: 'thuduc',
                notifAppId: '834a8383-f468-4492-b97e-4674f48bdd22',
                notifApiKey: 'NzczZGE4NGItMGMxYi00ZDE0LWEzZWYtMmQ2MGM5NmFmNjZk',
            },
            {
                appId: 'nhidong1',
                notifAppId: '9e39f48c-382b-4a1f-a94b-94113c580b4b',
                notifApiKey: 'NmZkZGY3NTAtZTgwNC00OGQxLWEyZmMtYjhkYzMyMDcxOTkz',
            },
            {
                appId: 'medpro',
                notifAppId: '4553d603-4a56-4e56-b138-c0c0515101f6',
                notifApiKey: 'YzdjMGNlOWUtZTc5Mi00NmU0LThjNWEtMjY4YmIxMmUxOGIy',
            },
            {
                appId: 'minhanh',
                notifAppId: '74ad6397-7aaa-4f40-8c68-a19f6fb17112',
                notifApiKey: 'OTk0YWFiOTMtM2RkZC00ODE0LWEwNzctNzVkNDJmY2FmZTQz',
            },
            {
                appId: 'leloi',
                notifAppId: '222899f8-7246-4c17-afd5-a9ed4bd61bb7',
                notifApiKey: 'MmNkYjY0M2ItOWU3MS00YzBlLWI5NDMtNzA1YjNlZWE3OGU3',
            },
            {
                appId: 'dkkvangiang',
                notifAppId: '204022cf-116e-489a-9953-82d5d813d64a',
                notifApiKey: 'NmFhZGNkYWUtOTJmZS00NGE4LTlkMTktMWRhM2Q0MjJjNWE3',
            },
            // {
            //     appId: '',
            //     notifAppId: '',
            //     notifApiKey: '',
            // },
        ];
        for await (const partner of partnersConfig) {
            await this.hospitalModel
                .findOneAndUpdate({ partnerId: partner.appId }, { notifAppId: partner.notifAppId, notifApiKey: partner.notifApiKey })
                .exec();
        }
    }

    async updateOne(updateHospitalDTO: UpdateHospitalDTO): Promise<any> {
        // try {
        //     const { id, ...updateInfo } = updateHospitalDTO;
        //     const info = await this.hospitalModel
        //         .findByIdAndUpdate(updateHospitalDTO.id, { ...updateInfo }, { new: true })
        //         .exec();
        //     const { subjects, ...rest } = info.toJSON();
        //     return rest;
        // } catch (error) {
        //     throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        // }
    }

    async updateStaticResource(fromData: UpdateStaticResourceDTO): Promise<any> {
        const { partnerId, ...updateData } = fromData;
        try {
            const hospitalInfo = await this.hospitalModel.findOneAndUpdate({ partnerId }, { ...updateData }, { new: true }).exec();
            if (hospitalInfo) {
                return hospitalInfo.toJSON();
            } else {
                throw new HttpException('Cập nhật không thành công. Vui lòng kiểm tra lại thông tin.', HttpStatus.FORBIDDEN);
            }
        } catch (err) {
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.BAD_REQUEST);
        }
    }

    async isExistAppId(appId: string): Promise<boolean> {
        const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
        if (appIdInfo) {
            return true;
        }
        return false;
    }

    async getListHospitalOfAppId(appId: string): Promise<any> {
        const listPartner: any = [];
        const appIdInfo = await this.appIdModel.find({ appId }).exec();
        for await (const appIdData of appIdInfo) {
            listPartner.push(appIdData.get('detail.partnerId'));
        }
        const list = listPartner.splice(',');
        return list[0];
    }

    async findAllHospitalByAppId(appId: string): Promise<any> {
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        const listPartner = await this.getListHospitalOfAppId(appId);
        const dataOrigin = await this.hospitalModel
            .find({
                partnerId: { $in: listPartner },
                status: 1,
            })
            .populate('city')
            .sort({ createdAt: 'asc' })
            .exec();
        let resultData: any = [];
        for (const hospital of dataOrigin) {
            const hosObj = hospital.toObject();
            const features = hosObj.features;
            if (hospital.partnerId === 'trungvuong') {
                const findBookingDate = (features || []).filter(item => item.type === this.BOOKING_DATE_TYPE);
                const resultFeatures = findBookingDate.length === 1 ? findBookingDate : features;
                hosObj.features = resultFeatures;
                resultData = [...resultData, hosObj];
            } else {
                let resultFeatures: any = [];
                for (const feature of features) {
                    const getChildren = get(feature, 'children', []);
                    if (getChildren.length > 0) {
                        resultFeatures = [...resultFeatures, ...getChildren];
                    } else {
                        resultFeatures = [...resultFeatures, feature];
                    }
                }
                hosObj.features = resultFeatures;
                resultData = [...resultData, hosObj];
            }
        }
        return resultData;
    }

    // To update data for delivery case
    async feedDataHospital(): Promise<any> {
        try {
            await Promise.all([
                this.hospitalModel.updateMany({ message: { $nin: [null, ''] } }, { deliveryStatus: DeliveryStatusEnum.IN_PROGRESS }).exec(),
                this.hospitalModel.updateMany({ message: { $in: [null, ''] } }, { deliveryStatus: DeliveryStatusEnum.DONE }).exec(),
            ]);
        } catch (error) {
            throw error;
        }
    }

    deliveryMessage(deliveryStatus: number, globalSetting: any): string {
        enum DeliveryType {
            IN_PROGRESS = 'DELIVERY_STATUS_IN_PROGRESS',
            HOLD = 'DELIVERY_STATUS_HOLD',
            SORT_PARTNER_LIST = 'SORT_PARTNER_LIST',
        }

        switch (deliveryStatus) {
            case DeliveryStatusEnum.IN_PROGRESS:
                const inProgress = find(globalSetting, { key: DeliveryType.IN_PROGRESS });
                return get(inProgress, 'value', '');
            case DeliveryStatusEnum.HOLD:
                const hold = find(globalSetting, { key: DeliveryType.HOLD });
                return get(hold, 'value', '');
            case DeliveryStatusEnum.SORT_PARTNER_LIST:
                const sortPartnerList = find(globalSetting, { key: DeliveryType.SORT_PARTNER_LIST });
                return get(sortPartnerList, 'value', '');
            default:
                return '';
        }
    }

    async seedCircleLogo(): Promise<any> {
        try {
            const hospitals = await this.hospitalModel
                .find({})
                .lean()
                .exec();
            await Promise.all([
                hospitals.map((element: IHospital) => {
                    if (!element?.circleLogo) {
                        const circleLogo = `${this.CIRCLE_LOGO_URL}/${element.circleLogo}/web/logo.png`;
                        this.hospitalModel.findOneAndUpdate({ partnerId: element.partnerId }, { circleLogo }).exec();
                    }
                }),
            ]);
        } catch (error) {
            throw error;
        }
    }

    async seedHospitalType(): Promise<any> {
        try {
            await this.hospitalModel.updateMany({ hospitalType: { $eq: null } }, { hospitalType: 1 }).exec();
        } catch (error) {
            throw error;
        }
    }

    async verifyCskhToken(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        return this.patientMongoService.verifyCskhToken(cskhToken);
    }

    async verifyUserTokenModeCskh(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        return this.patientMongoService.verifyUserTokenModeCskh(cskhToken);
    }

    async getHospitalHaveHisConnector(appId: string, type: number): Promise<any> {
        const listPartnerHaveHis = await this.partnerConfigService.getPartnerHaveHisConnector();
        // set
        const haveHis = new Set([...listPartnerHaveHis]);
        // get data
        const hospitalData = await this.danhSachByVersion(appId, type);

        return hospitalData.filter((element: IHospital) => haveHis.has(element.partnerId));
    }

    async handlePartnersForCskh(partners: any, cskhInfo: CSKHTokenVerifyDTO): Promise<any> {
        let overridePartnerCskh = partners;
        const user = await this.userModel.findById(cskhInfo.cskhUserId).populate({ path: 'partners', select: { partnerId: true } });
        if (user.isCS && user.partners && user.partners.length) {
            const userObj = user.toObject();
            const setPartnerCskh = new Set(userObj.partners.map(partnerObj => partnerObj.partnerId));
            overridePartnerCskh = overridePartnerCskh.filter(partner => setPartnerCskh.has(partner.partnerId));
        }
        return overridePartnerCskh;
    }

    async overrideFeaturesModeBeta(hospitals: any): Promise<any> {
        const setRepoName = new Set(['api-v2-111', 'api-v2-beta']);
        if (setRepoName.has(this.repoName)) {
            return hospitals.map(hospital => {
                if (has(hospital, 'features')) {
                    hospital.features = hospital.features.map(feature => {
                        feature.disabled = false;
                        feature.message = '';
                        return feature;
                    });
                }
                hospital.message = '';
                return hospital;
            });
        } else {
            return hospitals;
        }
    }

    async getPartnersForUmcMono(
        globalSettings: any,
        type?: number,
        partnersOnlyShowBeta?: string,
        trackingDeliveryStatus?: Set<DeliveryStatusEnum>,
        ignoreMessageConfig?: Set<string>,
    ): Promise<any> {
        let filterSql: any = ['api-v2-beta', 'api-v2-111'].includes(this.repoName)
            ? { partnerId: { $in: ['umc2', 'umc3'] }, hospitalType: type }
            : { partnerId: { $in: ['umc2', 'umc3'] }, status: 1, hospitalType: type };
        let dataOrigin = await this.hospitalModel
            .find(filterSql)
            .populate('city')
            .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
            .exec();
        if (!partnersOnlyShowBeta) {
            const partnerFilter = await this.globalSettingService.findByKeyAndRepoName('PARTNER_ONLY_SHOW_BETA');
            const setPartnersOnlyShowBeta = new Set(partnerFilter.split(','));
            dataOrigin = dataOrigin.filter(item => !setPartnersOnlyShowBeta.has(item.partnerId));
        }
        const partnerPriority = await this.globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST');
        dataOrigin = this.utilsService.sortPartnersPriorityFolowGlobalConfig(partnerPriority.split(','), dataOrigin);
        let hospitalFeatures: any = [];
        for (const hospital of dataOrigin) {
            const hosObj = hospital.toObject();
            const features = hosObj.features;
            let resultFeatures: any = [];
            for (const feature of features) {
                const getChildren = get(feature, 'children', []);
                if (getChildren.length > 0) {
                    resultFeatures = [...resultFeatures, ...getChildren];
                } else {
                    resultFeatures = [...resultFeatures, feature];
                }
            }
            hosObj.features = resultFeatures;
            hospitalFeatures = [...hospitalFeatures, hosObj];
        }
        if (ignoreMessageConfig) {
            hospitalFeatures = hospitalFeatures.map((item: any) => {
                if (ignoreMessageConfig.has(item.partnerId)) {
                    return {
                        ...item,
                        message: '',
                        disabled: false,
                    };
                }
                return item;
            });
        }
        if (trackingDeliveryStatus) {
            hospitalFeatures = await Promise.all(
                hospitalFeatures.map(async (element: any) => {
                    const { circleLogo } = element;
                    const deliveryMessage = this.deliveryMessage(element.deliveryStatus, globalSettings);
                    if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                        return {
                            ...element,
                            deliveryMessage,
                            ...(circleLogo && { image: circleLogo }),
                        };
                    }
                    return { ...element, ...(circleLogo && { image: circleLogo }) };
                }),
            );
        }
        return hospitalFeatures;
    }

    async handleFeatureChoRay(hospitals: any, appid: string, platform: string): Promise<any> {
        const configRepoGlobal = await this.globalSettingService.findByKeyAndRepoName('REPO_CONFIG_BOOKING_CHORAY');
        const configRepos = new Set(configRepoGlobal ? configRepoGlobal.split(',') : []);
        if (hospitals && hospitals.length > 0) {
            hospitals = hospitals.map(hospital => {
                if (
                    appid === 'choray' &&
                    hospital.partnerId === 'choray' &&
                    configRepos.has(this.repoName) &&
                    !new Set(['web', 'pc']).has(platform)
                ) {
                    hospital.features = hospital.features.filter(feature => feature.type !== 'booking.date');
                }
                return hospital;
            });
        }
        return hospitals;
    }

    async clinicGAReport(): Promise<unknown> {
        try {
            const organization = this.urlConfigService.getGoogleAnalyticsOrganization;
            const token = this.urlConfigService.getGoogleAnalyticsToken;
            const url = `${this.urlConfigService.getGoogleAnalyticsUrl}/report?type=CLINIC&organization=${organization}&token=${token}`;

            return (await this.httpService.get(url).toPromise()).data;
        } catch (error) {
            this.logger.error(`Error when exec clinicGAReport. Cause: ${error.message}`);

            const message = error?.response?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!';
            const statusCode = error?.response?.status || 400;
            throw new HttpException(message, statusCode);
        }
    }

    async excludeFeatureHospital(hospitals: any, appId: string, platform: string): Promise<void> {
        const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_EXCLUDE_FEATURE_APP');
        const configObj = JSON.parse(config);
        const appConfigs = new Set(configObj.app);
        const featuresConfigs = new Set(configObj.features);
        if (appConfigs.has(appId)) {
            return hospitals.map(hospital => {
                hospital.features = hospital?.features.filter(feature => !featuresConfigs.has(feature.type));
                return hospital;
            });
        } else {
            return hospitals;
        }
    }

    async includeFeatureHospital(hospitals: any, appId: string, platform: string): Promise<void> {
        return hospitals;
        // const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_INCLUDE_FEATURE_APP');
        // const configObj = JSON.parse(config);
        // const appConfigs = new Set(configObj.app);
        // const featuresConfigs = new Set(configObj.features);
        // if (appConfigs.has(appId)) {
        //     return hospitals.map(hospital => {
        //         hospital.features = hospital?.features.filter(feature => !featuresConfigs.has(feature.type));
        //         return hospital;
        //     });
        // } else {
        //     return hospitals;
        // }
    }

    async overideFeatureModeBetaConfig(hospitals: any, appid: string, platform: string): Promise<any> {
        const [configProductJson, configsJson] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('CONFIG_FEATURE_SHOW_IN_PRODUCTION'),
            this.globalSettingService.findByKeyAndRepoName('CONFIG_FEATURE_SHOW_IN_BETA'),
        ]);
        if (configsJson) {
            let configs = JSON.parse(configsJson);
            const configProducts = JSON.parse(configProductJson);
            configs = [...configs, ...configProducts];
            hospitals = this.handleFeatureOverride(hospitals, configs, appid, platform);
        }
        return hospitals;
    }

    handleFeatureOverride(hospitals: any, configs: any, appid: string, platform: string): any {
        const groupPartners = groupBy(hospitals, 'partnerId');
        let partnerIdConfigs: string[] = [];
        for (const config of configs) {
            partnerIdConfigs = [...partnerIdConfigs, ...config.partners];
        }
        const partnerIdConfigsSet = new Set(partnerIdConfigs);
        partnerIdConfigs = [...partnerIdConfigsSet];
        if (partnerIdConfigs.length !== 0) {
            let overrideHospitals: any[] = [];
            for (const partnerIdConfig of partnerIdConfigs) {
                const hospital = first(groupPartners[partnerIdConfig]);
                if (hospital) {
                    for (const config of configs) {
                        const repoSet = new Set(config?.repo || []);
                        const partnerSet = new Set(config?.partners || []);
                        const appSet = new Set(config?.appids || []);
                        const platformSet = new Set(config?.platforms || []);
                        const featuresType = new Set(hospital?.features.map(feature => feature.type));
                        if (repoSet.has(this.repoName) && partnerSet.has(hospital.partnerId) && appSet.has(appid) && platformSet.has(platform)) {
                            if (featuresType.has(config?.feature.type)) {
                                hospital.features = hospital?.features.map(feature => {
                                    if (feature.type === config?.feature.type && feature.status === false) {
                                        feature = {
                                            ...feature,
                                            ...config?.feature,
                                        };
                                    }
                                    return feature;
                                });
                            } else {
                                hospital.features = [...hospital?.features, config?.feature];
                            }
                        }
                    }

                    overrideHospitals = [...overrideHospitals, hospital];
                }
            }

            const overrideHospitalsGroup = groupBy(overrideHospitals, 'partnerId');

            if (overrideHospitals) {
                return hospitals.map(hospital => {
                    if (partnerIdConfigsSet.has(hospital?.partnerId)) {
                        return first(overrideHospitalsGroup[hospital?.partnerId]);
                    } else {
                        return hospital;
                    }
                });
            } else {
                return hospitals;
            }
        } else {
            return hospitals;
        }
    }

    async appendFeatureToPartner(hospitals: any, appId?: string, platform: string = 'pc'): Promise<any> {
        platform = new Set(['android', 'ios']).has(platform) ? 'mobile' : 'web';
        const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_FEATURE_APPEND_TO_PARTNER');
        let configObj: any;
        try {
            configObj = JSON.parse(config);
        } catch (error) {
            return hospitals;
        }
        if (`ON` !== configObj?.env) {
            return hospitals;
        }
        return hospitals.map(hospital => {
            const key = `${appId || hospital?.partnerId}::${hospital?.partnerId}::${this.repoConfigService.getRepoName()}::${platform}`;
            // console.log('key', key);
            const featureConfigs = get(configObj, key, []);
            // console.log('featureConfigs', featureConfigs);
            if (featureConfigs.length <= 0) {
                return hospital;
            }
            for (const featureConfig of featureConfigs) {
                const existFeature = hospital?.features.find(feature => `${feature?.type}` === `${featureConfig?.type}`);
                if (existFeature) {
                    // console.log('existFeature', existFeature, featureConfig?.type);
                    let overrideFeature = hospital?.features.filter(feature => `${feature?.type}` !== `${featureConfig?.type}`);
                    const newFeature = { ...existFeature, ...featureConfig };
                    overrideFeature = [...overrideFeature, newFeature];
                    hospital.features = overrideFeature;
                } else {
                    const overrideFeature = [...hospital?.features, featureConfig];
                    hospital.features = overrideFeature;
                }
            }
            // console.log('hospital.features', hospital.features);
            return hospital;
        });
    }

    async getHospitalExtraInfo(partnerId: string, locale = 'vi') {
        const one = await this.hospitalExtraInfoModel.findOne({ partnerId }).exec();
        if (!one) {
            throw new HttpException({ message: 'Không tìm thấy thông tin HospitalExtra', data: { partnerId } }, HttpStatus.NOT_FOUND);
        }

        const { features, supportMethods, backgrounds, ...rest } = one.toObject();
        return {
            ...rest,
            features: filter(features, { locale }),
            supportMethods: filter(supportMethods, { locale }),
            backgrounds: filter(backgrounds, { locale }),
        };
    }

    async danhSachV6(type: number): Promise<any> {
        let conditions: any = {
            status: 1,
            partnerId: { $nin: ['medpro', 'umcmono', 'digimed', 'canthozone'] },
        };
        if (type) {
            conditions.newHospitalTypes = type;
        }

        const cacheKey = `hospital-sorting:danh-sach-v6:${this.utilService.sortedStringifyUrl({ type })}:${this.repoName}`;
        let cacheData = await this.cacheService.get(cacheKey);
        if (cacheData) {
            cacheData = await this.overrideFeaturesModeBeta(cacheData);
            return cacheData;
        }

        let list: any[] = await this.hospitalModel
            .find(conditions)
            .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
            .select({
                name: 1,
                address: 1,
                hotlinePartner: 1,
                image: 1,
                newHospitalType: 1,
                newHospitalTypes: 1,
                city_id: 1,
                partnerId: 1,
                slug: 1,
                googleMap: 1,
                deliveryStatus: 1,
                message: 1,
                sponsored: 1,
                listingPackagePaid:1,
                isCashBack: 1,
                packageImgDefault: 1,
            })
            .lean();

        const [inProgess, hold, fees] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_IN_PROGRESS'),
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_HOLD'),
            this.homePageService.getPartnerCashBackPopupList()
        ]);

        const deliveryListStatus = {
            hold,
            inProgess,
        };

        const trackingDeliveryStatus = new Set([1, 2]);
        let hospitalList = [];
        list.forEach(l => hospitalList.push(l.partnerId));
        hospitalList = await this.hospitalDescriptionModel.find().lean();
        let hospitalListObj: Object = {};
        hospitalList.forEach(h => {
            if (!!h?.hospitalId) {
                hospitalListObj[h.hospitalId] = h;
            }
        });
        const hospitalDescriptions = await this.hospitalDescriptionModel
            .find({}, { hospitalId: 1, showPartnerInfo: 1, banner: 1, mapView: 1, rating: 1 })
            .lean();
        let hospitalDescriptionObj: Object = {};
        hospitalDescriptions.reduce((res, h) => {
            if (h.hospitalId) {
                hospitalDescriptionObj[h.hospitalId] = h;
            }
            return res;
        }, {});

        list = list.map(element => {
            const hospitalDescription = hospitalDescriptionObj[element.slug];
            const { circleLogo } = element;

            let res = { ...element, ...(circleLogo && { image: circleLogo }) };
            if (trackingDeliveryStatus.has(element.deliveryStatus)) {
                const deliveryMessage = this.deliveryMessageOptimize(element.deliveryStatus, deliveryListStatus);
                res = {
                    ...element,
                    deliveryMessage,
                    ...(circleLogo && { image: circleLogo }),
                };
            }
            res.popup = fees.hasOwnProperty(`${element.partnerId}`) ? fees[`${element.partnerId}`] : null;
            res.showPartnerInfo = !!hospitalDescription?.showPartnerInfo;
            res.banner = hospitalDescription?.banner;
            res.mapView = hospitalDescription?.mapView;
            res.rating = hospitalDescription?.rating || 4.5;
            return res;
        });
        let bookingYesterdayTotal = await this.cacheService.get('daily-transaction-report:latest-report-group-by-partnerId');
        if (!bookingYesterdayTotal) {
            let filterDate = {};
            // 1: Monday, 0: Sunday
            if (
                [0, 1].includes(
                    moment()
                        .utc()
                        .add(7, 'hours')
                        .weekday(),
                )
            ) {
                const fridayNewest = moment()
                    .utc()
                    .add(7, 'hours')
                    .days(1);
                filterDate = { createdAt: { $gte: fridayNewest.startOf('days').toISOString() } };
            }

            bookingYesterdayTotal = await this.transactionDailyModel
                .findOne(filterDate)
                .sort({ createdAt: -1 })
                .exec();
            bookingYesterdayTotal = bookingYesterdayTotal?.hospitals.reduce((r, h) => {
                r[h.partnerId] = h.total;
                return r;
            }, {});
        }
        const hospitalsSponsored = list
            .filter(h => h.sponsored == true)
            .sort((a, b) => (get(bookingYesterdayTotal, a.partnerId, null) > get(bookingYesterdayTotal, b.partnerId, null) ? -1 : 1));
        hospitalsSponsored.forEach(h0 =>
            list.splice(
                list.findIndex(e => e._id == h0._id),
                1,
            ),
        );
        list.sort((a, b) => (get(bookingYesterdayTotal, a.partnerId, null) > get(bookingYesterdayTotal, b.partnerId, null) ? -1 : 1));
        list = [...hospitalsSponsored, ...list];

        const hospitalSortingManual: any = (await this.globalSettingService.findByKeyAndRepoName('HOSPITALS_SORTING_MANUAL')) || '{}';
        const hospitalSortingManualData: Object = JSON.parse(hospitalSortingManual);

        for (let k in hospitalSortingManualData) {
            const thisIndex = list.findIndex(l => l.partnerId == k);
            let targetIndex: number;

            if (has(hospitalSortingManualData, `${k}.next`)) {
                targetIndex = list.findIndex(l => l.partnerId == hospitalSortingManualData[k].next);
                if (targetIndex != -1 && thisIndex != -1) {
                    list.splice(targetIndex == 0 || targetIndex < thisIndex ? targetIndex : targetIndex - 1, 0, list.splice(thisIndex, 1)[0]);
                }
            } else if (has(hospitalSortingManualData, `${k}.prev`)) {
                targetIndex = list.findIndex(l => l.partnerId == hospitalSortingManualData[k].prev);
                if (targetIndex != -1 && thisIndex != -1) {
                    list.splice(
                        targetIndex == list.length - 1 || targetIndex > thisIndex ? targetIndex : targetIndex + 1,
                        0,
                        list.splice(thisIndex, 1)[0],
                    );
                }
            }
        }

        this.cacheService.set(cacheKey, list);

        list = await this.overrideFeaturesModeBeta(list);

        return list;
    }

    async getHospitalStatus(query: any = {}) {
        const { partnerId, treeId = '', platform = 'mobile' } = query;
        const hospital = await this.hospitalModel
            .findOne(
                { partnerId },
                {
                    features: true,
                    message: true,
                    status: true,
                    deliveryStatus: true,
                },
            )
            .exec();

        if (!hospital) {
            throw new HttpException('Không tìm thấy bệnh viện!', 404);
        }

        const feature = hospital.features.find(f => f.type === `booking.${treeId.toLowerCase()}`);

        if (!feature) {
            return {
                partnerId,
                treeId,
                status: false,
                message: hospital.message,
            };
        }

        const [inProgess, hold] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_IN_PROGRESS'),
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_HOLD'),
        ]);

        const deliveryListStatus = {
            hold,
            inProgess,
        };

        const trackingDeliveryStatus = new Set([1, 2]);

        const deliveryMessage = this.deliveryMessageOptimize(hospital.deliveryStatus, deliveryListStatus);
        const isFeatureEnable = this.utilService.isFeatureEnableInplatform(feature, platform);

        return {
            partnerId,
            treeId,
            status: !hospital.message && !deliveryMessage && hospital.status === 1 && isFeatureEnable ? true : false,
            message: hospital.message || deliveryMessage || (feature?.disabled ? feature?.message : ''),
        };
    }

    async getListHospitalStatus(query: any = {}) {
        const { partnerId, platform = 'mobile' } = query;
        const hospitals = await this.hospitalModel
            .find(
                {},
                {
                    partnerId: true,
                    features: true,
                    message: true,
                    status: true,
                    deliveryStatus: true,
                },
            )
            .exec();

        const [inProgess, hold] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_IN_PROGRESS'),
            this.globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_HOLD'),
        ]);

        const deliveryListStatus = {
            hold,
            inProgess,
        };

        const trackingDeliveryStatus = new Set([1, 2]);

        return hospitals.map(hospital => {
            return {
                partnerId: hospital.partnerId,
                features: hospital.features
                    .filter(f => f.type.startsWith('booking.'))
                    .map(feature => {
                        const treeId = feature.type.split('.')[1].toUpperCase();
                        const deliveryMessage = this.deliveryMessageOptimize(hospital.deliveryStatus, deliveryListStatus);
                        const isFeatureEnable = this.utilService.isFeatureEnableInplatform(feature, platform);
                        return {
                            treeId,
                            status: !hospital.message && !deliveryMessage && hospital.status === 1 && isFeatureEnable ? true : false,
                            message: hospital.message || deliveryMessage || (feature?.disabled ? feature?.message : ''),
                        };
                    }),
            };
        });
    }

    resetCacheDanhSachV6() {
        this.cacheService.delByPattern(`hospital-sorting:danh-sach-v6*`);
        this.cacheService.delByPattern(`app-id:partner-by-feature-in-app*`);
        return true;
    }

    async getSeoPage(query: SeoPageQuery) {
        const seoPage = await this.seoPageModel.findOne({ path: query.path }).exec();

        if (!seoPage) {
            throw new HttpException(`Không tìm thấy SEO page ${query.path}`, 404);
        }

        return seoPage;
    }
}
