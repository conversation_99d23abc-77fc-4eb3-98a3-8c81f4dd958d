import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class BookingCare247QueryDto {
    @ApiProperty({
        description: 'transactionId',
        required: false,
        type: String,
    })
    transactionId?: string;
    
    @ApiProperty({
        description: 'cskhUserIdGuide',
        required: false,
        type: String,
    })
    cskhUserIdGuide?: string;

    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId?: string;
    
    @ApiProperty({
        description: 'status',
        required: false,
        type: String,
    })
    status?: number;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    fromDate?: string;
    
    @ApiProperty({
        description: 'Format kiểu YYYY-MM-DD String',
        required: false,
        type: String,
    })
    toDate?: string;


    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @Transform(value => Number(value))
    pageIndex?: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
