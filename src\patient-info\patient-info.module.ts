import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/common';
import { PatientInfoService } from './patient-info.service';
import { PatientInfoController } from './patient-info.controller';
import { PatientInfoSchema } from './schemas/patient-info.schema';
import { HealthHistorySchema } from './schemas/health-history.schema';
import { PATIENT_INFO_COLLECTION_NAME, HEALTH_HISTORY_COLLECTION_NAME } from './schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';
import { GlobalSettingModule } from 'src/global-setting/global-setting.module';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: PATIENT_INFO_COLLECTION_NAME, schema: PatientInfoSchema },
            { name: HEALTH_HISTORY_COLLECTION_NAME, schema: HealthHistorySchema },
            { name: CITY_COLLECTION_NAME, schema: CitySchema },
            { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
            { name: WARD_COLLECTION_NAME, schema: WardSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema}
        ]),
        EventEmitterModule,
        GlobalSettingModule,
        HttpModule,
    ],
    controllers: [PatientInfoController],
    providers: [PatientInfoService],
    exports: [PatientInfoService],
})
export class PatientInfoModule {}
