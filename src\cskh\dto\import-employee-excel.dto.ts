
export class ImportEmployeeExcelBufferDto {
    fileBuffer: Buffer;
    originalName?: string;
}

export interface ImportResult {
    success: boolean;
    message: string;
    data: {
        totalProcessed: number;
        successCount: number;
        errorCount: number;
        addedToAnKhang: string[];
        addedUserIds?: string[]; // User IDs được thêm vào AN_KHANG_CSKH_USER
        validPhones?: string[]; // Số điện thoại hợp lệ (để reference)
        errors: ErrorRecord[];
        errorFileUrl?: string;
        errorFileBuffer?: Buffer;
    };
}

export interface ErrorRecord {
    row: number;
    phone: string;
    error: string;
    reason: 'NOT_REGISTERED' | 'INVALID_PHONE' | 'DUPLICATE' | 'OTHER';
    employeeInfo?: {
        stt?: string;
        maSieuThi?: string;
        tenSieuThi?: string;
        maNhanVien?: string;
        tenNhanVien?: string;
        chucVu?: string;
    };
}
