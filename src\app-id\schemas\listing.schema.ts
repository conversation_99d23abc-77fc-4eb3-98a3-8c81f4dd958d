import { LISTING_COLLECTION_NAME } from './constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import * as json<PERSON><PERSON>o from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { HOSPITAL_DESCRIPTION_COLLECTION_NAME } from '../../hospital-description/schema/constants';
import { DOCTOR_DESCRIPTION_COLLECTION_NAME, SERVICE_COLLECTION_NAME, SERVICE_DESCRIPTION_COLLECTION_NAME } from '../../service-mongo/schemas/constants';


const Schema = mongoose.Schema;

export const ListingSchema = new Schema(
    {
        partnerId: { type: String },
        partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        partnerDescription: { type: Schema.Types.ObjectId, ref: HOSPITAL_DESCRIPTION_COLLECTION_NAME },
        doctorId: { type: String },
        doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
        doctorDescription: { type: Schema.Types.ObjectId, ref: DOCTOR_DESCRIPTION_COLLECTION_NAME },
        packageId: { type: String },
        package: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
        packageDescription: { type: Schema.Types.ObjectId, ref: SERVICE_DESCRIPTION_COLLECTION_NAME },
        category: { type: String },
        order: { type: Number },
        image: { type: String },
        name: { type: String },
        display: { type: Boolean, default: false },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: { type: Boolean, default: false },
        type: { type: String },
    },
    {
        collection: LISTING_COLLECTION_NAME,
        timestamps: true
    },
).plugin(jsonMongo);
