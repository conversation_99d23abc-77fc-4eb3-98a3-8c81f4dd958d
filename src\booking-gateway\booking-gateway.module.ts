import { ConstraintsDateSchema } from './schemas/constraints-date.shema';
import { BookingResultSchema } from './schemas/booking-result.schema';
import { Module, HttpModule, OnModuleInit } from '@nestjs/common';
import { BookingGatewayController } from './booking-gateway.controller';
import { BookingGatewayService } from './booking-gateway.service';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingSchema } from './schemas/booking.schema';
// tslint:disable-next-line: max-line-length
import {
    BOOKING_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    BOOKING_ACTION_COLLECTION_NAME,
    HOSPITAL_FEE_COLLECTION_NAME,
    TRANSACTION_LOG_NAME,
    TRANSACTION_EVENT_NAME,
    EXPIRED_BOOKING_COLLECTION_NAME,
    SECTION_COLLECTION_NAME,
    CONSTRAINTS_COLLECTION_NAME,
    BOOKING_SEARCH_COLLECTION_NAME,
    BOOKING_DATE_CHANGE_EVENT_NAME,
    HEALTH_HISTORY_COLLECTION_NAME,
    EXAM_RESULT_LOG_COLLECTION_NAME,
    BOOKING_ORDER_COLLECTION_NAME,
    NEW_BILL_LOG_COLLECTION_NAME,
    BOOKING_RESULT_COLLECTION_NAME,
    CONSTRAINTS_DATE_COLLECTION_NAME,
    BOOKING_TRACKING_COLLECTION_NAME,
    RETRY_TRASACTION_COLLECTION_NAME,
    CONSTRAINTS_BOOKING_UDPATE_COLLECTION_NAME,
    CANCEL_RESERVATIONS_COLLECTION_NAME,
    BOOKING_LOCKED_COLLECTION_NAME,
    VIEW_BOOKING_LOG_COLLECTION_NAME,
    BOOKING_COMPLAINS_COLLECTION_NAME,
    MEDPRO_CARE_TRACKING,
    BOOKING_CARE_247,
    CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY,
    VISA_USER_BOOKINGS_COLLECTION_NAME,
    BVMAT_DETECT_ONEDAY_ISSUES,
    DEAL_COLLECTION_NAME,
    CONVERT_USER_CSKH,
} from './schemas/constants';
import { HisGatewayService } from 'src/his-gateway/his-gateway.service';
import { BOOKING_SLOT_COLLECTION_NAME, BOOKING_HIS_COLLECTION_NAME, CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME, BOOKING_NUMBER_COLLECTION_NAME } from 'src/his-gateway/schemas/constants';
import { BookingSlotSchema } from 'src/his-gateway/schemas/booking-slot.schema';
import { BookingHISSchema } from 'src/his-gateway/schemas/booking.schema';
import { PaymentSchema } from './schemas/payment.schema';
import { UserSchema } from 'src/user/schemas/user.schema';
import {
    USER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    ORG_PROFILE_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
    USER_REQUEST_COLLECTION_NAME,
    USER_DOCTOR_COLLECTION_NAME,
    VISA_USER_COLLECTION_NAME,
} from "src/user/schemas/constants";
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SERVICE_ATTRIBUTE_COLLECTION_NAME, SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import {
  PATIENT_COLLECTION_NAME, PATIENT_CODE_COLLECTION_NAME, PATIENT_SEARCH_LOG_COLLECTION_NAME,
  PATIENT_VERSION_COLLECTION_NAME, RELATIVE_TYPE_COLLECTION_NAME, PATIENT_PROFILE_COLLECTION_NAME,
  PATIENT_TRACKING_COLLECTION_NAME,
} from 'src/patient-mongo/schemas/constants';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { HOSPITAL_COLLECTION_NAME, PUSH_NOTIF_CSKH } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { BookingActionSchema } from './schemas/booking-action.schema';
import { EventService } from 'src/event/event.service';
import {
    EVENT_COLLECTION_NAME,
    MESSAGE_SEND_RECORD_COLLECTION_NAME,
    MESSAGE_SEND_CONFIG_COLLECTION_NAME,
    SYNC_BOOKING_PROCESS,
    SYNC_BOOKING_SUCCESS,
    SYNC_BOOKING_PROCESS_FAILED,
    SYNC_PATIENT_PROCESS,
    SYNC_PATIENT_PROCESS_FAILED,
    SYNC_PATIENT_SUCCESS,
    SYNC_BOOKING_DATE_PROCESS,
    SYNC_BOOKING_DATE_PROCESS_FAILED,
    SYNC_BOOKING_DATE_SUCCESS,
    SYNC_SKIN_BOOKING_PROCESS,
    SYNC_SKIN_BOOKING_PROCESS_FAILED,
    SYNC_SKIN_BOOKING_SUCCESS,
    SYNC_SKIN_PATIENT_PROCESS,
    SYNC_SKIN_PATIENT_PROCESS_FAILED,
    SYNC_SKIN_PATIENT_SUCCESS,
    SYNC_USER_PROCESS,
    SYNC_USER_PROCESS_FAILED,
    SYNC_USER_SUCCESS,
    SYNC_V2_BOOKING,
    SYNC_DALIEU_PATIENT,
    SYNC_DALIEU_PATIENT_PROCESS,
    SYNC_DALIEU_PATIENT_PROCESS_FAILED,
    SYNC_DALIEU_PATIENT_SUCCESS,
    SYNC_DALIEU_BOOKING,
    SYNC_DALIEU_BOOKING_PROCESS,
    SYNC_DALIEU_BOOKING_SUCCESS,
    SYNC_DALIEU_BOOKING_PROCESS_FAILED,
    SYNC_ND1_BOOKING_PROCESS,
    SYNC_ND1_BOOKING_SUCCESS,
    SYNC_ND1_BOOKING_PROCESS_FAILED,
    SEND_MAIL_OR_SMS,
    ADDRESS_TRACKING_COLLECTION_NAME,
    SYNC_NHI_DONG_1_PATIENT,
    SYNC_NHI_DONG_1_PATIENT_PROCESS,
    SYNC_NHI_DONG_1_PATIENT_PROCESS_FAILED,
    SYNC_NHI_DONG_1_PATIENT_SUCCESS,
    SYNC_NHI_DONG_1_BOOKING,
    SYNC_NHI_DONG_1_BOOKING_PROCESS,
    SYNC_NHI_DONG_1_BOOKING_PROCESS_FAILED,
    SYNC_NHI_DONG_1_BOOKING_SUCCESS,
    SYNC_DHYD_PATIENT,
    SYNC_DHYD_PATIENT_SUCCESS,
    SYNC_DHYD_BOOKING,
    SYNC_DHYD_PATIENT_PROCESS,
    SYNC_DHYD_BOOKING_PROCESS,
    SYNC_DHYD_BOOKING_PROCESS_FAILED,
    SYNC_DHYD_PATIENT_PROCESS_FAILED,
    SYNC_DHYD_BOOKING_SUCCESS,
    NOTIFICATION_COLLECTION,
    MESSAGE_EVENT_COLLECTION,
    MESSAGE_EVENT_PROCESS_COLLECTION,
    MESSAGE_EVENT_PROCESS_FAILED_COLLECTION,
    MESSAGE_EVENT_SUCCESS_COLLECTION,
} from 'src/event/schemas/constants';
import { EventSchema } from 'src/event/schemas/event.schema';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { PushDeviceSchema } from 'src/push-device/schemas/push-device.schema';
import { PatientCodeSchema } from 'src/patient-mongo/schemas/patient-codes.schema';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { NationSchema } from 'src/nation-mongo/schemas/nation.schema';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { ProfessionSchema } from 'src/profession-mongo/schemas/profession.schema';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PatientService } from 'src/patient/patient.service';
import { PatientSearchLogSchema } from 'src/patient-mongo/schemas/patient-search-log.schema';
import { PaymentFeeGatewayService } from 'src/payment-fee-gateway/payment-fee-gateway.service';
import { HospitalFeeSchema } from './schemas/hospital-fee.schema';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { RoomSchema } from 'src/room-mongo/schemas/room.schema';
import { DoctorSchema } from 'src/doctor-mongo/schemas/doctor.schema';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { TransactionLogSchema } from './schemas/transaction-log.schema';
import { MailerService } from 'src/mailer/mailer.service';
import { RECEIVER_LIST_COLLECTION_NAME, RECEIVER_VERSION_COLLECTION_NAME, MESSAGE_ITEM_COLLECTION_NAME } from 'src/message-config/schemas/constants';
import { ReceiverListSchema } from 'src/message-config/schemas/receiver-list.schema';
import { ReceiverVersionListSchema } from 'src/message-config/schemas/receiver-version.schema';
import { MessageItemSchema } from 'src/message-config/schemas/message-item.schema';
import { MessageSendRecordSchema } from 'src/event/schemas/message-send-record.schema';
import { MessageSendConfigSchema } from 'src/event/schemas/message-send-config.schema';
import { TransactionEventSchema } from './schemas/transaction-event.schema';
import { PatientVersionSchema } from 'src/patient-mongo/schemas/patient-version.schema';
import { SignInProviderSchema } from 'src/user/schemas/signin-provider.schema';
import { ExpiredBookingSchema } from './schemas/expired-booking.schema';
import { SessionService } from 'src/session/session.service';
import { RelativeSchema } from 'src/patient-mongo/schemas/relative-mongo.schema';
import { SectionSchema } from './schemas/section.schema';
import { UserService } from 'src/user/user.service';
import { LocalUserStrategy } from 'src/user/local.user.strategy';
import { UserJwtStrategy } from 'src/user/jwt.strategy';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ORDER_COLLECTION_NAME, ORDER_ITEM_COLLECTION_NAME } from 'src/vaccine/schemas/constants';
import { OrderSchema } from 'src/vaccine/schemas/orders.schema';
import { OrderItemSchema } from 'src/vaccine/schemas/order-items.schema';
import { RE_EXAM_COLLECTION_NAME } from 'src/re-exam/schemas/constants';
import { ReExamSchema } from 'src/re-exam/schemas/re-exam.schema';
import { ConstraintsSchema } from './schemas/constraints.schema';
import { ProviderConstraintSchema } from 'src/user/schemas/provider-constraints.schema';
import { UserConstraintSchema } from 'src/user/schemas/user-constraints.schema';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { UserAccountSchema } from 'src/user-account/schemas/user-account.schema';
import { FeederSchema } from 'src/event-proccessor/schemas/feeder.schema';
import { FEEDER_COLLECTION_NAME } from 'src/event-proccessor/schemas/constants';
import { UserProfileSchema } from 'src/user/schemas/user-profile.schema';
import { BookingSearchSchema } from './schemas/search-booking.schema';
import { SyncBookingService } from 'src/sync-booking/sync-booking.service';
import { CONSTRAINTS_BOOKING_CODE_COLLECTION_NAME, CONSTRAINTS_PAYMENT_TRANSACTION_COLLECTION_NAME } from 'src/sync-booking/schemas/constants';
import { ConstraintsBookingCodeSchema } from 'src/sync-booking/schemas/constraints-booking-code.schema';
import { ConstraintsPaymentTransactionSchema } from 'src/sync-booking/schemas/constraints-payment-transaction.schema';
import { SyncBookingProcessSchema } from 'src/event/schemas/sync-booking-process.schema';
import { SyncBookingSuccessSchema } from 'src/event/schemas/sync-booking-success.schema';
import { SyncBookingProcessFailedSchema } from 'src/event/schemas/sync-booking-process-failed.schema';
import { SyncPatientProcessSchema } from 'src/event/schemas/sync-patient-process.schema';
import { SyncPatientProcessFailedSchema } from 'src/event/schemas/sync-patient-process-failed.schema';
import { SyncPatientSuccessSchema } from 'src/event/schemas/sync-patient-success.schema';
import { BookingDateChangeEventSchema } from './schemas/bookimg-date-change-event.schema';
import { SyncBookingDateProcessFailedSchema } from 'src/event/schemas/sync-booking-date-process-failed.schema';
import { SyncBookingDateSuccessSchema } from 'src/event/schemas/sync-booking-date-success.schema';
import { SyncBookingDateProcessSchema } from 'src/event/schemas/sync-booking-date-process.schema';
import { EventProccessorService } from 'src/event-proccessor/event-proccessor.service';
import { SyncSkinBookingProcessSchema } from 'src/event/schemas/sync-skin-booking-process.schema';
import { SyncSkinBookingSuccessSchema } from 'src/event/schemas/sync-skin-booking-success.schema';
import { SyncSkinBookingProcessFailedSchema } from 'src/event/schemas/sync-skin-booking-process-failed.schema';
import { SyncSkinPatientProcessSchema } from 'src/event/schemas/sync-skin-patient-process.schema';
import { SyncSkinPatientProcessFailedSchema } from 'src/event/schemas/sync-skin-patient-process-failed.schema';
import { SyncSkinPatientSuccessSchema } from 'src/event/schemas/sync-skin-patient-success.schema';
import { SyncUserProcessSchema } from 'src/event/schemas/sync-user-process.schema';
import { SyncUserProcessFailedSchema } from 'src/event/schemas/sync-user-process-failed.schema';
import { SyncUserSuccessSchema } from 'src/event/schemas/sync-user-success.schema';
import { SyncUserService } from 'src/sync-user/sync-user.service';
import { FilesService } from 'src/files/files.service';
import { HocViSchema } from 'src/user/schemas/hoc-vi.schema';
import { ViTriCongViecSchema } from 'src/user/schemas/vi-tri-cong-viec.schema';
import {
  SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME, SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME,
} from 'src/sync-trung-vuong-medpro/schemas/constants';
import { SyncUserTrungVuongProcessSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-process.schema';
import { SyncUserTrungVuongSuccessSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-success.schema';
import { SyncUserTrungVuongProcessFailedSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-booking-process-failed.schema';
import { SyncUserTrungVuongUpgradeSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-upgrade.schema';
import { SyncUserDaLieuUpgradeSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-upgrade.schema';
import {
    SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME,
    SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME,
    SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME,
    SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME,
} from 'src/sync-da-lieu-medpro/schemas/constants';
import { SyncUserDaLieuProcessSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-process.schema';
import { SyncUserDaLieuProcessFailedSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-process-failed.schema';
import { SyncUserDaLieuSuccessSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-success.schema';
import { HealthHistorySchema } from './schemas/health-history.schema';
import { ConstraintsSequenceNumberSchema } from 'src/his-gateway/schemas/constraints-sequence-number.schema';
import { SyncV2BookingSchema } from 'src/event/schemas/sync-v2-booking.schema';
import { ExamResultLogSchema } from './schemas/exam-result-log.schema';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDaLieuPatientProcessSchema } from 'src/event/schemas/sync-dalieu-patient-process.schema';
import { SyncDaLieuPatientProcessFailedSchema } from 'src/event/schemas/sync-dalieu-patient-process-failed.schema';
import { SyncDaLieuPatientSuccessSchema } from 'src/event/schemas/sync-dalieu-patient-success.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { SyncDaLieuBookingProcessSchema } from 'src/event/schemas/sync-dalieu-booking-process.schema';
import { SyncDaLieuBookingSuccessSchema } from 'src/event/schemas/sync-dalieu-booking-success.schema';
import { SyncDaLieuBookingProcessFailedSchema } from 'src/event/schemas/sync-dalieu-booking-process-failed.schema';
import { OrgProfileSchema } from 'src/user/schemas/org-profile.schema';
import { SyncNd1BookingProcessSchema } from 'src/event/schemas/sync-nd1-booking-process.schema';
import { SyncNd1BookingSuccessSchema } from 'src/event/schemas/sync-nd1-booking-success.schema';
import { SyncNd1BookingProcessFailedSchema } from 'src/event/schemas/sync-nd1-booking-process-failed.schema';
import { SendMailOrSmsSchema } from 'src/event/schemas/send-mail-or-sms.schema';
import { AddressTrackingSchema } from 'src/event/schemas/address-tracking.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncNhiDong1PatientProcessSchema } from 'src/event/schemas/sync-nhidong1-patient-process.schema';
import { SyncNhiDong1PatientProcessFailedSchema } from 'src/event/schemas/sync-nhidong1-patient-process-failed.schema';
import { SyncNhiDong1PatientSuccessSchema } from 'src/event/schemas/sync-nhidong1-patient-success.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { SyncNhiDong1BookingProcessSchema } from 'src/event/schemas/sync-nhidong1-booking-process.schema';
import { SyncNhiDong1BookingProcessFailedSchema } from 'src/event/schemas/sync-nhidong1-booking-process-failed.schema';
import { SyncNhiDong1BookingSuccessSchema } from 'src/event/schemas/sync-nhidong1-booking-success.schema';
import { ReferralCodeRegisterSchema } from 'src/user/schemas/referral-code-register';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncDHYDPatientSuccessSchema } from 'src/event/schemas/sync-dhyd-patient-success.schema';
import { SyncDHYDBookingProcessSchema } from 'src/event/schemas/sync-dhyd-booking-process.schema';
import { SyncDHYDBookingProcessFailedSchema } from 'src/event/schemas/sync-dhyd-booking-process-failed.schema';
import { SyncDHYDBookingSuccessSchema } from 'src/event/schemas/sync-dhyd-booking-success.schema';
import { SyncDHYDPatientProcessSchema } from 'src/event/schemas/sync-dhyd-patient-process.schema';
import { SyncDHYDPatientProcessFailedSchema } from 'src/event/schemas/sync-dhyd-patient-process-failed.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { BookingOrderSchema } from './schemas/booking-order';
import { NewBillLogSchema } from './schemas/new-bill-log.schema';
import { NotificationSchema } from 'src/event/schemas/notification.schema';
import { BookingNumberSchema } from 'src/his-gateway/schemas/booking-numbers.schema';
import { MessageEventSchema } from 'src/event/schemas/message-event.schema';
import { MessageEventProcessSchema } from 'src/event/schemas/message-event-process.schema';
import { MessageEventProcessFailedSchema } from 'src/event/schemas/message-event-process-failed.schema';
import { MessageEventSuccessSchema } from 'src/event/schemas/message-event-success.schema';
import { UrlConfigService } from 'src/config/config.url.service';
import { CHECK_FILTER_COLLECTION_NAME } from 'src/filter-process/schemas/constants';
import { CheckFilterSchema } from 'src/filter-process/schemas/check-filter.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { BookingTrackingSchema } from './schemas/booking-tracking.schema';
import { RetryTransactionSchema } from './schemas/retry-transactions.schema';
import { ConfigRepoService } from '../config/config.repo.service';
import { UserPermissionModule } from 'src/user-permission/user-permission.module';
import { AppIdModule } from './../app-id/app-id.module';
import { UserRequestsSchema } from 'src/user/schemas/user-requests.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { UserModule } from 'src/user/user.module';
import { ConstraintsBookingUpdateSchema } from './schemas/constraints-booking-update.schema';
import { CancelReservationsSchema } from './schemas/cancel-reservations.schema';
import { AuthModule } from "../auth/auth.module";
import { PatientTrackingSchema } from "../patient-mongo/schemas/patient-tracking.schema";
import { UserDoctorSchema } from "../user/schemas/user-doctor.schema";
import { EventModule } from '../event/event.module';
import { ServiceAttributeSchema } from '../service-mongo/schemas/service-attribute.schema';
import { DynamicLinkModule } from '../dynamic-link/dynamic-link.module';
import { BookingLockSchema } from './schemas/booking-locked.schema';
import { ViewBookingLogSchema } from './schemas/view-booking-log.schema';
import { PaymentMethodModule } from '../payment-method/payment-method.module';
import { BookingComplainSchema } from './schemas/booking-complain.schema';
import { MedproCareTrackingSchema } from './schemas/medpro-care-tracking.schema';
import { BookingCare247Schema } from './schemas/booking-care-247.schema';
import { BOOKING_CARE_247_CONSTRAINT, BOOKING_CARE_247_CONSTRAINT_USER } from '../cskh/schemas/constants';
import { BookingCare247ConstraintSchema } from '../cskh/schemas/booking-care247-constraint.schema';
import { PushNotifCskhSchema } from '../cskh/schemas/push-notif-cskh.schema';
import { FirebaseConfigModule } from 'src/firebase-config/firebase-config.module';
import { CskhModule } from '../cskh/cskh.module';
import { ConstraintsUserPatientBookingOneDaySchema } from './schemas/constraints-booking-user-patient-one-day.schema';
import { BookingCare247ConstraintUserSchema } from '../cskh/schemas/booking-care247-constraint-user.schema';
import { VisaUserBookingsSchema } from './schemas/visa-user-bookings.schema';
import { VisaUserSchema } from '../user/schemas/visa-users.schema';
import { BvmatDetectOnedayIssuesSchema } from './schemas/bvmat-detect-oneday-issues.schema';
import { COOPERATION, COOPERATION_PACKAGES } from '../cooperation/schemas/constants';
import { CooperationPackagesSchema } from '../cooperation/schemas/cooperation-packages.schema';
import { DealSchema } from './schemas/deals.schema';
import { CooperationsSchema } from '../cooperation/schemas/cooperation.schema';
import { ConvertUserCskhSchema } from './schemas/convert-user-cskh.schema';

@Module({
  imports: [
    HttpModule,
    PassportModule.register({ defaultStrategy: 'user-jwt' }),
    JwtModule.registerAsync({
      useExisting: JwtUserConfigService,
    }),
    MongooseModule.forFeature([
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: EXPIRED_BOOKING_COLLECTION_NAME, schema: ExpiredBookingSchema },
      { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
      { name: BOOKING_SLOT_COLLECTION_NAME, schema: BookingSlotSchema },
      { name: BOOKING_HIS_COLLECTION_NAME, schema: BookingHISSchema },
      { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
      { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PATIENT_VERSION_COLLECTION_NAME, schema: PatientVersionSchema },
      { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
      { name: PATIENT_SEARCH_LOG_COLLECTION_NAME, schema: PatientSearchLogSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: BOOKING_ACTION_COLLECTION_NAME, schema: BookingActionSchema },
      { name: EVENT_COLLECTION_NAME, schema: EventSchema },
      { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: NATION_COLLECTION_NAME, schema: NationSchema },
      { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: WARD_COLLECTION_NAME, schema: WardSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: HOSPITAL_FEE_COLLECTION_NAME, schema: HospitalFeeSchema },
      { name: ROOM_COLLECTION_NAME, schema: RoomSchema },
      { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
      { name: TRANSACTION_LOG_NAME, schema: TransactionLogSchema },
      { name: RECEIVER_LIST_COLLECTION_NAME, schema: ReceiverListSchema },
      { name: RECEIVER_VERSION_COLLECTION_NAME, schema: ReceiverVersionListSchema },
      { name: BOOKING_COMPLAINS_COLLECTION_NAME, schema: BookingComplainSchema },
      { name: MESSAGE_ITEM_COLLECTION_NAME, schema: MessageItemSchema },
      { name: MESSAGE_SEND_RECORD_COLLECTION_NAME, schema: MessageSendRecordSchema },
      { name: MESSAGE_SEND_CONFIG_COLLECTION_NAME, schema: MessageSendConfigSchema },
      { name: TRANSACTION_EVENT_NAME, schema: TransactionEventSchema },
      { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
      { name: SECTION_COLLECTION_NAME, schema: SectionSchema },
      { name: ORDER_COLLECTION_NAME, schema: OrderSchema },
      { name: ORDER_ITEM_COLLECTION_NAME, schema: OrderItemSchema },
      { name: RE_EXAM_COLLECTION_NAME, schema: ReExamSchema },
      { name: CONSTRAINTS_COLLECTION_NAME, schema: ConstraintsSchema },
      { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
      { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
      { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
      { name: FEEDER_COLLECTION_NAME, schema: FeederSchema },
      { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
      { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
      { name: BOOKING_SEARCH_COLLECTION_NAME, schema: BookingSearchSchema },
      { name: CONSTRAINTS_BOOKING_CODE_COLLECTION_NAME, schema: ConstraintsBookingCodeSchema },
      { name: CONSTRAINTS_PAYMENT_TRANSACTION_COLLECTION_NAME, schema: ConstraintsPaymentTransactionSchema },
      { name: SYNC_V2_BOOKING, schema: SyncV2BookingSchema },
      { name: SYNC_BOOKING_PROCESS, schema: SyncBookingProcessSchema },
      { name: SYNC_BOOKING_PROCESS_FAILED, schema: SyncBookingProcessFailedSchema },
      { name: SYNC_BOOKING_SUCCESS, schema: SyncBookingSuccessSchema },
      { name: SYNC_SKIN_BOOKING_PROCESS, schema: SyncSkinBookingProcessSchema },
      { name: SYNC_SKIN_BOOKING_PROCESS_FAILED, schema: SyncSkinBookingProcessFailedSchema },
      { name: SYNC_SKIN_BOOKING_SUCCESS, schema: SyncSkinBookingSuccessSchema },
      { name: SYNC_PATIENT_PROCESS, schema: SyncPatientProcessSchema },
      { name: SYNC_PATIENT_PROCESS_FAILED, schema: SyncPatientProcessFailedSchema },
      { name: SYNC_PATIENT_SUCCESS, schema: SyncPatientSuccessSchema },
      { name: BOOKING_DATE_CHANGE_EVENT_NAME, schema: BookingDateChangeEventSchema },
      { name: SYNC_BOOKING_DATE_PROCESS, schema: SyncBookingDateProcessSchema },
      { name: SYNC_BOOKING_DATE_PROCESS_FAILED, schema: SyncBookingDateProcessFailedSchema },
      { name: SYNC_BOOKING_DATE_SUCCESS, schema: SyncBookingDateSuccessSchema },
      { name: SYNC_SKIN_PATIENT_PROCESS, schema: SyncSkinPatientProcessSchema },
      { name: SYNC_SKIN_PATIENT_PROCESS_FAILED, schema: SyncSkinPatientProcessFailedSchema },
      { name: SYNC_SKIN_PATIENT_SUCCESS, schema: SyncSkinPatientSuccessSchema },
      { name: SYNC_USER_PROCESS, schema: SyncUserProcessSchema },
      { name: SYNC_USER_PROCESS_FAILED, schema: SyncUserProcessFailedSchema },
      { name: SYNC_USER_SUCCESS, schema: SyncUserSuccessSchema },
      { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
      { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
      { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
      { name: SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME, schema: SyncUserTrungVuongProcessSchema },
      { name: SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME, schema: SyncUserTrungVuongProcessFailedSchema },
      { name: SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME, schema: SyncUserTrungVuongSuccessSchema },
      { name: SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME, schema: SyncUserTrungVuongUpgradeSchema },
      { name: SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME, schema: SyncUserDaLieuProcessSchema },
      { name: SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME, schema: SyncUserDaLieuProcessFailedSchema },
      { name: SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME, schema: SyncUserDaLieuSuccessSchema },
      { name: SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME, schema: SyncUserDaLieuUpgradeSchema },
      { name: HEALTH_HISTORY_COLLECTION_NAME, schema: HealthHistorySchema },
      { name: CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME, schema: ConstraintsSequenceNumberSchema },
      { name: EXAM_RESULT_LOG_COLLECTION_NAME, schema: ExamResultLogSchema },
      { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
      { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
      { name: SYNC_DALIEU_PATIENT_PROCESS, schema: SyncDaLieuPatientProcessSchema },
      { name: SYNC_DALIEU_PATIENT_PROCESS_FAILED, schema: SyncDaLieuPatientProcessFailedSchema },
      { name: SYNC_DALIEU_PATIENT_SUCCESS, schema: SyncDaLieuPatientSuccessSchema },
      { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
      { name: SYNC_DALIEU_BOOKING_PROCESS, schema: SyncDaLieuBookingProcessSchema },
      { name: SYNC_DALIEU_BOOKING_PROCESS_FAILED, schema: SyncDaLieuBookingProcessFailedSchema },
      { name: SYNC_DALIEU_BOOKING_SUCCESS, schema: SyncDaLieuBookingSuccessSchema },
      { name: SYNC_ND1_BOOKING_PROCESS, schema: SyncNd1BookingProcessSchema },
      { name: SYNC_ND1_BOOKING_PROCESS_FAILED, schema: SyncNd1BookingProcessFailedSchema },
      { name: SYNC_ND1_BOOKING_SUCCESS, schema: SyncNd1BookingSuccessSchema },
      { name: SEND_MAIL_OR_SMS, schema: SendMailOrSmsSchema },
      { name: ADDRESS_TRACKING_COLLECTION_NAME, schema: AddressTrackingSchema },
      { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
      { name: SYNC_NHI_DONG_1_PATIENT_PROCESS, schema: SyncNhiDong1PatientProcessSchema },
      { name: SYNC_NHI_DONG_1_PATIENT_PROCESS_FAILED, schema: SyncNhiDong1PatientProcessFailedSchema },
      { name: SYNC_NHI_DONG_1_PATIENT_SUCCESS, schema: SyncNhiDong1PatientSuccessSchema },
      { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
      { name: SYNC_NHI_DONG_1_BOOKING_PROCESS, schema: SyncNhiDong1BookingProcessSchema },
      { name: SYNC_NHI_DONG_1_BOOKING_PROCESS_FAILED, schema: SyncNhiDong1BookingProcessFailedSchema },
      { name: SYNC_NHI_DONG_1_BOOKING_SUCCESS, schema: SyncNhiDong1BookingSuccessSchema },
      { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
      { name: SYNC_DHYD_PATIENT_PROCESS, schema: SyncDHYDPatientProcessSchema },
      { name: SYNC_DHYD_PATIENT_PROCESS_FAILED, schema: SyncDHYDPatientProcessFailedSchema },
      { name: SYNC_DHYD_PATIENT_SUCCESS, schema: SyncDHYDPatientSuccessSchema },
      { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
      { name: SYNC_DHYD_BOOKING_PROCESS, schema: SyncDHYDBookingProcessSchema },
      { name: SYNC_DHYD_BOOKING_PROCESS_FAILED, schema: SyncDHYDBookingProcessFailedSchema },
      { name: SYNC_DHYD_BOOKING_SUCCESS, schema: SyncDHYDBookingSuccessSchema },
      { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
      { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
      { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
      { name: NOTIFICATION_COLLECTION, schema: NotificationSchema },
      { name: BOOKING_NUMBER_COLLECTION_NAME, schema: BookingNumberSchema },
      { name: MESSAGE_EVENT_COLLECTION, schema: MessageEventSchema },
      { name: MESSAGE_EVENT_PROCESS_COLLECTION, schema: MessageEventProcessSchema },
      { name: MESSAGE_EVENT_PROCESS_FAILED_COLLECTION, schema: MessageEventProcessFailedSchema },
      { name: MESSAGE_EVENT_SUCCESS_COLLECTION, schema: MessageEventSuccessSchema },
      { name: CHECK_FILTER_COLLECTION_NAME, schema: CheckFilterSchema },
      { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: BOOKING_RESULT_COLLECTION_NAME, schema: BookingResultSchema },
      { name: CONSTRAINTS_DATE_COLLECTION_NAME, schema: ConstraintsDateSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
      { name: BOOKING_TRACKING_COLLECTION_NAME, schema: BookingTrackingSchema },
      { name: RETRY_TRASACTION_COLLECTION_NAME, schema: RetryTransactionSchema },
      { name: CONSTRAINTS_BOOKING_UDPATE_COLLECTION_NAME, schema: ConstraintsBookingUpdateSchema },
      { name: CANCEL_RESERVATIONS_COLLECTION_NAME, schema: CancelReservationsSchema },
      { name: USER_DOCTOR_COLLECTION_NAME, schema: UserDoctorSchema },
      { name: PATIENT_TRACKING_COLLECTION_NAME, schema: PatientTrackingSchema },
      { name: SERVICE_ATTRIBUTE_COLLECTION_NAME, schema: ServiceAttributeSchema },
      { name: BOOKING_LOCKED_COLLECTION_NAME, schema: BookingLockSchema },
      { name: VIEW_BOOKING_LOG_COLLECTION_NAME, schema: ViewBookingLogSchema },
      { name: MEDPRO_CARE_TRACKING, schema: MedproCareTrackingSchema },
      { name: BOOKING_CARE_247, schema: BookingCare247Schema },
      { name: BOOKING_CARE_247_CONSTRAINT, schema: BookingCare247ConstraintSchema },
      { name: PUSH_NOTIF_CSKH, schema: PushNotifCskhSchema },
      { name: BOOKING_CARE_247_CONSTRAINT_USER, schema: BookingCare247ConstraintUserSchema },
      { name: CONSTRAINTS_USER_PATIENT_BOOKING_ONEDAY, schema: ConstraintsUserPatientBookingOneDaySchema },
      { name: VISA_USER_COLLECTION_NAME, schema: VisaUserSchema },
      { name: VISA_USER_BOOKINGS_COLLECTION_NAME, schema: VisaUserBookingsSchema },
      { name: BVMAT_DETECT_ONEDAY_ISSUES, schema: BvmatDetectOnedayIssuesSchema },
      { name: COOPERATION_PACKAGES, schema: CooperationPackagesSchema },
      { name: DEAL_COLLECTION_NAME, schema: DealSchema },
      { name: COOPERATION, schema: CooperationsSchema },
      { name: CONVERT_USER_CSKH, schema: ConvertUserCskhSchema },

    ]),
    UserPermissionModule,
    AppIdModule,
    PatientMongoModule,
    UserModule,
    AuthModule,
    EventModule,
    DynamicLinkModule,
    PaymentMethodModule,
    FirebaseConfigModule,
    CskhModule
  ],
  controllers: [BookingGatewayController],
  providers: [BookingGatewayService, HisGatewayService, FilesService,
    PatientService, PaymentFeeGatewayService, SyncBookingService, SyncUserService,
    MailerService, SessionService, LocalUserStrategy, UserJwtStrategy, PhoneLoginService, SmsService, EventProccessorService,
    ReferralCodeService, GlobalSettingService
  ],
  exports: [BookingGatewayService],
})
export class BookingGatewayModule implements OnModuleInit {
    private readonly allowTelemedReminder: boolean;

    private readonly repoValidToRemind = 'REPO_VALID_REMIND_TELEMD_JOB';

    constructor(
        private bookingGatewayService: BookingGatewayService,
        private urlConfigService: UrlConfigService,
        private readonly globalSettingService: GlobalSettingService,
        private configRepo: ConfigRepoService,
    ) {
        this.allowTelemedReminder = this.urlConfigService.getAllowTelemedReminder();
    }

  async onModuleInit() {
    try {
      const repoValidToRemind = await this.globalSettingService.findByKeyAndRepoName(this.repoValidToRemind);
      if (this.allowTelemedReminder && process.env.NODE_ENV !== 'development' && repoValidToRemind === this.configRepo.getRepoName()) {
        await Promise.all([this.bookingGatewayService.reScheduleCronJobRemindTelemed(), this.bookingGatewayService.remindBefore30Minutes()]);
      }
    } catch (error) {
      console.log(error)
    }

  }
}
