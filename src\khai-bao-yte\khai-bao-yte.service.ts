import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { KHAI_BAO_YTE_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import { IKhaiBaoYTe } from './intefaces/khai-bao-yte.inteface';
import { SubmitKhaiBaoYTeDTO } from './dto/submit-form.dto';
import * as uuid from 'uuid';
import { UrlConfigService } from 'src/config/config.url.service';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { PkhHttpService } from 'src/config/config.http.service';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';

@Injectable()
export class KhaiBaoYteService {

    constructor(
        @InjectModel(KHAI_BAO_YTE_COLLECTION_NAME) private yteModel: Model<IKhaiBaoYTe>,
        private readonly urlConfigService: UrlConfigService,
        private readonly httpService: PkhHttpService,
        @InjectSentry() private readonly clientSentry: SentryService,
    ) { }

    async submit(appId: string, partnerId: string, formData: SubmitKhaiBaoYTeDTO): Promise<any> {
        const jsonObject = JSON.parse(formData.data);
        // const { data } = jsonObject;
        const { version = 1, id: idInData } = jsonObject;
        if (version === 1) {
            const id = uuid.v4().replace(/-/g, '');
            const yte = new this.yteModel({ id, data: { ...jsonObject }, partnerId });
            return yte.save();
        } else {
            /* gọi qua Lâm */
            try {
                const dataDetail = (await this.getDetailKhaiBaoYTe(idInData)
                    .toPromise()).data;
                const id = uuid.v4().replace(/-/g, '');
                const yte = new this.yteModel({ id, data: { ...dataDetail }, partnerId });
                return yte.save();
            } catch (error) {
                this.clientSentry.instance().captureException(error);
                throw new HttpException('Không tìm thấy thông tin khai bao y te', HttpStatus.BAD_REQUEST);
            }

        }
    }

    getDetailKhaiBaoYTe(id: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/web-form/khaibaoyte-info/${id}`;
        return this.httpService.getHttpRequest(url);
    }

    // async submit(appId: string, partnerId: string, formData: SubmitKhaiBaoYTeDTO): Promise<any> {
    //     const jsonObject = JSON.parse(formData.data);
    //     const id = uuid.v4().replace(/-/g, '');
    //     const yte = new this.yteModel({ id, data: { ...jsonObject }, partnerId });
    //     return yte.save();
    // }

    async list(appId: string, partnerId: string): Promise<any> {
        return this.yteModel.find({ partnerId }).sort({ createdAt: 'desc' }).exec();
    }

}
